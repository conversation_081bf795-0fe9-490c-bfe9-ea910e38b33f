"use client"

import Image from 'next/image';

import { EmailSubscription } from "../emailSubscription"
import { socialLinks } from '@/config/socialLinks'
import { Footer } from "../footer"
import { useDeviceDetect } from "../DeviceDetection"

export const CallToAction = () => {
  const { isMobile } = useDeviceDetect();

  return (
    <section className="py-[10px] pt-[10px] sm:pt-10 md:pt-0 pb-0 relative lg:min-h-[691px] md:min-h-[691px] h-auto pb-0 mb-0">
  {/* Background */}
  <div className="absolute inset-0">
       <img 
      src="/images/footer.png" 
      alt="Background" 
      className="w-full h-full object-cover hidden sm:hidden lg:block" 
    />
    <img 
      src="/images/footer-tab.png" 
      alt="Background" 
      className="w-full h-full object-cover hidden sm:block lg:hidden" 
    />
    <img 
      src="/images/footer-m.png" 
      alt="Background" 
      className="w-full h-full object-cover block sm:hidden lg:hidden" 
    />
  </div>

  <div className="container mt-4 mb-1 sm:mb-1 md:mb-1 lg:mt-0 sm:mt-24 md:mt-4 mx-auto text-center relative">
  <br className="hidden sm:hidden lg:block" />
  <br className="hidden sm:hidden lg:block" />
  <br className="hidden sm:block lg:block" />
  <br className="hidden sm:block lg:block" />
  <br className="hidden sm:block lg:block" />
  <br className="hidden sm:block lg:block" />
  <br className="hidden sm:block lg:block" />
    <h2 className="pl-[61px] pr-[61px] sm:pr-36 sm:pl-36 text-[20px] lg:text-[52px] sm:text-[20px] md:text-[38px] font-bold text-white mb-4">
      Get Ready for a Smarter Travel Experience!
    </h2>

    <p className="pl-3 pr-6 sm:pr-24 sm:pl-24 text-[12px] text-base lg:text-[18px] sm:text-[12px] md:text-[18px] text-white/80 mb-8">
      Be the first to access NxVoy and unlock the future of AI-powered travel planning with Shasa.
    </p>

    {/* Email Subscription */}
    <div className="w-full mx-auto">
  
      <EmailSubscription />
      <br/>
      <br/>      
    </div>

    <h2 className="text-[20px] lg:text-[52px] sm:text-[20px] md:text-[38px] font-bold text-white mb-4">
      Connect with NxVoy
    </h2>

    <p className="sm:pr-16 sm:pl-16 text-[12px] text-base lg:text-[18px] sm:text-[12px] md:text-[14px] sm:text-base text-white mb-2 sm:mb-5">
      {isMobile ? "Follow us for exclusive updates, travel tips, and more from Shasa." : "We're launching across multiple platforms soon! Follow us for exclusive updates, travel tips, and more from Shasa."}
    </p>

    {/* Social Icons */}
    <div className="flex justify-center gap-4 sm:gap-6 mb-8">
      <div className="flex items-center gap-4 sm:gap-4 lg:gap-6">
        {socialLinks.group2.map((link, index) => (
          <a
            key={link.name}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 flex items-center justify-center transition-transform hover:scale-110"
          >
            <Image 
              src={`/images/social/${link.name.toLowerCase()}.png`} 
              alt={link.name} 
              width={0}
              height={0}
              style={{ height: '1.75rem', width: '1.75rem', cursor: "pointer" }}
              className="transition-all duration-300 filter hover:brightness-0 hover:invert"
            />
          </a>
        ))}
      </div>
    </div>

  </div>
  <br className="hidden lg:hidden" />
{/* Separator Line */}
<div className="w-full flex justify-center pt-10 relative z-20 ">
  <span className="w-full mx-[40px] h-[1px] bg-[#707FF5] rounded-md"></span>
</div>
<br className="hidden lg:hidden" />
 
{/* Footer */}
<div className="w-full relative z-20">
  <Footer />
</div>
</section>

  
  )
}
