import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginSuccess } from "@/store/slices/authSlice";

/**
 * Custom hook to handle mobile app authentication via URL params using CSRF-protected credentials login.
 */
export function useMobileAppAuth({
  setCheckedParams,
  setToken,
}: {
  setCheckedParams: (checked: boolean) => void;
  setToken: (token: string) => void;
}) {
  const dispatch = useDispatch();
  const params = useSearchParams();

  const accessToken = params.get("accessToken");
  const refreshToken = params.get("refreshToken");
  const accessTokenExpireOn = params.get("accessTokenExpiresOn");
  const refreshTokenExpireOn = params.get("refreshTokenExpiresOn");
  const userParam = params.get("user");

  useEffect(() => {
    if (typeof window === "undefined") return;

    if (!userParam || !accessToken || !refreshToken) {
      setCheckedParams(true);
      return;
    }

    const signInWithCSRF = async () => {
      try {
        const user = JSON.parse(decodeURIComponent(userParam));

        // Step 1: Get CSRF token
        const csrfRes = await fetch("/api/auth/csrf");
        const csrfData = await csrfRes.json();
        const csrfToken = csrfData.csrfToken;

        if (!csrfToken) {
          console.error("CSRF token not received");
          return;
        }

        console.log("mobile-auth====== csrfToken", csrfToken);


        // Step 2: Submit credentials login form manually
        const form = new URLSearchParams({
          csrfToken,
          id: user.id,
          email: user.email,
          name: `${user.firstName ?? ""} ${user.lastName ?? ""}`,
          picture: user.picture ?? "",
          role: user.role ?? "user",
          accessToken,
          refreshToken,
          accessTokenExpireOn: accessTokenExpireOn ?? "",
          refreshTokenExpireOn: refreshTokenExpireOn ?? "",
          tokenType: "Bearer",
        });

        console.log("mobile-auth====== form", form);


        const res = await fetch("/api/auth/callback/credentials", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: form.toString(),
          credentials: "include",
        });
        console.log("mobile-auth====== res", res);
        dispatch(loginSuccess({ accessToken, refreshToken, user }));
        setToken(accessToken);
      } catch (error) {
        console.error("Mobile sign-in via CSRF failed:", error);
      } finally {
        setCheckedParams(true);
      }
    };

    signInWithCSRF();
  }, [
    accessToken,
    refreshToken,
    accessTokenExpireOn,
    refreshTokenExpireOn,
    userParam,
    dispatch,
    setCheckedParams,
    setToken,
  ]);
}
