export const initialFlightState: any = {
  activePage: "flight_search",
  from: "",
  recommendedFlight: {},
  flightSearch: {},
  filterOptions: {
    stopsOptions: [],
    airlinesOptions: [],
    airportOptions: {},
    layoverAirportsOptions: [],
  },
  selectedFilters: {
    stops: [],
    allAirlines: false,
    airline: [],
    priceRange: [0, 100000],
    airports: [],
    allLayoverAirports: false,
    departureOutbound: [0, 1439],
    departureInbound: [0, 1439],
    layoverAirports: [],
  },
  flightSearchRespnse: {},
  sharedFlightResults: {},
  selectedInboundFlight: {},
  selectedOutboundFlight: {},
  outboundTravelers: [],
  outboundTotal: 0,
  priceBreakdown: {},
  totalPrice: 0,
  passengersList: [],
  outboundCounts: { adult: 0, child: 0, infant: 0 },
  inboundTravelers: [],
  inboundTotal: 0,
  inboundCounts: { adult: 0, child: 0, infant: 0 },
  passengerDetails: [],
  passengerDetailsUpdated: false,
  selectedFareOption: [],
  selectedOutboundFareOption: {},
  selectedInboundFareOption: {},
  paymentDetails: {},
  paymentDetailsUpdated: false,
  bookingDetails: {},
  contactDetails: {},
  luggageOptions: [],
  supplierInfo: [],
  selectedLuggageInfo: { _outward: {}, _return: {}, totalPrice: 0 },
};

export const resetFlightState = () => initialFlightState;
