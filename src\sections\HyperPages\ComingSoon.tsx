import React from "react";

const ComingSoon = () => {
  return (
    <div className="relative min-h-screen flex flex-col items-center justify-center text-center px-4 bg-white">
      <div className="absolute inset-0 bg-cover bg-center opacity-10"></div>
      <div className="relative z-10 max-w-3xl flex flex-col items-center gap-6">
        <img
          src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/Hello-Shasa%202.png"
          alt="Mobile Shasa App"
          className="w-[180px] sm:w-[220px] md:w-[260px] lg:w-[300px] object-contain"
        />
        <h1 className="bg-gradient-to-r from-[#4D4DC5] via-[#707FF5] to-[#A195F9] bg-clip-text text-transparent font-proxima-nova font-bold text-[20px] sm:text-[28px] md:text-[36px] lg:text-[42px] leading-snug">
          Mobile App Coming Soon!
        </h1>
        <p className="text-[#0E0B2B] text-[14px] sm:text-[16px] md:text-[18px] lg:text-[20px] max-w-xl font-medium">
          We’re excited to announce that the NxVoy iOS and Android apps are
          ready and have been published to the app stores. We’re just waiting
          for final approval—stay tuned, you’ll be able to download
          our app very soon!
        </p>
      </div>
    </div>
  );
};

export default ComingSoon;
