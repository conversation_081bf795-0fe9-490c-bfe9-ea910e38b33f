import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronDown, Circle, PlaneLanding, PlaneTakeoff } from "lucide-react"
import { Flight, Airport, CabinClass } from "@/constants/models";
import Image from "next/image";
import { useEffect, useState } from "react";
import { getCurrencySymbol } from "@/lib/utils/formatPrice";
interface FlightCardProps {
    flight: Flight;
    flightType: "OUT-BOUND" | "INBOUND";
    showDetails?: boolean;
    onCardSelect?: (picked_flight: Flight, picked_class: string) => void;
    airport: { [iataCode: string]: Airport };
    flight2?: Flight;
    showPrice?: boolean;
    class_type?: string;
}

type Hubrouting = {
    status: boolean,
    hub_routing_text: string,
    hub_airport_dept: string,
}


export default function FlightCard({ flight, flightType, onCardSelect, showDetails = false, airport, flight2, class_type, showPrice = true }: FlightCardProps) {
    const [show, setShow] = useState(showDetails);
    const [show2, setShow2] = useState(showDetails);
    const [hubrouting, setHubrouting] = useState<Hubrouting>({ hub_airport_dept: "", status: false, hub_routing_text: "" });
    const [hubrouting2, setHubrouting2] = useState<Hubrouting>({ hub_airport_dept: "", status: false, hub_routing_text: "" });
    const [cabinClass, setCabinClass] = useState<CabinClass>();

    useEffect(() => {
        const cabin_class = flight.cabin_classes.find(item => item.travel_class.class === class_type);
        if (cabin_class?.id) {
            setCabinClass(cabin_class);
        } else {
            setCabinClass(flight.cabin_classes?.[0])
        }

    }, [class_type])

    useEffect(() => {
        flight.segments.length > 1 && setHubrouting(processFlightData(flight))
    }, [flight])

    useEffect(() => {
        if (flight2) {
            flight2.segments.length > 1 && setHubrouting2(processFlightData(flight2))
        }
    }, [flight2])

    function processFlightData(flight_data: Flight) {
        const h_route_data: Hubrouting = hubrouting;
        if (flight_data.segments.length > 1) {
            h_route_data.status = true;
            h_route_data.hub_routing_text = `${flight_data.segments.length - 1} Stops`;

            flight_data.segments.map((item, indx) => {
                if (indx === 0) {
                    h_route_data.hub_routing_text = h_route_data.hub_routing_text + ` | ${item.destination}`;
                }
            })
        }

        if (h_route_data.status) {
            return h_route_data
        }

        return { hub_airport_dept: "", status: false, hub_routing_text: "" };
    }

    function singleFlight(flight_data: Flight) {
        if (flight_data.segments.length === 1) {
            return (<div className="flex justify-start gap-10">
                <div className="flex flex-col justify-between gap-10">
                    <p className="font-semibold">{flight_data.departure_time_24hr}</p>
                    <p className="text-sm text-[#999999]">{flight_data.duration}</p>
                    <p className="font-semibold">{flight_data.arrival_time_24hr}</p>
                </div>
                <div className="flex gap-4">
                    <div className="flex flex-col items-center">
                        <PlaneTakeoff className="w-5 h-5 text-black" />
                        <div className="border-l-2 border-[#0000001A] flex-1 my-2"></div>
                        <PlaneLanding className="w-5 h-5 text-black mb-2" />
                    </div>

                    <div className="flex flex-col justify-between flex-1">
                        <div>
                            <p className="font-semibold">{`${airport?.[flight_data.origin].airport_name} (${flight_data.origin})`}</p>
                            <p className="text-[#999999] text-sm">{`${flight_data.segments[0]?.operator} | ${cabinClass?.travel_class.class} | ${flight_data.segments[0]?.flight_code}`}</p>
                            <p className="text-[#999999] text-sm">Departs {flight_data.departure_date}</p>
                        </div>
                        <div>
                            <p className="font-semibold">{`${airport?.[flight_data.destination].airport_name} (${flight_data.destination})`}</p>
                            <p className="text-[#999999] text-sm">Arrives {flight_data.arrival_date}</p>
                        </div>
                    </div>
                </div>
            </div>)
        }

        return <div className="text-red-400">Segment data mismatch</div>

    }

    function multiFlight(flight_data: Flight) {

        return flight_data.segments.map((item, indx) => {
            if (indx === 0) {
                return (
                    <div className="flex justify-start gap-10">
                        <div className="flex flex-col justify-between gap-10">
                            <p className="font-semibold">{item.departure_time_24hr}</p>
                            <p className="text-sm text-[#999999]">{item.duration}</p>
                            <p className="font-semibold">{item.arrival_time_24hr}</p>
                        </div>
                        <div className="flex gap-4">
                            <div className="flex flex-col items-center">
                                <PlaneTakeoff className="w-5 h-5 text-black" />
                                <div className="border-l-2 border-[#0000001A] flex-1 my-2"></div>
                                <Circle className="w-5 h-5 text-black mb-2" />
                            </div>

                            <div className="flex flex-col justify-between flex-1">
                                <div>
                                    <p className="font-semibold">{`${airport?.[item.origin].airport_name} (${item.origin})`}</p>
                                    {/* <p className="text-[#999999] text-sm">Gulf Air Company | Economy | GF 65 | Airbus A321neo - Jet</p> */}
                                    <p className="text-[#999999] text-sm">{`${item.operator} | ${cabinClass?.travel_class.class} | ${item.flight_code}`}</p>
                                    <p className="text-[#999999] text-sm">Departs {item.departure_date}</p>
                                </div>
                                <div>
                                    <p className="font-semibold">{`${airport?.[item.destination].airport_name} (${item.destination})`}</p>
                                    <p className="text-[#999999] text-sm">Arrives {item.arrival_date}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }

            if (indx === flight_data.segments.length - 1) {
                return (
                    <div>
                        <div className="bg-[#F2F2FF] py-3 px-4 m-2 rounded-lg">
                            <p className="text-brand text-sm">{`${flight_data.wait_time} ${airport?.[item.origin].airport_name} (${item.origin})`}</p>
                        </div>
                        <div className="flex justify-start gap-10">
                            <div className="flex flex-col justify-between gap-10">
                                <p className="font-semibold">{item.departure_time_24hr}</p>
                                <p className="text-sm text-[#999999]">{item.duration}</p>
                                <p className="font-semibold">{item.arrival_time_24hr}</p>
                            </div>
                            <div className="flex gap-4">
                                <div className="flex flex-col items-center">
                                    <Circle className="w-5 h-5 text-black mb-2" />
                                    <div className="border-l-2 border-[#0000001A] flex-1 my-2"></div>
                                    <PlaneLanding className="w-5 h-5 text-black mb-2" />
                                </div>

                                <div className="flex flex-col justify-between flex-1">
                                    <div>
                                        <p className="font-semibold">{`${airport?.[item.origin].airport_name} (${item.origin})`}</p>
                                        <p className="text-[#999999] text-sm">{`${item.operator} | ${cabinClass?.travel_class.class} | ${item.flight_code}`}</p>
                                        <p className="text-[#999999] text-sm">Departs {item.departure_date}</p>
                                    </div>
                                    <div>
                                        <p className="font-semibold">{`${airport?.[item.destination].airport_name} (${item.destination})`}</p>
                                        <p className="text-[#999999] text-sm">Arrives {item.arrival_date}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }
        })
    }

    return (
        <div className="w-full mx-auto">
            <Card className="rounded-2xl bg-white border-[#00000014] border-2">
                <CardContent className="p-0">
                    <div className="flex justify-between items-center px-4 py-3">
                        <div className="grid">
                            <p className="text-[#999999] font-medium text-sm md:text-base w-24">{flightType}</p>
                            <div className="grid grid-cols-2 gap-0">
                                {flight.segments.map(item => {
                                    return (<Image alt="flight logo" className="" src={item.operator_logo} height={30} width={30} />)
                                })}
                            </div>
                        </div>
                        <div className="flex gap-2">
                            <div className="grid">
                                <p className="font-semibold">{flight.departure_time_24hr}</p>
                                <p className="text-[#999999] text-center">{flight.origin}</p>
                            </div>
                            <div className="grid md:min-w-40">
                                <p className="text-[#999999] text-center">{flight.duration}</p>
                                <div className="flex items-center justify-center relative">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                    <div className="flex-1 border-t border-dashed border-gray-400 relative">
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                                <path d="M10.2898 8.5L6.5 14.5L5 14.5L6.8945 8.5L2.8745 8.5L1.625 10.75L0.500001 10.75L1.25 7.375L0.500002 4L1.625 4L2.87525 6.25L6.89525 6.25L5 0.25L6.5 0.25L10.2898 6.25L14.375 6.25C14.6734 6.25 14.9595 6.36853 15.1705 6.57951C15.3815 6.79048 15.5 7.07663 15.5 7.375C15.5 7.67337 15.3815 7.95952 15.1705 8.1705C14.9595 8.38148 14.6734 8.5 14.375 8.5L10.2898 8.5Z" fill="#1E1E76" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                </div>
                                <p className="text-[#999999] text-center">{hubrouting.status ? hubrouting.hub_routing_text : "Non-Stop"}</p>
                            </div>
                            <div className="grid">
                                <p className="font-semibold">{flight.arrival_time_24hr}</p>
                                <p className="text-[#999999] text-center">{flight.destination}</p>
                            </div>
                        </div>
                        <div>
                            <div className="p-2 border border-gray-300 rounded-full cursor-pointer" onClick={() => setShow(!show)}>
                                <ChevronDown className={`w-4 h-4 text-brand transition-transform duration-200 ${show ? 'rotate-180' : ''}`} />
                            </div>
                        </div>
                    </div>
                    <div className={show ? "block" : "hidden"}>
                        <div className="p-5 border border-b-0 border-x-0 border-gray-200">
                            {hubrouting.status ? multiFlight(flight) : singleFlight(flight)}
                        </div>
                    </div>
                    {flight2 ? (
                        <>
                            <div className="flex justify-between items-center px-4 py-3 border-t">
                                <div className="grid">
                                    <p className="text-[#999999] font-medium text-sm md:text-base w-24">INBOUND</p>
                                    <div className="grid grid-cols-2 gap-0">
                                        {flight2.segments.map(item => {
                                            return (<Image alt="flight logo" className="" src={item.operator_logo} height={30} width={30} />)
                                        })}
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <div className="grid">
                                        <p className="font-semibold">{flight2.departure_time_24hr}</p>
                                        <p className="text-[#999999] text-center">{flight2.origin}</p>
                                    </div>
                                    <div className="grid md:min-w-40">
                                        <p className="text-[#999999] text-center">{flight2.duration}</p>
                                        <div className="flex items-center justify-center relative">
                                            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                            <div className="flex-1 border-t border-dashed border-gray-400 relative">
                                                <div className="absolute inset-0 flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                                                        <path d="M10.2898 8.5L6.5 14.5L5 14.5L6.8945 8.5L2.8745 8.5L1.625 10.75L0.500001 10.75L1.25 7.375L0.500002 4L1.625 4L2.87525 6.25L6.89525 6.25L5 0.25L6.5 0.25L10.2898 6.25L14.375 6.25C14.6734 6.25 14.9595 6.36853 15.1705 6.57951C15.3815 6.79048 15.5 7.07663 15.5 7.375C15.5 7.67337 15.3815 7.95952 15.1705 8.1705C14.9595 8.38148 14.6734 8.5 14.375 8.5L10.2898 8.5Z" fill="#1E1E76" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                        </div>
                                        <p className="text-[#999999] text-center">{hubrouting2.status ? hubrouting2.hub_routing_text : "Non-Stop"}</p>
                                    </div>
                                    <div className="grid">
                                        <p className="font-semibold">{flight.arrival_time_24hr}</p>
                                        <p className="text-[#999999] text-center">{flight.destination}</p>
                                    </div>
                                </div>
                                <div>
                                    <div className="p-2 border border-gray-300 rounded-full cursor-pointer" onClick={() => setShow2(!show2)}>
                                        <ChevronDown className={`w-4 h-4 text-brand transition-transform duration-200 ${show2 ? 'rotate-180' : ''}`} />
                                    </div>
                                </div>
                            </div>
                            <div className={show2 ? "block" : "hidden"}>
                                <div className="p-5 border border-b-0 border-x-0 border-gray-200">
                                    {hubrouting2.status ? multiFlight(flight2) : singleFlight(flight2)}
                                </div>
                            </div></>) : <></>}
                    {showPrice ? (<div className="flex justify-between items-center bg-[#F2F2FF] px-4 py-3 rounded-b-2xl">
                        <div className="grid">
                            <p className="font-semibold">{`${cabinClass && getCurrencySymbol(cabinClass?.price.currency)}${cabinClass?.price.amount}`}</p>
                            {/* <p className="font-medium text-sm md:text-base">Round Trip <span className="text-xl">|</span> $001 per person</p> */}
                            <p className="font-medium text-sm md:text-base">For all Passengers</p>
                        </div>
                        <div className="flex gap-4">
                            {/* <Button variant="outline" className="rounded-lg border-brand text-brand font-medium">Show more flights</Button> */}
                            <Button className="bg-brand text-white rounded-lg font-medium" onClick={() => onCardSelect?.(flight, cabinClass?.id || "")}>Select Flight</Button>
                        </div>
                    </div>) : <></>}
                </CardContent>
            </Card>
        </div>
    )
}