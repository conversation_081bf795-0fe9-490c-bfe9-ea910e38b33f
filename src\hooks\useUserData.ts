"use client";

import { useState, useEffect } from "react";
import axios from "axios";
import type { User, UserResponse } from "@/constants/user";
import { useAuth } from "@/components/AuthProvider/auth-Provider";

export const normalizeUserData = (userData: any): any => {
  const normalized = { ...userData };
  for (const key in normalized) {
    if (normalized[key] === "None" || normalized[key] === null) {
      normalized[key] = "";
    }
  }
  return normalized;
};

export const useUserData = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>("Loading...");
  const { token } = useAuth();

  const fetchUserDetails = async () => {
    if (!token) {
      setLoading(false);
      return;
    }

    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/get-details`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to fetch User");
      }

      const data: UserResponse = await response.data;
      console.log(data);
      setUser(normalizeUserData(data?.detail?.data));
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!user?.lastName) {
      fetchUserDetails();
    }
  }, [token]);

  return {
    user,
    loading,
    error,
    fetchUserDetails,
    setUser,
  };
};
