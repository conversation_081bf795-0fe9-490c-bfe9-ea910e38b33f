"use client";

import {
    X,
    ThumbsUp,
    ThumbsDown,
    ChevronLeft,
    ChevronRight,
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import type { Review } from "@/lib/types";

interface ReviewsModalProps {
    reviews: Review[];
    hotelName: string;
    rating?: number;
    reviewCount: number;
    onClose: () => void;
}

export default function ReviewsModal({
    reviews,
    hotelName,
    rating,
    reviewCount,
    onClose,
}: ReviewsModalProps) {
    const [activeFilter, setActiveFilter] = useState("All Travelers");
    const [isOpen, setIsOpen] = useState(false);
    const filtersRef = useRef<HTMLDivElement>(null);

    // Handle opening animation
    useEffect(() => {
        setIsOpen(true);
        // Add body class to prevent scrolling of background content
        document.body.classList.add("overflow-hidden");

        return () => {
            document.body.classList.remove("overflow-hidden");
        };
    }, []);

    // Handle closing with animation
    const handleClose = () => {
        setIsOpen(false);
        setTimeout(() => {
            onClose();
        }, 300); // Match transition duration
    };

    // Scroll filter tabs
    const scrollFilters = (direction: "left" | "right") => {
        if (!filtersRef.current) return;

        const scrollAmount = 200; // Adjust as needed
        const currentScroll = filtersRef.current.scrollLeft;

        filtersRef.current.scrollTo({
            left:
                direction === "left"
                    ? currentScroll - scrollAmount
                    : currentScroll + scrollAmount,
            behavior: "smooth",
        });
    };

    const filters = [
        { name: "All Travelers", count: null },
        { name: "Traveling as Couple", count: 665 },
        { name: "Business Traveler", count: 758 },
        { name: "Senior Traveler", count: 16 },
        { name: "Family with Young Children", count: 751 },
        { name: "Solo Traveler", count: 124 },
        { name: "Group", count: 89 },
    ];

    // Extended reviews data to match the image
    const extendedReviews = [
        {
            title: "Best in the town",
            rating: 10,
            travelerType: "Group",
            date: "Apr 2025",
            positives: [
                "Its a palace so no complaints, its 7 star luxury, central place for all the places for you to move, high level security.",
            ],
            negatives: ["All okay"],
            author: "Loganathan",
        },
        {
            title: "Perfect",
            rating: 10,
            travelerType: "Family with Young Children",
            date: "Apr 2025",
            positives: ["Overall"],
            negatives: [],
            author: "Nirushan",
        },
        {
            title: "Excellent and reconsider coming back again!",
            rating: 10,
            travelerType: "Family with Young Children",
            date: "Apr 2025",
            positives: ["Hospitable and great customer service"],
            negatives: [
                "Some amenities in the room had issues but they managed to get someone to fix them quickly",
            ],
            author: "Marc",
        },
        {
            title: "An unmissable stay in Chennai",
            rating: 10,
            travelerType: "Traveling as Couple",
            date: "Apr 2025",
            positives: [
                "Huge property with lots to do. Many good restaurants, the spa was very good, the pool was great and the rooms are very large.",
            ],
            negatives: [],
            author: "Abhishek",
        },
        {
            title: "Good",
            rating: 7,
            travelerType: "Traveling as Couple",
            date: "Mar 2025",
            positives: ["Cleanliness, ambience, nice rooms"],
            negatives: ["Poor service"],
            author: "Bobby",
        },
    ];

    return (
        <>
            {/* Semi-transparent overlay */}
            <div
                className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${isOpen ? "opacity-100" : "opacity-0"}`}
                onClick={handleClose}
            />

            {/* Slide-in modal */}
            <div
                className={`fixed top-0 bottom-0 right-0 z-50 w-full md:w-[60%] bg-white shadow-xl transition-transform duration-300 ease-in-out transform ${isOpen ? "translate-x-0" : "translate-x-full"
                    }`}
            >
                {/* Header */}
                <div className="sticky top-0 bg-white p-6 border-b flex items-center justify-between">
                    <h2 className="font-bold text-[30px] text-[#080236]">
                        1,306 Verified Reviews
                    </h2>
                    <button
                        onClick={handleClose}
                        className="h-8 w-8 flex items-center justify-center"
                    >
                        <X className="h-6 w-6" />
                    </button>
                </div>

                {/* Ratings summary */}
                <div className="border-b">
                    <div className="flex justify-between px-6 py-4">
                        <div className="text-center">
                            <div className="text-lg font-bold text-[#4B4BC3] flex gap-6">
                                <img
                                    src={
                                        "https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                                    }
                                    width={16}
                                    height={16}
                                    alt="rating star"
                                    className="object-contain m-0"
                                />
                                9.1
                            </div>
                            <div className="text-sm font-bold text-[#080236]">
                                Exceptional
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg  text-[#4B4BC3]">9.3</div>
                            <div className="text-sm text-[#080236]">Exceptional</div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg  text-[#4B4BC3]">9.3</div>
                            <div className="text-sm text-[#080236]">Staff</div>
                        </div>
                        <div className="text-center">
                            <div className="text-lg text-[#4B4BC3]">9.2</div>
                            <div className="text-sm text-[#080236]">Location</div>
                        </div>
                    </div>
                </div>

                {/* Filter tabs */}
                <div className="border-b px-4 py-3 flex items-center">
                    <button
                        className="p-1 mr-2 hover:bg-gray-100 rounded-full"
                        onClick={() => scrollFilters("left")}
                    >
                        <ChevronLeft
                            className="h-5 w-5"
                            color={"#080236"}
                            strokeWidth={3}
                        />
                    </button>

                    <div
                        ref={filtersRef}
                        className="flex gap-2 overflow-x-auto no-scrollbar flex-grow scroll-smooth"
                    >
                        {filters.map((filter) => (
                            <button
                                key={filter.name}
                                className={`whitespace-nowrap px-4 py-2 rounded-full text-sm border border-[#B4BBE8] text-[#4B4BC3] ${activeFilter === filter.name ? "bg-[#E9E8FC] " : "bg-white  "
                                    }`}
                                onClick={() => setActiveFilter(filter.name)}
                            >
                                {filter.name} {filter.count ? `(${filter.count})` : ""}
                            </button>
                        ))}
                    </div>

                    <button
                        className="p-1 ml-2 hover:bg-gray-100 rounded-full"
                        onClick={() => scrollFilters("right")}
                    >
                        <ChevronRight
                            className="h-5 w-5"
                            color={"#080236"}
                            strokeWidth={3}
                        />
                    </button>
                </div>

                {/* Reviews list */}
                <div
                    className="overflow-y-auto"
                    style={{ height: "calc(100vh - 200px)" }}
                >
                    {extendedReviews.map((review, index) => (
                        <div key={index} className="border-b last:border-b-0">
                            <div className="px-6 py-4">
                                {/* Traveler type */}
                                <div className="text-sm text-[#B4BBE8] mb-1">
                                    {review.travelerType}
                                </div>

                                {/* Rating and title */}
                                <div className="flex items-center gap-2 mb-1">
                                    <div className="flex items-center">
                                        <img
                                            src={
                                                "https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                                            }
                                            width={16}
                                            height={16}
                                            alt="rating star"
                                            className="object-contain m-0"
                                        />
                                        <span className="ml-1 font-bold text-[#4B4BC3]">
                                            {review.rating}
                                        </span>
                                    </div>
                                    <div className="flex justify-between w-full">
                                        <div className="font-bold text-lg text-[#080236]">
                                            {review.title}
                                        </div>
                                        {/* Date - right aligned */}
                                        <div className="text-right text-sm text-[#B4BBE8] mb-2">
                                            {review.date}
                                        </div>
                                    </div>
                                </div>

                                {/* Positive comments */}
                                {review.positives.map((positive, i) => (
                                    <div key={`pos-${i}`} className="flex items-start gap-2 mb-2">
                                        <ThumbsUp className="h-5 w-5 text-[#24C72F] mt-0.5" />
                                        <p className="text-[#080236] text-sm">{positive}</p>
                                    </div>
                                ))}

                                {/* Negative comments */}
                                {review.negatives.map((negative, i) => (
                                    <div key={`neg-${i}`} className="flex items-start gap-2 mb-2">
                                        <ThumbsDown className="h-5 w-5 text-[#080236] mt-0.5" />
                                        <p className="text-[#080236] text-sm ">{negative}</p>
                                    </div>
                                ))}

                                {/* Author */}
                                <div className="font-bold mt-2 text-[#080236] text-sm">
                                    {review.author}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
}
