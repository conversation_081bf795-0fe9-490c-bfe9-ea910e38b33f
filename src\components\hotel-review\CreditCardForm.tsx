import { Input } from "@/components/ui/input";

const CreditCardForm = () => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Credit Card Information</h3>
        <div className="flex gap-2">
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/320px-Visa_Inc._logo.svg.png" alt="Visa" className="h-6" />
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b5/PayPal.svg/320px-PayPal.svg.png" alt="PayPal" className="h-6" />
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/Google_Pay_Logo.svg/320px-Google_Pay_Logo.svg.png" alt="Google Pay" className="h-6" />
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Apple_Pay_logo.svg/320px-Apple_Pay_logo.svg.png" alt="Apple Pay" className="h-6" />
          <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/320px-Mastercard-logo.svg.png" alt="Mastercard" className="h-6" />
        </div>
      </div>
      
      <p className="text-sm text-gray-600 mb-4">Visa, Mastercard, American Express, Discover</p>
      
      <div className="space-y-4">
        <Input 
          placeholder="Name on card*" 
          className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-lg"
        />
        
        <Input 
          placeholder="Card Number*" 
          className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-lg"
        />
        
        <div className="text-sm text-gray-600 mb-2">Exp. date</div>
        <div className="grid grid-cols-3 gap-4">
          <Input 
            placeholder="Month*" 
            className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-lg"
          />
          <Input 
            placeholder="Year*" 
            className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-lg"
          />
          <Input 
            placeholder="CVV*" 
            className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-lg"
          />
        </div>
      </div>
    </div>
  );
};

export default CreditCardForm;