import { execSync } from 'child_process';
import { LocaleHelper } from '../properties/LocaleHelper';
import { Properties } from '../properties/Properties';
import { sendSlackReport } from '../reporting/SlackReporter';
import { ConfigHandler } from '../properties/ConfigHandler';

async function runScript(command: string) {
    execSync(command, { stdio: 'inherit' });
}

export async function runTests(suite: string) {
    let hasError = false;

    try {
        ConfigHandler.loadConfig();
        await runScript(`cucumber-js test --tags "${suite} and not (@ignore or @bug or @wip)" env_${Properties.ENVIRONMENT} locale_${LocaleHelper.LOCALE}`);
    } catch (error) {
        hasError = true;
    } finally {
        await runScript(`npx ts-node src/test/reporting/HtmlReporter.ts env_${Properties.ENVIRONMENT} locale_${LocaleHelper.LOCALE}`);
        // TODO: Enable Slack reporting when you have filled in your Slack token and channel ID in the properties file
        // await sendSlackReport(suite);
        if (hasError) {
            process.exit(1);
        }
    }
}
