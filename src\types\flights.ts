
export interface FlightsProps {
    onward_flights: {
        flights: any[];
        src_airports?: string[];
        dst_airports?: string[];
        layover_airports?: string[];
        airlines?: string[];
    };
    return_flights: any | null;
    airport_data?: any | null;
    airline_data?: any | null;
}

export interface Airport {
    iata_code: string;
    airport_name: string;
    city_name_original: string;
    country_name: string;
    country_code: string;
  };

export type AirportData = {
  [code: string]: Airport;
};

export type InputType = "departure" | "destination";

export interface FlightSearchForm {
    trip_type: string;
    travel_class: string;
    adults: number;
    children: number;
    infants: number;
    departure_date: string;
    return_date: string;
    destination: { city: string, iata_code: string };
    origin: { city: string, iata_code: string };
}

interface Suggestion {
    iata_code: string;
    city_name_original: string;
    airport_name: string;
    country_name: string;
}