import React, { useRef } from "react";
import { ArrowLeft, ChevronLeft } from "lucide-react";

import ChatFooter from "@/components/footer/chatFooter";
import HotelCard from "@/components/hotel-review/HotelCard";
import GuestDetailsForm from "@/components/hotel-review/GuestDetailsForm";
import ContactForm from "@/components/hotel-review/ContactForm";
import { Button } from "@/components/ui/button";
import PriceBreakdown from "@/components/hotel-review/PriceBreakDown";
import Navbar from "@/components/NavBar";
import BookingStepper from "@/components/hotelBooking/HotelBookStepper";
import { useRouter } from 'next/router';


export default function ReviewAndPayPage() {
  const footerRef = useRef<HTMLDivElement | null>(null);

  const router = useRouter();

  const handleSignIn = () => {
    // setIsSignInClicked(true);
  };

   const handleBookAndPay = () => {
    router.push('/hotel-confirmation');
  };

  return (
    <div
      className={`md:relative h-screen xl:pb-10 lg:pb-5  bg-[#F2F3FA] flex flex-col gap-2 mx-auto font-proxima-nova`}
    >
      <div className="z-50 w-full mx-auto items-center flex flex-col justify-center">
        <div className="w-[95%] fixed top-0 mx-auto">
          <Navbar handleSignIn={handleSignIn} />
        </div>
      </div>

      <div className="md:relative top-10 bg-gray-100 md:overflow-y-auto md:hide-scrollbar">
        <div className="md:max-w-7xl md:mx-auto xs:pr-2 xs:pl-2">
          <div className="max-w-3xl mx-auto mt-6">
            <BookingStepper currentStep={2} />
          </div>
          {/* Back to search button */}
          <button onClick={() => router.push('/hotel-room-selection')} className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-6 transition-colors ">
            <ChevronLeft size={20} strokeWidth={3} color="#080236" />
            <span className="font-medium underline text-[20px] text-[#080236]">
              Back to search
            </span>
          </button>

          <div className="grid lg:grid-cols-3 md:gap-8">
            {/* Main Content */}
            <div className="md:rounded-xl md:p-px mb-12 lg:col-span-2 space-y-6 md:bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)]">
              <div className="bg-gray-100 rounded-xl ">
                {/* Header */}
                <div className=" rounded-xl md:p-6 border-gray-100">
                  <div className="text-center mb-16">
                    <h1 className="text-[30px] font-bold text-[#4B4BC3] mb-2">
                      Almost done!
                    </h1>
                    <p className="text-[#4B4BC3] text-[24px]">
                      Enter your details and complete your booking now.
                    </p>
                  </div>
                  <HotelCard />
                </div>

                <div className="md:px-4 grid gap-y-6 xs:mt-6">
                  {/* Guest Details */}
                <GuestDetailsForm />

                {/* Contact */}
                <ContactForm />
                {/* Important Information */}
                <div className="rounded-xl relative w-full p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
                  <div className="bg-gray-100 p-6 rounded-xl">
                    <h3 className="text-lg font-semibold mb-4">
                      Important information
                    </h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>
                        • Fully Refundable until 11:59pm (property local time)
                        on 06-20-2025. After that time hotel cancellation and
                        change fees apply as stated in the Booking Conditions.
                      </li>
                      <li>
                        • Check-in begins at 3pm and check-out is at 12pm.
                      </li>
                      <li>
                        • By selecting Book & Pay Later you agree to the Booking
                        Conditions.
                      </li>
                      <li>• Customer service provided by Nxvoy.com</li>
                    </ul>
                  </div>
                </div>
                </div>

                {/* Book & Pay Button */}
                <div className="w-full flex justify-center mt-12 mb-6">
                  <Button onClick={handleBookAndPay} className="w-[312px] bg-indigo-600 text-white py-4 rounded-full text-[18px] font-medium bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)] h-[42px]">
                    Book & Pay
                  </Button>
                </div>

                <p className="text-xs text-gray-500 text-center py-6">
                  By selecting "Confirm & Pay," I agree to this purchase of
                  $60,912 USD. I have read, accept, and agree to the terms and
                  conditions and privacy policy, and important terms.
                </p>
              </div>
            </div>

            {/* Price Sidebar */}
            <div className="lg:col-span-1 mb-6">
              <PriceBreakdown />
            </div>
          </div>
        </div>
        <div ref={footerRef}>
          <ChatFooter />
        </div>
      </div>
    </div>
  );
}
