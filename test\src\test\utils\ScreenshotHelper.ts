import { fixture } from '../fixtures/Fixture';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Helper class for taking screenshots during test execution
 */
export class ScreenshotHelper {
    private static screenshotDir = 'test-reports/screenshots';
    private static captureOnlyOnFailure = true; // Default to only capturing on failure
    private static directoryCreated = false; // Track if we've created the directory
    private static screenshotDirConfigured = false; // Track if we've configured the directory
      /**
     * Configure screenshot settings without creating directories
     * @param captureOnlyOnFailure - Whether to capture screenshots only on failure
     */
    static configure(captureOnlyOnFailure = true) {
        this.captureOnlyOnFailure = captureOnlyOnFailure;
        // Reset the flag to create a new timestamped directory for the next run
        this.screenshotDirConfigured = true;
        this.directoryCreated = false;
        this.screenshotDir = 'test-reports/screenshots'; // Reset to base path
        console.log(`Screenshot mode: ${this.captureOnlyOnFailure ? 'Only on failure' : 'All steps'}`);
    }/**
     * Initialize or get the screenshot directory path
     * @returns The configured screenshot directory path
     */
    private static getScreenshotDirPath(): string {
        if (!this.screenshotDirConfigured) {
            this.configure(true); // Configure with default settings if not already done
        }
        
        // Generate timestamp pattern for the directory only when first needed
        if (!this.directoryCreated) {
            const baseDir = 'test-reports/screenshots';
            const timestamp = new Date().toISOString().replace(/:/g, '-');
            this.screenshotDir = path.join(baseDir, timestamp);
            
            // Don't create the directory yet - only when actually taking a screenshot
            console.log(`Screenshot directory path configured: ${this.screenshotDir}`);
        }
        
        return this.screenshotDir;
    }
      /**
     * Creates the screenshot directory if needed
     * @returns The screenshot directory path
     */
    private static ensureDirectoryExists(): string {
        // Get the configured directory path
        const dirPath = this.getScreenshotDirPath();
        
        // Create the directory if it doesn't exist and we're actually going to save a screenshot
        if (!this.directoryCreated) {
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
                console.log(`Screenshots will be saved to: ${dirPath}`);
            }
            this.directoryCreated = true;
        }
        
        return dirPath;
    }
    
    /**
     * Set screenshot capture mode
     * @param captureOnlyOnFailure - Whether to capture screenshots only on failure
     */
    static setCaptureMode(captureOnlyOnFailure: boolean): void {
        this.captureOnlyOnFailure = captureOnlyOnFailure;
        console.log(`Screenshot mode set to: ${this.captureOnlyOnFailure ? 'Only on failure' : 'All steps'}`);
    }    /**
     * Take a screenshot with a descriptive name
     * @param name - Base name for the screenshot
     * @param fullPage - Whether to capture the full page
     * @param forceCapture - Force screenshot capture even if captureOnlyOnFailure is true
     */    static async takeScreenshot(name: string, fullPage = true, forceCapture = false): Promise<string> {
        try {
            // Skip screenshot if we're only capturing on failure and not forcing capture
            const isError = name && (name.includes('error') || name.includes('failed') || name.includes('fail'));
            if (this.captureOnlyOnFailure && !forceCapture && !isError) {
                console.log(`Screenshot skipped for ${name} due to captureOnlyOnFailure setting`);
                return ''; // Return empty string when screenshot is skipped
            }
            
            // Only create directory when we're actually going to take a screenshot
            // Get/create the base screenshot directory
            const baseDir = 'test-reports/screenshots';
            if (!fs.existsSync(baseDir)) {
                fs.mkdirSync(baseDir, { recursive: true });
            }
            
            // Use a static timestamp for the run to group all screenshots from a test run
            if (!this.directoryCreated) {
                const timestamp = new Date().toISOString().replace(/:/g, '-');
                this.screenshotDir = path.join(baseDir, timestamp);
                
                if (!fs.existsSync(this.screenshotDir)) {
                    fs.mkdirSync(this.screenshotDir, { recursive: true });
                    console.log(`Created screenshot directory for test run: ${this.screenshotDir}`);
                }            
            this.directoryCreated = true;
        }
        
        const fileName = `${name.replace(/\s+/g, '-')}-${Date.now()}.png`;
        const filePath = path.join(this.screenshotDir, fileName);
        
        try {
            if (fixture.page) {
                await fixture.page.screenshot({ path: filePath, fullPage });
                console.log(`Screenshot saved: ${filePath}`);
                return filePath;
            } else {
                console.log('No page object available for screenshot');
                return '';
            }
        } catch (error) {
            console.error(`Failed to take screenshot: ${error.message}`);
            return '';
        }
        } catch (error) {
            console.error(`Error setting up screenshot: ${error.message}`);
            return '';
        }
    }
        /**
     * Take a screenshot after an error occurs
     * @param error - The error that occurred
     * @param context - Additional context for the screenshot
     */
    static async takeErrorScreenshot(error: Error, context?: string): Promise<string> {
        const name = `error-${context || 'unknown'}-${error.name}`;
        return this.takeScreenshot(name, true, true); // Force capture for error screenshots
    }
    
    /**
     * Clean up any empty screenshot directories
     */
    static cleanupEmptyDirectories(): void {
        try {
            // Only check the base screenshots directory
            const baseDir = 'test-reports/screenshots';
            if (!fs.existsSync(baseDir)) {
                return; // No directory to clean up
            }
            
            // Get all timestamp directories
            const timestampDirs = fs.readdirSync(baseDir);
            let cleanedCount = 0;
            
            for (const dir of timestampDirs) {
                const fullPath = path.join(baseDir, dir);
                
                // Check if the path is a directory
                if (fs.statSync(fullPath).isDirectory()) {
                    // Check if directory is empty
                    const files = fs.readdirSync(fullPath);
                    if (files.length === 0) {
                        // Remove empty directory
                        fs.rmdirSync(fullPath);
                        cleanedCount++;
                        console.log(`Removed empty screenshot directory: ${fullPath}`);
                    }
                }
            }
            
            if (cleanedCount > 0) {
                console.log(`Cleaned up ${cleanedCount} empty screenshot directories`);
            }
        } catch (error) {
            console.error(`Error cleaning up empty directories: ${error}`);
        }
    }
}
