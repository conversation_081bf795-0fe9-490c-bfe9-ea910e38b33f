"use client";

import {
    She<PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    She<PERSON><PERSON>rigger,
    SheetClose,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState, useEffect } from "react";
import { User, UserResponse } from "@/constants/user";
import axios from "axios";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { persistor } from "@/store/store";
import { clearLoggedInUser } from "@/store/slices/loggedInUser";
import { resetShowMoreFlights } from "@/store/slices/showMoreFlights";
import { clearCurrentChatPageThreadId } from "@/store/slices/chatThread";
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { logoutMethod } from "@/utils/auth";
import { logout } from "@/store/slices/authSlice";
import { useAuth } from "@/components/AuthProvider/auth-Provider";
import { Undo2 } from "lucide-react";
import { useCustomSession } from "@/hooks/use-custom-session"
import { signOut } from "next-auth/react";
import { clearReduxOnLogout } from "@/store/clearRedux";
import ScrollableTabs from "@/components/ScrollableTabs";

interface DashboardNavbarProps {
    sectionChangeHandler?: (section: string) => void;
}

export const getInitials = (user: any) => {
    const firstName = user?.firstName || "";
    const lastName = user?.lastName || "";
    return (
        (firstName[0]?.toUpperCase() || "") + (lastName[0]?.toUpperCase() || "")
    );
};

export default function DashboardNavbar({
    sectionChangeHandler,
}: DashboardNavbarProps) {
    // Reference to the SheetClose component
    const closeButtonRef = useRef<HTMLButtonElement>(null);
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    const dispatch = useDispatch();
    const router = useRouter();

    const { data: session, status } = useCustomSession();
    const token = session?.accessToken;

    // Fetch user details
    const fetchUserDetails = async () => {
        try {

            if (!token) {
                console.error("No access token found");
                setLoading(false);
                return;
            }

            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/get-details`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        Accept: "application/json",
                    },
                }
            );

            if (response.status !== 200) {
                throw new Error("Failed to fetch User");
            }

            const data: UserResponse = await response.data;
            setUser(data?.detail?.data);
        } catch (error: any) {
            console.error("Error fetching user details:", error.message);
        } finally {
            setLoading(false);
        }
    };

    // Fetch user data on component mount
    useEffect(() => {
        fetchUserDetails();
    }, []);

    // Map section names to their respective routes
    const sectionMap = {
        "User Profile": "/profile",
        "Address Details": "/address",
        // "Payment Card Details": "/payment",
        "Passenger Profiles": "/passengers",
        "Sign out": "/logout",
    };

    // Function to handle logout
    const handleLogout = async () => {
        await clearReduxOnLogout();
        signOut({ redirect: false });
        router.push("/");
    };

    // Function to handle section changes
    const handleSectionChange = (section: string) => {
        if (section === "Sign out") {
            handleLogout();
            return;
        }

        if (sectionChangeHandler) {
            sectionChangeHandler(section);

            // Close the menu
            if (closeButtonRef.current) {
                closeButtonRef.current.click();
            }
        }
    };

    // Get user display name and initials
    const displayName = user ? user.firstName || "User" : "User";

    // Get user initials for avatar fallback

    return (
        <div className="w-full">
            <div className="">

                {/* Logo */}
                {/* <Link href="/" className="flex items-center space-x-2">
                    <Image
                        src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png"
                        alt="Logo"
                        width={100}
                        height={40}
                        className="object-contain"
                    />
                </Link> */}

                {/* Menu Icon replaced with provided image */}
                <div className="hidden">
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon">
                                <Image
                                    src="/menu-fold-line.png"
                                    alt="Menu"
                                    width={24}
                                    height={24}
                                    className="text-violet-600"
                                />
                            </Button>
                        </SheetTrigger>
                        <SheetContent
                            side="right"
                            className="w-4/5 h-full px-0 pt-0 bg-white border-l border-gray-200 shadow-lg rounded-l-2xl overflow-y-auto"
                        >
                            {/* Hidden close button that we can trigger programmatically */}
                            <SheetClose ref={closeButtonRef} className="hidden" />

                            <div className="w-full flex flex-col h-full bg-white">
                                {/* Header with user profile */}
                                <div className="p-6 border-b flex items-center space-x-4">
                                    <div className="rounded-full overflow-hidden w-12 h-12">
                                        <Avatar className="w-full h-full">
                                            <AvatarImage
                                                src={user?.profile_picture || ""}
                                                alt="Profile"
                                            />
                                            <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5] text-lg">
                                                {getInitials(user)}
                                            </AvatarFallback>
                                        </Avatar>
                                    </div>
                                    <div className="flex items-center">
                                        {/* <Image
                                            src="/images/dashboard/Crown.png"
                                            alt="Crown Icon"
                                            width={20}
                                            height={20}
                                            className="mr-2"
                                        /> */}
                                        <span className="font-semibold text-base">
                                            {loading ? "Loading..." : displayName}
                                        </span>
                                    </div>
                                </div>

                                {/* Menu Items */}
                                <div className="flex-1 p-6 space-y-6">
                                    <button
                                        onClick={() => handleSectionChange("User Profile")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                width="20"
                                                height="20"
                                                fill="#080236"
                                            >
                                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                            </svg>
                                        </div>
                                        User Profile
                                    </button>
                                    <button
                                        onClick={() => handleSectionChange("Address Details")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                width="20"
                                                height="20"
                                                fill="#080236"
                                            >
                                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
                                            </svg>
                                        </div>
                                        Address Details
                                    </button>
                                    {/* <button
                                        onClick={() => handleSectionChange("Payment Card Details")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                width="20"
                                                height="20"
                                                fill="#080236"
                                            >
                                                <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
                                            </svg>
                                        </div>
                                        Payment Card Details
                                    </button> */}
                                    <button
                                        onClick={() => handleSectionChange("Passenger Profiles")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                width="20"
                                                height="20"
                                                fill="#080236"
                                            >
                                                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
                                            </svg>
                                        </div>
                                        Passenger Profiles
                                    </button>
                                    <button
                                        onClick={() => router.push("/")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <Undo2 />
                                        </div>
                                        Back to home
                                    </button>
                                    <button
                                        onClick={() => handleSectionChange("Sign out")}
                                        className="flex items-center text-base font-medium text-[#080236] w-full text-left"
                                    >
                                        <div className="w-6 mr-3">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                width="20"
                                                height="20"
                                                fill="#080236"
                                            >
                                                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" />
                                            </svg>
                                        </div>
                                        Sign out
                                    </button>

                                    {/* Chat with Shasa Button */}
                                    {/* <div className="pt-6">
                                        <Link href="/chat">
                                            <button className="w-full relative flex flex-row gap-2 items-center text-center px-4 py-3 rounded-full justify-center bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)] text-white">
                                                <img
                                                    className="w-4 h-4"
                                                    src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                                                    alt="Chat Icon"
                                                />
                                                <span>Chat with Shasa</span>
                                            </button>
                                        </Link>
                                    </div> */}
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>
                </div>
                <div className="hidden md:block">
                    <div className="flex gap-4 items-center">
                        {/* <Link href="/chat">
                            <button className="relative flex flex-row gap-2 lg:gap-2 md:gap-2 sm:gap-1 lg:text-base sm:text-xs items-center text-center md:text-sm px-4 py-1 xl:px-4 md:px-2 xl:py-1 md:py-0.5 rounded-full justify-around bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)] text-white">
                                <img
                                    className="md:w-4 md:h-4 sm:w-3 sm:h-3"
                                    src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                                    alt="Chat Icon"
                                />
                                <div>Chat with Shasa</div>
                            </button>
                        </Link> */}

                        {/* Dropdown Menu for Desktop */}
                        <DropdownMenu>
                            {/* <DropdownMenuTrigger asChild>
                                <div className="rounded-full overflow-hidden w-8 h-8 cursor-pointer">
                                    <Avatar className="w-full h-full">
                                        <AvatarImage
                                            src={user?.profile_picture || ""}
                                            alt="Profile"
                                        />
                                        <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                                            {getInitials(user)}
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                            </DropdownMenuTrigger> */}
                            <DropdownMenuContent
                                align="end"
                                className="w-[150px] rounded-b-[10px] rounded-t-[0px]"
                            >
                                <DropdownMenuGroup>
                                    <DropdownMenuItem
                                        onClick={() => router.push("/")}
                                    >
                                        <Undo2 />
                                        <span>Back to home</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={handleLogout}>
                                        <img
                                            src="https://storage.googleapis.com/nxvoytrips-img/navbar/sign-out.svg"
                                            className="w-4 h-4 mr-2 object-contain"
                                            alt="Logout"
                                        />
                                        <span>Logout</span>
                                    </DropdownMenuItem>
                                </DropdownMenuGroup>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
            </div>
        </div >
    );
}
