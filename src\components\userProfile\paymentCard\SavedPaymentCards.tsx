import React, { useState } from "react";
import { PencilLine, CircleX } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import ConfirmDeleteModal from "../ConfirmDeleteModal";

interface SavedPaymentCardsProps {
  onAddNewCard: () => void;
  onEdit: () => void;
  onRemove?: (cardId: string) => void;
}

const cards = [
  {
    id: "1",
    cardType: "VISA",
    lastFour: "7196",
    expiry: "12 / 2026",
    name: "User Name",
    billingAddress:
      "User Name, User Door No, Street Name, City Name, State Name, Country Name, Zip Code."
  },
  {
    id: "2",
    cardType: "VISA",
    lastFour: "7196",
    expiry: "12 / 2026",
    name: "User Name",
    billingAddress:
      "User Name, User Door No, Street Name, City Name, State Name, Country Name, Zip Code."
  },
  {
    id: "3",
    cardType: "VISA",
    lastFour: "7196",
    expiry: "12 / 2026",
    name: "User Name",
    billingAddress:
      "User Name, User Door No, Street Name, City Name, State Name, Country Name, Zip Code."
  }
];

const SavedPaymentCards: React.FC<SavedPaymentCardsProps> = ({ onAddNewCard, onEdit, onRemove }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteCardId, setDeleteCardId] = useState<string | null>(null);

  const handleRemoveClick = (cardId: string) => {
    setDeleteCardId(cardId);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteCardId && onRemove) {
      onRemove(deleteCardId);
    }
    setModalOpen(false);
    setDeleteCardId(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteCardId(null);
  };

  if (!cards || cards.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="text-xl font-semibold">There's No Payment Method Registered For Your Account.</div>
        <div className="text-xl font-bold text-[#4B4BC3] my-8">You can add new payment card here</div>
        <button
          className="flex items-center gap-2 bg-[#4B4BC3] text-white px-6 py-2 rounded-full font-semibold text-lg"
          onClick={onEdit}
        >
          <PencilLine /> Add New Payment
        </button>
      </div>
    );
  }

  return (
    <div>
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are Sure you want to delete?"
      />
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 md:gap-0 mb-8">
        <h1 className="text-md md:text-3xl font-bold text-[#4B4BC3]">Manage Payment Card Details</h1>
        <Button className="text-sm text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={onEdit}>
          <PencilLine /> Add New Payment
        </Button>
      </div>
      <div className="flex flex-wrap gap-8 grid sm:grid-cols-1 md:grid-cols-2">
        {cards.map(card => (
          <div
            key={card.id}
            className="w-full max-w-md bg-[#E6E3FF] rounded-xl shadow flex flex-col justify-between"
          >
            <div className="flex justify-between items-center mb-4 bg-[#b4bbe8] py-2 px-6 rounded-t-xl">
              <div className="flex items-center gap-2">
                <span className="font-bold text-[#4B4BC3]">{card.cardType}</span>
                <span className="text-sm">Credit Card ending in {card.lastFour}</span>
              </div>
              <span className="text-sm">{card.expiry}</span>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="font-semibold text-[#313183]">Name on card:</div>
                <div className="font-normal text-[#43428f]">{card.name}</div>
                <div className="font-semibold text-[#313183]">Billing Address:</div>
                <div className="font-normal text-[#43428f]">{card.billingAddress}</div>
              </div>
            </div>
            <div className="flex justify-end gap-4 p-4">
              <button className="flex items-center gap-1 px-4 py-1 rounded-full border border-[#4B4BC3] text-[#4B4BC3] hover:bg-[#E6E3FF] text-sm" onClick={() => handleRemoveClick(card.id)}>
                <CircleX /> Remove
              </button>
              <button className="flex items-center gap-1 px-4 py-1 rounded-full border border-[#4B4BC3] text-white bg-[#4B4BC3] hover:bg-[#707FF5] text-sm">
                <PencilLine /> Edit
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SavedPaymentCards;
