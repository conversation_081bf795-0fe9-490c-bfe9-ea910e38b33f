import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { MenuButton } from "./menu-button";

const MockIcon = () => <span data-testid="mock-icon">Icon</span>;

describe("MenuButton", () => {
    const label = "Test Button";
    const defaultClass = "flex items-center text-base font-medium text-[#080236] w-full text-left";

    it("renders the label and icon", () => {
        render(<MenuButton icon={<MockIcon />} label={label} onClick={() => {}} />);
        expect(screen.getByText(label)).toBeInTheDocument();
        expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
    });

    it("calls onClick when clicked", () => {
        const handleClick = jest.fn();
        render(<MenuButton icon={<MockIcon />} label={label} onClick={handleClick} />);
        fireEvent.click(screen.getByRole("button"));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("applies default className if none is provided", () => {
        render(<MenuButton icon={<MockIcon />} label={label} onClick={() => {}} />);
        expect(screen.getByRole("button")).toHaveClass(...defaultClass.split(" "));
    });

    it("applies custom className if provided", () => {
        const customClass = "custom-class";
        render(
            <MenuButton
                icon={<MockIcon />}
                label={label}
                onClick={() => {}}
                className={customClass}
            />
        );
        expect(screen.getByRole("button")).toHaveClass(customClass);
    });
});