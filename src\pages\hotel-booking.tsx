"use client";

import { FilterSection, Hotel, SearchParams } from "@/lib/types";
import { HotelCard } from "@/components/hotelBooking/hotelSearch/hotel-card";
import { SidebarFilter } from "@/components/hotelBooking/hotelSearch/sidebar-filter";
import { filterSections } from "@/constants/hotelBookingData";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState, useEffect, useCallback, useRef } from "react";
import { ChevronDown, Loader2 } from "lucide-react";
import HotelSearchBar from "@/components/hotelBooking/hotelSearch/hotel-search-bar";
import axios from "axios";
import { useCustomSession } from "@/hooks/use-custom-session"
import { useDispatch } from "react-redux";
import { setSearchContext } from "@/store/slices/hotelBookingContext";

const HotelBooking = () => {
    const isMobile = useIsMobile();
    const dispatch = useDispatch();
    const { data: session, status } = useCustomSession();
    const token = session?.accessToken;
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [hotels, setHotels] = useState<Hotel[]>([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchPerformed, setSearchPerformed] = useState(false);
    const [totalHotels, setTotalHotels] = useState(0);
    const [currentLocation, setCurrentLocation] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [lastSearchParams, setLastSearchParams] = useState<SearchParams | null>(null);
    const [dynamicFilterSections, setDynamicFilterSections] = useState<FilterSection[]>(filterSections);
    const [searchReferenceKey, setSearchReferenceKey] = useState<string>("");
    const [isApplyingFilters, setIsApplyingFilters] = useState(false);

    // Ref for the main content container
    const mainContentRef = useRef<HTMLDivElement>(null);

    // Build quick_filter object from selected filters
    const buildQuickFilter = () => {
        const quickFilter: any = {};

        dynamicFilterSections.forEach(section => {
            const selectedOptions = section.options.filter(option => option.checked);
            
            if (selectedOptions.length === 0) return;

            switch (section.id) {
                case "property-rating":
                    quickFilter.categoryCode = selectedOptions.map(option => option.filter);
                    break;
                case "property-type":
                    quickFilter.accommodationTypeCode = selectedOptions.map(option => option.filter);
                    break;
                case "facilities":
                    if (!quickFilter.facilities) quickFilter.facilities = [];
                    quickFilter.facilities.push(...selectedOptions.map(option => option.filter));
                    break;
                case "room-facilities":
                    if (!quickFilter.facilities) quickFilter.facilities = [];
                    quickFilter.facilities.push(...selectedOptions.map(option => option.filter));
                    break;
                case "interest-points":
                    quickFilter["interestPoints.poiName"] = selectedOptions.map(option => option.filter);
                    break;
            }
        });

        return Object.keys(quickFilter).length > 0 ? quickFilter : undefined;
    };

    // Apply filters by making a new search request with quick_filter
    const applyFilters = async () => {
        console.log('applyFilters called');
        console.log('lastSearchParams:', lastSearchParams);
        console.log('token:', !!token);
        console.log('searchReferenceKey:', searchReferenceKey);
        console.log('dynamicFilterSections:', dynamicFilterSections);

        if (!lastSearchParams || !token) {
            console.error('Cannot apply filters: missing search parameters or token');
            console.error('Missing:', {
                lastSearchParams: !lastSearchParams,
                token: !token,
            });
            return;
        }

        if (!searchReferenceKey) {
            console.warn('No search reference key available - proceeding without it');
        }

        setIsApplyingFilters(true);
        setLoading(true);

        try {
            const quickFilter = buildQuickFilter();
            console.log('Built quick filter:', quickFilter);
            
            const searchData = {
                ...lastSearchParams,
                page: 1, // Reset to first page when applying filters
                ...(searchReferenceKey && { search_reference_key: searchReferenceKey }),
                ...(quickFilter && { quick_filter: quickFilter })
            };

            console.log('Applying filters with payload:', searchData);

            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/hotel/search`,
                searchData,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    }
                }
            );

            console.log('Filter response:', response.data);

            if (response.status === 200) {
                const responseData = response.data?.detail?.data;
                const hotelArray = responseData?.hotels || [];
                const total = responseData?.total_count || 0;

                const transformedHotels = transformHotelData(hotelArray, currentLocation);
                
                setHotels(transformedHotels);
                setTotalHotels(total);
                setCurrentPage(1);
                setHasMore(transformedHotels.length < total);
                
                // DON'T update search reference key or filter options from filter responses
                // They should only come from the initial search
                console.log('Filter applied successfully, hotels updated');
            }
        } catch (error: any) {
            console.error('Error applying filters:', error);
            setError("Failed to apply filters");
        } finally {
            setIsApplyingFilters(false);
            setLoading(false);
        }
    };

    // Transform API filter options to filter section format
    const transformFilterOptions = (filterOptions: any): FilterSection[] => {
        const sections: FilterSection[] = [];

        try {
            // Transform property rating
            if (filterOptions.property_rating?.data && Array.isArray(filterOptions.property_rating.data)) {
                sections.push({
                    id: "property-rating",
                    title: filterOptions.property_rating.label || "Property Rating",
                    options: filterOptions.property_rating.data.map((item: any) => ({
                        id: item.filter || `rating-${Math.random()}`,
                        label: item.label || 'Unknown Rating',
                        count: item.count || 0,
                        filter: item.filter,
                        checked: false
                    })),
                    showCount: Math.min(filterOptions.property_rating.data.length, 5),
                    expandable: filterOptions.property_rating.data.length > 5
                });
            }

            // Transform accommodation types
            if (filterOptions.accomodation_types?.data && Array.isArray(filterOptions.accomodation_types.data)) {
                sections.push({
                    id: "property-type",
                    title: filterOptions.accomodation_types.label || "Property Type",
                    options: filterOptions.accomodation_types.data.map((item: any) => ({
                        id: item.filter || `type-${Math.random()}`,
                        label: item.label || 'Unknown Type',
                        count: item.count || 0,
                        filter: item.filter,
                        checked: false
                    })),
                    showCount: Math.min(filterOptions.accomodation_types.data.length, 5),
                    expandable: filterOptions.accomodation_types.data.length > 5
                });
            }

            // Transform facilities
            if (filterOptions.facilities?.data && Array.isArray(filterOptions.facilities.data)) {
                sections.push({
                    id: "facilities",
                    title: filterOptions.facilities.label || "Facilities",
                    options: filterOptions.facilities.data.map((item: any, index: number) => ({
                        id: `facility-${item.filter?.facilityCode || index}`,
                        label: item.label || 'Unknown Facility',
                        count: item.count || 0,
                        filter: item.filter,
                        checked: false
                    })),
                    showCount: Math.min(filterOptions.facilities.data.length, 10),
                    expandable: filterOptions.facilities.data.length > 10
                });
            }

            // Transform room facilities
            if (filterOptions.room_facilities?.data && Array.isArray(filterOptions.room_facilities.data)) {
                sections.push({
                    id: "room-facilities",
                    title: filterOptions.room_facilities.label || "Room Facilities",
                    options: filterOptions.room_facilities.data.map((item: any, index: number) => ({
                        id: `room-facility-${item.filter?.facilityCode || index}`,
                        label: item.label || 'Unknown Room Facility',
                        count: item.count || 0,
                        filter: item.filter,
                        checked: false
                    })),
                    showCount: Math.min(filterOptions.room_facilities.data.length, 10),
                    expandable: filterOptions.room_facilities.data.length > 10
                });
            }

            // Transform interest points
            if (filterOptions.interest_points?.data && Array.isArray(filterOptions.interest_points.data)) {
                sections.push({
                    id: "interest-points",
                    title: filterOptions.interest_points.label || "Interest Points",
                    options: filterOptions.interest_points.data.map((item: any, index: number) => {
                        // For interest points, the filter is directly a string, not an object
                        const filterValue = item.filter || item.label || `poi-${index}`;
                        return {
                            id: `poi-${filterValue.replace(/\s+/g, '-').toLowerCase()}`,
                            label: item.label || 'Unknown Point of Interest',
                            count: item.count || 0,
                            filter: filterValue,
                            checked: false
                        };
                    }),
                    showCount: Math.min(filterOptions.interest_points.data.length, 10),
                    expandable: filterOptions.interest_points.data.length > 10
                });
            }
        } catch (transformError) {
            console.error('Error in transformFilterOptions:', transformError);
            console.error('Filter options that caused error:', filterOptions);
        }

        return sections;
    };

    const handleFilterChange = (
        sectionId: string,
        optionId: string,
        checked: boolean
    ) => {
        console.log(`Filter changed: ${sectionId} - ${optionId} - ${checked}`);
        
        // Update the dynamic filter sections state immediately
        setDynamicFilterSections(prev => 
            prev.map(section => 
                section.id === sectionId 
                    ? {
                        ...section,
                        options: section.options.map(option => 
                            option.id === optionId 
                                ? { ...option, checked }
                                : option
                        )
                    }
                    : section
            )
        );
    };

    const handleReset = () => {
        console.log("Filters reset");
        
        // Reset all filters in parent state
        setDynamicFilterSections(prev => 
            prev.map(section => ({
                ...section,
                options: section.options.map(option => ({
                    ...option,
                    checked: false
                }))
            }))
        );
    };

    const handleDone = () => {
        console.log("Done clicked - applying filters");
        setIsFilterOpen(false);
        applyFilters();
    };

    const handleFilterDone = () => {
        
        // Check if any filters are selected
        const hasSelectedFilters = dynamicFilterSections.some(section => 
            section.options.some(option => option.checked)
        );
        console.log("Has selected filters:", hasSelectedFilters);
        
        if (isMobile) {
            setIsFilterOpen(false);
        }

        // If no search reference key, try to do a new search instead
        if (!searchReferenceKey) {
            console.warn('No search reference key available. This might be the first search.');
        }
        
        applyFilters();
    };

    const generateRandomRating = (min: number = 3, max: number = 5, step: number = 0.5): number => {
        const range = (max - min) / step;
        const randomStep = Math.floor(Math.random() * (range + 1));
        return min + (randomStep * step);
    };

    const transformHotelData = (hotelArray: Hotel[], location: string) => {
        return hotelArray.map((hotel: Hotel) => ({
            id: hotel.code?.toString() || Math.random().toString(),
            name: hotel.name || "Unknown Hotel",
            city: hotel.city || "Unknown Location",
            address: hotel.address || "Address not available",
            description: hotel.description?.substring(0, 400) + "..." || "No description available",
            category_name: hotel.category_name,
            imageSrc: hotel.images && hotel.images.length > 0
                ? `${hotel.img_base_url}${hotel.images[0].path}`
                : "/placeholder-hotel.jpg",
            rating: generateRandomRating(),
            min_rate: hotel.min_rate,
            currency: hotel.currency,
            available_rooms: hotel.available_rooms || 0,
            provider: hotel.provider,
            rooms: hotel.rooms || [],
            code: hotel.code,
            img_base_url: hotel.img_base_url,
        }));
    };

    const fetchMoreHotels = useCallback(async (page: number) => {
        if (!lastSearchParams || !token) return;

        setLoadingMore(true);

        try {
            const quickFilter = buildQuickFilter();
            
            const searchData = {
                ...lastSearchParams,
                page: page,
                ...(searchReferenceKey && { search_reference_key: searchReferenceKey }),
                ...(quickFilter && { quick_filter: quickFilter })
            };

            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/hotel/search`,
                searchData,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    }
                }
            );

            if (response.status === 200) {
                const responseData = response.data?.detail?.data;
                const hotelArray = responseData?.hotels || [];
                const total = responseData?.total_count || 0;

                if (hotelArray.length === 0) {
                    setHasMore(false);
                } else {
                    const transformedHotels = transformHotelData(hotelArray, currentLocation);
                    setHotels(prev => [...prev, ...transformedHotels]);
                    setCurrentPage(page);

                    // Check if we've loaded all available hotels
                    const totalLoadedHotels = hotels.length + transformedHotels.length;
                    if (totalLoadedHotels >= total) {
                        setHasMore(false);
                    }
                }
            }
        } catch (error: any) {
            console.error('Error fetching more hotels:', error);
            setError("Failed to load more hotels");
        } finally {
            setLoadingMore(false);
        }
    }, [lastSearchParams, token, currentLocation, hotels.length, searchReferenceKey, dynamicFilterSections]);

    // Infinite scroll handler
    const handleScroll = useCallback(() => {
        if (!mainContentRef.current || loadingMore || !hasMore) return;

        const { scrollTop, scrollHeight, clientHeight } = mainContentRef.current;

        // Trigger when user is 100px from the bottom
        if (scrollTop + clientHeight >= scrollHeight - 100) {
            fetchMoreHotels(currentPage + 1);
        }
    }, [fetchMoreHotels, currentPage, loadingMore, hasMore]);

    // Set up scroll listener
    useEffect(() => {
        const mainContent = mainContentRef.current;
        if (!mainContent) return;

        mainContent.addEventListener('scroll', handleScroll);
        return () => mainContent.removeEventListener('scroll', handleScroll);
    }, [handleScroll]);

    const handleSearchStart = () => {
        setLoading(true);
        setError(null);
        setSearchPerformed(true);
        // Reset pagination state for new search
        setCurrentPage(1);
        setHasMore(true);
        setHotels([]);
    };

    const handleSearchResults = (data: any, searchParams?: SearchParams) => {
        console.log('Search results received:', data);

        try {
            // Store search parameters for pagination
            if (searchParams) {
                setLastSearchParams(searchParams);
                
                // Store search context in Redux for use throughout the booking flow
                dispatch(setSearchContext({
                    location: searchParams.location,
                    checkIn: searchParams.check_in,
                    checkOut: searchParams.check_out,
                    adults: searchParams.adults,
                    children: searchParams.children,
                    rooms: searchParams.rooms,
                }));
            }

            const responseData = data?.detail?.data;
            console.log('Response data:', responseData);
            
            const hotelArray = responseData?.hotels || [];
            const total = responseData?.total_count || 0;
            const location = searchParams?.location || "Unknown Location";
            const filterOptions = responseData?.filter_options;

            console.log('Filter options:', filterOptions);
            console.log('Hotel array length:', hotelArray.length);

            // Update filter sections with dynamic data from API
            if (filterOptions) {
                try {
                    const dynamicSections = transformFilterOptions(filterOptions);
                    console.log('Dynamic sections created:', dynamicSections);
                    setDynamicFilterSections(dynamicSections);
                } catch (filterError) {
                    console.error('Error transforming filter options:', filterError);
                    // Continue with default filters if transformation fails
                    setDynamicFilterSections(filterSections);
                }
            } else {
                console.log('No filter options in response, using default filters');
                setDynamicFilterSections(filterSections);
            }

            const transformedHotels = transformHotelData(hotelArray, location);
            console.log('Transformed hotels:', transformedHotels);

            setHotels(transformedHotels);
            setTotalHotels(total);
            setCurrentLocation(location);
            setLoading(false);
            setError(null);
            setCurrentPage(1);

            // Check if there are more results available
            if (transformedHotels.length >= total || hotelArray.length === 0) {
                setHasMore(false);
            } else {
                setHasMore(true);
            }
        } catch (err) {
            console.error('Error processing search results:', err);
            console.error('Error stack:', err instanceof Error ? err.stack : 'No stack trace');
            console.error('Data that caused error:', data);
            setError("Failed to process search results");
            setLoading(false);
            // Fallback to default filters on error
            setDynamicFilterSections(filterSections);
        }
    };

    const handleSearchError = (errorMessage: string) => {
        setError(errorMessage);
        setLoading(false);
        setHasMore(false);
    };

    const renderContent = () => {
        if (loading) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#080236] mx-auto mb-4"></div>
                        <p className="text-gray-600">Searching for hotels...</p>
                    </div>
                </div>
            );
        }

        if (error && hotels.length === 0) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <p className="text-red-600 mb-4">{error}</p>
                        <p className="text-gray-600">Please try searching again</p>
                    </div>
                </div>
            );
        }

        if (!searchPerformed || hotels.length === 0) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <p className="text-gray-600 text-lg">
                            {!searchPerformed ? "Search for hotels to see results" : "No hotels found"}
                        </p>
                        <p className="text-gray-500 text-sm mt-2">
                            {!searchPerformed ? "Enter your destination and travel dates above" : "Try adjusting your search criteria"}
                        </p>
                    </div>
                </div>
            );
        }

        return (
            <div className="space-y-6">
                {hotels.map((hotel, index) => (
                    <HotelCard
                        key={`${hotel.id}-${index}`}
                        hotelData={hotel}
                    />
                ))}

                {/* Loading more indicator */}
                {loadingMore && (
                    <div className="flex items-center justify-center py-8">
                        <div className="flex items-center gap-2 text-gray-600">
                            <Loader2 className="h-5 w-5 animate-spin" />
                            <span>Loading more hotels...</span>
                        </div>
                    </div>
                )}

                {/* End of results indicator */}
                {!hasMore && hotels.length > 0 && (
                    <div className="flex items-center justify-center py-8">
                        <p className="text-gray-500 text-sm">
                            You've reached the end of the results
                        </p>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="flex h-screen">
            {/* Left side content (header and hotel listings) */}
            <div className="flex-1 flex flex-col overflow-hidden font-proxima-nova">
                {/* Mobile filter header */}
                {isMobile && (
                    <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
                        <div className="flex-1 text-center">
                            <button
                                className="font-proxima-nova hover:bg-[#F2F3FA] text-[#080236] xl:text-base lg:text-sm sm:text-base xs:text-xs flex items-center sm:gap-2 xs:gap-0.5 rounded-full 2xl:px-4 sm:px-2 xs:px-2 xs:py-1 2xl:py-2 sm:py-1"
                                onClick={() => setIsFilterOpen(true)}
                            >
                                Filters <ChevronDown className="inline-block h-4 w-4 ml-1" />
                            </button>
                        </div>
                    </div>
                )}

                {/* Header with search bar */}
                <header className="bg-[#080236] text-white p-4 font-proxima-nova">
                    <HotelSearchBar
                        onSearchResults={handleSearchResults}
                        onSearchStart={handleSearchStart}
                        onSearchError={handleSearchError}
                    />
                </header>

                {/* Main content area with hotel listings */}
                <main
                    ref={mainContentRef}
                    className="flex-1 p-4 bg-gray-50 overflow-y-auto font-proxima-nova"
                >
                    <div className="max-w-4xl mx-auto">
                        {/* Only show header when we have search results */}
                        {searchPerformed && !loading && hotels.length > 0 && (
                            <div className="flex justify-between items-center mb-6">
                                <h1 className="text-xl md:text-2xl font-bold text-[#080236]">
                                    {currentLocation}: {totalHotels} properties found
                                </h1>
                            </div>
                        )}

                        {renderContent()}
                    </div>
                </main>
            </div>

            {/* Sidebar filter on the right - full height (only visible on desktop) */}
            {!isMobile && (
                <div className="hidden md:block font-proxima-nova">
                    <SidebarFilter
                        sections={dynamicFilterSections}
                        onChange={handleFilterChange}
                        onReset={handleReset}
                        onDone={handleFilterDone}
                        width={280}
                        className="h-screen"
                    />
                </div>
            )}

            {/* Mobile filter */}
            {isMobile && (
                <SidebarFilter
                    sections={dynamicFilterSections}
                    onChange={handleFilterChange}
                    onReset={handleReset}
                    onDone={handleFilterDone}
                    isOpen={isFilterOpen}
                    onOpenChange={setIsFilterOpen}
                />
            )}
        </div>
    );
};

export default HotelBooking;