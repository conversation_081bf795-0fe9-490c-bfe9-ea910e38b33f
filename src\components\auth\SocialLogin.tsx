import React, { useEffect, useRef, useState } from "react";
import { authPostMethod } from "@/utils/auth";
import { signIn } from "next-auth/react";
import tracker from "@/utils/posthogTracker";

declare global {
  interface Window {
    AppleID: any;
    google: any;
  }
}

interface SocialLoginProps {
  authType: "signIn" | "signup";
  onLoginSuccess?: (loggedInData: any) => void;
}

const SocialLogin: React.FC<SocialLoginProps> = ({ authType, onLoginSuccess }) => {
  const [googleAuthError, setGoogleAuthError] = useState<string | null>(null);
  const [appleAuthError, setAppleAuthError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined" && window.AppleID) {
      window.AppleID.auth.init({
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID, // replace with your real bundle ID
        scope: "name email",
        redirectURI:
          process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI, // not used with usePopup
        usePopup: true,
      });
    }
  }, []);

  const handleAppleLogin = async () => {
    if (typeof window === "undefined" || !window.AppleID) return;
    try {
      const response = await window.AppleID.auth.signIn();
      const { authorization, user } = response;
      const payload = {
        code: authorization.id_token,
        // user: user || authorization.id_token
      };
      try {
        const res = await authPostMethod("auth/apple", payload);

        if (!res.data?.detail.data?.accessToken) {
          const errorMessage = res.data?.detail.message || "Oops, Apple SignIn failed!!";
          tracker.trackEvent("Apple Sign-Up Failed", {
            reason: errorMessage,
          });

          setAppleAuthError(response.data?.detail.message || "Oops, Apple SignIn failed!!")
          return;
        }

        const {
          accessToken,
          refreshToken,
          accessTokenExpireOn,
          refreshTokenExpireOn,
          tokenType,
          isNewUser,
          user,
        } = res.data.detail.data;

        await signIn("credentials", {
          redirect: false,
          id: user.id,
          email: user.email,
          ...user,
          accessToken,
          refreshToken,
          accessTokenExpireOn,
          refreshTokenExpireOn,
        });

        tracker.trackEvent("Apple Sign-Up Success", {
          userId: user.id,
          email: user.email,
          isNewUser,
        });

        onLoginSuccess?.(res.data?.detail.data);
      } catch (err) {
        console.log("error", err);

        tracker.trackEvent("Apple Sign-Up Failed", {
          reason: "API call error",
          error: err instanceof Error ? err.message : String(err),
        });
      }
      // Optionally store token or redirect the user here
    } catch (error: any) {
      console.error("Apple Sign In failed:", error);
      tracker.trackEvent("Apple Sign-Up Failed", {
        reason: "Apple signIn rejected or failed",
        error: error instanceof Error ? error.message : String(error),
      });
      setAppleAuthError("Apple Sign In failed. Please try again.");
    }
  };

  const codeClient = useRef<any>(null);

  useEffect(() => {
    if (typeof window !== "undefined" && window.google) {
      codeClient.current = window.google.accounts.oauth2.initCodeClient({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        scope: "email profile openid",
        redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI, // ✅ must match Google Console
        callback: async (response: any) => {
          if (response?.code) {
            try {
              const res = await authPostMethod("auth/google/token", {
                code: response.code,
              });
              if (!res.data?.detail.data?.accessToken) {
                const errorMessage = res.data?.detail.message || "Oops, Google SignIn failed!!";
                tracker.trackEvent("Google Sign-Up Failed", {
                  reason: errorMessage,
                });
                setGoogleAuthError(response.data?.detail.message || "Oops, Google SignIn failed!!")
                return;
              }
              const {
                accessToken,
                refreshToken,
                accessTokenExpireOn,
                refreshTokenExpireOn,
                tokenType,
                isNewUser,
                user,
              } = res.data.detail.data;
              await signIn("credentials", {
                redirect: false,
                id: user.id,
                email: user.email,
                ...user,
                accessToken,
                refreshToken,
                accessTokenExpireOn,
                refreshTokenExpireOn,
              });
              tracker.trackEvent("Google Sign-Up Success", {
                userId: user.id,
                email: user.email,
                isNewUser,
              });
              onLoginSuccess?.(res.data?.detail.data);
            } catch (err) {
              console.error("Google login error:", err);
              setGoogleAuthError("Login failed. Please try again.");
              tracker.trackEvent("Google Sign-Up Failed", {
                reason: "API call error",
                error: err instanceof Error ? err.message : String(err),
              });
            }
          } else {
            setGoogleAuthError("No authorization code received.");
            tracker.trackEvent("Google Sign-Up Failed", {
              reason: "No authorization code received",
            });
          }
        },
      });
    }
  }, []);

  const handleGoogleLogin = () => {
    if (codeClient.current) {
      codeClient.current.requestCode(); // 🔥 This opens Google login popup
    } else {
      setGoogleAuthError("Google login client is not ready.");
    }
  };
  return (
    <div className="flex md:flex-row lg:flex-col lg:w-full font-proxima-nova gap-4 xs:gap-2 sm:flex-row xs:flex-col lg:justify-center xs:justify-center sm:items-center xs:items-center">
      <button
        onClick={handleGoogleLogin}
        className="lg:w-full md:w-max flex sm:text-sm xs:text-xs items-center justify-center py-3 sm:py-2 xs:py-2 md:px-4 sm:px-3 xs:px-3 border border-brand-border transition"
        style={styles.button}
      >
        <img
          src="https://storage.googleapis.com/nxvoytrips-img/social/google-icon.svg"
          alt="Google"
          className="w-5 h-5 mr-2 xs:mr-1"
        />
        {authType === "signIn" ? "Sign In With Google" : "Sign Up With Google"}
      </button>
      {googleAuthError && (
        <p className="text-red-600 text-sm mt-2 mb-4 text-center">
          {googleAuthError}
        </p>
      )}
      <button
        onClick={handleAppleLogin}
        className="lg:w-full md:w-max flex sm:text-sm xs:text-xs items-center justify-center py-3 sm:py-2 xs:py-2 md:px-4 sm:px-3 xs:px-3 border border-brand-border transition"
        style={styles.button}
      >
        <img
          src="https://storage.googleapis.com/nxvoytrips-img/social/apple-icon.svg"
          alt="Apple"
          className="w-5 h-5 mr-2 xs:mr-1"
        />
        {authType === "signIn" ? "Sign In With Apple" : "Sign Up With Apple"}
      </button>
      {appleAuthError && (
        <p className="text-red-600 text-sm mt-2 mb-4 text-center">
          {appleAuthError}
        </p>
      )}
    </div>
  );
};

const styles = {
  button: {
    color: "#080236",
    borderRadius: "100px",
    fontWeight: 600,
    boxShadow: "inset 0 100vw white",
    border: "1px solid #EBEBEB",
  },
};

export default SocialLogin;
