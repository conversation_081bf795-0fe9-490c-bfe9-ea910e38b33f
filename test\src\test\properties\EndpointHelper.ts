const endpoints = require('../../resources/data/config/endpoints.json');

export class EndpointHelper {
    static getEndpoint = function (key: string) {
        return EndpointHelper.getEndpointWithCompoundKey(endpoints, key.split("."));
    }

    static getEndpointWithCompoundKey = function (parent: object, keys: Array<string>): string {
        if (keys.length <= 1) {
            return parent[keys[0] as keyof typeof parent];
        }
        else {
            return EndpointHelper.getEndpointWithCompoundKey(parent[keys[0] as keyof typeof parent], keys.slice(1, keys.length));
        }
    }
}