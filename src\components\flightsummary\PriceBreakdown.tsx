import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/custom-dialog"
import { Airport, Flight, PassengerPrice } from "@/constants/models";
import { useEffect, useState } from "react";
import { formatFlightPrice } from "@/lib/utils/formatPrice";
import { ChevronDown } from "lucide-react";

interface PriceBreakdownProps {
    outbound: Flight;
    inbound?: Flight;
    airport: { [iataCode: string]: Airport };
    showServiceFee?: boolean;
}

interface GroupedPassengers {
    Adult: PassengerPrice[];
    Child: PassengerPrice[];
    Infant: PassengerPrice[];
}

const PriceBreakdownDialog: React.FC<PriceBreakdownProps> = ({ outbound, inbound, airport, showServiceFee = false }) => {
    const [groupedPassengers, setGroupedPassengers] = useState<GroupedPassengers>();
    const [inboundGroupedPassengers, setInboundGroupedPassengers] = useState<GroupedPassengers>();
    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
    const [additionalChargesExpanded, setAdditionalChargesExpanded] = useState(false);
    const [inboundAdditionalChargesExpanded, setInboundAdditionalChargesExpanded] = useState(false);

    useEffect(() => {
        console.log('outbound', outbound)
        if (outbound) {
            const grouped: GroupedPassengers = {
                Adult: [],
                Child: [],
                Infant: []
            };

            outbound.cabin_classes?.[0].passenger_prices.forEach((item) => {
                if (parseInt(item.PassengerPrice.Age) >= 13) {
                    grouped.Adult.push(item.PassengerPrice)
                } else if (parseInt(item.PassengerPrice.Age) >= 2 && parseInt(item.PassengerPrice.Age) <= 12) {
                    grouped.Child.push(item.PassengerPrice);
                } else if (parseInt(item.PassengerPrice.Age) < 2) {
                    grouped.Infant.push(item.PassengerPrice);
                }
            })

            if (grouped.Adult.length || grouped.Child.length || grouped.Infant.length) {
                setGroupedPassengers(grouped)
            }
        }

        // Process inbound passenger data
        if (inbound) {
            const inboundGrouped: GroupedPassengers = {
                Adult: [],
                Child: [],
                Infant: []
            };

            inbound.cabin_classes?.[0].passenger_prices.forEach((item) => {
                if (parseInt(item.PassengerPrice.Age) >= 13) {
                    inboundGrouped.Adult.push(item.PassengerPrice)
                } else if (parseInt(item.PassengerPrice.Age) >= 2 && parseInt(item.PassengerPrice.Age) <= 12) {
                    inboundGrouped.Child.push(item.PassengerPrice);
                } else if (parseInt(item.PassengerPrice.Age) < 2) {
                    inboundGrouped.Infant.push(item.PassengerPrice);
                }
            })

            if (inboundGrouped.Adult.length || inboundGrouped.Child.length || inboundGrouped.Infant.length) {
                setInboundGroupedPassengers(inboundGrouped)
            }
        }
    }, [outbound, inbound])

    const calculateFareAmount = (passenger: PassengerPrice) => {
        const totalAmount = parseFloat(passenger.Amount);
        const taxAmount = passenger.TaxItemList?.[0]?.TaxItem?.reduce((sum, tax) => {
            return sum + parseFloat(tax.Amount);
        }, 0) || 0;
        return totalAmount - taxAmount;
    };

    const toggleSection = (sectionKey: string) => {
        setExpandedSections(prev => ({
            ...prev,
            [sectionKey]: !prev[sectionKey]
        }));
    };

    const renderPassengerTable = (passenger: PassengerPrice, type: string, index: number, flightType: string = '') => {
        const fareAmount = calculateFareAmount(passenger);
        const sectionKey = `${flightType}${type}-${index}`;
        const isExpanded = expandedSections[sectionKey];

        return (
            <div key={sectionKey} className="border border-slate-200 rounded-lg overflow-hidden bg-white">
                {/* Header */}
                <div
                    onClick={() => toggleSection(sectionKey)}
                    className="flex items-center justify-between p-4 cursor-pointer hover:bg-slate-50 transition-colors bg-slate-25"
                >
                    <h3 className="text-base font-semibold text-slate-800">
                        {type} {index + 1}
                    </h3>
                    <div className="flex items-center gap-3">
                        {!isExpanded && (
                            <span className="font-semibold text-slate-900 text-lg">
                                {formatFlightPrice({
                                    amount: parseFloat(passenger.Amount),
                                    currency: passenger.Currency,
                                })}
                            </span>
                        )}
                        <ChevronDown
                            className={`w-5 h-5 transition-transform duration-200 text-slate-500 ${isExpanded ? 'rotate-180' : ''}`}
                        />
                    </div>
                </div>

                {/* Expanded Details */}
                {isExpanded && (
                    <div className="border-t border-slate-200">
                        <div className="p-4 bg-slate-50">
                            <div className="grid grid-cols-[1fr_auto] gap-4 py-2 border-b border-slate-200 pb-3">
                                <span className="font-semibold text-slate-700">Item</span>
                                <span className="font-semibold text-slate-700">Amount</span>
                            </div>

                            <div className="grid grid-cols-[1fr_auto] gap-4 py-2">
                                <span className="text-slate-600">Base Fare</span>
                                <span className="text-slate-900 font-medium">
                                    {formatFlightPrice({
                                        amount: fareAmount,
                                        currency: passenger.Currency,
                                    })}
                                </span>
                            </div>

                            {passenger.TaxItemList?.[0]?.TaxItem?.map((tax, taxIndex) => (
                                <div key={taxIndex} className="grid grid-cols-[1fr_auto] gap-4 py-2">
                                    <span className="text-slate-600">{tax.Name}</span>
                                    <span className="text-slate-900 font-medium">
                                        {formatFlightPrice({
                                            amount: parseFloat(tax.Amount),
                                            currency: tax.Currency,
                                        })}
                                    </span>
                                </div>
                            ))}

                            <div className="grid grid-cols-[1fr_auto] gap-4 py-3 mt-3 border-t border-slate-300">
                                <span className="font-semibold text-slate-900">Total for {type} {index + 1}</span>
                                <span className="font-semibold text-slate-900 text-lg">
                                    {formatFlightPrice({
                                        amount: parseFloat(passenger.Amount),
                                        currency: passenger.Currency,
                                    })}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderAdditionalCharges = () => {
        if (!outbound.price?.tax_items?.[0]?.TaxItem || outbound.price.tax_items[0].TaxItem.length === 0) {
            return null;
        }

        const totalCharges = outbound.price.tax_items[0].TaxItem.reduce((sum, charge) => {
            return sum + parseFloat(charge.Amount);
        }, 0);

        return (
            <div className="border border-slate-200 rounded-lg overflow-hidden bg-white">
                {/* Header */}
                <div
                    onClick={() => setAdditionalChargesExpanded(!additionalChargesExpanded)}
                    className="flex items-center justify-between p-4 cursor-pointer hover:bg-slate-50 transition-colors bg-slate-25"
                >
                    <h3 className="text-base font-semibold text-slate-800">
                        Additional Charges
                    </h3>
                    <div className="flex items-center gap-3">
                        {!additionalChargesExpanded && (
                            <span className="font-semibold text-slate-900 text-lg">
                                {formatFlightPrice({
                                    amount: totalCharges,
                                    currency: outbound.price.tax_items[0].TaxItem[0].Currency,
                                })}
                            </span>
                        )}
                        <ChevronDown
                            className={`w-5 h-5 transition-transform duration-200 text-slate-500 ${additionalChargesExpanded ? 'rotate-180' : ''}`}
                        />
                    </div>
                </div>

                {/* Expanded Details */}
                {additionalChargesExpanded && (
                    <div className="border-t border-slate-200">
                        <div className="p-4 bg-slate-50">
                            <div className="grid grid-cols-[1fr_auto] gap-4 py-2 border-b border-slate-200 pb-3">
                                <span className="font-semibold text-slate-700">Item</span>
                                <span className="font-semibold text-slate-700">Amount</span>
                            </div>

                            {outbound.price.tax_items[0].TaxItem.map((charge, chargeIndex) => (
                                <div key={chargeIndex} className="grid grid-cols-[1fr_auto] gap-4 py-2">
                                    <span className="text-slate-600">{charge.Name}</span>
                                    <span className="text-slate-900 font-medium">
                                        {formatFlightPrice({
                                            amount: parseFloat(charge.Amount),
                                            currency: charge.Currency,
                                        })}
                                    </span>
                                </div>
                            ))}

                            <div className="grid grid-cols-[1fr_auto] gap-4 py-3 mt-3 border-t border-slate-300">
                                <span className="font-semibold text-slate-900">Total Additional Charges</span>
                                <span className="font-semibold text-slate-900 text-lg">
                                    {formatFlightPrice({
                                        amount: totalCharges,
                                        currency: outbound.price.tax_items[0].TaxItem[0].Currency,
                                    })}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderInboundAdditionalCharges = () => {
        if (!inbound?.price?.tax_items?.[0]?.TaxItem || inbound.price.tax_items[0].TaxItem.length === 0) {
            return null;
        }

        const totalCharges = inbound.price.tax_items[0].TaxItem.reduce((sum, charge) => {
            return sum + parseFloat(charge.Amount);
        }, 0);

        return (
            <div className="border border-slate-200 rounded-lg overflow-hidden bg-white">
                {/* Header */}
                <div
                    onClick={() => setInboundAdditionalChargesExpanded(!inboundAdditionalChargesExpanded)}
                    className="flex items-center justify-between p-4 cursor-pointer hover:bg-slate-50 transition-colors bg-slate-25"
                >
                    <h3 className="text-base font-semibold text-slate-800">
                        Additional Charges
                    </h3>
                    <div className="flex items-center gap-3">
                        {!inboundAdditionalChargesExpanded && (
                            <span className="font-semibold text-slate-900 text-lg">
                                {formatFlightPrice({
                                    amount: totalCharges,
                                    currency: inbound.price.tax_items[0].TaxItem[0].Currency,
                                })}
                            </span>
                        )}
                        <ChevronDown
                            className={`w-5 h-5 transition-transform duration-200 text-slate-500 ${inboundAdditionalChargesExpanded ? 'rotate-180' : ''}`}
                        />
                    </div>
                </div>

                {/* Expanded Details */}
                {inboundAdditionalChargesExpanded && (
                    <div className="border-t border-slate-200">
                        <div className="p-4 bg-slate-50">
                            <div className="grid grid-cols-[1fr_auto] gap-4 py-2 border-b border-slate-200 pb-3">
                                <span className="font-semibold text-slate-700">Item</span>
                                <span className="font-semibold text-slate-700">Amount</span>
                            </div>

                            {inbound.price.tax_items[0].TaxItem.map((charge, chargeIndex) => (
                                <div key={chargeIndex} className="grid grid-cols-[1fr_auto] gap-4 py-2">
                                    <span className="text-slate-600">{charge.Name}</span>
                                    <span className="text-slate-900 font-medium">
                                        {formatFlightPrice({
                                            amount: parseFloat(charge.Amount),
                                            currency: charge.Currency,
                                        })}
                                    </span>
                                </div>
                            ))}

                            <div className="grid grid-cols-[1fr_auto] gap-4 py-3 mt-3 border-t border-slate-300">
                                <span className="font-semibold text-slate-900">Total Additional Charges</span>
                                <span className="font-semibold text-slate-900 text-lg">
                                    {formatFlightPrice({
                                        amount: totalCharges,
                                        currency: inbound.price.tax_items[0].TaxItem[0].Currency,
                                    })}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const baseTotal = (outbound.cabin_classes?.[0].price?.amount || 0) + (inbound?.cabin_classes?.[0].price?.amount || 0);
    const serviceFee = baseTotal * 0.05;
    const grandTotal = showServiceFee ? baseTotal + serviceFee : baseTotal;

    return (
        <Dialog>
            <DialogTrigger asChild>
                <p className='underline cursor-pointer text-brand-grey hover:text-brand-grey/80 transition-colors'>
                    Price breakdown
                </p>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md md:max-w-3xl max-h-[85vh] overflow-y-auto">
                <DialogHeader className="border-b border-slate-200 pb-4">
                    <DialogTitle className="text-2xl font-bold text-slate-900">Price Breakdown</DialogTitle>
                    <DialogDescription className="text-slate-600 mt-2">
                        Detailed cost breakdown for all passengers and services
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6 mt-6">
                    {/* Outbound Flight */}
                    <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
                        <div className="bg-slate-50 px-6 py-4 border-b border-slate-200 rounded-t-lg">
                            <p className="text-lg font-semibold text-slate-900">
                                <span className="text-slate-600">Outbound Flight</span>
                                <span className="mx-2 text-slate-400">•</span>
                                <span className="text-slate-800">{`${airport?.[outbound.origin].city_name_original} (${outbound.origin}) → ${airport?.[outbound.destination].city_name_original} (${outbound.destination})`}</span>
                            </p>
                        </div>
                        <div className="p-6 space-y-2">
                            {/* Outbound Adults */}
                            {groupedPassengers?.Adult?.map((passenger, index) =>
                                renderPassengerTable(passenger, 'Adult', index, 'outbound-')
                            )}

                            {/* Outbound Children */}
                            {groupedPassengers?.Child?.map((passenger, index) =>
                                renderPassengerTable(passenger, 'Child', index, 'outbound-')
                            )}

                            {/* Outbound Infants */}
                            {groupedPassengers?.Infant?.map((passenger, index) =>
                                renderPassengerTable(passenger, 'Infant', index, 'outbound-')
                            )}

                            {/* Outbound Additional Charges */}
                            {renderAdditionalCharges()}
                        </div>
                    </div>

                    {/* Inbound Flight */}
                    {inbound && inboundGroupedPassengers && (
                        <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
                            <div className="bg-slate-50 px-6 py-4 border-b border-slate-200 rounded-t-lg">
                                <p className="text-lg font-semibold text-slate-900">
                                    <span className="text-slate-600">Return Flight</span>
                                    <span className="mx-2 text-slate-400">•</span>
                                    <span className="text-slate-800">{`${airport?.[inbound.origin].city_name_original} (${inbound.origin}) → ${airport?.[inbound.destination].city_name_original} (${inbound.destination})`}</span>
                                </p>
                            </div>
                            <div className="p-6 space-y-2">
                                {/* Inbound Adults */}
                                {inboundGroupedPassengers?.Adult?.map((passenger, index) =>
                                    renderPassengerTable(passenger, 'Adult', index, 'inbound-')
                                )}

                                {/* Inbound Children */}
                                {inboundGroupedPassengers?.Child?.map((passenger, index) =>
                                    renderPassengerTable(passenger, 'Child', index, 'inbound-')
                                )}

                                {/* Inbound Infants */}
                                {inboundGroupedPassengers?.Infant?.map((passenger, index) =>
                                    renderPassengerTable(passenger, 'Infant', index, 'inbound-')
                                )}

                                {/* Inbound Additional Charges */}
                                {renderInboundAdditionalCharges()}
                            </div>
                        </div>
                    )}

                    {/* Total Summary */}
                    <div className="bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl p-6 border border-slate-200 shadow-sm">
                        {showServiceFee && (
                            <>
                                <div className="flex justify-between items-center border-slate-200">
                                    <span className="text-slate-700 font-medium">Subtotal</span>
                                    <span className="text-slate-900 font-semibold">
                                        {formatFlightPrice({
                                            amount: baseTotal,
                                            currency: outbound.price?.currency || '',
                                        })}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center py-2 border-slate-200">
                                    <span className="text-slate-700 font-medium">Service Fee</span>
                                    <span className="text-slate-900 font-semibold">
                                        {formatFlightPrice({
                                            amount: serviceFee,
                                            currency: outbound.price?.currency || '',
                                        })}
                                    </span>
                                </div>
                            </>
                        )}
                        <div className="flex justify-between items-center">
                            <span className="text-slate-900 font-bold text-xl">
                                {showServiceFee ? 'Total Amount' : 'Total'}
                            </span>
                            <span className="text-slate-900 font-bold text-xl">
                                {formatFlightPrice({
                                    amount: grandTotal,
                                    currency: outbound.cabin_classes?.[0].price?.currency || '',
                                })}
                            </span>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default PriceBreakdownDialog;