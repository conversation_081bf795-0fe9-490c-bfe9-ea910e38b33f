import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  onClick?: () => void;
  className?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  type = 'button',
  onClick,
  className = '',
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      className={`w-full py-2 font-semibold transition ${className}`}
      style={styles.button}
    >
      {children}
    </button>
  );
};

const styles = {
  button: {
    background: '#1E1E76',
    color: 'white',
    borderRadius: '8px'
  }
};

export default Button;