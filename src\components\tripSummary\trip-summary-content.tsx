"use client";

import { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import type { FlightSegment } from "@/components/SeatSelection/types";
import FlightSeatMap from "@/components/SeatSelection/FlightSeatMap";
import BookingConfirmation from "@/components/flightsummary/BookingConfirmation";
import Baggage from "@/components/flightsummary/Baggage";
import PaymentForm from "@/components/flightsummary/payment";
import Flyer from "@/components/flightsummary/Flyer";
import ProgressStepper from "@/components/flightsummary/progressSteps";
import Navbar from "@/components/NavBar";
import ChatFooter from "@/components/footer/chatFooter";
import { updateTripSummary } from "@/store/slices/tripSummary";

// Import our new components
import { useTripSummaryState } from "../../hooks/use-trip-summary-state";
import { useFormValidation } from "../../hooks/use-form-validation";
import { useStripePayment } from "../../hooks/use-stripe-payment";
import StepHeader from "./step-header";
import FlightInfoSection from "./flight-info-section";
import PassengerInfoSection from "./passenger-info-section";
import SeatsSection from "./seats-section";
import BaggageSection from "./baggage-section";
import ImportantInfoSection from "./important-info-section";
import CreditCardSection from "./credit-card-section";
import TripSummarySidebar from "./trip-summary-sidebar";
import { ErrorModal } from "../chat/errorModal/error-modal";
import CustomModal from "./custom-modal";
import tracker from "@/utils/posthogTracker";

const TripSummaryContent = () => {
    const {
        currentStep,
        setCurrentStep,
        baggageModal,
        setBaggageModal,
        flightSeatModal,
        setFlightSeatModal,
        isTransitioning,
        setIsTransitioning,
        tripSummaryDetails,
        dispatch,
    } = useTripSummaryState();

    const {
        formErrors,
        setFormErrors,
        paymenFormErrors,
        validatePassegerForm,
        validatePaymentFormData,
    } = useFormValidation();

    const stripePayment = useStripePayment();

    //Error Handling in ChatScreen
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [currentImage, setCurrentImage] = useState(
        "https://storage.googleapis.com/nxvoytrips-img/HomeImgs/ErrorModal/shash-I.png"
    );
    const [currentMessage, setCurrentMessage] = useState(
        "Oops! Something went wrong on my end. Try again in a bit — I'm on it!"
    );

    const [flight, setFlight] = useState<FlightSegment>({
        id: "FL123",
        departure: "Chennai (MAA)",
        arrival: "London (LHR)",
        aircraftType: "B738",
        bookedSeats: ["5A", "5B", "10C", "10D", "15E", "15F"],
    });

    const steps = [
        { id: 1, label: "Trip Summary" },
        { id: 2, label: "Review & Pay" },
        { id: 3, label: "Booking Confirmation" },
    ];

    const handlePayandReview = async () => {
        if (Array.isArray(tripSummaryDetails?.passengerDetails)) {
            const { allErrors, validForm } = validatePassegerForm(tripSummaryDetails);
            setFormErrors({ ...formErrors, passengers: allErrors });
            if (validForm) {
                setIsTransitioning(true);
                setCurrentStep(2);
                dispatch(
                    updateTripSummary({
                        ...tripSummaryDetails,
                        passengerDetailsUpdated: true,
                        passengerDetails: tripSummaryDetails?.passengerDetails,
                    })
                );
            }
        }
    };

    const handleProceedPay = async () => {
        console.log("=== PAY NOW BUTTON CLICKED ===");
        console.log("Current trip summary details:", tripSummaryDetails);

        // Validate payment form first
        const isPaymentFormValid = validatePaymentFormData(
            tripSummaryDetails?.paymentDetails
        );
        if (!isPaymentFormValid) {
            console.log("Payment form validation failed");
            return;
        }

        // Validate passenger form
        const { validForm } = validatePassegerForm(tripSummaryDetails);
        if (!validForm) {
            console.log("Passenger form validation failed");
            return;
        }

        console.log("Starting Stripe payment validation...");
        // Remove the dispatch parameter since the hook now handles it internally
        const { validPayment } =
            await stripePayment.validateStripePayment(tripSummaryDetails);

        if (validPayment) {
            console.log("Payment successful, proceeding to confirmation");
            setIsTransitioning(true);
            setCurrentStep(3);
        } else {
            console.log("Payment failed");
        }
    };

    const handleBookingConfirmation = (confirmation: boolean) => {
        if (!confirmation) {
            setCurrentStep(2);
        }
    };

    useEffect(() => {
        const flowId = tracker.getFlowId();
        tracker.trackEvent("Step 3 - Page Loaded", { page: "Step3", flowId });
    }, []);

    const showErrorModal = (response: any) => {
        setIsErrorModalOpen(true);
        setCurrentMessage(response?.detail?.message);
    };

    if (Object.keys(tripSummaryDetails?.flightSearchRespnse).length === 0) {
        return null;
    }

    return (
        <>
            <div
                className={`relative w-full min-h-screen bg-brand-white flex flex-col gap-2 mx-auto`}
            >
                <div className="z-50 w-full mx-auto items-center flex flex-col justify-center">
                    <div className="w-[80%] fixed top-0 mx-auto">
                        <Navbar />
                    </div>
                </div>

                <div className="w-[80%] md:w-[95%] lg:w-[90%] xs:w-[100%] mx-auto flex flex-col gap-5 mt-20 xs:mt-10 relative font-proxima-nova">
                    <div className="container mx-auto px-4 py-4">
                        <ProgressStepper currentStep={currentStep} steps={steps} />
                    </div>
                </div>

                <div className="flex w-[80%] md:w-[90%] lg:w-[90%] xs:w-[95%] mx-auto h-0.5 bg-[#B4BBE8]"></div>

                <div
                    className={`${currentStep === 3 ? "w-[80%] md:w-[95%] lg:w-[90%] xs:w-[90%]" : "w-[85%] md:w-[95%] lg:w-[90%] xs:w-[100%]"} mx-auto mt-10 xs:mt-5 mb-10`}
                >
                    {currentStep !== 3 && (
                        <div className="flex flex-col lg:flex-row gap-10 md:gap-5 xs:gap-5 w-full justify-around min-h-0">
                            {/* LEFT SIDE: Scrollable Content */}
                            <div className="flex flex-col lg:w-3/4 md:w-full xs:w-[90%] xs:mx-auto font-proxima-nova gap-2 lg:pr-2 custom-scrollbar order-1 lg:order-1">
                                <StepHeader
                                    currentStep={currentStep}
                                    setCurrentStep={setCurrentStep}
                                />

                                <FlightInfoSection tripSummaryDetails={tripSummaryDetails} />

                                <PassengerInfoSection
                                    tripSummaryDetails={tripSummaryDetails}
                                    formErrors={formErrors}
                                    currentStep={currentStep}
                                />

                                {/* Todo : Enable Seats Section option after live  */}

                                {/* <SeatsSection
                                    currentStep={currentStep}
                                    tripSummaryDetails={tripSummaryDetails}
                                    setFlightSeatModal={setFlightSeatModal}
                                /> */}

                                {currentStep === 1 && (
                                    <BaggageSection
                                        tripSummaryDetails={tripSummaryDetails}
                                        setBaggageModal={setBaggageModal}
                                    />
                                )}

                                <Flyer />
                                {currentStep === 2 && tripSummaryDetails?.supplierInfo && (
                                    <ImportantInfoSection
                                        supplierInfo={tripSummaryDetails?.supplierInfo}
                                    />
                                )}

                                {currentStep === 2 && (
                                    <div className="py-10">
                                        <CreditCardSection
                                            hasActiveCard={stripePayment.hasActiveCard}
                                            showCardForm={stripePayment.showCardForm}
                                            setShowCardForm={stripePayment.setShowCardForm}
                                            cardLast4={stripePayment.cardLast4}
                                            isCardExpired={stripePayment.isCardExpired}
                                            cardTouched={stripePayment.cardTouched}
                                            setCardTouched={stripePayment.setCardTouched}
                                            cardError={stripePayment.cardError}
                                            setCardError={stripePayment.setCardError}
                                            cardComplete={stripePayment.cardComplete}
                                            setCardComplete={stripePayment.setCardComplete}
                                            isCardEmpty={stripePayment.isCardEmpty}
                                            setIsCardEmpty={stripePayment.setIsCardEmpty}
                                            message={stripePayment.message}
                                            success_message={stripePayment.success_message}
                                        />

                                        <PaymentForm
                                            paymenFormErrors={paymenFormErrors}
                                            onProceed={handleProceedPay}
                                            onLoaded={() => setIsTransitioning(false)}
                                        />
                                    </div>
                                )}
                            </div>

                            {/* RIGHT SIDE: Sticky Summary for Desktop, Bottom for Mobile */}
                            <TripSummarySidebar
                                tripSummaryDetails={tripSummaryDetails}
                                currentStep={currentStep}
                                onReviewAndPay={handlePayandReview}
                            />
                        </div>
                    )}

                    {currentStep === 3 && (
                        <BookingConfirmation
                            handleBookingConfirmation={handleBookingConfirmation}
                            showErrorModal={showErrorModal}
                        />
                    )}
                </div>

                <CustomModal
                    isOpen={baggageModal}
                    onClose={() => setBaggageModal(false)}
                >
                    <Baggage onClose={() => setBaggageModal(false)} />
                </CustomModal>

                <Dialog open={flightSeatModal} onOpenChange={setFlightSeatModal}>
                    <DialogContent className="sm:max-w-max rounded-xl">
                        <FlightSeatMap flight={flight} />
                    </DialogContent>
                </Dialog>

                <ChatFooter />
                <ErrorModal
                    isOpen={isErrorModalOpen}
                    onClose={() => {
                        setIsErrorModalOpen(false);
                    }}
                    errorMessage={currentMessage}
                    errorImage={currentImage}
                />
            </div>
        </>
    );
};

export default TripSummaryContent;
