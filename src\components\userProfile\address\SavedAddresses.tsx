import React, { useState } from "react";
import { PencilLine, User } from "lucide-react";

import { Button } from "@/components/ui/button";
import ConfirmDeleteModal from "../ConfirmDeleteModal";

interface Address {
  memorableName: string;
  userName: string;
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

interface SavedAddressesProps {
  addresses: Address[];
  onAddNew: () => void;
  onEdit: (address: Address, idx: number) => void;
  onDelete: (idx: number) => void;
}

const SavedAddresses: React.FC<SavedAddressesProps> = ({ addresses, onAddNew, onEdit, onDelete }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteIdx, setDeleteIdx] = useState<number | null>(null);

  const handleDeleteClick = (idx: number) => {
    setDeleteIdx(idx);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteIdx !== null) {
      onDelete(deleteIdx);
    }
    setModalOpen(false);
    setDeleteIdx(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteIdx(null);
  };

  if (!addresses || addresses.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="text-xl font-semibold mb-2">No address to show</div>
        <div className="text-xl font-bold text-[#4B4BC3] my-8">You Can Create Address Here.</div>
        <button
          className="flex items-center gap-2 bg-[#4B4BC3] text-white px-6 py-2 rounded-full font-semibold text-lg"
          onClick={onAddNew}
        >
          <PencilLine /> Add New Address
        </button>
      </div>
    );
  }

  return (
    <div className="w-full p-6 space-y-10">
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are Sure you want to delete?"
      />
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 md:gap-0 mb-8">
        <h2 className="text-md md:text-3xl font-bold text-[#4B4BC3]">Saved Address</h2>
        <Button className="text-sm md:text-lg text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={onAddNew}>
          <PencilLine /> Add New Address
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {addresses.map((address, idx) => (
          <div key={idx} className="bg-[#E6E3FF] rounded-2xl p-6 flex-1 text-sm md:text-xl">
            <div className="flex items-center gap-2 mb-4">
              <User />
              <span className="font-bold text-lg text-[#1E1E76]">{address.memorableName}</span>
            </div>
            <div className="text-[#1E1E76] mb-6">
              <div>{address.userName}</div>
              <div>{address.street}</div>
              <div>{address.city}</div>
              <div>{address.state}</div>
              <div>{address.country}</div>
              <div>{address.postalCode}</div>
            </div>
            <div className="flex gap-4 mt-4">
              <button
                className="border border-[#4B4BC3] text-[#1E1E76] px-4 py-2 rounded-full font-semibold"
                onClick={() => handleDeleteClick(idx)}
              >
                Delete Address
              </button>
              <button
                className="border border-[#4B4BC3] text-[#1E1E76] px-4 py-2 rounded-full font-semibold"
                onClick={() => onEdit(address, idx)}
              >
                Edit Address
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SavedAddresses;
