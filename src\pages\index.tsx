"use client"

import { useState, useEffect } from "react"
import AuthContainer from "../components/layout/AuthContainer"
import { HeroBanner } from "@/components/homeBanner"
import { About } from "@/components/about"
import Faqs from "@/sections/Faq"
import Subscribe from "@/sections/Subscribe"
import HowItWorks from "@/sections/HowItWorks"
import Features from "@/sections/Features"
import TravellerPlan from "@/sections/TravellerPlan"
import Destinations from "@/sections/Destinations"
import TrustedPartners from "@/sections/TrustedPartners"
import Testimonials from "@/sections/Testimonials"
import AlternateTravellerPlan from "@/sections/AlternateTravellerPlan"

export default function LoginPage() {
  const [isSignInClicked, setIsSignInClicked] = useState<boolean>(false)

  const handleSignIn = () => {
    setIsSignInClicked(true)
  }

  const handleAuthClose = () => {
    setIsSignInClicked(false)
  }

  // ✅ Ensure body can scroll
  useEffect(() => {
    // Reset any scroll-blocking styles
    document.body.style.overflow = "auto"
    document.body.style.overflowX = "hidden"
    document.body.style.overflowY = "auto"
    document.documentElement.style.overflow = "auto"

    return () => {
      // Cleanup on unmount
      document.body.style.overflow = ""
      document.body.style.overflowX = ""
      document.body.style.overflowY = ""
      document.documentElement.style.overflow = ""
    }
  }, [])

  return (
    <div className="relative min-h-screen">
      {/* ✅ Each section should be a separate scrollable block */}
      <section className="relative">
        <HeroBanner handleSignIn={handleSignIn} />
      </section>

      <section className="relative">
        <About />
      </section>

      <section className="relative">
        <HowItWorks />
      </section>

      <section className="relative">
        <Features />
      </section>

      <div className="hidden sm:block">
        <section className="relative">
          <TravellerPlan />
        </section>
      </div>

      <div className="block sm:hidden">
        <section className="relative">
          <AlternateTravellerPlan />
        </section>
      </div>

      <section className="relative">
        <Destinations />
      </section>

      <section className="relative">
        <TrustedPartners />
      </section>

      <section className="relative">
        <Testimonials />
      </section>

      <section className="relative">
        <Faqs />
      </section>

      <section className="relative">
        <Subscribe />
      </section>

      {/* Auth Modal */}
      {isSignInClicked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="rounded-lg p-4">
            <AuthContainer onCloseAuth={handleAuthClose} />
          </div>
        </div>
      )}
    </div>
  )
}
