import { useSelector } from "react-redux";
import { AppState } from "@/store/store";
import { Star, HandHeart } from "lucide-react";
import { Wifi, Car, Coffee, Phone, Wind, Dumbbell } from "lucide-react";

const HotelCard = () => {
  const hotelBookingContext = useSelector((state: AppState) => state.hotelBookingContext.hotelBookingContext);

  if (!hotelBookingContext || !hotelBookingContext.selectedRoom) {
    return <div>Loading booking details...</div>;
  }

  const { selectedRoom, checkIn, checkOut, nights, adults, children } = hotelBookingContext;
  const { hotel, room, selectedRate } = selectedRoom;

  const formatCurrency = (amount: string | number) => {
    const currency = hotel?.currency || 'INR';
    const currencySymbol = currency === 'EUR' ? '€' : '₹';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${currencySymbol}${numAmount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // Get amenities with icons
  const getAmenityIcon = (amenityName: string) => {
    if (amenityName.toLowerCase().includes('breakfast')) {
      return <Coffee className="w-4 h-4" />;
    } else if (amenityName.toLowerCase().includes('wifi')) {
      return <Wifi className="w-4 h-4" />;
    } else if (amenityName.toLowerCase().includes('parking')) {
      return <Car className="w-4 h-4" />;
    } else if (amenityName.toLowerCase().includes('room service')) {
      return <Phone className="w-4 h-4" />;
    } else if (amenityName.toLowerCase().includes('air conditioning')) {
      return <Wind className="w-4 h-4" />;
    } else if (amenityName.toLowerCase().includes('fitness') || amenityName.toLowerCase().includes('gym')) {
      return <Dumbbell className="w-4 h-4" />;
    }
    return <HandHeart className="w-4 h-4" />;
  };

  const roomImage = room.room_images?.[0] || hotel.imageSrc || "/placeholder.jpg";
  const amenities = selectedRate.amenities || [];

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        {/* Hotel Image */}
        <div className="md:w-[342px] h-[240px] bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
          <img 
            src={roomImage}
            alt="Hotel room" 
            className="w-full h-full object-cover"
          />
        </div>

        {/* Hotel Details */}
        <div className="flex-1 ">
          <h2 className="text-[30px] font-bold text-[#1E1E76] mb-2 xs:text-center">
            {hotel.name}
          </h2>
          
          <div className="flex items-center gap-2 mb-3 xs:justify-center">
            <span className="text-[#080236] text-[24px] font-semibold">128 Verified Reviews</span>
          </div>

          <div className="flex justify-around gap-4 mb-3 xs:justify-center">
            <div className="">
              <div className="flex items-center gap-1 justify-center">
                <Star className="w-4 h-4 fill-[#4B4BC3] text-[#4B4BC3]" />
                <span className="text-[18px] font-medium">{hotel.rating || 4.0}</span>
              </div>
              <span className="text-[14px] font-semibold text-[#080236]">Exceptional</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <div className="flex items-center gap-1">
                <span className="text-[14px] font-medium text-[#4B4BC3]">{hotel.rating || 4.0}</span>
              </div>
              <span className="text-[14px] text-[#080236]">Service</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <div className="flex items-center gap-1">
                <span className="text-[14px] font-medium text-[#4B4BC3]">{hotel.rating || 4.0}</span>
              </div>
              <span className="text-[14px] text-[#080236]">Staff</span>
            </div>
            <div className="flex flex-col items-center justify-center">
              <div className="flex items-center gap-1">
                <span className="text-[14px] font-medium text-[#4B4BC3]">{hotel.rating || 4.0}</span>
              </div>
              <span className="text-[14px] text-[#080236]">Location</span>
            </div>
          </div>

          <p className="text-[14px] text-[#24C72F] font-medium xs:text-center">
            {selectedRate.cancellation_policies?.length > 0 
              ? `Fully Refundable until ${formatDate(selectedRate.cancellation_policies[0].from)}`
              : "Non-refundable booking"
            }
          </p>
        </div>
      </div>

      {/* Booking Details */}
      <div className="relative font-proxima-nova w-full p-px rounded-lg h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 px-4 py-2 bg-[#E6E3FF] rounded-lg">
          <div className="text-center text-[18px] font-semibold text-[#707FF5]">
            <p className="mb-1">CHECK-IN</p>
            <p className="text-[#1E1E76]">{formatDate(checkIn)}</p>
          </div>
          <div className="text-center text-[18px] font-semibold text-[#707FF5]">
            <p className="mb-1">CHECK-OUT</p>
            <p className="text-[#1E1E76]">{formatDate(checkOut)}</p>
          </div>
          <div className="text-center text-[18px] font-semibold text-[#707FF5]">
            <p className="mb-1">NIGHTS</p>
            <p className="text-[#1E1E76]">{nights}</p>
          </div>
          <div className="text-center text-[18px] font-semibold text-[#707FF5]">
            <p className="mb-1">GUESTS</p>
            <p className="text-[#1E1E76]">{adults + children}</p>
          </div>
        </div>
      </div>

      {/* Room Type */}
      <div className="pt-4 ">
        <h3 className="font-semibold mb-2 text-[24px] text-[#080236]">
          {room.room_name} - {selectedRate.board_name}
        </h3>
        <div className="flex flex-wrap gap-3 text-xs text-gray-600">
          {amenities.map((amenity: string, index: number) => (
            <div key={index} className="flex items-center gap-1 bg-[#E6E3FF] rounded-full px-2 py-1">
              {getAmenityIcon(amenity)}
              <span className="text-[12px]">{amenity}</span>
            </div>
          ))}
          
          {/* Add room capacity info */}
          <div className="flex items-center gap-1 bg-[#E6E3FF] rounded-full px-2 py-1">
            <HandHeart className="w-4 h-4" />
            <span className="text-[12px]">Sleeps {room.room_size?.max_pax || 2}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelCard;