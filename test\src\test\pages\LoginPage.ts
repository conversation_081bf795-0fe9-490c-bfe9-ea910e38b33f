import { BasePage } from './BasePage';
import { fixture } from '../fixtures/Fixture';
import { ScreenshotHelper } from '../utils/ScreenshotHelper';

export class LoginPage extends BasePage {
    // Modern Playwright approach doesn't need to store selectors in elements object
    // since we use getBy methods directly in the page methods
    private static elements = {
        // Messages
        loginFailureMessage: "[data-test = 'error']"
    }

    public static async typeUsername(username: string) {
        try {
            console.log('Typing username using exact HTML attributes');
            
            // Use multiple precise locators based on the exact HTML attributes
            const usernameInput = fixture.page
                // First try by exact placeholder text
                .getByPlaceholder('Enter your email ID / Phone Number')
                // If that fails, try direct attribute locator
                .or(fixture.page.locator('input[name="username"][type="text"]'));
                
            // Take screenshot before interaction
            await ScreenshotHelper.takeScreenshot('before-username-field');
            
            // Wait for the field to be visible and ready
            await usernameInput.waitFor({ state: 'visible', timeout: 10000 });
            
            // Clear any existing value, focus the field, then fill it
            await usernameInput.click();
            await usernameInput.clear();
            await usernameInput.fill(username);
            
            // Press Tab to ensure field loses focus and value is committed
            await fixture.page.keyboard.press('Tab');
            
            console.log('Successfully typed username');
            await ScreenshotHelper.takeScreenshot('after-username-input');
            
        } catch (error) {
            console.error('Error typing username:', error);
            await ScreenshotHelper.takeScreenshot('username-input-error', true);
            
            // Emergency fallback approach if all else fails
            try {
                console.log('Trying emergency fallback for username input');
                await fixture.page.evaluate((username) => {
                    const inputs = document.querySelectorAll('input[type="text"]');
                    // Convert NodeList to Array before using for...of
                    Array.from(inputs).forEach(input => {
                        if ((input as HTMLInputElement).placeholder?.includes('email') || 
                            (input as HTMLInputElement).name === 'username') {
                            (input as HTMLInputElement).value = username;
                        }
                    });
                }, username);
                console.log('Emergency username input complete');
            } catch (fallbackError) {
                console.error('All username input methods failed');
                throw error; // throw the original error
            }
        }
    }

    public static async typePassword(password: string) {
        try {
            console.log('Typing password using exact HTML attributes');
            
            // Use multiple precise locators based on the exact HTML attributes
            const passwordInput = fixture.page
                // First try by exact placeholder text
                .getByPlaceholder('Enter your password')
                // Last resort, try direct attribute locator
                .or(fixture.page.locator('input[name="password"][type="password"]'));
                
            // Take screenshot before interaction
            await ScreenshotHelper.takeScreenshot('before-password-field');
            
            // Wait for the field to be visible and ready
            await passwordInput.waitFor({ state: 'visible', timeout: 10000 });
            
            // Clear any existing value, focus the field, then fill it
            await passwordInput.click();
            await passwordInput.clear();
            await passwordInput.fill(password);
            
            // Press Tab to ensure field loses focus and value is committed
            await fixture.page.keyboard.press('Tab');
            
            console.log('Successfully typed password');
            await ScreenshotHelper.takeScreenshot('after-password-input');
            
        } catch (error) {
            console.error('Error typing password:', error);
            await ScreenshotHelper.takeScreenshot('password-input-error', true);
            
            // Emergency fallback approach if all else fails
            try {
                console.log('Trying emergency fallback for password input');
                await fixture.page.evaluate((password) => {
                    const inputs = document.querySelectorAll('input[type="password"]');
                    // Convert NodeList to Array before using forEach
                    Array.from(inputs).forEach(input => {
                        if ((input as HTMLInputElement).placeholder?.includes('password') || 
                            (input as HTMLInputElement).name === 'password') {
                            (input as HTMLInputElement).value = password;
                        }
                    });
                }, password);
                console.log('Emergency password input complete');
            } catch (fallbackError) {
                console.error('All password input methods failed');
                throw error; // throw the original error
            }
        }
    }

    public static async clickLoginButton() {
        try {
            console.log('Attempting to click login button using exact structure from HTML...');
            // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-click-login-button');
            
            // Force wait to ensure form is ready
            await fixture.page.waitForTimeout(1000);
            await fixture.page.locator('button[type="submit"]').click({ force: true });
            console.log('Successfully clicked login button');
           
         } 
         catch (error) {
            console.error('Failed to click login button:', error);
        //     await ScreenshotHelper.takeScreenshot('login-button-click-error', true);
            
            // Try multiple emergency fallback approaches
            console.log('Attempting emergency fallbacks for Sign In button...');
            
            try {
                // First fallback: Direct submit button selector
                console.log('Fallback 1: Direct submit button selector');
                await fixture.page.locator('button[type="submit"]').click({ force: true });
                console.log('Fallback 1 succeeded');
                return;
            } catch (e) {
                console.log('Fallback 1 failed, trying next approach');
            }
            
            try {
                // Second fallback: JavaScript form submit
                console.log('Fallback 2: JavaScript form submit');
                await fixture.page.evaluate(() => {
                    const loginForm = document.querySelector('form:has(input[name="username"], input[name="password"])');
                    if (loginForm) (loginForm as HTMLFormElement).submit();
                });
                console.log('Fallback 2 succeeded');
                return;
            } catch (e) {
                console.log('Fallback 2 failed, trying next approach');
            }
            
            try {
                // Third fallback: Dispatch Enter key on password field
                console.log('Fallback 3: Enter key on password field');
                await fixture.page.locator('input[name="password"]').press('Enter');
                console.log('Fallback 3 succeeded');
                return;
            } catch (e) {
                console.log('All fallbacks failed');
                throw error; // Throw the original error
            }
        }
    }

    public static async getLoginFailureMessage() {
        return this.getText(this.elements.loginFailureMessage);
    }

    public static async isAccountIconVisible() {
        console.log('Checking if account icon is visible...');
        try {
            // Take a screenshot to help with debugging
            await ScreenshotHelper.takeScreenshot('checking-account-icon');
            
            // Use multiple strategies to find the account icon based on the exact HTML you provided
            // '<span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full cursor-pointer" type="button" id="radix-:r12:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">CD</span></span>'
            
            // Give the UI more time to load
            await fixture.page.waitForTimeout(5000);
            
            // 1. Try to find by the exact element structure (most specific)
            const accountIconSelector = fixture.page
                .locator('span.rounded-full span.flex.items-center.justify-center')
                .or(fixture.page.locator('span[aria-haspopup="menu"][data-state="closed"] span'));
            
            console.log('Looking for account icon by exact structure...');
            const isExactMatch = await accountIconSelector.isVisible({ timeout: 3000 })
                .catch(() => false);
                
            if (isExactMatch) {
                console.log('Found account icon by exact structure');
                return true;
            }
            
            // 2. Try to find the initials "CD" inside a rounded container
            console.log('Looking for user initials "CD" in rounded container...');
            const userInitialsSelector = fixture.page
                .getByText(/^[A-Z]{2}$/) // Looks for exactly two uppercase letters
                .filter({ has: fixture.page.locator('.rounded-full') });
                
            const hasInitials = await userInitialsSelector.isVisible({ timeout: 3000 })
                .catch(() => false);
                
            if (hasInitials) {
                console.log('Found user initials in rounded container');
                return true;
            }
            
            // 3. Try to find any rounded-full element that might be the account icon
            console.log('Looking for any rounded-full element...');
            const roundedElement = fixture.page.locator('.rounded-full');
            const hasRoundedElement = await roundedElement.isVisible({ timeout: 3000 })
                .catch(() => false);
                
            if (hasRoundedElement) {
                console.log('Found rounded-full element');
                return true;
            }
            
            // 4. Look for typical post-login elements as fallback
            console.log('Looking for typical post-login elements...');
            const postLoginElement = fixture.page
                .getByRole('button', { name: 'Chat with Shasa' })
                .or(fixture.page.getByText(/profile|logout|account/i).filter({ 
                    has: fixture.page.locator('button, a')
                }));
                
            const hasPostLoginElement = await postLoginElement.isVisible({ timeout: 3000 })
                .catch(() => false);
                
            if (hasPostLoginElement) {
                console.log('Found post-login UI element');
                return true;
            }

            console.log('No login indicators found with any locator');
            await ScreenshotHelper.takeScreenshot('account-icon-not-found', true);
            return false;
        } catch (error) {
            console.error('Error while checking account icon visibility:', error);
            await ScreenshotHelper.takeScreenshot('account-icon-error', true);
            return false;
        }
    }

    public static async verifyLoggedInState() {
        console.log('Verifying logged in state...');

        // Take a screenshot for debugging
        await ScreenshotHelper.takeScreenshot('before-login-verification');

        // Check if account icon is visible
        const isIconVisible = await this.isAccountIconVisible();
        if (!isIconVisible) {
            throw new Error('Account icon is not visible. User might not be logged in.');
        }

        console.log('Login verification successful - user is logged in');
        return true;
    }
}