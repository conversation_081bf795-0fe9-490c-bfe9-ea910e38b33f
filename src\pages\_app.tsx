import type { AppProps } from "next/app";
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { useEffect } from 'react';
import { ReCaptchaProvider } from "@/components/GoogleReCaptchaProvider/recaptcha-provider";
import "../styles/globals.css";
import { AuthProvider } from "@/components/AuthProvider/auth-Provider";
import FlightProvider from "@/context/FlightContext";
import { TravelerDisplayProvider } from "@/context/TravelerDisplayContext";
import { PassengerDetailsProvider } from "@/context/PassengerDetailsContext";
import { PaymentDetailsProvider } from "@/context/PaymentDetailsContext";
import ErrorBoundary from "@/components/errorBoundary/ErrorBoundary";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "@/components/ui/toaster";
import posthog from "posthog-js";
import tracker from "@/utils/posthogTracker";
import { store, persistor } from '@/store/store';
import TrackPageTime from "@/components/Posthog/TrackPageTime";


const initializePosthog = () => {
  if (
    typeof window !== "undefined" &&
    process.env.NEXT_PUBLIC_ENABLE_POSTHOG === "true" &&
    process.env.NEXT_PUBLIC_POSTHOG_KEY &&
    process.env.NEXT_PUBLIC_POSTHOG_HOST
  ) {
    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
      person_profiles: "identified_only",
    });
    console.log("PostHog initialized");
  }
};

initializePosthog();


function MyApp({ Component, pageProps: { session, ...pageProps } }: AppProps) {
  console.log('PostHog Enabled:', process.env.NEXT_PUBLIC_ENABLE_POSTHOG === 'true');
  useEffect(() => {
    tracker.trackEvent("browser_info", {
      message: "Browser identified for analytics",
    });

    tracker.trackEvent("Page Loaded");
  }, []);


  return (
    <SessionProvider session={session}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <ReCaptchaProvider>
            <AuthProvider>
              <FlightProvider>
                <TravelerDisplayProvider>
                  <PassengerDetailsProvider>
                    <PaymentDetailsProvider>
                      <ErrorBoundary>
                        <TrackPageTime />
                        <Component {...pageProps} />
                        <Toaster />
                      </ErrorBoundary>
                    </PaymentDetailsProvider>
                  </PassengerDetailsProvider>
                </TravelerDisplayProvider>
              </FlightProvider>
            </AuthProvider>
          </ReCaptchaProvider>
        </PersistGate>
      </Provider>
    </SessionProvider>
  );
}

export default MyApp;
