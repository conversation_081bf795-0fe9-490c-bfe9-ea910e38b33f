import {
  FareOption,
  LuggageOptionsResponse,
  PassengerPriceWrapperVariant,
} from "@/constants/models";
import { generateUUID } from "./uuid";
import { agentPostMethod } from "@/utils/api";
import { toast } from "@/components/ui/use-toast";

export const processFlightSelection = async ({
  routing_id,
  outward_id,
  return_id,
  updateFareOptions,
  updatePassengerList,
  updateLuggageOptions,
  updateSupplierInfo,
  updateServiceFee,
  router,
  token,
}: {
  routing_id: string | undefined;
  outward_id: string | undefined;
  return_id: string;
  updateFareOptions: (fares: FareOption[]) => void;
  updatePassengerList: (passengers: PassengerPriceWrapperVariant[]) => void;
  updateLuggageOptions: (luggage: LuggageOptionsResponse) => void;
  updateSupplierInfo: any;
  updateServiceFee: any;
  router: any;
  token: any;
}) => {
  const payload = {
    routing_id,
    outward_id,
    return_id,
    request_uuid: generateUUID(),
  };

  try {
    const response = await agent<PERSON>ostMethod(
      "flight/flight-process",
      payload,
      token
    );
    if (response?.detail?.status === "success") {
      const data = response.detail.data;
      console.log("hello", data.service_fee_percentage);
      updateFareOptions(data.fare_options);
      updatePassengerList(data.passenger_price_segregation);
      updateLuggageOptions(data.luggage_options);
      updateServiceFee(data.service_fee_percentage);
      updateSupplierInfo(data.supplier_info);

      return response.detail.data;
    } else {
      return response.detail;
    }
  } catch (error) {
    console.log("Flight processing error:", error);
    throw new Error("Flight processing failed");
  }
};
