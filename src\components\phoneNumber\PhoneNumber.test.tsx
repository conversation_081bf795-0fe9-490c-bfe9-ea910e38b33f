import { render,  waitFor, fireEvent } from "@testing-library/react";
import PhoneNumberInput from "./index";

// Mock react-phone-input-2 for controlled input simulation
jest.mock('react-phone-input-2', () => {
  return jest.fn(({ value, onChange, inputProps, inputStyle }) => (
    <input
      data-testid="phone-input"
      value={value}
      onChange={e => onChange(e.target.value, { dialCode: "44" }, null, e.target.value)}
      {...inputProps}
      style={inputStyle}
    />
  ));
});

describe("PhoneNumberInput", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders with default props", () => {
    const handleCode = jest.fn();
    const { getByTestId } = render(<PhoneNumberInput handleCode={handleCode} />);
    expect(getByTestId("phone-input")).toBeInTheDocument();
    expect(getByTestId("phone-input")).toHaveValue("");
  });

  it("calls handleCode with correct values on input change", async () => {
    const handleCode = jest.fn();
    const { getByTestId } = render(<PhoneNumberInput handleCode={handleCode} />);
    const input = getByTestId("phone-input");
    fireEvent.change(input, { target: { value: "441234567890" } });
    await waitFor(() => {
      expect(handleCode).toHaveBeenCalledWith("441234567890", "44", "1234567890");
    });
  });

  it("sets initial values from phoneNumberDetails prop", () => {
    const handleCode = jest.fn();
    const phoneNumberDetails = {
      phone: "441234567890",
      phone_int_code: "44",
      contact_details_phone_number: "1234567890"
    };
    const { getByTestId } = render(
      <PhoneNumberInput handleCode={handleCode} phoneNumberDetails={phoneNumberDetails} />
    );
    expect(getByTestId("phone-input")).toHaveValue("441234567890");
  });

  it("calls handleCode on mount if phoneNumberDetails is provided", () => {
    const handleCode = jest.fn();
    const phoneNumberDetails = {
      phone: "441234567890",
      phone_int_code: "44",
      contact_details_phone_number: "1234567890"
    };
    render(
      <PhoneNumberInput handleCode={handleCode} phoneNumberDetails={phoneNumberDetails} />
    );
    expect(handleCode).toHaveBeenCalledWith("441234567890", "44", "1234567890");
  });

  it("updates local state on input change", async () => {
    const handleCode = jest.fn();
    const { getByTestId } = render(<PhoneNumberInput handleCode={handleCode} />);
    const input = getByTestId("phone-input");
    fireEvent.change(input, { target: { value: "44111222333" } });
    await waitFor(() => {
      expect(input).toHaveValue("44111222333");
      expect(handleCode).toHaveBeenCalledWith("44111222333", "44", "111222333");
    });
  });
});
