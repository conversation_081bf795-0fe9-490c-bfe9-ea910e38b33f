"use client"

import { useEffect, useState } from "react"
import { useSelector, useDispatch } from "react-redux"
import { ChevronLeft, ChevronRight } from "lucide-react"
import * as Tabs from "@radix-ui/react-tabs"
import { formatFlightPrice } from "@/lib/utils/formatPrice"
import { formatAirportDisplayShort } from "@/lib/utils/formatAirport"
import type { BaggageProps } from "@/constants/models"
import type { AppState } from "@/store/store"
import { updateTripSummary } from "@/store/slices/tripSummary"

const Baggage = ({ onClose }: BaggageProps) => {
  const TABS_PER_PAGE = 2
  const tripSummaryDetails = useSelector((state: AppState) => state.tripSummary)
  const [activePassengerBaggage, setActivePassengerBaggage] = useState<any>({
    _outward: {},
    _return: {},
  })
  const [activeLuggageSlide, setActiveLuggageSlide] = useState<any>({
    activePassenger: 1,
    type: "_outward",
    selectData: "selectedOutboundFlight",
  })
  const [tabIndex, setTabIndex] = useState(0)
  const [activeTab, setActiveTab] = useState("0")

  const dispatch = useDispatch()

  const travelers = tripSummaryDetails?.outboundTravelers || []
  const visibleTravelers = travelers.slice(tabIndex, tabIndex + TABS_PER_PAGE)

  const canGoPrev = tabIndex > 0
  const canGoNext = tabIndex + TABS_PER_PAGE < travelers.length
  const currentTraveler = travelers[activeLuggageSlide.activePassenger - 1]
  const isInfant = currentTraveler?.travelerType === "infant"

  const styles = {
    button: {
      background: "#1E1E76",
      color: "white",
      borderRadius: "8px",
    },
  }

  const nextSlide = () => {
    setActiveLuggageSlide({
      activePassenger: 1,
      type: "_return",
      selectData: "selectedInboundFlight",
    })
    handleNext()
  }
  const prevSlide = () => {
    setActiveLuggageSlide({
      activePassenger: 1,
      type: "_outward",
      selectData: "selectedOutboundFlight",
    })
    handlePrev()
  }

  const handleInOutboundCount = (optionValue: string, type: string) => {
    const currentSelection =
      activePassengerBaggage[type as "_outward" | "_return"]?.[activeLuggageSlide.activePassenger]
    const isCurrentlySelected = currentSelection?.option === optionValue

    let result: { [_key in "_outward" | "_return"]?: any }

    if (isCurrentlySelected) {
      // Uncheck - remove the selection
      const updatedTypeData = { ...activePassengerBaggage[type as "_outward" | "_return"] }
      delete updatedTypeData[activeLuggageSlide.activePassenger]

      result = {
        [type as "_outward" | "_return"]: updatedTypeData,
      }
    } else {
      // Check - add the selection
      const option = tripSummaryDetails?.luggageOptions?.[type]?.find((opt: any) => opt.option_value === optionValue)

      const price = option?.price ? Number.parseFloat(option.price) : 0

      result = {
        [type as "_outward" | "_return"]: {
          ...activePassengerBaggage[type as "_outward" | "_return"],
          [activeLuggageSlide.activePassenger]: {
            option: optionValue,
            price: price,
            currency: option?.currency || "USD",
          },
        },
      }
    }

    setActivePassengerBaggage({ ...activePassengerBaggage, ...result })
  }

  const handleInOutboundLuggageOptions = () => {
    if (activePassengerBaggage && Object.keys(activePassengerBaggage).length > 0) {
      let finalPrice = 0
      Object.keys(activePassengerBaggage).forEach((key) => {
        const passengerBaggage = activePassengerBaggage[key]
        if (passengerBaggage && typeof passengerBaggage === "object") {
          Object.keys(passengerBaggage).forEach((passengerKey) => {
            const baggage = passengerBaggage[passengerKey]
            if (baggage && baggage.price) {
              // Ensure consistent price parsing
              const price = typeof baggage.price === "string" ? Number.parseFloat(baggage.price) : baggage.price
              finalPrice += price
            }
          })
        }
      })

      //luggage fee added here
      dispatch(
        updateTripSummary({
          selectedLuggageInfo: {
            ...tripSummaryDetails.selectedLuggageInfo,
            ...activePassengerBaggage,
            totalPrice: finalPrice,
          },
        }),
      )
    }

    onClose && onClose()
  }

  const handlePassengerClick = (index: number) => {
    setActiveLuggageSlide({ ...activeLuggageSlide, activePassenger: index })
  }

  useEffect(() => {
    setActivePassengerBaggage(tripSummaryDetails?.selectedLuggageInfo)
  }, [])

  const handleNext = () => {
    if (canGoNext) {
      const activeIndex = tabIndex + TABS_PER_PAGE
      setTabIndex(activeIndex)
      setActiveTab(String(activeIndex))
      setActiveLuggageSlide({
        ...activeLuggageSlide,
        activePassenger: activeIndex + 1,
      })
    }
  }

  const handlePrev = () => {
    if (canGoPrev) {
      const activeIndex = tabIndex - TABS_PER_PAGE
      setTabIndex(activeIndex)
      setActiveTab(String(activeIndex))
      setActiveLuggageSlide({
        ...activeLuggageSlide,
        activePassenger: activeIndex + 1,
      })
    }
  }

  const totalPrice = (() => {
    const baggage = activePassengerBaggage[activeLuggageSlide.type]?.[activeLuggageSlide.activePassenger]
    if (!baggage?.price) return 0

    return typeof baggage.price === "string" ? Number.parseFloat(baggage.price) : baggage.price
  })()

  return (
    <div className="h-full flex flex-col bg-brand-white">
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header Section - Fixed */}
        <div className="px-6 pt-8 flex-shrink-0">
          <h2 className="text-2xl font-bold text-center mb-3 text-[#080236]">
            {activeLuggageSlide.type === "_outward" ? "Outbound" : "Inbound"} Baggage
          </h2>
          {/* Route display */}
          <div className="flex items-center justify-center mb-0 gap-4">
            {tripSummaryDetails?.luggageOptions?._return &&
              tripSummaryDetails?.luggageOptions?._return?.length > 0 &&
              activeLuggageSlide.type === "_return" && (
                <button className="focus:outline-none" onClick={prevSlide}>
                  <ChevronLeft />
                </button>
              )}
            <div className="flex justify-center items-center text-center text-[#1E1E76] text-xl font-bold">
              {formatAirportDisplayShort(
                tripSummaryDetails?.[activeLuggageSlide.selectData]?.origin ?? "",
                tripSummaryDetails?.sharedFlightResults?.airport_data,
              )}
              <div className="mx-3">
                <img
                  alt=""
                  className="w-5 h-5 d-inline-block"
                  src={
                    "https://storage.googleapis.com/nxvoytrips-img/TripSummary/flight-return-arrow.svg" ||
                    "/placeholder.svg"
                  }
                />
              </div>
              {formatAirportDisplayShort(
                tripSummaryDetails?.[activeLuggageSlide.selectData]?.destination ?? "",
                tripSummaryDetails?.sharedFlightResults?.airport_data,
              )}
            </div>
            {tripSummaryDetails?.luggageOptions?._return &&
              tripSummaryDetails?.luggageOptions?._return?.length > 0 &&
              activeLuggageSlide.type === "_outward" && (
                <button className="focus:outline-none" onClick={nextSlide}>
                  <ChevronRight />
                </button>
              )}
          </div>
        </div>

        {/* Traveler tabs - only show if there are multiple travelers */}
        {travelers.length > 1 && (
          <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
            <div className="relative mb-6">
              <button
                onClick={handlePrev}
                disabled={!canGoPrev}
                className="absolute left-0 top-1/2 -translate-y-1/2 focus:outline-none disabled:opacity-0"
              >
                <ChevronLeft />
              </button>
              <div className="flex justify-center">
                <Tabs.List className="flex border-b-2 border-[#B4BBE8] relative w-3/4 justify-center">
                  {visibleTravelers.map((traveler: any, i: number) => {
                    const originalIndex = tabIndex + i
                    return (
                      <Tabs.Trigger
                        key={originalIndex}
                        value={String(originalIndex)}
                        className="px-6 py-2 text-lg text-[#B4BBE8] data-[state=active]:text-[#4B4BC3] data-[state=active]:border-b-2 data-[state=active]:border-[#4B4BC3]"
                        onClick={() => handlePassengerClick(originalIndex + 1)}
                      >
                        Traveler {originalIndex + 1}
                      </Tabs.Trigger>
                    )
                  })}
                </Tabs.List>
              </div>

              <button
                onClick={handleNext}
                disabled={!canGoNext}
                className="absolute right-0 top-1/2 -translate-y-1/2 focus:outline-none disabled:opacity-0"
              >
                <ChevronRight />
              </button>
            </div>
          </Tabs.Root>
        )}

        {/* Baggage section header */}

        <div className="mb-4">
          <h3 className="text-xl font-bold text-[#080236] px-6">Add Baggage</h3>
          <p className="text-sm text-[#080236] px-6 pb-2">
            {isInfant
              ? "Infants are not eligible for additional baggage"
              : "Included Check-in baggage per person - 15 KGS"}
          </p>
          <div className="h-px bg-[#B4BBE8]"></div>
        </div>
        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto px-6"></div>

        <div className="overflow-y-auto px-6">
          {tripSummaryDetails?.luggageOptions?.[activeLuggageSlide.type]?.map((option: any) => (
            <div key={option.option_value} className="flex flex-col">
              <div key={option.option_value} className=" border-[#B4BBE8] last:border-b-0">
                <div className="py-2">
                  <div className="flex justify-between items-center">
                    <div className="flex flex-col">
                      <div className="text-lg text-[#080236] font-bold xs:text-sm whitespace-nowrap">
                        Additional {option.allowed_weights}
                      </div>
                      <div className=" text-lg font-bold text-brand-black">
                        {formatFlightPrice({
                          amount: Number.parseFloat(option.price) || 0,
                          currency: option.currency,
                        })}
                      </div>
                    </div>
                    {/* {option.allowed_weights && (
                      <div className="text-xs text-gray-500">
                        {option.allowed_weights}
                      </div>
                    )} */}
                    <div className="flex flex-col gap-1 w-full items-end mr-4">
                      <input
                        type="checkbox"
                        className={`w-[18px] h-[18px] accent-[#4B4BC3] ${isInfant ? "cursor-not-allowed opacity-50" : "cursor-pointer"}`}
                        onChange={() => handleInOutboundCount(option.option_value, activeLuggageSlide.type)}
                        checked={
                          activePassengerBaggage[activeLuggageSlide.type]?.[activeLuggageSlide.activePassenger]
                            ?.option === option.option_value
                        }
                        disabled={isInfant}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex w-full h-0.5 bg-[#B4BBE8]"></div>
            </div>
          ))}
        </div>
        <div className="px-6 py-4 border-t border-brand-grey bg-brand-white flex-shrink-0">
          <div className="flex justify-between items-center mb-4">
            <div className="text-base text-brand-black font-medium">
              {activePassengerBaggage[activeLuggageSlide.type]?.[activeLuggageSlide.activePassenger]?.option ? 1 : 0} of
              1 Baggage Selected
            </div>
            <div className="flex flex-col  items-end">
              <div className="text-sm text-gray-400">Added to fare</div>
              <div className="text-brand-black font-bold text-xl">
                {formatFlightPrice({
                  amount: totalPrice,
                  currency: tripSummaryDetails?.luggageOptions?.[activeLuggageSlide.type][0]?.currency,
                })}
              </div>
            </div>
          </div>
          <div className="flex justify-center">
            <button
              style={styles.button}
              className="bg-brand text-white px-16 py-3 rounded-lg text-lg font-medium w-full"
              onClick={handleInOutboundLuggageOptions}
            >
              Update
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Baggage
