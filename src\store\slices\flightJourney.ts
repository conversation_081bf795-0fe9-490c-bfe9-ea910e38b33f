import { Flight } from '@/constants/models';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FlightJourney {
    pickedOutbound: { flight: Flight, class: string } | null;
    pickedReturn: { flight: Flight, class: string } | null;
}

const initialState: FlightJourney = {
    pickedOutbound: null,
    pickedReturn: null
};

const flightJourneySlice = createSlice({
    name: 'flightJourney',
    initialState,
    reducers: {
        // Set outbound flight with class
        setPickedOutbound: (state, action: PayloadAction<{ flight: Flight, class: string }>) => {
            state.pickedOutbound = action.payload;
        },

        // Set return flight with class
        setPickedReturn: (state, action: PayloadAction<{ flight: Flight, class: string }>) => {
            state.pickedReturn = action.payload;
        },

        // Set both flights at once
        setFlightJourney: (state, action: PayloadAction<{
            pickedOutbound?: { flight: Flight, class: string },
            pickedReturn?: { flight: Flight, class: string }
        }>) => {
            if (action.payload.pickedOutbound) {
                state.pickedOutbound = action.payload.pickedOutbound;
            }
            if (action.payload.pickedReturn) {
                state.pickedReturn = action.payload.pickedReturn;
            }
        },

        // Clear outbound selection
        clearPickedOutbound: (state) => {
            state.pickedOutbound = null;
        },

        // Clear return selection
        clearPickedReturn: (state) => {
            state.pickedReturn = null;
        },

        // Reset to initial state
        resetFlightJourney: () => {
            return initialState;
        }
    },
});

// Export actions
export const {
    setPickedOutbound,
    setPickedReturn,
    setFlightJourney,
    clearPickedOutbound,
    clearPickedReturn,
    resetFlightJourney
} = flightJourneySlice.actions;

// Export reducer
export default flightJourneySlice.reducer;

// Selectors
export const selectFlightJourney = (state: { flightJourney: FlightJourney }) => state.flightJourney;
export const selectPickedOutbound = (state: { flightJourney: FlightJourney }) => state.flightJourney.pickedOutbound;
export const selectPickedReturn = (state: { flightJourney: FlightJourney }) => state.flightJourney.pickedReturn;