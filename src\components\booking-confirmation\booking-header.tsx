import { Check<PERSON>ir<PERSON>, Clock } from "lucide-react"
import type { BookingStatus } from "@/types/booking"

interface BookingHeaderProps {
    bookingStatus: BookingStatus
    email?: string
}

export const BookingHeader = ({ bookingStatus, email }: BookingHeaderProps) => {
    const statusConfig = getStatusConfig(bookingStatus, email)

    return (
        <div className="text-center mb-8">
            <div className="flex justify-center mb-4">{statusConfig.icon}</div>
            <h1 className="text-2xl font-bold text-brand-black mb-2">{statusConfig.title}</h1>
            <p className="text-brand-grey mb-4">{statusConfig.description}</p>
            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${statusConfig.badgeClass}`}>
                {statusConfig.badgeText}
            </span>
        </div>
    )
}

function getStatusConfig(status: BookingStatus, email?: string) {
    switch (status) {
        case "CONFIRMED":
            return {
                icon: <CheckCircle className="w-12 h-12 text-green-500" />,
                title: "Your Ticket was Booked Successfully!",
                description: `Booking details have been sent to: ${email || "your email"}`,
                badgeClass: "bg-green-100 text-green-800",
                badgeText: "Confirmed",
            }
        case "UNCONFIRMED":
            return {
                icon: <Clock className="w-12 h-12 text-yellow-500" />,
                title: "Your Booking is Being Processed",
                description: "We're confirming your booking with the airline. You'll receive an update within 24 hours.",
                badgeClass: "bg-yellow-100 text-yellow-800",
                badgeText: "Pending Confirmation",
            }
        case "UNCONFIRMED_BY_SUPPLIER":
            return {
                icon: <Clock className="w-12 h-12 text-yellow-500" />,
                title: "Booking Confirmation Pending",
                description: "There's an issue with confirming your booking. Our team is working to resolve this.",
                badgeClass: "bg-yellow-100 text-yellow-800",
                badgeText: "Unconfirmed by Supplier",
            }
        default:
            return {
                icon: <CheckCircle className="w-12 h-12 text-green-500" />,
                title: "Your Ticket was Booked Successfully!",
                description: `Booking details have been sent to: ${email || "your email"}`,
                badgeClass: "bg-green-100 text-green-800",
                badgeText: "Confirmed",
            }
    }
}
