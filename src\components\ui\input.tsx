import * as React from "react";
import { cn } from "@/lib/utils";

type InputProps = React.ComponentProps<"input"> & {
  variant?: "default" | "gradient";
};

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant = "default", ...props }, ref) => {
    const isDate = type === "date";

    if (variant === "gradient") {
      return (
        <div className="relative font-proxima-nova w-full p-px rounded-sm h-auto bg-brand-white border-[1px] border-neutral shadow-sm">
          <input
            type={type}
            ref={ref}
            className={cn(
              "placeholder-neutral-dark w-full px-4 py-2 bg-brand-white rounded-sm focus:outline-none",
              isDate &&
              "text-neutral-dark [&::-webkit-calendar-picker-indicator]:invert-[0.5] [&::-webkit-calendar-picker-indicator]:sepia-[1] [&::-webkit-calendar-picker-indicator]:saturate-[1000] [&::-webkit-calendar-picker-indicator]:hue-rotate-[240deg] [&::-webkit-calendar-picker-indicator]:brightness-[0.7] [&::-webkit-calendar-picker-indicator]:contrast-[0.8]",
              isDate &&
              "appearance-none [&::-webkit-datetime-edit]:text-neutral-dark [&::-webkit-datetime-edit-fields-wrapper]:text-neutral-dark [&::-webkit-datetime-edit-text]:text-neutral-dark [&::-webkit-datetime-edit-month-field]:text-neutral-dark [&::-webkit-datetime-edit-day-field]:text-neutral-dark [&::-webkit-datetime-edit-year-field]:text-neutral-dark",
              className
            )}
            {...props}
          />
        </div>
      );
    }

    // Default input
    return (
      <input
        type={type}
        ref={ref}
        className={cn(
          "text-neutral-dark  placeholder:text-neutral-dark  flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        {...props}
      />
    );
  }
);

Input.displayName = "Input";

export { Input };
