/**
 * This file helps ensure TypeScript step definitions are correctly loaded in Cucumber
 * for both local and GitHub Actions environments.
 */
import path from 'path';
import glob from 'glob';
import * as fs from 'fs';

/**
 * Get a list of all TypeScript step definition files to be required by Cucumber
 */
export function getStepDefinitionPaths(): string[] {
    const stepsDir = path.resolve(process.cwd(), 'src/test/steps');
    const stepFiles = glob.sync('**/*.ts', { cwd: stepsDir });
    
    return stepFiles.map(file => path.join('src/test/steps', file));
}

/**
 * Returns the proper Cucumber command arguments for loading step definitions
 * in a way that works across different environments (local and CI).
 */
export function getCucumberStepsArgument(): string {
    // Using quotes around the glob pattern to ensure it works in both environments
    return '--require "src/test/steps/**/*.ts"';
}
