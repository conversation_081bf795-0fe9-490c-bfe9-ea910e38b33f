import { APIRequestContext, APIResponse, request } from '@playwright/test';

export class ApiService {
    public static baseUrl: string = 'BASE_URL_NOT_SET';

    public static endpoint: string;
    public static method: string;
    public static queryParams: { [key: string]: any };
    public static pathParams: { [key: string]: any };
    public static headers: { [key: string]: any };
    public static requestBody: string;
    public static response: APIResponse | undefined;
    public static responseBody: string;

    public static reset() {
        this.endpoint = 'ENDPOINT_NOT_SET';
        this.method = 'METHOD_NOT_SET';
        this.queryParams = {};
        this.pathParams = {};
        this.headers = {};
        this.requestBody = '';

        this.response = undefined;
    }

    public static setBaseUrl(baseUrl: string) {
       this.baseUrl = baseUrl;
    }

    public static async makeRequest(method:string, endpoint: string) {
        this.method = method;
        this.endpoint = endpoint;
        this.printRequest();

        this.response = await 
        (await this.initialiseRequestContext())[method.toLowerCase()]
            (this.insertPathParams(endpoint), this.getRequestOptions());
    }

    private static getRequestOptions() {
        const options = {};

        if (this.queryParams) {
            options['params'] = this.queryParams;
        }

        if (this.headers) {
            options['headers'] = this.headers;
        }

        if (this.requestBody) {
            options['data'] = this.requestBody;
        }

        return options;
    }

    private static insertPathParams(endpoint: string): string {
        if (this.pathParams) {
            Object.keys(this.pathParams).forEach((key: string) => {
                endpoint = endpoint.replace(`{${key}}`, this.pathParams[key]);
            });
        }
        return endpoint;
    }

    private static async initialiseRequestContext() {
        return await request.newContext({ baseURL: this.baseUrl });
    }

    public static printRequest() {
        console.log();
        console.log('--------------REQUEST--------------');
        console.log(this.requestAsString());
        console.log('------------REQUEST-END------------');
        console.log();
    }

    public static requestAsString() {
        return (
            `Base URL: ${this.baseUrl}\n` +
            `Path: ${this.endpoint}\n` +
            `Method: ${this.method}\n` +
            `Headers: ${JSON.stringify(this.redactedHeaders(), null, 2)}\n` +
            `Path params: ${JSON.stringify(this.pathParams, null, 2)}\n` +
            `Query params: ${JSON.stringify(this.queryParams, null, 2)}\n` +
            `Request body: ${JSON.stringify(this.requestBody, null, 2)}\n`
        );
    }

    public static redactedHeaders() {
        let redacted: { [key: string]: any } = {};
        Object.keys(this.headers).forEach((key: string) => {
            if (
                key.toLowerCase().includes('key') ||
                key.toLowerCase().includes('secret') ||
                key.toLowerCase().includes('authorization')
            ) {
                redacted[key] = 'REDACTED';
            } else {
                redacted[key] = this.headers[key];
            }
        });
        return redacted;
    }
}

