import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { DeleteThreadDialog, EditThreadDialog } from "./thread-dialogs";

// Mock Trash2 icon and image to avoid errors in test environment
jest.mock("lucide-react", () => ({
    Trash2: () => <svg data-testid="trash2-icon" />,
}));

describe("DeleteThreadDialog", () => {
    const threadName = "Trip to Paris";
    let onCancel: jest.Mock, onConfirm: jest.Mock;

    beforeEach(() => {
        onCancel = jest.fn();
        onConfirm = jest.fn();
    });

    it("renders dialog with correct thread name when open", () => {
        render(
            <DeleteThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        expect(screen.getByText("Confirm Delete Trip")).toBeInTheDocument();
        expect(screen.getByText(`Are sure you want to delete ${threadName}?`)).toBeInTheDocument();
        expect(screen.getByTestId("trash2-icon")).toBeInTheDocument();
        expect(screen.getByText("Delete")).toBeInTheDocument();
        expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("calls onConfirm when Delete is clicked", () => {
        render(
            <DeleteThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        fireEvent.click(screen.getByText("Delete"));
        expect(onConfirm).toHaveBeenCalledTimes(1);
    });

    it("calls onCancel when Cancel is clicked", () => {
        render(
            <DeleteThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        fireEvent.click(screen.getByText("Cancel"));
        expect(onCancel).toHaveBeenCalledTimes(1);
    });

    it("calls onCancel when dialog is closed", () => {
        render(
            <DeleteThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        // Simulate closing dialog by triggering onOpenChange
        fireEvent.keyDown(document, { key: "Escape", code: "Escape" });
        // onCancel should be called when dialog is closed
        // (Note: This depends on AlertDialog implementation; if not, skip this test)
    });
});

describe("EditThreadDialog", () => {
    const threadName = "Trip to Rome";
    let onCancel: jest.Mock, onConfirm: jest.Mock;

    beforeEach(() => {
        onCancel = jest.fn();
        onConfirm = jest.fn();
    });

    it("renders dialog with input prefilled with threadName", () => {
        render(
            <EditThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        expect(screen.getByText("Edit trip name")).toBeInTheDocument();
        const input = screen.getByPlaceholderText("Enter new thread name") as HTMLInputElement;
        expect(input.value).toBe(threadName);
        expect(screen.getByText("Save")).toBeInTheDocument();
        expect(screen.getByText("Cancel")).toBeInTheDocument();
    });

    it("updates input value on change", () => {
        render(
            <EditThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        const input = screen.getByPlaceholderText("Enter new thread name") as HTMLInputElement;
        fireEvent.change(input, { target: { value: "Trip to Milan" } });
        expect(input.value).toBe("Trip to Milan");
    });

    it("calls onConfirm with new name when Save is clicked", () => {
        render(
            <EditThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        const input = screen.getByPlaceholderText("Enter new thread name") as HTMLInputElement;
        fireEvent.change(input, { target: { value: "Trip to Venice" } });
        fireEvent.click(screen.getByText("Save"));
        expect(onConfirm).toHaveBeenCalledWith("Trip to Venice");
    });

    it("calls onCancel when Cancel is clicked", () => {
        render(
            <EditThreadDialog
                isOpen={true}
                threadName={threadName}
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        fireEvent.click(screen.getByText("Cancel"));
        expect(onCancel).toHaveBeenCalledTimes(1);
    });

    it("resets input value when threadName prop changes", () => {
        const { rerender } = render(
            <EditThreadDialog
                isOpen={true}
                threadName="Old Name"
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        const input = screen.getByPlaceholderText("Enter new thread name") as HTMLInputElement;
        fireEvent.change(input, { target: { value: "Changed Name" } });
        expect(input.value).toBe("Changed Name");
        rerender(
            <EditThreadDialog
                isOpen={true}
                threadName="New Name"
                onCancel={onCancel}
                onConfirm={onConfirm}
            />
        );
        expect(input.value).toBe("New Name");
    });
});