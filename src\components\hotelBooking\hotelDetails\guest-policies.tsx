import { forwardRef } from "react";

interface Policy {
    name: string;
    icon: string;
    details: string[];
}

interface GuestPoliciesProps {
    policies: Policy[];
}

const GuestPolicies = forwardRef<HTMLDivElement, GuestPoliciesProps>(
    ({ policies }, ref) => {
        const getIcon = (iconName: string) => {
            switch (iconName) {
                case "clock-in":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/CheckIn.png"
                            alt="Check-In "
                        />
                    );

                case "clock-out":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Checkout.png"
                            alt="Check-Out "
                        />
                    );
                case "wifi":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Internet.png"
                            alt="WiFi"
                        />
                    );
                case "children":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Child.png"
                            alt="Children"
                        />
                    );
                case "beds":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Bed.png"
                            alt="Bed"
                        />
                    );
                case "parking":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Parking.png"
                            alt="Parking"
                        />
                    );
                case "pets":
                    return (
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/Pet.png"
                            alt="Pet"
                        />
                    );
                default:
                    return <img
                        src=""
                        alt="Not Found"
                    />;
            }
        };

        return (
            <div ref={ref} id="policies-section">
                <h2 className="text-xl font-bold mb-4 text-[#080236]">
                    Guest Policies
                </h2>

                <div className="rounded-sm overflow-hidden">
                    {policies.map((policy, index) => (
                        <div
                            key={index}
                            className={`${index % 2 === 0 ? "bg-[#E6E3FF]" : "bg-white"
                                } py-4 px-6 flex ${policy.name === "Extra Beds" ? "" : "items-center"} text-[#080236]`}
                        >
                            <div
                                className={`w-1/4 font-bold ${policy.name === "Check-in" ? "text-sm" : ""} ${policy.name === "Extra Beds" ? "flex items-center pt-1" : ""}`}
                            >
                                {policy.name}
                            </div>
                            <div
                                className={`w-1/12 flex justify-center ${policy.name === "Extra Beds" ? "items-center pt-1" : ""}`}
                            >
                                {getIcon(policy.icon)}
                            </div>
                            <div className="flex-1 text-[#080236]">
                                {policy.name === "Extra Beds" ? (
                                    <ul className="space-y-2">
                                        {policy.details.map((detail, detailIndex) => (
                                            <li key={detailIndex} className="flex gap-2">
                                                <span>•</span>
                                                <span>
                                                    {detail.startsWith("•")
                                                        ? detail.substring(1).trim()
                                                        : detail}
                                                </span>
                                            </li>
                                        ))}
                                    </ul>
                                ) : (
                                    <div
                                        className={
                                            policy.name === "Check-in" || policy.name === "Check-out"
                                                ? "font-bold"
                                                : ""
                                        }
                                    >
                                        {policy.details[0]}
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }
);

GuestPolicies.displayName = "GuestPolicies";

export default GuestPolicies;
