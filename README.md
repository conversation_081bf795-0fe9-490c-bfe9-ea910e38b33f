# NXVoy Travel Agent - Frontend (Next.js)

## Overview
NXVoy Travel Agent is a front-end application built using **Next.js**. It provides a user-friendly interface for managing travel bookings, itineraries, and customer interactions. 

This repository contains the Next.js application along with <PERSON>er and Nginx configurations for production deployment.

---

## Table of Contents
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Installation](#installation)
- [How to Build](#how-to-build)
- [How to Run](#how-to-run)
- [Environment Variables](#environment-variables)
- [Folder Structure](#folder-structure)
- [Docker Setup](#docker-setup)
- [Nginx Configuration](#nginx-configuration)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

---

## Features
-  **Server-Side Rendering (SSR)**
-  **Static Site Generation (SSG)**
-  **Responsive UI with Tailwind CSS**
-  **API Integration**
-  **Multi-Environment Configuration**
-  **Docker & Nginx Support**
-  **Optimized for Performance & SEO**

---

## Tech Stack
- **Frontend:** Next.js, React.js, Tailwind CSS
- **State Management:** Context API (or Redux if used)
- **API Calls:** Axios / Fetch
- **Deployment:** Docker, Nginx, AWS, Vercel

---

## Installation

### Prerequisites
Make sure you have the following installed:
- **Node.js** (v14 or later) – [Download](https://nodejs.org/)
- **npm** or **Yarn**
- **Git** (for cloning the repository)
- **Docker** (for containerized deployment)

### Clone the repository
```bash
git clone https://github.com/NxVoy/nxvoy-travel-agent-fe.git
cd nxvoy-travel-agent-fe
```
### Clone the repository
```bash
npm install
```

### Development Mode
```bash
npm run dev
```

### Build for Different Environments
```bash
npm run build:dev       # Build for development
npm run build:qa   # Build for staging
npm run build:preprod      # Build for pre production
npm run build:prod      # Build for production
```

### Running with Environment-Specific Configurations

```bash
npm run start:dev       # Build for development
npm run start:qa   # Build for staging
npm run start:preprod      # Build for pre production
npm run start:prod      # Build for production
```