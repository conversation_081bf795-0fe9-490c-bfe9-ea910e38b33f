import ReviewCard from "@/components/review-card/ReviewCard";
import { reviews } from "@/constants";
import { simpleFadeIn, textVariant } from "@/utils/motion";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useState } from "react";

const Testimonials = () => {
  const [current, setCurrent] = useState(0);
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  useEffect(() => {
    // Check screen size on the client side
    const handleResize = () => {
      if (typeof window !== "undefined") {
        setIsLargeScreen(window.innerWidth >= 1024); // Tailwind xl breakpoint
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrent((prev) => (prev + 1) % reviews.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="flex flex-col bg-brand-white gap-10 w-full h-full py-10">
      <motion.div
        initial="hidden"
        whileInView="show"
        variants={textVariant(0.5)}
        className="font-proxima-nova text-center lg:text-5xl md:text-4xl sm:text-3xl xs:text-3xl font-bold
      text-brand"
      >
        Testimonials
      </motion.div>
      <div className="flex font-proxima-nova">
        <div className="flex flex-row w-[85%] h-72 sm:h-80 xs:h-96 mx-auto bg-[#1E1E76] rounded-2xl group transition-all duration-500 ease-in-out hover:w-[100%] xs:mb-10">
          <div
            className={`${isLargeScreen ? "visible flex w-[90%] mx-auto" : "flex flex-row xs:flex xs:flex-col xs:items-center gap-4"} w-[90%] mx-auto group-hover:flex group-hover:w-[80%] group-hover:mx-auto group-hover:visible`}
          >
            <div className="flex flex-col font-proxima-nova justify-center gap-4 xl:w-2/4 md:w-1/3 sm:w-1/3 xs:w-full xs:text-center xs:mt-5">
              <motion.div
                initial={isLargeScreen ? "hidden" : "show"}
                whileInView={isLargeScreen ? "show" : "show"}
                variants={isLargeScreen ? simpleFadeIn(0.5, 2) : {}}
                className="2xl:text-5xl xl:text-5xl lg:text-4xl md:text-2xl sm:text-2xl xs:text-xl xl:w-2/3 md:w-[90%] font-bold leading-tight text-[#F2F3FA]"
              >
                I’m Loved by 10,000+ Trip Planners
              </motion.div>
            </div>
            <div className="flex items-end justify-center h-full w-full xl:w-2/4 md:w-2/3 xs:w-full">
              <div
                className="relative flex justify-end items-end w-full bg-[#F2F3FA] 
                h-[80%] px-[24px] py-[22px] mr-[30px] xs:mr-[0px] xs:h-[90%] rounded-t-[18px]"
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={current}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="w-full"
                  >
                    <ReviewCard {...reviews[current]} />
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <div className='flex items-center justify-around mx-7 bg-lucky-blue h-[188px]
        xs:rounded-[6px] sm:rounded-[12px] md:rounded-[18px] my-10 pt-[30px] gap-10'>
            <div className='relative w-2/5 items-start px-[30px] h-full'>
                <p className='text-white font-bold text-[34px] leading-normal'>
                    I’m Loved by 10,000+ Trip Planners
                </p>
            </div>
            <div className='relative flex-1 flex justify-start items-start bg-white 
                h-full px-[24px] py-[22px] mr-[30px] rounded-t-[18px] border-[0.64px] border-lucky-blue'>
                <AnimatePresence mode='wait'>
                    <motion.div
                        key={current}
                        initial={{ opacity: 0, y: 40 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -40 }}
                        transition={{ duration: 0.3 }}
                        className='w-full'
                    >
                        <ReviewCard {...reviews[current]} />
                    </motion.div>
                </AnimatePresence>
            </div>
      </div> */}
    </section>
  );
};

export default Testimonials;
