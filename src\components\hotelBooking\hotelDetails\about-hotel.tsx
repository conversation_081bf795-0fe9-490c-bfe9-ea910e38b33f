interface AboutHotelProps {
    aboutHotel: string
    aboutLuxuryCollection: string
}

export default function AboutHotel({ aboutHotel, aboutLuxuryCollection }: AboutHotelProps) {
    return (
        <div className="mt-8 text-[#1E1E76]">
            <h2 className="text-xl font-bold mb-4">About the Hotel</h2>
            <div className="space-y-4">
                <div>
                    <h3 className="font-bold text-lg mb-2">Hotel Features</h3>
                    <p className="text-sm">{aboutHotel}</p>
                </div>
                <div>
                    <h3 className="font-bold text-lg mb-2">About Luxury Collection</h3>
                    <p className="text-sm">{aboutLuxuryCollection}</p>
                </div>
            </div>
        </div>
    )
}
