import React, { useState } from "react";
import FormInput from "./FormInput";
import { Button } from "@/components/ui/button";
import Checkbox from "../ui/LoginCheckbox";
import { authPostMethod } from "@/utils/auth";
import { useAuth } from "../AuthProvider/auth-Provider";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { useDispatch } from 'react-redux';
import { loginSuccess, logout } from '@/store/slices/authSlice';
import { signIn } from "next-auth/react";
import tracker from "@/utils/posthogTracker";


interface LoginFormProps {
  onForgotPassword: () => void;
  onEmailChange?: (email: string) => void;
  onLoginSuccess?: () => void;
}

interface Credentials {
  username: string;
  password: string;
  rememberMe: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onForgotPassword,
  onEmailChange,
  onLoginSuccess,
}) => {
  const { setToken } = useAuth();
  const [credentials, setCredentials] = useState<Credentials>({
    username: "",
    password: "",
    rememberMe: false,
  });
  const [errors, setErrors] = useState<Partial<Credentials>>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [generalError, setGeneralError] = useState<string | null>(null);
  const dispatch = useDispatch();
  const [isHovered, setIsHovered] = useState(false);


  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;

    setCredentials((prev) => ({
      ...prev,
      [name]: newValue,
    }));

    if (errors[name as keyof Credentials]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }

    if (name === "username" && onEmailChange) {
      onEmailChange(value);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Credentials> = {};

    if (!credentials.username) {
      newErrors.username = "I need your email or phone to continue.";
    } else if (
      !/\S+@\S+\.\S+/.test(credentials.username) &&
      !/^\+?\d{10,15}$/.test(credentials.username)
    ) {
      newErrors.username = "That doesn't look right. Try again?";
    }

    if (!credentials.password) {
      newErrors.password = "Password's missing. Pop it in!";
    }
    // No additional password validation during login form submission
    // Just check if password is provided

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setGeneralError(null);
    const isPhone = /^\+?\d{10,15}$/.test(credentials.username);
    const payload = {
      password: credentials.password,
      rememberMe: credentials.rememberMe,
      ...(isPhone
        ? { phone: credentials.username }
        : { email: credentials.username }),
    };
    try {
      // For passwords shorter than required or with invalid format,
      // we want to show the standard error message instead of causing an application error
      if (credentials.password.length < 8) {
        const error = "Hmm… password didn't match. Try again?";
        setGeneralError(error);

        tracker.trackEvent("Login Failed", {
          reason: "Password too short",
          username: credentials.username,
        });

        return;
      }
      
      const response = await authPostMethod("auth/signin", payload);
      if (!response.data?.detail.data?.accessToken) {
        let errorMessage = "Hmm… password didn't match. Try again?";
        let reason = "Invalid credentials";

        // Handle specific error cases
        if (response.data?.detail.message?.includes("not found")) {
          errorMessage = "No account yet? Let's get you signed up.";
          reason = "User not found";
        } else if (response.data?.detail.message?.includes("Apple")) {
          errorMessage = "Apple sign-in didn't go through. Mind trying again?";
          reason = "Apple sign-in failed";
        } else if (response.data?.detail.message?.includes("Google")) {
          errorMessage = "Google sign-in hit a snag. Give it another go?";
          reason = "Google sign-in failed";
        }
        
        setGeneralError(errorMessage);

        tracker.trackEvent("Login Failed", {
          reason,
          username: credentials.username,
        });

        return;
      }

      const {
        accessToken,
        refreshToken,
        accessTokenExpireOn,
        refreshTokenExpireOn,
        tokenType,
        isNewUser,
        user,
      } = response.data.detail.data;

      await signIn("credentials", {
        redirect: false,
        id: user.id,
        email: user.email,
        ...user,
        accessToken,
        refreshToken,
        accessTokenExpireOn,
        refreshTokenExpireOn,
      });
      dispatch(loginSuccess({ accessToken, refreshToken, user }));

      tracker.trackEvent("Login Success", {
        userId: user.id,
        email: user.email,
        isNewUser,
      });

      onLoginSuccess && onLoginSuccess(response.data?.detail.data);
    } catch (error) {
      console.error("Sign in error:", error);
      // Default error message
      setGeneralError("Hmm… password didn't match. Try again?");

      tracker.trackEvent("Login Failed", {
        reason: "Unhandled error",
        username: credentials.username,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  };

  // Added hover state button styles via CSS instead of directly modifying the Button component
  // The Button component should handle the hover state internally

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <label
          className="block text-brand-black mb-1 text-sm md:text-lg"
          style={styles.label}
        >
          Email / Phone Number
        </label>
        <FormInput
          type="text"
          name="username"
          placeholder="Enter your email ID / Phone Number"
          value={credentials.username}
          onChange={handleChange}
        />
        {errors.username && (
          <p className="text-red-500 text-sm mt-1">{errors.username}</p>
        )}
      </div>

      <div className="mb-6">
        <div className="flex justify-between items-center mb-1">
          <label
            className="block text-brand-black text-sm md:text-lg"
            style={styles.label}
          >
            Password
          </label>
          <button
            type="button"
            className="text-brand-black text-sm hover:underline"
            onClick={onForgotPassword}
          >
            Forgot Password?
          </button>
        </div>
        <div className="relative">
          <FormInput
            type={showPassword ? "text" : "password"}
            name="password"
            placeholder="Enter your password"
            value={credentials.password}
            onChange={handleChange}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 cursor-pointer">
            {showPassword ? (
              <EyeIcon onClick={() => setShowPassword(!showPassword)}
                style={{ color: '#999999' }} />
            ) : (
              <EyeOffIcon onClick={() => setShowPassword(!showPassword)}
                style={{ color: '#999999' }} />
            )}
          </div>

          {/* Password tooltip removed as requested */}
        </div>

        {errors.password && (
          <p className="text-red-500 text-sm mt-1">{errors.password}</p>
        )}
      </div>
      
      {/* All errors now appear at the bottom with consistent styling */}
      {generalError && (
        <p className="text-red-500 text-sm mt-1 mb-4 text-center">
          {generalError}
        </p>
      )}

      <Button type="submit"
        variant="primary"
        className={`font-proxima-nova w-full text-center py-2 px-4 xs:text-sm xs:py-1 rounded-[8px] transition-all duration-200  bg-brand text-white font-semibold
          }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}>
        Sign In
      </Button>

      <div className="mt-4">
        <Checkbox
          name="rememberMe"
          checked={credentials.rememberMe}
          onChange={handleChange}
          label="Remember Me"
        />
      </div>
    </form>
  );
};

const styles = {
  label: {
    fontFamily: "Proxima Nova, sans-serif",
    fontWeight: 500,
    lineHeight: "24.36px",
    letterSpacing: "0%",
  },
};

export default LoginForm;