import type React from "react"
import Sidebar from "@/components/dashboard/Sidebar"
import type { User } from "@/constants/user"

interface UserProfileSidebarProps {
    activeSection: string
    setActiveSection: (section: string) => void
    user: User | null
    updateUser: (data: User) => Promise<void>
}

const UserProfileSidebar: React.FC<UserProfileSidebarProps> = ({
    activeSection,
    setActiveSection,
    user,
    updateUser,
}) => {
    return (
        <div className="hidden md:block w-64 flex-shrink-0 border border-neutral rounded-[8px]">
            <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} user={user} updateUser={updateUser} />
        </div>
    )
}

export default UserProfileSidebar
