import { fixture } from '../fixtures/Fixture';
import { ScreenshotHelper } from './ScreenshotHelper';
import { Properties } from '../properties/Properties';
import { HomePage } from '../pages/HomePage';
import { LoginPage } from '../pages/LoginPage';
import { ChatPage } from '../pages/ChatPage';

/**
 * Utility class for cleaning Shasa chat history
 * This class provides methods to clear chat history by removing the thread cookie
 * or by using the UI to delete individual chat history items
 */
export class ShasaChatCleaner {
    // The name of the cookie that stores the Shasa chat thread ID
    private static readonly THREAD_COOKIE_NAME = 'nxvoy_thread_id';
    
    // Playwright locators and selectors for chat history UI elements
    private static readonly elements = {
        // Modern Playwright locator references
        locators: {
            // Chat history section locators
            chatHistoryHeader: { role: 'heading', name: 'Recent chats' },
            
            // UI Action elements
            ellipsisButton: { role: 'button', name: '<PERSON><PERSON>' },
            deleteOptionMenuItem: { role: 'menuitem', name: 'Delete Cha<PERSON>' },
            
            // Dialog elements
            confirmDeleteButton: { role: 'button', name: 'Delete' },
            cancelButton: { role: 'button', name: '<PERSON><PERSON>' }
        },
        
        // Legacy CSS selectors - kept for backwards compatibility
        // Chat history section - new UI
        chatHistorySection: 'div[data-sidebar="content"]',
        chatHistoryHeader: 'div[data-sidebar="group-label"]:has-text("Recent chats")',
        chatHistoryItems: 'ul[data-sidebar="menu"]',
        singleChatItem: 'li[data-sidebar="menu-item"]',
        
        // Ellipsis button for a chat history item - new UI
        ellipsisButton: 'button[data-sidebar="menu-action"]',
        
        // Delete chat option in the dropdown menu - new UI
        deleteOption: 'div[role="menuitem"]:has(svg.lucide-trash), div[role="menuitem"].text-destructive',
        
        // Confirmation dialog - new UI
        confirmDialog: 'div[role="dialog"], div.fixed.inset-0.z-50',
        deleteButton: 'button.bg-destructive, button:has-text("Delete")',
        cancelButton: 'button:has-text("Cancel")'
    };/**
     * Clears Shasa chat history by removing the thread cookie
     * @returns Promise<boolean> true if successful, false otherwise
     */
    public static async clearShasaChatHistory(): Promise<boolean> {
        try {
            console.log('Clearing Shasa chat history...');
            
            // Take screenshot before clearing chat history
            await ScreenshotHelper.takeScreenshot('before-clear-chat-history');
            
            // Simple and direct approach - use the JavaScript API to delete the cookie
            // This is the approach that was working before
            await fixture.page.evaluate((cookieName) => {
                console.log(`Removing cookie: ${cookieName}`);
                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                return true;
            }, this.THREAD_COOKIE_NAME);
            
            // Take screenshot after clearing chat history
            await ScreenshotHelper.takeScreenshot('after-clear-chat-history');
            
            // Verify cookie was removed
            const cookieExists = await fixture.page.evaluate((cookieName) => {
                const cookies = document.cookie.split(';');
                for (const cookie of cookies) {
                    const [name] = cookie.trim().split('=');
                    if (name === cookieName) {
                        return true;
                    }
                }
                return false;
            }, this.THREAD_COOKIE_NAME);
            
            if (!cookieExists) {
                console.log('✅ Successfully cleared Shasa chat history');
                return true;
            } else {
                console.error('❌ Failed to clear Shasa chat history - cookie still exists');
                return false;
            }
        } catch (error) {
            console.error(`❌ Error clearing Shasa chat history: ${error}`);
            return false;
        }
    }
    
    /**
     * Navigates to the chat page and clears chat history by removing the thread cookie
     * This method follows the steps:
     * 1. Navigate to app URL
     * 2. Click Sign in button
     * 3. Enter credentials and login
     * 4. Navigate to chat page
     * 5. Clear chat history
     * @returns Promise<boolean> true if successful, false otherwise
     */    public static async clearShasaChatHistoryWithNavigation(): Promise<boolean> {
        try {
            console.log('Starting chat history cleanup with navigation sequence...');
            
            // Step 1: Navigate to the app URL
            const baseUrl = Properties.getProperty('ui.nxvoy.appUrl');
            console.log(`Navigating to app URL: ${baseUrl}`);
            await fixture.page.goto(baseUrl);
            fixture.baseUrl = baseUrl;
            
            // Take a screenshot after landing on the page
            await ScreenshotHelper.takeScreenshot('cleanup-app-home');
            
            // Check if we're already logged in by looking for user account icon
            const userAccountIconSelectors = [
                'span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer',
                'span[aria-haspopup="menu"]',
                '.rounded-full:has(span.flex.h-full.w-full)'
            ];
            
            let isLoggedIn = false;
            for (const selector of userAccountIconSelectors) {
                try {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
                    if (isVisible) {
                        console.log(`User is already logged in. Found user account icon with selector: ${selector}`);
                        isLoggedIn = true;
                        break;
                    }
                } catch (error) {
                    // Ignore errors for individual selectors
                }
            }
            
            if (!isLoggedIn) {
                // Step 2: Click Sign in button if not logged in
                console.log('Clicking Sign in button...');
                await HomePage.clickSignInButton();
                await fixture.page.waitForTimeout(2000);
                await ScreenshotHelper.takeScreenshot('cleanup-login-page');
              // Step 3: Enter credentials and login - only if we're not already logged in
                console.log('Entering login credentials...');
                await LoginPage.typeUsername(Properties.getProperty('ui.nxvoy.users.standard.username'));
                await LoginPage.typePassword(Properties.getProperty('ui.nxvoy.users.standard.password'));
                
                console.log('Clicking login button...');
                await LoginPage.clickLoginButton();
                
                // Wait for login to complete and verify
                await fixture.page.waitForTimeout(5000);
                try {
                    await LoginPage.verifyLoggedInState();
                    console.log('Successfully logged in');
                    await ScreenshotHelper.takeScreenshot('cleanup-logged-in');
                } catch (loginError) {
                    console.error('Login verification failed:', loginError);
                    await ScreenshotHelper.takeScreenshot('cleanup-login-failed', true);
                    return false;
                }
            } else {
                console.log('User is already logged in, skipping login steps');
                await ScreenshotHelper.takeScreenshot('cleanup-already-logged-in');
            }
            
            // Step 4: Navigate to chat page
            console.log('Navigating to chat page...');
            const chatButtonClicked = await ChatPage.clickChatWithShasaButton();
            if (!chatButtonClicked) {
                console.error('Failed to click Chat with Shasa button');
                await ScreenshotHelper.takeScreenshot('cleanup-chat-button-failed', true);
                return false;
            }
            
            // Wait for chat interface to appear
            await fixture.page.waitForTimeout(5000);
            const isChatVisible = await ChatPage.isChatInterfaceVisible();
            if (!isChatVisible) {
                console.error('Chat interface did not appear');
                await ScreenshotHelper.takeScreenshot('cleanup-chat-interface-not-visible', true);
                return false;
            }
            
            await ScreenshotHelper.takeScreenshot('cleanup-chat-page');
            
            // Step 5: Clear chat history
            console.log('Clearing chat history...');
            return await this.clearShasaChatHistory();
            
        } catch (error) {
            console.error(`❌ Error during chat history cleanup with navigation: ${error}`);
            await ScreenshotHelper.takeScreenshot('cleanup-navigation-error', true);
            return false;
        }
    }
    
    /**
     * Deletes chat history items through the UI by:
     * 1. Finding chat history items
     * 2. Clicking the ellipsis button
     * 3. Selecting "Delete Chat" option
     * 4. Confirming deletion
     * @param maxItemsToDelete Maximum number of chat items to delete (defaults to 1)
     * @returns Promise<boolean> true if successful, false otherwise
     */
    public static async deleteChatHistoryViaUI(maxItemsToDelete: number = 1): Promise<boolean> {
        try {
            console.log(`Attempting to delete up to ${maxItemsToDelete} chat history items via UI...`);
            
            // Take screenshot before starting
            await ScreenshotHelper.takeScreenshot('before-chat-history-delete-ui');
            
            // Check if we're on the chat page, if not, navigate there
            const isChatVisible = await ChatPage.isChatInterfaceVisible();
            if (!isChatVisible) {
                console.log('Not on chat page, navigating there first...');
                
                // Use the existing navigation method to get to the chat page
                const navigated = await this.clearShasaChatHistoryWithNavigation();
                if (!navigated) {
                    console.error('Failed to navigate to chat page');
                    return false;
                }
            }
            
            // Wait for the chat history section to be visible with Playwright locators first
            console.log('Waiting for chat history section to be visible...');
            let chatHistorySectionFound = false;
            
            // Try with modern Playwright locators first
            try {
                // Look for the Recent chats heading
                const chatHistoryHeading = fixture.page.getByRole('heading', { name: 'Recent chats' });
                if (await chatHistoryHeading.isVisible({ timeout: 3000 }).catch(() => false)) {
                    console.log('Found chat history section with "Recent chats" heading locator');
                    chatHistorySectionFound = true;
                } else {
                    // Try locating by sidebar attribute with Playwright's locator
                    const sidebarContent = fixture.page.locator('div[data-sidebar="content"]');
                    if (await sidebarContent.isVisible({ timeout: 2000 }).catch(() => false)) {
                        console.log('Found chat history section with data-sidebar attribute locator');
                        chatHistorySectionFound = true;
                    }
                }
            } catch (error) {
                console.log(`Error finding chat history section with Playwright locators: ${error}`);
            }
            
            // Fall back to legacy selectors if needed
            if (!chatHistorySectionFound) {
                console.log('Falling back to legacy selectors for chat history section');
                
                // Try multiple selectors for the chat history section
                const chatHistorySectionSelectors = [
                    this.elements.chatHistorySection,
                    'div[data-sidebar="content"]',
                    'div:has(div[data-sidebar="group-label"])',
                    'div.flex.min-h-0.flex-col'
                ];
                
                for (const selector of chatHistorySectionSelectors) {
                    try {
                        await fixture.page.waitForSelector(selector, { timeout: 3000 });
                        console.log(`Found chat history section with selector: ${selector}`);
                        chatHistorySectionFound = true;
                        break;
                    } catch (error) {
                        console.log(`Selector ${selector} not found for chat history section`);
                    }
                }
                
                if (!chatHistorySectionFound) {
                    console.log('Trying header selectors as fallback');
                    // Try to find the header as fallback
                    const headerSelectors = [
                        this.elements.chatHistoryHeader,
                        'div[data-sidebar="group-label"]',
                        'div:has-text("Recent chats")'
                    ];
                    
                    for (const selector of headerSelectors) {
                        try {
                            await fixture.page.waitForSelector(selector, { timeout: 3000 });
                            console.log(`Found chat history header with selector: ${selector}`);
                            chatHistorySectionFound = true;
                            break;
                        } catch (error) {
                            console.log(`Header selector ${selector} not found`);
                        }
                    }
                }
            }
            
            if (!chatHistorySectionFound) {
                console.error('Could not find chat history section or header');
                await ScreenshotHelper.takeScreenshot('chat-history-section-not-found', true);
                return false;
            }
            
            // Take screenshot after finding chat history section
            await ScreenshotHelper.takeScreenshot('chat-history-section-found');
            
            // Check if there are any chat history items using Playwright locators
            console.log('Checking for chat history items...');
            await fixture.page.waitForTimeout(1000); // Short wait to ensure UI has settled
            
            // Try with Playwright's getBy methods first
            let chatItems = [];
            let chatItemsFound = false;
            
            try {
                // Look for list items in the sidebar menu
                const menuItems = fixture.page.locator('li[data-sidebar="menu-item"]');
                const itemCount = await menuItems.count();
                
                if (itemCount > 0) {
                    console.log(`Found ${itemCount} chat history items with Playwright locator`);
                    chatItems = await menuItems.all();
                    chatItemsFound = true;
                } else {
                    // Try with buttons inside the sidebar
                    const menuButtons = fixture.page.locator('button[data-sidebar="menu-button"]');
                    const buttonCount = await menuButtons.count();
                    
                    if (buttonCount > 0) {
                        console.log(`Found ${buttonCount} chat history buttons with Playwright locator`);
                        chatItems = await menuButtons.all();
                        chatItemsFound = true;
                    }
                }
            } catch (error) {
                console.log(`Error finding chat items with Playwright locators: ${error}`);
            }
            
            // Fall back to legacy selectors if needed
            if (!chatItemsFound) {
                console.log('Falling back to legacy selectors for chat items');
                
                // Try multiple selectors for chat items
                const itemSelectors = [
                    this.elements.singleChatItem,
                    'li[data-sidebar="menu-item"]',
                    'button[data-sidebar="menu-button"]'
                ];
                
                for (const selector of itemSelectors) {
                    chatItems = await fixture.page.$$(selector);
                    if (chatItems && chatItems.length > 0) {
                        console.log(`Found ${chatItems.length} chat history items with selector: ${selector}`);
                        chatItemsFound = true;
                        break;
                    }
                }
            }
            
            if (!chatItemsFound || chatItems.length === 0) {
                console.log('No chat history items found to delete');
                return true; // Nothing to delete, so technically successful
            }
            
            console.log(`Found ${chatItems.length} chat history items to process`);
            
            // Delete up to maxItemsToDelete items
            let itemsDeleted = 0;
            console.log(`Attempting to delete up to ${maxItemsToDelete} chat items out of ${chatItems.length} found`);
            for (let i = 0; i < Math.min(chatItems.length, maxItemsToDelete); i++) {
                console.log(`Attempting to delete chat history item ${i + 1}`);
                
                // First take screenshot to show what we're working with
                await ScreenshotHelper.takeScreenshot(`before-delete-item-${i+1}`);
                
                // Try to find and click the ellipsis button using Playwright locators first
                let ellipsisButtonClicked = false;
                
                try {
                    // First try using the role-based locator for the menu button
                    const menuButtons = fixture.page.getByRole('button', { name: 'Menu' });
                    const buttonCount = await menuButtons.count();
                    
                    if (buttonCount > 0) {
                        // If there are multiple menu buttons, click the one corresponding to our index
                        if (i < buttonCount) {
                            console.log(`Found ${buttonCount} menu buttons, clicking button ${i+1}`);
                            await menuButtons.nth(i).click();
                            ellipsisButtonClicked = true;
                        } else {
                            // If we've run out of buttons, click the first one as fallback
                            console.log(`Index ${i} exceeds button count, clicking first button`);
                            await menuButtons.first().click();
                            ellipsisButtonClicked = true;
                        }
                    }
                    
                    if (!ellipsisButtonClicked) {
                        // Try with data-sidebar attribute
                        const actionButtons = fixture.page.locator('button[data-sidebar="menu-action"]');
                        const actionCount = await actionButtons.count();
                        
                        if (actionCount > 0) {
                            if (i < actionCount) {
                                console.log(`Found ${actionCount} action buttons, clicking button ${i+1}`);
                                await actionButtons.nth(i).click();
                                ellipsisButtonClicked = true;
                            } else {
                                console.log(`Index ${i} exceeds action button count, clicking first button`);
                                await actionButtons.first().click();
                                ellipsisButtonClicked = true;
                            }
                        }
                    }
                } catch (error) {
                    console.log(`Error finding ellipsis buttons with Playwright locators: ${error}`);
                }
                
                // Fall back to legacy selectors if needed
                if (!ellipsisButtonClicked) {
                    console.log('Falling back to legacy selectors for ellipsis buttons');
                    
                    let ellipsisButtons = [];
                    const ellipsisButtonSelectors = [
                        this.elements.ellipsisButton,
                        'button[data-sidebar="menu-action"]',
                        'button[type="button"]:has(svg.lucide-ellipsis)',
                        'button[aria-haspopup="menu"]'
                    ];
                    
                    for (const selector of ellipsisButtonSelectors) {
                        ellipsisButtons = await fixture.page.$$(selector);
                        if (ellipsisButtons && ellipsisButtons.length > 0) {
                            console.log(`Found ${ellipsisButtons.length} ellipsis buttons with selector: ${selector}`);
                            break;
                        }
                    }
                    
                    if (!ellipsisButtons || ellipsisButtons.length === 0) {
                        console.error('Could not find any ellipsis buttons');
                        await ScreenshotHelper.takeScreenshot('ellipsis-buttons-not-found', true);
                        continue;
                    } else {
                        // Click the ellipsis button for the current chat history item
                        const buttonToClick = i < ellipsisButtons.length ? ellipsisButtons[i] : ellipsisButtons[0];
                        await buttonToClick.click();
                        console.log(`Clicked ellipsis button using legacy selector`);
                        ellipsisButtonClicked = true;
                    }
                }
                
                // Wait for the delete option to appear
                console.log('Waiting for delete option to appear...');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot(`chat-delete-menu-open-${i}`);
                
                // Try to click delete option using Playwright locators first
                let deleteOptionClicked = false;
                
                try {
                    // Try using role and name
                    const deleteMenuItem = fixture.page.getByRole('menuitem', { name: 'Delete Chat' })
                        .or(fixture.page.getByRole('menuitem', { name: 'Delete' }));
                    
                    if (await deleteMenuItem.isVisible({ timeout: 2000 }).catch(() => false)) {
                        await deleteMenuItem.click();
                        console.log('Clicked delete option with Playwright role locator');
                        deleteOptionClicked = true;
                    } else {
                        // Try locating by text content
                        const deleteMenuByText = fixture.page.getByText('Delete Chat', { exact: false })
                            .filter({ has: fixture.page.locator('div[role="menuitem"]') });
                        
                        if (await deleteMenuByText.isVisible({ timeout: 1000 }).catch(() => false)) {
                            await deleteMenuByText.click();
                            console.log('Clicked delete option with Playwright text locator');
                            deleteOptionClicked = true;
                        } else {
                            // Try locating by the destructive class or trash icon
                            const trashIcon = fixture.page.locator('div[role="menuitem"]')
                                .filter({ has: fixture.page.locator('svg.lucide-trash') });
                            
                            if (await trashIcon.isVisible({ timeout: 1000 }).catch(() => false)) {
                                await trashIcon.click();
                                console.log('Clicked delete option with trash icon locator');
                                deleteOptionClicked = true;
                            } else {
                                const destructiveClass = fixture.page.locator('div[role="menuitem"].text-destructive');
                                
                                if (await destructiveClass.isVisible({ timeout: 1000 }).catch(() => false)) {
                                    await destructiveClass.click();
                                    console.log('Clicked delete option with destructive class locator');
                                    deleteOptionClicked = true;
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.log(`Error finding delete option with Playwright locators: ${error}`);
                }
                
                // Fall back to legacy approach if needed
                if (!deleteOptionClicked) {
                    console.log('Falling back to legacy selectors for delete option');
                    
                    try {
                        // Define all possible delete option selectors
                        const deleteOptionSelectors = [
                            this.elements.deleteOption,
                            'div[role="menuitem"]:has(svg.lucide-trash)',
                            'div[role="menuitem"].text-destructive',
                            'div[role="menuitem"]:has-text("Delete")',
                            'div[role="menuitem"]:has(span:text("Delete"))'
                        ];
                        
                        // Try each selector
                        for (const selector of deleteOptionSelectors) {
                            try {
                                const element = await fixture.page.$(selector);
                                if (element) {
                                    console.log(`Found delete option with selector: ${selector}`);
                                    await element.click();
                                    deleteOptionClicked = true;
                                    break;
                                }
                            } catch (selectorError) {
                                console.log(`Error with selector ${selector}: ${selectorError}`);
                            }
                        }
                        
                        // If all direct attempts fail, try JavaScript approach
                        if (!deleteOptionClicked) {
                            console.log('Trying JavaScript approach for delete option');
                            const jsClicked = await fixture.page.evaluate(() => {
                                // Find all menu items
                                const menuItems = Array.from(document.querySelectorAll('div[role="menuitem"]'));
                                
                                // Look for delete option based on text or destructive class
                                const deleteItem = menuItems.find(item => 
                                    item.textContent?.includes('Delete') || 
                                    item.classList.contains('text-destructive') ||
                                    item.querySelector('svg.lucide-trash')
                                );
                                
                                if (deleteItem) {
                                    (deleteItem as HTMLElement).click();
                                    return true;
                                }
                                return false;
                            });
                            
                            if (jsClicked) {
                                console.log('Successfully clicked delete option via JavaScript');
                                deleteOptionClicked = true;
                            }
                        }
                    } catch (error) {
                        console.error(`Failed to click delete option: ${error}`);
                    }
                }
                
                if (!deleteOptionClicked) {
                    console.error('Could not find delete option');
                    await ScreenshotHelper.takeScreenshot('delete-option-not-found', true);
                    
                    // Try to click outside to close the menu, then continue with next item
                    await fixture.page.mouse.click(10, 10);
                    continue;
                }
                
                // Wait for the confirmation dialog using Playwright locators first
                console.log('Waiting for delete confirmation dialog...');
                let confirmDialogFound = false;
                
                try {
                    // Try using dialog role
                    const confirmDialog = fixture.page.getByRole('dialog');
                    if (await confirmDialog.isVisible({ timeout: 3000 }).catch(() => false)) {
                        console.log('Found confirmation dialog with role locator');
                        confirmDialogFound = true;
                        
                        // Look for Delete button in the dialog with Playwright locators
                        const deleteButton = fixture.page.getByRole('button', { name: 'Delete' });
                        
                        if (await deleteButton.isVisible({ timeout: 2000 }).catch(() => false)) {
                            await deleteButton.click();
                            console.log('Clicked Delete button in confirmation dialog with role locator');
                            
                            // Wait for the action to complete
                            await fixture.page.waitForTimeout(2000);
                            itemsDeleted++;
                            continue;
                        } else {
                            // Try by text
                            const deleteButtonByText = fixture.page.getByText('Delete', { exact: true })
                                .filter({ has: fixture.page.locator('button') });
                            
                            if (await deleteButtonByText.isVisible({ timeout: 1000 }).catch(() => false)) {
                                await deleteButtonByText.click();
                                console.log('Clicked Delete button in confirmation dialog with text locator');
                                
                                await fixture.page.waitForTimeout(2000);
                                itemsDeleted++;
                                continue;
                            }
                        }
                    }
                } catch (error) {
                    console.log(`Error finding confirmation dialog with Playwright locators: ${error}`);
                }
                
                // Fall back to legacy selectors if needed
                if (!confirmDialogFound) {
                    console.log('Falling back to legacy selectors for confirmation dialog');
                    
                    // Try multiple selectors for confirmation dialog
                    const confirmDialogSelectors = [
                        this.elements.confirmDialog,
                        'div[role="dialog"]',
                        'div.fixed.inset-0.z-50',
                        'div:has(button:has-text("Delete"))'
                    ];
                    
                    for (const selector of confirmDialogSelectors) {
                        try {
                            await fixture.page.waitForSelector(selector, { timeout: 3000 });
                            console.log(`Found confirmation dialog with selector: ${selector}`);
                            confirmDialogFound = true;
                            break;
                        } catch (error) {
                            console.log(`Selector ${selector} not found for confirmation dialog`);
                        }
                    }
                    
                    if (confirmDialogFound) {
                        // Try to find and click the Delete button using legacy selectors
                        try {
                            const deleteButtonSelectors = [
                                this.elements.deleteButton,
                                'button.bg-destructive',
                                'button:has-text("Delete")',
                                'button.text-white:has-text("Delete")'
                            ];
                            
                            let deleteButtonClicked = false;
                            for (const selector of deleteButtonSelectors) {
                                try {
                                    const deleteButton = await fixture.page.$(selector);
                                    if (deleteButton) {
                                        console.log(`Found Delete button with selector: ${selector}`);
                                        await deleteButton.click();
                                        console.log('Clicked Delete button in confirmation dialog');
                                        deleteButtonClicked = true;
                                        
                                        // Wait for the action to complete
                                        await fixture.page.waitForTimeout(2000);
                                        itemsDeleted++;
                                        break;
                                    }
                                } catch (buttonError) {
                                    console.log(`Error with Delete button selector ${selector}: ${buttonError}`);
                                }
                            }
                            
                            if (!deleteButtonClicked) {
                                console.error('Could not find Delete button in confirmation dialog');
                                // Try to click Cancel or outside the dialog to close it
                                await fixture.page.mouse.click(10, 10);
                            }
                        } catch (error) {
                            console.error(`Error clicking Delete button: ${error}`);
                        }
                    } else {
                        console.error('Could not find confirmation dialog');
                        // Try to click outside to close any open menus
                        await fixture.page.mouse.click(10, 10);
                    }
                }
            }
            
            // Take screenshot after deletion attempts
            await ScreenshotHelper.takeScreenshot('after-chat-history-delete-ui');
            
            if (itemsDeleted > 0) {
                console.log(`Successfully deleted ${itemsDeleted} chat history items via UI`);
            } else {
                console.log(`No chat history items were deleted, but continuing with test flow`);
                // Return true anyway to avoid failing the test if we couldn't delete items
                // The cookie-based cleanup will still run as a fallback
            }
            
            // Return true even if we couldn't delete all items, to avoid breaking test flow
            // The test should continue even if history cleanup isn't perfect
            return true;
        } catch (error) {
            console.error(`❌ Error deleting chat history via UI: ${error}`);
            await ScreenshotHelper.takeScreenshot('chat-delete-ui-error', true);
            return false;
        }
    }
    
    /**
     * Complete cleanup of chat history through UI navigation and deletion:
     * 1. Navigate to the chat page
     * 2. Delete chat history items through UI
     * 3. Clear thread cookie as fallback
     * @returns Promise<boolean> true if any cleanup method succeeded
     */
    public static async completeChatHistoryCleanup(): Promise<boolean> {
        try {
            console.log('Starting complete chat history cleanup process...');
            
            // First attempt to navigate and delete chats via UI
            const uiDeleteSuccess = await this.deleteChatHistoryViaUI(5); // Delete up to 5 items
            
            // Then also clear the cookie as a fallback
            const cookieSuccess = await this.clearShasaChatHistory();
            
            // Return true if either method succeeded
            return uiDeleteSuccess || cookieSuccess;
        } catch (error) {
            console.error(`❌ Error during complete chat history cleanup: ${error}`);
            await ScreenshotHelper.takeScreenshot('complete-cleanup-error', true);
            
            // Try the cookie method as last resort
            try {
                return await this.clearShasaChatHistory();
            } catch {
                return false;
            }
        }
    }
}
