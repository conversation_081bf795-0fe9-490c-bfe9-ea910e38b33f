import {
    Popover,
    PopoverTrigger,
    PopoverContent,
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { useCustomSession } from "@/hooks/use-custom-session";
import DashboardLayout from "@/layout/DashboardLayout";
import { ArrowRight, Check, Link, RotateCcw, ChevronDown, PlaneTakeoff, PlaneLanding, CalendarDaysIcon, UserPlus2Icon, UserPlus } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { FlightClasses } from "@/constants/flight";
import { FlightSearchForm } from "@/types/flights";
import { DateRange } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { agentPostMethod } from "@/utils/api";
import { useDispatch, useSelector } from "react-redux";
import {
    updateTripConfig,
    updatePassengers,
    updateDates,
    updateLocations,
    selectTotalPassengers,
} from "@/store/slices/flightSearchSlice";
import {
    selectOutboundFlights,
    selectInboundFlights,
    selectLoadingState,
    selectErrorState,
    setFlightResponseData,
    setLoading,
    selectAirportData,
    setError as setResponseError
} from '@/store/slices/flightResponse';
import { setPickedOutbound, selectPickedReturn, setPickedReturn } from '@/store/slices/flightJourney'

import { generateUUID } from "@/lib/utils/uuid";
import { AppState } from "@/store/store";
import LoadingOverlay from "@/components/LoadingOverlay/LoadingOverlay";
import FlightList from "@/components/flight/FlightList";
import { Flight } from "@/constants/models";
import { useRouter } from "next/router";

export const TripOptionsIcons = [
    { label: "One Way", icon: ArrowRight },
    { label: "Round Trip", icon: RotateCcw },
    { label: "Multi-Trip", icon: Link },
];

type InputType = "origin" | "destination";

interface Suggestion {
    iata_code: string;
    city_name_original: string;
    airport_name: string;
    country_name: string;
}

interface FormErrors {
    origin: string;
    destination: string;
}

const FlightSearchPage: React.FC = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const searchForm = useSelector((state: AppState) => state.flightSearchForm);
    const totalPassengers = useSelector((state: AppState) => selectTotalPassengers(state));
    const outboundFlights = useSelector(selectOutboundFlights);
    const inboundFlights = useSelector(selectInboundFlights);
    const loading = useSelector(selectLoadingState);
    const airportData = useSelector(selectAirportData);
    const error = useSelector(selectErrorState);

    const [showCalendar, setShowCalendar] = useState(false);
    const calendarRef = useRef<HTMLDivElement>(null);

    // Form validation errors
    const [formErrors, setFormErrors] = useState<FormErrors>({
        origin: "",
        destination: ""
    });

    const calendarMonths = window.innerWidth < 768 ? 1 : 2;

    // Suggestion-related state (keeping local as it's UI-only state)
    const [suggestions, setSuggestions] = useState({
        origin: [] as Suggestion[],
        destination: [] as Suggestion[],
    });
    const [isLoading, setIsLoading] = useState({
        origin: false,
        destination: false,
    });
    const [activeInput, setActiveInput] = useState<InputType | null>(null);

    const { data: session, status } = useCustomSession();
    const token = session?.accessToken;

    // Debounce hook
    const useDebounce = (value: string, delay: number) => {
        const [debouncedValue, setDebouncedValue] = useState(value);
        useEffect(() => {
            const handler = setTimeout(() => {
                setDebouncedValue(value);
            }, delay);
            return () => {
                clearTimeout(handler);
            };
        }, [value, delay]);
        return debouncedValue;
    };

    const debouncedOrigin = useDebounce(searchForm.origin.city, 300);
    const debouncedDestination = useDebounce(searchForm.destination.city, 300);

    // Function to update the travel class
    const updateTravelClass = (classType: FlightSearchForm['travel_class']) => {
        dispatch(updateTripConfig({ travel_class: classType }));
    };

    // Function to update trip type
    const updateTripType = (tripType: FlightSearchForm['trip_type']) => {
        dispatch(updateTripConfig({ trip_type: tripType }));
    };

    // Function to clear field errors
    const clearFieldError = (field: keyof FormErrors) => {
        if (formErrors[field]) {
            setFormErrors(prev => ({
                ...prev,
                [field]: ""
            }));
        }
    };

    // Function to handle input changes for origin/destination
    const handleInputChange = (value: string, type: InputType) => {
        setActiveInput(type);
        dispatch(updateLocations({
            [type]: { city: value, iata_code: "" }
        }));

        // Clear error when user starts typing
        clearFieldError(type);
    };

    // Function to handle suggestion click
    const handleSuggestionClick = (suggestion: Suggestion, type: InputType) => {
        const cityCode = suggestion.iata_code || "";
        const cityName = suggestion.city_name_original || "";
        const countryName = suggestion.country_name || "";
        const displayName = `${cityName}, ${countryName} (${cityCode})`;

        dispatch(updateLocations({
            [type]: { city: displayName, iata_code: cityCode }
        }));

        setSuggestions((prev) => ({ ...prev, [type]: [] }));
        setActiveInput(null);

        // Clear error when user selects a suggestion
        clearFieldError(type);
    };

    // Function to update passenger counts
    const updateCount = (key: 'adults' | 'children' | 'infants', increment: number) => {
        const currentValue = searchForm[key];
        const newValue = currentValue + increment;
        const minValue = key === 'adults' ? 1 : 0;
        const finalValue = Math.max(minValue, newValue);

        dispatch(updatePassengers({ [key]: finalValue }));
    };

    // Validation function
    const validateForm = (): boolean => {
        const errors: FormErrors = {
            origin: "",
            destination: ""
        };

        // Check if origin is empty or wrong iata code
        if (!searchForm.origin.city.trim()) {
            errors.origin = "Please select a departure location";
        } else if (!searchForm.origin.iata_code.trim()) {
            errors.origin = "Please select a departure location from suggestion";
        }

        // Check if destination is empty
        if (!searchForm.destination.city.trim()) {
            errors.destination = "Please select a destination";
        } else if (!searchForm.destination.iata_code.trim()) {
            errors.destination = "Please select a destination location from suggestion";
        }

        // Check if origin and destination are the same
        if (searchForm.origin.city.trim() && searchForm.destination.city.trim()) {
            const originCode = searchForm.origin.iata_code || extractAirportCode(searchForm.origin.city);
            const destinationCode = searchForm.destination.iata_code || extractAirportCode(searchForm.destination.city);

            if (originCode === destinationCode) {
                errors.destination = "Destination must be different from departure location";
            }
        }

        setFormErrors(errors);

        // Return true if no errors
        return !errors.origin && !errors.destination;
    };

    // Get detailed traveler breakdown for display
    const getTravelerBreakdown = () => {
        const parts = [];

        if (searchForm.adults > 0) {
            parts.push(`${searchForm.adults} Adult${searchForm.adults !== 1 ? 's' : ''}`);
        }
        if (searchForm.children > 0) {
            parts.push(`${searchForm.children} Child${searchForm.children !== 1 ? 'ren' : ''}`);
        }
        if (searchForm.infants > 0) {
            parts.push(`${searchForm.infants} Infant${searchForm.infants !== 1 ? 's' : ''}`);
        }

        if (parts.length === 0) {
            return "1 Adult"; // fallback
        }

        return parts.join(', ');
    };

    // Handle date selection
    const handleSelect = (ranges: any) => {
        const selection = ranges.selection;
        const departureDate = format(selection.startDate, 'yyyy-MM-dd');
        const returnDate = format(selection.endDate, 'yyyy-MM-dd');

        dispatch(updateDates({
            departure_date: departureDate,
            return_date: returnDate
        }));

        // Close calendar after selecting dates for one-way trips
        if (searchForm.trip_type !== "Round Trip" && selection.startDate) {
            setShowCalendar(false);
        }
    };

    const getDisplayDateText = () => {
        try {
            const depDate = new Date(searchForm.departure_date);
            if (searchForm.trip_type === "Round Trip") {
                const retDate = new Date(searchForm.return_date);
                return `${format(depDate, "EEE MMM dd")} - ${format(retDate, "EEE MMM dd")}`;
            } else {
                return format(depDate, "EEE MMM dd");
            }
        } catch (error) {
            return "Select dates";
        }
    };

    const getDateRangeFromSearchForm = () => {
        const startDate = new Date(searchForm.departure_date);
        const endDate = searchForm.trip_type === "Round Trip"
            ? new Date(searchForm.return_date)
            : new Date(searchForm.departure_date);

        return [{
            startDate,
            endDate,
            key: 'selection'
        }];
    };

    // Fetch suggestions function
    const fetchSuggestions = async (query: string, type: InputType) => {
        if (query?.length < 1) {
            setSuggestions((prev) => ({ ...prev, [type]: [] }));
            return;
        }

        setIsLoading((prev) => ({ ...prev, [type]: true }));
        try {
            const response = await agentPostMethod(
                "flight/suggest",
                { query },
                token ?? ""
            );
            setSuggestions((prev) => ({ ...prev, [type]: response.detail?.data || [] }));
        } catch (error) {
            console.error("Error fetching suggestions:", error);
            setSuggestions((prev) => ({ ...prev, [type]: [] }));
        } finally {
            setIsLoading((prev) => ({ ...prev, [type]: false }));
        }
    };

    // Close calendar when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
                setShowCalendar(false);
            }
        };

        if (showCalendar) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showCalendar]);

    // Effect to handle suggestion fetching
    useEffect(() => {
        if (activeInput === "origin") {
            fetchSuggestions(debouncedOrigin, "origin");
        } else if (activeInput === "destination") {
            fetchSuggestions(debouncedDestination, "destination");
        }
    }, [debouncedOrigin, debouncedDestination, activeInput, token]);

    // Extract airport code helper function
    const extractAirportCode = (locationString: string): string => {
        if (!locationString) return "";

        // Return if 3 letters
        if (locationString.length === 3 && /^[A-Z]{3}$/.test(locationString)) {
            return locationString;
        }

        // Extract code from "City, Country (CODE)" format
        const match = locationString.match(/\(([A-Z]{3})\)$/);
        return match ? match[1] : locationString;
    };

    async function handleSearch() {
        // Validate form before proceeding
        if (!validateForm()) {
            return; // Stop if validation fails
        }

        // Extract codes for API payload
        const originCode = searchForm.origin.iata_code || extractAirportCode(searchForm.origin.city);
        const destinationCode = searchForm.destination.iata_code || extractAirportCode(searchForm.destination.city);

        console.log("search data: ", searchForm);

        const payload = {
            request_uuid: generateUUID(),
            origin: originCode,
            destination: destinationCode,
            departure_date: searchForm.departure_date,
            return_date: searchForm.trip_type === "Round Trip" ? searchForm.return_date : "",
            adults: searchForm.adults,
            children: searchForm.children,
            infants: searchForm.infants,
            travel_class: searchForm.travel_class,
            trip_type: searchForm.trip_type
        };

        console.log("Search payload:", payload);

        try {
            dispatch(setLoading(true));
            const response = await agentPostMethod(
                "flight/search",
                payload,
                token ?? ""
            );

            dispatch(setFlightResponseData(response.detail.data));
            dispatch(setLoading(false));
        } catch (error) {
            dispatch(setResponseError(String(error)));
            console.error("Search error:", error);
        }
    }

    function handleSelectOutbound(picked_flight: Flight, picked_class: string) {
        dispatch(setPickedOutbound({ flight: picked_flight, class: picked_class }))
        if (searchForm.trip_type === "One Way") {
            router.push("/flightsummary");
        }
    }

    function handleSelectInbound(picked_flight: Flight, picked_class: string) {
        dispatch(setPickedReturn({ flight: picked_flight, class: picked_class }))
        router.push("/flightsummary");
    }

    return (
        <DashboardLayout>
            <div className="space-y-4 mx-4">
                <div className="flex flex-col md:flex-row justify-between">
                    <div className="flex items-center bg-white rounded-full h-10 p-1 w-fit md:w-auto border border-[#00000014]">
                        {TripOptionsIcons.map((trip, index) => {
                            const IconComponent = trip.icon;
                            const isSelected = searchForm.trip_type === trip.label;
                            return (
                                <button
                                    key={index}
                                    onClick={() => updateTripType(trip.label as FlightSearchForm['trip_type'])}
                                    className={`
                                flex items-center md:gap-2 px-2 md:px-6 md:py-2 py-1.5 rounded-full md:font-medium text-sm
                                ${isSelected
                                            ? "bg-brand text-white shadow-sm"
                                            : "text-brand-black hover:text-brand-black hover:border-2 hover:border-brand"
                                        }
                            `}
                                >
                                    <IconComponent className="md:w-4 md:h-4 h-3 w-3 mr-1" />
                                    <span>{trip.label}</span>
                                </button>
                            );
                        })}
                    </div>
                    <div className="w-64">
                        {/* Class Type Dropdown */}
                        <Popover>
                            <PopoverTrigger asChild className="mt-4 md:mt-0 h-10">
                                <button className="bg-white border border-gray-200 hover:border-gray-300 text-brand-black font-medium flex items-center justify-between gap-1 rounded-full px-4 md:py-3 py-2 md:min-w-[100px] transition-colors w-full">
                                    <span className="md:font-medium text-sm">
                                        {searchForm.travel_class}
                                    </span>
                                    <ChevronDown className="w-5 h-5 text-brand-black" />
                                </button>
                            </PopoverTrigger>
                            <PopoverContent className="rounded-2xl shadow-lg bg-white border border-gray-200 p-4 font-medium">
                                <div className="space-y-1">
                                    {FlightClasses.map((classType, index) => (
                                        <div
                                            key={classType}
                                            className={`
                                        flex items-center justify-between p-2 rounded-xl cursor-pointer transition-colors
                                        ${searchForm.travel_class === classType
                                                    ? "bg-gray-100 text-brand-black"
                                                    : "text-brand-black hover:bg-gray-50"
                                                }
                                    `}
                                            onClick={() => updateTravelClass(classType)}
                                        >
                                            <span className="font-medium">{classType}</span>
                                            {searchForm.travel_class === classType && (
                                                <Check className="w-5 h-5 text-[#4B4BC3]" />
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </PopoverContent>
                        </Popover>
                    </div>
                </div>
                <div className="relative w-full h-auto font-proxima-nova">
                    <div className="p-0.5 bg-gradient-to-r from-[#1E1E76] via-[#3838DC] via-[#3131B6] via-[#3838DC] to-[#181882] rounded-2xl">
                        <div className="grid md:grid-cols-2 gap-4 bg-white rounded-t-2xl p-4">
                            {/* Origin Input with Suggestions */}
                            <div className="relative">
                                <div className={`bg-white border flex gap-2 items-center rounded-xl px-2 xl:py-2 xl:py-2 md:py-1 sm:py-0.5 xs:py-1 ${formErrors.origin ? 'border-red-500' : 'border-gray-200'}`}>
                                    <PlaneTakeoff color="#999999" />
                                    <input
                                        className="w-full text-base xs:text-sm focus:outline-none"
                                        placeholder="From Where"
                                        value={searchForm.origin.city}
                                        onChange={(e) => handleInputChange(e.target.value, "origin")}
                                        onFocus={() => setActiveInput("origin")}
                                    />
                                </div>
                                {/* Error message for origin */}
                                {formErrors.origin && (
                                    <p className="text-red-500 text-xs mt-1 px-2">
                                        {formErrors.origin}
                                    </p>
                                )}
                                {/* Origin Suggestions Dropdown */}
                                {activeInput === "origin" && ((suggestions.origin.length > 0) || isLoading.origin) && (
                                    <div className="absolute w-full z-10 mt-1 bg-white text-black rounded-md shadow-lg max-h-60 overflow-auto">
                                        {isLoading.origin ? (
                                            <div className="px-4 py-2 text-gray-500">
                                                Loading suggestions...
                                            </div>
                                        ) : (
                                            suggestions.origin.map((suggestion, index) => (
                                                <div
                                                    key={`origin-${suggestion.city_name_original}-${index}`}
                                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                                    onClick={() => handleSuggestionClick(suggestion, "origin")}
                                                >
                                                    {suggestion.city_name_original}, {suggestion.country_name} ({suggestion.iata_code})
                                                </div>
                                            ))
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Destination Input with Suggestions */}
                            <div className="relative">
                                <div className={`bg-white flex gap-2 px-2 2xl:py-2 xl:py-2 md:py-1 sm:py-0.5 xs:py-1 border rounded-xl relative ${formErrors.destination ? 'border-red-500' : 'border-gray-200'}`}>
                                    <PlaneLanding color="#999999" />
                                    <input
                                        className="w-full text-base xs:text-sm focus:outline-none"
                                        placeholder="To Where"
                                        value={searchForm.destination.city}
                                        onChange={(e) => handleInputChange(e.target.value, "destination")}
                                        onFocus={() => setActiveInput("destination")}
                                    />
                                </div>
                                {/* Error message for destination */}
                                {formErrors.destination && (
                                    <p className="text-red-500 text-xs mt-1 px-2">
                                        {formErrors.destination}
                                    </p>
                                )}
                                {/* Destination Suggestions Dropdown */}
                                {activeInput === "destination" && ((suggestions.destination.length > 0) || isLoading.destination) && (
                                    <div className="absolute z-10 mt-1 w-full bg-white text-black rounded-md shadow-lg max-h-60 overflow-auto">
                                        {isLoading.destination ? (
                                            <div className="px-4 py-2 text-gray-500">
                                                Loading suggestions...
                                            </div>
                                        ) : (
                                            suggestions.destination.map((suggestion, index) => (
                                                <div
                                                    key={`destination-${suggestion.city_name_original}-${index}`}
                                                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                                    onClick={() => handleSuggestionClick(suggestion, "destination")}
                                                >
                                                    {suggestion.city_name_original}, {suggestion.country_name} ({suggestion.iata_code})
                                                </div>
                                            ))
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="relative bg-white flex justify-start gap-2 2xl:p-2 xl:p-2 md:py-1 sm:p-0.5 xs:py-1 xs:px-4 border rounded-xl">
                                <CalendarDaysIcon
                                    className="xs:w-6 h-6"
                                    color="#999999"
                                />
                                <input
                                    type="text"
                                    className="w-full text-base xs:text-sm focus:outline-none cursor-pointer"
                                    value={getDisplayDateText()}
                                    onClick={() => setShowCalendar(!showCalendar)}
                                    readOnly
                                />

                                {showCalendar && (
                                    <div
                                        className="absolute top-full rounded-xl z-10 mt-2 border border-gray-200"
                                        ref={calendarRef}
                                    >
                                        <DateRange
                                            className="rounded-xl"
                                            editableDateInputs={true}
                                            onChange={handleSelect}
                                            moveRangeOnFirstSelection={false}
                                            showDateDisplay={false}
                                            months={calendarMonths}
                                            direction="horizontal"
                                            retainEndDateOnFirstSelection={searchForm.trip_type !== "Round Trip"}
                                            minDate={new Date()}
                                            ranges={getDateRangeFromSearchForm()}
                                            rangeColors={["#4B4BC3"]}
                                            onRangeFocusChange={(focusedRange: number[]) => {
                                                if (searchForm.trip_type !== "Round Trip" && focusedRange[0] === 0) {
                                                    setShowCalendar(false);
                                                }
                                            }}
                                        />
                                    </div>
                                )}
                            </div>
                            <div>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <button className="bg-white border w-full font-proxima-nova flex items-center gap-2 p-2 rounded-xl">
                                            <UserPlus
                                                color="#999999"
                                                className="w-4 xs:w-3 xs:h-3 h-4"
                                            />
                                            <span className="text-base xs:text-sm text-gray-600">
                                                {getTravelerBreakdown()}
                                            </span>
                                        </button>
                                    </PopoverTrigger>

                                    {/* Popover Content */}
                                    <PopoverContent className="w-80 p-0 bg-white font-proxima-nova rounded-2xl shadow-lg border">
                                        <div className="px-4 pb-2">
                                            <div className="flex justify-center">
                                                <div className="flex items-center gap-2">
                                                    <UserPlus2Icon className="w-5 h-5 text-[#1E1E76]" />
                                                    <h3 className="text-lg font-semibold text-[#1E1E76]">Travelers</h3>
                                                </div>
                                            </div>
                                            <p className="text-sm text-center text-gray-500">
                                                {totalPassengers} Traveler{totalPassengers !== 1 ? 's' : ''}
                                            </p>
                                        </div>

                                        {/* Content */}
                                        <div className="px-4 pb-4">
                                            {/* Adults */}
                                            <div className="flex justify-between items-center py-4 border-t">
                                                <div>
                                                    <p className="font-semibold text-gray-900 text-base">Adults</p>
                                                    <p className="text-sm text-gray-500">Ages 13 or above</p>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                                                        onClick={() => updateCount('adults', -1)}
                                                        disabled={searchForm.adults <= 1}
                                                    >
                                                        −
                                                    </button>
                                                    <span className="w-8 text-center font-medium text-gray-900 text-lg">
                                                        {searchForm.adults}
                                                    </span>
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 text-lg"
                                                        onClick={() => updateCount('adults', 1)}
                                                    >
                                                        +
                                                    </button>
                                                </div>
                                            </div>

                                            {/* Children */}
                                            <div className="flex justify-between items-center py-4 border-y">
                                                <div>
                                                    <p className="font-semibold text-gray-900 text-base">Children</p>
                                                    <p className="text-sm text-gray-500">Ages 2-12</p>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                                                        onClick={() => updateCount('children', -1)}
                                                        disabled={searchForm.children <= 0}
                                                    >
                                                        −
                                                    </button>
                                                    <span className="w-8 text-center font-medium text-gray-900 text-lg">
                                                        {searchForm.children}
                                                    </span>
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 text-lg"
                                                        onClick={() => updateCount('children', 1)}
                                                    >
                                                        +
                                                    </button>
                                                </div>
                                            </div>

                                            {/* Infants */}
                                            <div className="flex justify-between items-center py-4">
                                                <div>
                                                    <p className="font-semibold text-gray-900 text-base">Infants</p>
                                                    <p className="text-sm text-gray-500">Under 2</p>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                                                        onClick={() => updateCount('infants', -1)}
                                                        disabled={searchForm.infants <= 0}
                                                    >
                                                        −
                                                    </button>
                                                    <span className="w-8 text-center font-medium text-gray-900 text-lg">
                                                        {searchForm.infants}
                                                    </span>
                                                    <button
                                                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 text-lg"
                                                        onClick={() => updateCount('infants', 1)}
                                                    >
                                                        +
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        <div className="">
                            <div className="bg-[#F2F2FF] rounded-b-2xl p-4">
                                <div className="flex gap-4 justify-end">
                                    <Button
                                        variant="secondary"
                                        onClick={() => alert("/chat")}
                                        className="text-sm md:text-base text-brand py-2 px-4 rounded-[8px] border-2 border-brand"
                                    >
                                        Back to Chat
                                    </Button>
                                    <Button
                                        onClick={handleSearch}
                                        className="text-sm md:text-base text-white bg-brand py-2 px-4 rounded-[8px]"
                                    >
                                        Search Flights
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {loading ? <LoadingOverlay loadingText="Just a moment! Sasha's lining up the best fares for your journey." /> :
                    <FlightList
                        airport={airportData}
                        outbound_flights={outboundFlights}
                        inbound_flights={inboundFlights}
                        travel_class={searchForm.travel_class}
                        onOutboundCardSelect={handleSelectOutbound}
                        onInboundCardSelect={handleSelectInbound}
                    />
                }


            </div>
        </DashboardLayout>
    )
}

export default FlightSearchPage;