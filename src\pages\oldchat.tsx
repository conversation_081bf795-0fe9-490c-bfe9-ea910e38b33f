import { GetServerSideProps } from "next";
import { ChatSidebar } from "@/components/chat/ChatSidebar";
import { ChatHeader } from "@/components/chat/ChatHeader";
import { ChatScreen } from "@/components/chat/ChatScreen";
import { Chat<PERSON>rovider } from "@/context/ChatContext";
import ChatMobileNav from "@/components/chat/ChatMobileNav";

function parseCookies(cookieHeader = ""): Record<string, string> {
  return cookieHeader.split(";").reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      acc[key] = decodeURIComponent(value || "");
      return acc;
    },
    {} as Record<string, string>
  );
}


export default function Chat() {
  return (
    <ChatProvider>
      <div className="flex h-screen bg-white tk-proxima-nova">
        <ChatSidebar />

        <div className="flex flex-col flex-1 overflow-hidden">
          <div className="md:hidden">
            <ChatMobileNav />
          </div>
          {/* Fixed Header */}
          <div className="h-[12%] md:h-auto top-0 z-50 bg-white mx-4">
            <ChatHeader />
          </div>

          {/* ChatScreen fills remaining space */}
          <div className="flex-1 overflow-hidden mx-4">
            <ChatScreen />
          </div>
        </div>
      </div>
    </ChatProvider>
  );
}
