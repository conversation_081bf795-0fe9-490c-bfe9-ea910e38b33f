import { createSlice, PayloadAction } from '@reduxjs/toolkit';

type GroupedThreads = Record<string, Array<{
  thread_id: string;
  thread_name: string;
  updated_at: string;
  created_at: string;
  message_count: number;
}>>

interface ChatThreadState {
  currentChatPageThreadId: string | null;
  currentThreadName: string | null;
  history: boolean;
  newThread: boolean;
  showMoreFlightsOption: boolean;
  allFlights: any;
  tripType: string;
  chatResult: any;
  chatHistory: GroupedThreads;
  chatHistoryLoaded: boolean;
  isRefreshingHistory: boolean;
  shouldRefreshHistory: boolean;
}

const initialState: ChatThreadState = {
  currentChatPageThreadId: "",
  currentThreadName: "",
  history: false,
  newThread: false,
  showMoreFlightsOption: false,
  allFlights: {},
  tripType: "",
  chatResult: {},
  chatHistory: {},
  chatHistoryLoaded: false,
  isRefreshingHistory: false,
  shouldRefreshHistory: false,
};

const chatThreadSlice = createSlice({
  name: 'chatThread',
  initialState,
  reducers: {
    setCurrentChatPageThreadId: (state, action: PayloadAction<string>) => {
      state.currentChatPageThreadId = action.payload;
    },
    clearCurrentChatPageThreadId: (state) => {
      state.currentChatPageThreadId = "";
    },
    updateCurrentThreadInfo: (state, action: PayloadAction<Object>) =>{
        return { ...state, ...action.payload };
    },
    clearChatThreadState: (state) => {
      state.currentChatPageThreadId = "";
      state.currentThreadName = "";
      state.history = false;
      state.newThread = false;
      state.showMoreFlightsOption = false;
      state.allFlights = {};
      state.tripType = "";
      state.chatResult = {};
    }
  },
});

export const {
  setCurrentChatPageThreadId,
  clearCurrentChatPageThreadId,
  updateCurrentThreadInfo,
  clearChatThreadState
} = chatThreadSlice.actions;

export default chatThreadSlice.reducer;
