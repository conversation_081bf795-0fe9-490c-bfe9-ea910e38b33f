import { travellerPlans } from '@/constants';
import { motion } from 'framer-motion';
import { useEffect } from 'react';

const AlternateTravellerPlan = () => {
  return (
    <section className="px-4 py-6">
      {/* Heading */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="text-center max-w-[15rem] mx-auto flex flex-col items-center"
      >
        <h3 className="font-proxima-nova text-2xl font-bold
            leading-tight text-brand-black">
          I Have a Plan for Every Traveller
        </h3>
        <p className="font-proxima-nova text-sm text-[#080236] leading-tight mt-2">
          I've got the perfect itinerary waiting for you. Just tell me your vibe,
          and I'll curate the best plan for you!
        </p>
      </motion.div>

      {/* Cards */}
      <div className="mt-6 flex flex-col gap-6 items-center">
        {travellerPlans.map((plan, idx) => (
          <motion.div
            key={idx}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: idx * 0.15 }}
            className="rounded-lg overflow-hidden w-[315px] h-[270px]"
            style={{
              border: '1px solid transparent',
              backgroundImage: 'linear-gradient(white, white), linear-gradient(315deg, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)',
              backgroundOrigin: 'border-box',
              backgroundClip: 'content-box, border-box'
            }}
          >
            <img
              src={plan.img}
              alt={plan.title}
              className="w-full h-[187.81px] object-cover"
            />
            <div className="pt-1 text-center">
              <h2 className='font-proxima-nova font-bold text-lg text-center
                    text-lucky-blue p-0'>{plan.title}</h2>
              <p className='font-proxima-nova font-medium text-sm text-[#080236] 
                        text-center'>{plan.desc}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        whileInView={{ opacity: 1, scale: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, ease: 'backOut', delay: 0.2 }}
        className="mt-3 flex justify-center"
      >
        <button
          className="rounded-full px-2 py-2 font-proxima-nova 
                text-base text-white font-semibold linear-button-gradient mt-5
                w-[148px] h-[32px] flex items-center justify-center"
        >
          Plan My Trip
        </button>
      </motion.div>
    </section>
  )
}

export default AlternateTravellerPlan