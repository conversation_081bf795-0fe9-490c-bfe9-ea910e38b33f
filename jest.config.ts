// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

/**
 * @type {import('jest').Config}
 */
const customJestConfig = {
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
 transformIgnorePatterns: [
  "/node_modules/(?!react-markdown|remark-parse|remark-rehype|unified|bail|trough|vfile|is-plain-obj|mdast-util-from-markdown|mdast-util-to-hast|hast-util-to-html|micromark|decode-named-character-reference|character-entities|property-information|space-separated-tokens|comma-separated-tokens|web-namespaces)"
],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
};

module.exports = createJestConfig(customJestConfig);



