import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChevronDown, Circle, PlaneLanding, PlaneTakeoff } from "lucide-react";
import { Flight, Airport, CabinClass } from "@/constants/models";
import Image from "next/image";
import { useEffect, useState } from "react";
import { getCurrencySymbol } from "@/lib/utils/formatPrice";
import { Badge } from "@/components/ui/badge";
interface FlightCardProps {
  flight: Flight;
  flightType: "OUT-BOUND" | "INBOUND";
  showDetails?: boolean;
  onCardSelect?: (picked_flight: Flight, picked_class: string) => void;
  airport: { [iataCode: string]: Airport };
  flight2?: Flight;
  showPrice?: boolean;
  class_type?: string;
}

type Hubrouting = {
  status: boolean;
  hub_routing_text: string;
  hub_airport_dept: string;
};

// Add type for segment to include 'duration'
type DummySegment = {
  departure_time: string;
  departure_day: string;
  arrival_time: string;
  arrival_day: string;
  origin: string;
  destination: string;
  depart_iota_code: string;
  arrival_iota_code: string;
  departure_airport: string;
  arrival_airport: string;
  airline_info: string;
  flight_duration: string;
  duration: string;
  connection_info: string;
  is_first: boolean;
  operator_logo: string;
  operator: string;
  flight_code: string;
  departure_time_24hr: string;
  arrival_time_24hr: string;
  departure_date: string;
  arrival_date: string;
};

// Replace all prop usage with static data
const dummyAirport: {
  [key: string]: {
    iata_code: string;
    airport_name: string;
    city_name_original: string;
    country_name: string;
    country_code: string;
  };
} = {
  IXR: {
    iata_code: "IXR",
    airport_name: "Ranchi Airport",
    city_name_original: "Ranchi",
    country_name: "India",
    country_code: "IN",
  },
  BOM: {
    iata_code: "BOM",
    airport_name: "Chhatrapati Shivaji Maharaj International Airport",
    city_name_original: "Mumbai",
    country_name: "India",
    country_code: "IN",
  },
  RKT: {
    iata_code: "RKT",
    airport_name: "Ras al Khaimah International Airport",
    city_name_original: "Ras al Khaimah",
    country_name: "UAE",
    country_code: "AE",
  },
};

// Add a type for the flight prop
interface FlightDetailsProps {
  flight?: {
    type: string;
    date: string;
    duration: string;
    time_note: string;
    note: string;
    segments: Array<{
      departure_time: string;
      departure_day: string;
      arrival_time: string;
      arrival_day: string;
      depart_iota_code: string;
      arrival_iota_code: string;
      departure_airport: string;
      arrival_airport: string;
      airline_info: string;
      flight_duration: string;
      connection_info: string;
      is_first: boolean;
      operator_logo: string;
    }>;
    route: string;
    flight_codes: string[];
    supplier: string;
    supplier_logo: string;
  };
  tf_reference: string;
  status: string;
}

export default function FlightDetails({
  flight,
  tf_reference,
  status,
}: FlightDetailsProps) {
  const flightData = flight;
  if (!flightData) return null; // <-- concise guard

  const [show, setShow] = useState(true);
  const [hubrouting, setHubrouting] = useState<Hubrouting>({
    hub_airport_dept: "",
    status: false,
    hub_routing_text: "",
  });

  useEffect(() => {
    if (
      flightData &&
      Array.isArray(flightData.segments) &&
      flightData.segments.length > 1
    ) {
      setHubrouting(processFlightData(flightData));
    }
  }, [flightData]);

  function processFlightData(flightData: any) {
    const h_route_data: Hubrouting = hubrouting;
    if (
      flightData &&
      Array.isArray(flightData.segments) &&
      flightData.segments.length > 1
    ) {
      h_route_data.status = true;
      h_route_data.hub_routing_text = `${flightData.segments.length - 1} Stops`;
      flightData.segments.forEach((item: any, indx: number) => {
        if (indx === 0 && item.destination) {
          h_route_data.hub_routing_text =
            h_route_data.hub_routing_text + ` | ${item.destination}`;
        }
      });
    }
    if (h_route_data.status) {
      return h_route_data;
    }
    return { hub_airport_dept: "", status: false, hub_routing_text: "" };
  }

  const isCompleted = status?.toLowerCase() === "completed";
  const textColor = isCompleted ? "text-neutral-dark" : "text-black";
  function renderFlightSegments(flight: any) {
    return flight.segments.map((segment: any, idx: number) => (
      <div key={idx} className="flex flex-col gap-4">
        {/* Segment details row */}
        <div className="flex justify-start gap-10">
          <div className="flex flex-col justify-between gap-6">
            <p className={`font-semibold ${textColor}`}>
              {segment.departure_time_24hr || segment.departure_time}
            </p>
            <p className="text-sm text-[#999999]">
              {segment.duration || flight.duration}
            </p>
            <p className={`font-semibold mb-4 ${textColor}`}>
              {segment.arrival_time_24hr || segment.arrival_time}
            </p>
          </div>
          <div className="flex gap-8">
            <div className="flex flex-col items-center">
              {/* Icon logic: takeoff for first, circle for middle, landing for last */}
              {idx === 0 ? (
                <PlaneTakeoff className={`w-5 h-5 ${textColor}`} />
              ) : (
                <Circle className={`w-5 h-5 ${textColor} mb-2`} />
              )}
              <div className="border-l-2 border-[#0000001A] flex-1 my-2"></div>
              {idx === flight.segments.length - 1 ? (
                <PlaneLanding className={`w-5 h-5 ${textColor} mb-4`} />
              ) : (
                <Circle className={`w-5 h-5 ${textColor} mb-4`} />
              )}
            </div>
            <div className="flex flex-col justify-between flex-1">
              <div>
                <p
                  className={`font-semibold md:text-base text-sm ${textColor}`}
                >
                  {dummyAirport?.[segment.depart_iota_code as string]
                    ?.airport_name
                    ? `${dummyAirport[segment.depart_iota_code as string].airport_name} (${segment.depart_iota_code})`
                    : segment.depart_iota_code}
                </p>
                <p className="text-[#999999] text-xs">{`${segment.airline_info || ""} | Economy`}</p>
                <p className="text-[#999999] text-xs">
                  Departs {segment.departure_day || ""}
                </p>
              </div>
              <div>
                <p
                  className={`font-semibold md:text-base text-sm ${textColor}`}
                >
                  {dummyAirport?.[segment.arrival_iota_code as string]
                    ?.airport_name
                    ? `${dummyAirport[segment.arrival_iota_code as string].airport_name} (${segment.arrival_iota_code})`
                    : segment.arrival_iota_code}
                </p>
                <p className="text-[#999999] text-xs">
                  Arrives {segment.arrival_day || ""}
                </p>
              </div>
            </div>
          </div>
        </div>
        {/* Layover info between segments */}
        {idx < flight.segments.length - 1 && (
          <div className="bg-[#F2F2FF] py-3 px-4 my-2 rounded-lg">
            <p className="text-brand text-sm">
              {`${segment.connection_info || ""} Layover at ${dummyAirport?.[flight.segments[idx + 1].depart_iota_code as string]?.airport_name || flight.segments[idx + 1].depart_iota_code}`}
            </p>
          </div>
        )}
      </div>
    ));
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Details for current trip */}
      {show && (
        <Card>
          <CardContent className="p-0">
            <div className="flex  md:flex-row flex-col justify-between items-center px-4 py-3">
              <div className="flex flex-row w-full md:max-w-20 md:flex-col justify-between items-left">
                <div className="text-[#999999] font-medium text-sm md:text-base w-24 uppercase">
                  {flightData.type}
                </div>
                <div className="flex gap-3 pt-1">
                  {flightData.segments.map((item, idx) => (
                    <img
                      key={idx}
                      src={item.operator_logo || flightData.supplier_logo}
                      alt="Operator Logo"
                      className="h-6 w-6 object-contain"
                    />
                  ))}
                </div>
              </div>
              <div className="flex gap-2">
                <div className="grid">
                  <p className={`${textColor} font-semibold`}>
                    {flightData.segments[0].departure_time}
                  </p>
                  <p className="text-[#999999] text-center text-sm">
                    {flightData.segments[0].depart_iota_code}
                  </p>
                </div>
                <div className="grid min-w-28 md:min-w-40">
                  <p className="text-[#999999] text-center text-xs md:text-sm md:pb-0 pb-2">
                    {flightData.duration}
                  </p>
                  <div className="flex items-center justify-center relative">
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <div className="flex-1 border-t border-dashed border-gray-400 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="15"
                          viewBox="0 0 16 15"
                          fill="none"
                        >
                          <path
                            d="M10.2898 8.5L6.5 14.5L5 14.5L6.8945 8.5L2.8745 8.5L1.625 10.75L0.500001 10.75L1.25 7.375L0.500002 4L1.625 4L2.87525 6.25L6.89525 6.25L5 0.25L6.5 0.25L10.2898 6.25L14.375 6.25C14.6734 6.25 14.9595 6.36853 15.1705 6.57951C15.3815 6.79048 15.5 7.07663 15.5 7.375C15.5 7.67337 15.3815 7.95952 15.1705 8.1705C14.9595 8.38148 14.6734 8.5 14.375 8.5L10.2898 8.5Z"
                            fill="#1E1E76"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  </div>
                  <p className="text-[#999999] text-center text-xs md:text-sm pt-2">
                    {hubrouting.status
                      ? hubrouting.hub_routing_text
                      : "Non-Stop"}
                  </p>
                </div>
                <div className="grid">
                  <p className={`${textColor} font-semibold`}>
                    {
                      flightData.segments[flightData.segments.length - 1]
                        .arrival_time
                    }
                  </p>
                  <p className="text-[#999999] text-center text-sm">
                    {
                      flightData.segments[flightData.segments.length - 1]
                        .arrival_iota_code
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-4 border-t px-4 py-3">
              {/* <div className="text-sm">
              <Badge className="bg-warning hover:bg-none">
                scheduled
              </Badge>
            </div> */}
              {renderFlightSegments(flightData)}
            </div>
            <div className={`px-4 py-3 text-sm ${textColor}`}>
              Arrives: {flight.segments[flight.segments.length - 1].arrival_day}
              {flight.segments[flight.segments.length - 1].arrival_time
                ? `, ${flight.segments[flight.segments.length - 1].arrival_time}`
                : ""}{" "}
              | {/* This is the vertical bar */} Journey Duration:{" "}
              {flight.duration}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
