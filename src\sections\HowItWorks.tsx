import React from "react";
import { motion } from "framer-motion";
import { fadeIn } from "@/utils/motion";

const HowItWorks = () => {
  return (
    <section className="flex w-full min-h-screen bg-brand-white">
      <div className="flex flex-col gap-10 sm:gap-2 xs:gap-2 w-[85%] mx-auto items-center py-12 sm:py-6">
        {/* Left Content */}
        <motion.div
          initial="hidden"
          whileInView="show"
          variants={fadeIn("", "", 0.5, 0.5)}
          className="lg:text-5xl md:text-4xl sm:text-3xl xs:text-3xl font-bold font-proxima-nova text-center md:text-left text-brand-black"
        >
          How I Work?
        </motion.div>
        <div className="flex flex-col sm:gap-2 md:gap-5 font-proxima-nova">
          <div className="w-full h-full flex sm:flex-row xs:flex-col xl:gap-10 md:gap-5 justify-between md:py-10 sm:py-2 xs:py-2 group">
            <div className="mt-6 xs:mt-0 w-full py-5 flex flex-col justify-center">
              <div className="flex flex-col w-[100%]">
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.25, 0.5)}
                  className="xl:text-5xl md:text-3xl sm:text-2xl xs:text-2xl font-bold bg-gradient-to-b  from-[#1E1E76] to-[#4B4BC3] text-transparent bg-clip-text"
                >
                  01
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.5, 0.5)}
                  className="xl:text-5xl lg:text-4xl md:text-2xl sm:text-lg xs:text-xl font-bold text-brand-black mt-1"
                >
                  Start Chatting with Me
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.75, 0.5)}
                  className="text-[#080236] mt-2 lg:text-base md:text-sm sm:text-xs xl:w-[70%] lg:w-[85%] md:w-[90%] sm:w-[85%] leading-relaxed"
                >
                  Tell me what you fancy - a wild adventure, a chilled beach
                  escape, or maybe a hidden gem only the locals know about? Be
                  as detailed as you like or let me jump in with instant prompts
                  and suggestions while we chat!
                </motion.div>
              </div>
            </div>

            {/* Right Image */}
            <motion.div
              initial="hidden"
              whileInView="show"
              variants={fadeIn("", "", 0.75, 0.5)}
              className="relative mt-10 xs:mt-4 md:mt-0 w-full flex justify-center"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/How%20It%20Works/HowDoIWork_IMGNEW1.png"
                alt="Chat UI"
                className="w-full h-full transform transition-transform duration-500 "
              />
            </motion.div>
          </div>
          <div className="w-full h-full flex sm:flex-row-reverse xs:flex-col gap-10 xs:gap-4 justify-between md:py-10 xs:py-2 sm:py-2 group">
            <div className="mt-6 xs:mt-0 w-full flex flex-col justify-center">
              <div className="flex flex-col xl:w-[80%] lg:w-[90%] mx-auto">
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.25, 0.5)}
                  className="xl:text-5xl md:text-3xl sm:text-2xl xs:text-2xl font-bold bg-gradient-to-b  from-[#1E1E76] to-[#4B4BC3] text-transparent bg-clip-text"
                >
                  02
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.5, 0.5)}
                  className="xl:text-5xl lg:text-4xl md:text-2xl sm:text-lg xs:text-xl font-bold text-brand-black mt-2"
                >
                  Get Your Custom Travel Plan
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.75, 0.5)}
                  className="text-[#080236] mt-2 lg:text-base md:text-sm sm:text-xs xl:w-[80%] lg:w-[90%] md:w-[95%] leading-relaxed"
                >
                  I’ll curate a personalised itinerary for you - a complete plan
                  with experiences, photos, reviews, and maps for reference. But
                  hey, this is your trip! Feel free to tweak, add, or remove
                  anything to match your vibe. You spotted something exciting
                  while browsing? Just favourite it, and I’ll slot it right into
                  your plan!
                </motion.div>
              </div>
            </div>

            {/* Left Image */}
            <motion.div
              initial="hidden"
              whileInView="show"
              variants={fadeIn("", "", 0.75, 0.5)}
              className="relative mt-10 xs:mt-4 md:mt-0 w-full flex justify-center group"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/How%20It%20Works/HowDoIWork_IMG2.png"
                alt="Chat UI"
                className="rounded-lg w-full h-full transform transition-transform duration-500 "
              />
            </motion.div>
          </div>
          <div className="w-full h-full flex sm:flex-row xs:flex-col xs:gap-4 justify-between md:py-10 xs:py-2 sm:py-2 group">
            <div className="mt-6 xs:mt-0 w-full flex flex-col justify-center">
              <div className="flex flex-col w-[100%]">
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.25, 0.5)}
                  className="xl:text-5xl md:text-3xl sm:text-2xl xs:text-2xl font-bold bg-gradient-to-b  from-[#1E1E76] to-[#4B4BC3] text-transparent bg-clip-text"
                >
                  03
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.5, 0.5)}
                  className="xl:text-5xl lg:text-4xl md:text-2xl sm:text-lg xs:text-xl font-bold text-brand-black mt-1"
                >
                  Plan with Your Crew
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.75, 0.5)}
                  className="text-[#080236] mt-2 lg:text-base md:text-sm sm:text-xs xl:w-[80%] lg:w-[90%] md:w-[95%] sm:w-[85%] leading-relaxed"
                >
                  Are you travelling with your friends? Get them involved! Share
                  your itinerary, split expenses, and make adjustments together.
                  No more back-and-forth messages - just clean and clear
                  coordination.
                </motion.div>
              </div>
            </div>

            {/* Right Image */}
            <motion.div
              initial="hidden"
              whileInView="show"
              variants={fadeIn("", "", 0.75, 0.5)}
              className="relative mt-10 xs:mt-4 md:mt-0 w-full flex justify-center"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/How%20It%20Works/HowDoIWork_IMG3.png"
                alt="Chat UI"
                className="rounded-lg w-full h-full transform transition-transform duration-500 "
              />
            </motion.div>
          </div>
          <div className="w-full h-full flex flex-row-reverse xs:flex-col gap-10 xs:gap-4 justify-between md:py-10 xs:py-2 sm:py-2 group">
            <div className="mt-6 xs:mt-0 w-full flex flex-col justify-center">
              <div className="flex flex-col xl:w-[80%] lg:w-[90%] mx-auto">
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.25, 0.5)}
                  className="xl:text-5xl md:text-3xl sm:text-2xl xs:text-2xl font-bold bg-gradient-to-b from-[#1E1E76] to-[#4B4BC3]text-transparent bg-clip-text"
                >
                  04
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.5, 0.5)}
                  className="xl:text-5xl lg:text-4xl md:text-2xl sm:text-lg xs:text-xl font-bold text-brand-black mt-2"
                >
                  Keep Everything in One Place
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn("", "", 0.75, 0.5)}
                  className="text-[#080236] mt-4 lg:text-base md:text-sm sm:text-xs xl:w-[80%] lg:w-[90%] md:w-[95%] leading-relaxed"
                >
                  This is an amazing feature I offer! You can store every
                  booking, ticket, and travel document in one place as a handy
                  PDF. Don’t waste time scrolling through emails! And yes, I’ll
                  keep you informed with instant notifications throughout your
                  trip – just like a guide!
                </motion.div>
              </div>
            </div>

            {/* Left Image */}
            <motion.div
              initial="hidden"
              whileInView="show"
              variants={fadeIn("", "", 0.75, 0.5)}
              className="relative mt-10 xs:mt-4 md:mt-0 w-full flex justify-center"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/How%20It%20Works/HowDoIWork_IMG4.png"
                alt="Chat UI"
                className="rounded-lg w-full h-full transform transition-transform duration-500 "
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
