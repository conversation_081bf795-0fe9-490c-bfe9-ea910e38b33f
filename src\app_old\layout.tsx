import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
//import { cn } from "@/lib/utils"
import type React from "react"; // Import React
import "./globals.css";
import { useEffect } from "react";
import Script from "next/script";
import { ReCaptchaProvider } from "@/components/GoogleReCaptchaProvider/recaptcha-provider";
import { AuthProvider } from "@/components/AuthProvider/auth-Provider";
import FlightProvider from "@/context/FlightContext";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Travel App",
  description: "Your next adventure starts here",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0"
        />
        <link
          href="https://use.typekit.net/emv3zbo.css"
          rel="stylesheet"
          crossOrigin="anonymous"
        />
        <link
          rel="stylesheet"
          href="https://use.typekit.net/eqy0tlg.css"
          crossOrigin="anonymous"
        ></link>
        <Script
          src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
          strategy="beforeInteractive"
        />
      </head>
      <ReCaptchaProvider>
        <AuthProvider>
            <body className={`${inter.className} font-proxima-nova`}>
              {children}
            </body>
        </AuthProvider>
      </ReCaptchaProvider>
    </html>
  );
}