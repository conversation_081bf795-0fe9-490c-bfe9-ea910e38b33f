import { PaymentFormData } from "@/constants/models";
import React, { createContext, ReactNode, useContext, useState } from "react";

interface PaymentDetailsContextType {
    paymentFormData: PaymentFormData;
    setPaymentFormData: (data: PaymentFormData) => void;
}

const PaymentDetailsContext = createContext<PaymentDetailsContextType | undefined>(undefined);

export const PaymentDetailsProvider = ({ children }: { children: ReactNode }) => {
    const [paymentFormData, setPaymentFormData] = useState<PaymentFormData>({} as PaymentFormData);

    return (
        <PaymentDetailsContext.Provider value={{ paymentFormData, setPaymentFormData }}>
          {children}
        </PaymentDetailsContext.Provider>
    );
}

export const usePaymentDetails = () => {
    const context = useContext(PaymentDetailsContext);
    if (!context) {
      throw new Error("usePaymentDetails must be used within a PaymentDetailsProvider");
    }
    return context;
};