import React from 'react';

interface FormInputProps {
  type: string;
  name: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

const FormInput: React.FC<FormInputProps> = ({
  type,
  name,
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur
}) => {
  return (
    <input
      type={type}
      name={name}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onFocus={onFocus}
      onBlur={onBlur}
      className="w-full px-4 xs:px-2 xs:py-1 py-2 text-base focus:outline-none xs:text-sm placeholder:xs:text-xs bg-brand-white placeholder-neutral-dark"
      style={styles.inputBox}
    />
  );
};

const styles = {
  inputBox: {
    border: '1px solid #EBEBEB',
    borderRadius: "8px",
    color: '#999999'
  }
}

export default FormInput;