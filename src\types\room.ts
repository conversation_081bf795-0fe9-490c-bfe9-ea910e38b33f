import type React from "react";

export interface Room {
  id: string;
  roomType: string;
  bedType: string;
  size: number;
  sleeps: number;
  price: number;
  totalPrice: number;
  nights: number;
  discount: number;
  refundable: boolean;
  refundableUntil?: string;
  images: string[];
  amenities: {
    icon: React.ReactNode;
    name: string;
  }[];
  description: string;
  reviews: {
    count: number;
    overall: number;
    categories: {
      [key: string]: number;
    };
  };
  facilities: string[];
}
