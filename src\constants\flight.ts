export const TravellerOptions = [
  { label: "Adults", desc: "Ages 13 or above", key: "adults" },
  { label: "Children", desc: "Ages 2-12", key: "children" },
  { label: "Infants", desc: "Under 2", key: "infants" },
];

export const FlightClasses = ["Economy With Restrictions", "Economy Without Restrictions", "Economy Premium", "Business","First"];
export const TripOptions = ["One Way", "Round Trip", "Multi-Trip"];
export const StopsOptions = ["Direct", "One Stop", "2+ Stops"];

export const activePages = {
  chat: "chat",
  flight_search: "flight_search",
  flight_summary: "flight_summary",
  trip_summary: "trip_summary"
}
