"use client"

import { ChevronLeft } from "lucide-react"
import { useRouter } from "next/router"
import { useDispatch } from "react-redux"
import { updateTripSummary } from "@/store/slices/tripSummary"
import { activePages } from "@/constants/flight"

interface StepHeaderProps {
    currentStep: number
    setCurrentStep: (step: number) => void
}

const StepHeader = ({ currentStep, setCurrentStep }: StepHeaderProps) => {
    const router = useRouter()
    const dispatch = useDispatch()

    const handleBackClick = () => {
        if (currentStep === 1) {
            dispatch(
                updateTripSummary({
                    activePage: activePages.flight_summary,
                    from: activePages.trip_summary,
                }),
            )
            router.push("/flightsummary")
        }
        if (currentStep === 2) setCurrentStep(1)
    }

    const getBackText = () => {
        if (currentStep === 1) return "Trip Summary"
        if (currentStep === 2) return "Trip Summary"
        return ""
    }

    return (
        <div className="text-brand-grey flex flex-row w-full items-center justify-between">
            <div className="flex items-center mb-4">
                <ChevronLeft className="w-5 h-5" />
                <span
                    className="text-xs xs:text-sm sm:text-base lg:text-2xl whitespace-normal font-bold cursor-pointer hover:underline"
                    onClick={handleBackClick}
                >
                    {getBackText()}
                </span>
            </div>
        </div>
    )
}

export default StepHeader
