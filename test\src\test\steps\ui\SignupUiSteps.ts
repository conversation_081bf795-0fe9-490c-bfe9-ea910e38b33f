import { Given, When, Then } from '@cucumber/cucumber';
import { Properties } from '../../properties/Properties';
import { SignupPage } from '../../pages/SignupPage';
import { expect } from '@playwright/test';

Given('The user is on the signup page', async function () {
    await SignupPage.goto('https://qa.nxvoytrips.ai/signup');
});

When('The user enters the valid first name for signup', async function () {
    await SignupPage.typeFirstName(Properties.getProperty('signup.valid.firstName'));
});

When('The user enters the valid last name for signup', async function () {
    await SignupPage.typeLastName(Properties.getProperty('signup.valid.lastName'));
});

When('The user enters the valid username for signup', async function () {
    await SignupPage.typeUsername(Properties.getProperty('signup.valid.username'));
});

When('The user enters the valid password for signup', async function () {
    await SignupPage.typePassword(Properties.getProperty('signup.valid.password'));
});

When('The user enters the valid confirm password for signup', async function () {
    await SignupPage.typeConfirmPassword(Properties.getProperty('signup.valid.confirmPassword'));
});

When('The user submits the signup form', async function () {
    await SignupPage.submit();
});

Then('The user should see a verification code prompt', async function () {
    expect(await SignupPage.isVerificationPromptVisible()).toBe(true);
});

When('The user enters the invalid first name for signup', async function () {
    await SignupPage.typeFirstName(Properties.getProperty('signup.invalid.firstName'));
});

When('The user enters the invalid last name for signup', async function () {
    await SignupPage.typeLastName(Properties.getProperty('signup.invalid.lastName'));
});

When('The user enters the invalid username for signup', async function () {
    await SignupPage.typeUsername(Properties.getProperty('signup.invalid.username'));
});

When('The user enters the invalid password for signup', async function () {
    await SignupPage.typePassword(Properties.getProperty('signup.invalid.password'));
});

When('The user enters the invalid confirm password for signup', async function () {
    await SignupPage.typeConfirmPassword(Properties.getProperty('signup.invalid.confirmPassword'));
});

Then('The user should see a signup error message', async function () {
    expect(await SignupPage.isSignupErrorVisible()).toBe(true);
}); 