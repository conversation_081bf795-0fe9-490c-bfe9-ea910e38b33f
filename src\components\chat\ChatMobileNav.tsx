"use client"

import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON><PERSON>ger, SheetClose } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MenuButton } from "./ui/menu-button"
import { CollapsibleSection } from "./ui/collapsible-section"
import { UserHeader } from "./ui/user-header"
import { ChatHistoryItem } from "./chatAction/chat-history-item"

import { useEffect, useState, useRef } from "react"
import axios from "axios"
import { format } from "date-fns"
import { useRouter } from "next/router"
import { AppState, persistor } from "@/store/store";
import { useSelector, useDispatch } from "react-redux"
import { setCurrentChatPageThreadId } from "@/store/slices/chatThread"
import { useCustomSession } from "@/hooks/use-custom-session"
import { getInitials } from "@/screens/dashboard/DashboardNavbar"
import { Undo2, Trash2, <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { useChatContext } from "@/context/ChatContext"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { useSearchParams } from 'next/navigation';
import { updateCurrentThreadInfo } from '@/store/slices/chatThread';
import { NEW_THREAD_DEFAULT_NAME } from "@/constants/chat"
import { logoutMethod } from "@/utils/auth";
import { logout } from "@/store/slices/authSlice";
import { signOut } from "next-auth/react";
import { clearReduxOnLogout } from "@/store/clearRedux"

type Thread = {
  thread_id: string
  thread_name: string
  updated_at: string
  created_at: string
  message_count: number
}

type GroupedThreads = Record<string, Thread[]>

const ChatMobileNav = () => {
  const currentUser = useSelector((state: AppState) => state.loggedInUser)
  const { currentUserDetails } = useSelector((state: AppState) => state.userDetails)
  const dispatch = useDispatch()
  const router = useRouter()
  const [threads, setThreads] = useState<GroupedThreads>({})
  const [alertDialogOpen, setAlertDialogOpen] = useState(false)
  const [threadToDelete, setThreadToDelete] = useState<Thread | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [threadToEdit, setThreadToEdit] = useState<Thread | null>(null)
  const { data: session } = useCustomSession()
  const searchParams = useSearchParams();
  const sheetCloseRef = useRef<HTMLButtonElement>(null)
  const token = session?.accessToken;

  const { setNewChatThreadId, refreshChatHistory, setRefreshChatHistory, setCurrentThreadTitle, setLoginModal } = useChatContext()

  // Icons for menu items
  const icons = {
    newChat: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
      </svg>
    ),
    chatHistory: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
      </svg>
    ),
    userProfile: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
      </svg>
    ),
    backToHome: <Undo2 width="20" height="20" />,
    signOut: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" />
      </svg>
    ),
  }

  // Load chat history
  useEffect(() => {
    if (currentUser?.user?.email || session?.accessToken) {
      loadChatHistory()
    }
  }, [currentUser, session, refreshChatHistory])

  // Reset refresh flag after loading
  useEffect(() => {
    if (refreshChatHistory) {
      setRefreshChatHistory(false)
    }
  }, [refreshChatHistory, setRefreshChatHistory])

  const loadChatHistory = async () => {
    try {
      const token = session?.accessToken
      if (!token) return

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/list`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        },
      )

      if (response.status === 200) {
        const data: Thread[] = response.data.detail.data.data
        const grouped: GroupedThreads = data.reduce((acc: GroupedThreads, thread: Thread) => {
          const month = format(new Date(thread.created_at), "MMMM")
          if (!acc[month]) acc[month] = []
          acc[month].push(thread)
          return acc
        }, {})
        setThreads(grouped)
      }
    } catch (error) {
      console.error("Failed to load chat history:", error)
    }
  }

  const handleNewChat = async () => {
    try {
      console.log('handleNewChat==========')
      if (!token) {
        setLoginModal(true);
      } else {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/thread/generate?_new=true`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "application/json",
            },
          }
        );

        if (response.status !== 200) throw new Error("Failed to generate thread");
        console.log('new thread Id created=======', response);
        const newThreadId = response.data.detail.data.thread_id;
        const query = { ...router.query };
        delete query.thread_id;
        router.replace(
          {
            pathname: router.pathname,
            query: query,
          },
          undefined,
          { shallow: true }
        );
        dispatch(updateCurrentThreadInfo({ currentChatPageThreadId: newThreadId, currentThreadName: NEW_THREAD_DEFAULT_NAME, history: false, newThread: true, allFlights: {}, tripType: '', chatResult: {} }));
        setNewChatThreadId(newThreadId);
        setCurrentThreadTitle("New Chat");
        sheetCloseRef.current?.click()
      }

    } catch (error: any) {
      console.error("Failed to start new chat:", error.message);
    }
  };

  const handleChatThread = (item: Thread) => {
    const params = new URLSearchParams(searchParams);
    params.set('thread_id', item.thread_id);
    router.replace(`?${params.toString()}`);
    dispatch(updateCurrentThreadInfo({ currentChatPageThreadId: item.thread_id, currentThreadName: item.thread_name, history: true, newThread: false, allFlights: {}, tripType: '', chatResult: {} }));
    setCurrentThreadTitle(item.thread_name);
    sheetCloseRef.current?.click()
  }

  const deleteThread = async (threadId: string) => {
    try {
      const token = session?.accessToken
      await axios.delete(`${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread`, {
        data: { thread_id: threadId },
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      })
      setRefreshChatHistory(true)
    } catch (error) {
      console.error("Failed to delete thread:", error)
    }
  }

  const editThread = async (threadId: string, newName: string) => {
    if (!newName.trim()) return

    try {
      const token = session?.accessToken
      await axios.put(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread-name`,
        {
          thread_id: threadId,
          thread_name: newName,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        },
      )
      setRefreshChatHistory(true)
      dispatch(
        updateCurrentThreadInfo({
          currentThreadName: newName || "",
        })
      );
    } catch (error) {
      console.error("Failed to rename thread:", error)
    }
  }

  const handleLogout = async () => {
    const response = await logoutMethod("auth/signout", token);
    if (response.success) {
      await clearReduxOnLogout();
      signOut({ redirect: false });
      handleNavigateHome();
    }
  };

  const handleNavigateHome = () => {
    router.push("/");
  };

  const handleSectionChange = (section: string) => {
    sheetCloseRef.current?.click()

    if (section === "Sign out") {
      // Handle sign out logic
      handleLogout()

    } else if (section === "Back to home") {
      router.push("/")
    } else if (section === "My Account") {
      router.push("/userprofile")
    }
  }

  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild className="[&>svg]:hidden">
          <div className="flex border mt-4 mx-2 p-2 px-4 justify-between items-center rounded-full">
            <div>
              <img
                alt="logo"
                src={`https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png`}
                className="h-[19px] w-[75px]"
              />
            </div>
            <div>
              <Button variant="ghost" size="icon">
                <img
                  className="h-[18px] w-[18px]"
                  src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Expansion.png"
                />
              </Button>
            </div>
          </div>
        </SheetTrigger>

        <SheetContent
          side="right"
          className="w-4/5 h-full px-0 pt-0 bg-white border-l border-gray-200 shadow-lg rounded-l-2xl overflow-y-auto"
        >
          <SheetClose ref={sheetCloseRef} className="hidden" />

          <div className="w-full flex flex-col h-full bg-white">
            {/* Header with user profile */}
            <UserHeader
              profilePicture={currentUserDetails?.profile_picture}
              firstName={currentUser?.firstName}
              lastName={currentUser?.lastName}
              getInitials={getInitials}
              user={currentUserDetails}
            />

            {/* Menu Items */}
            <div className="flex-1 p-6 space-y-6">
              {/* New Chat Button */}
              <MenuButton icon={<SquarePen />} label="New Chat" onClick={handleNewChat} className="w-full relative flex flex-row gap-2 items-center text-center px-4 py-3 rounded-full justify-center bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)] text-white" />

              {/* Chat History - Collapsible */}
              {currentUser?.id && Object.keys(threads).length > 0 && (
                <CollapsibleSection title="Chat History" icon={icons.chatHistory} defaultOpen={true}>
                  {Object.entries(threads).map(([month, items]) => (
                    <div key={month} className="space-y-1">
                      <div className="text-sm font-semibold text-gray-600">{month}</div>
                      {items.map((item, i) => (
                        <ChatHistoryItem
                          key={`${month}-${i}`}
                          threadName={item.thread_name}
                          onThreadClick={() => handleChatThread(item)}
                          onEdit={() => {
                            setThreadToEdit(item)
                            setEditDialogOpen(true)
                          }}
                          onDelete={() => {
                            setThreadToDelete(item)
                            setAlertDialogOpen(true)
                          }}
                        />
                      ))}
                    </div>
                  ))}
                </CollapsibleSection>
              )}

              {/* My Account */}
              <MenuButton
                icon={icons.userProfile}
                label="My Account"
                onClick={() => handleSectionChange("My Account")}
              />

              {/* Back to home */}
              <MenuButton
                icon={icons.backToHome}
                label="Back to home"
                onClick={() => handleSectionChange("Back to home")}
              />

              {/* Sign out */}
              <MenuButton icon={icons.signOut} label="Sign out" onClick={() => handleSectionChange("Sign out")} />


            </div>
          </div>

          {/* Delete Dialog */}
          <AlertDialog
            open={alertDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                setAlertDialogOpen(false)
                setThreadToDelete(null)
              }
            }}
          >
            <AlertDialogContent className="flex flex-col items-center">
              <AlertDialogHeader className="flex flex-col items-center">
                <div>
                  <Trash2 />
                </div>
                <AlertDialogTitle className="text-[#080236]">Confirm Delete Trip</AlertDialogTitle>
                <AlertDialogDescription className="font-medium text-[#080236]">
                  Are sure you want to delete {threadToDelete && threadToDelete.thread_name} ?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter className="flex w-full items-center gap-y-2">
                <AlertDialogAction
                  className="bg-white text-black border border-[#1E1E76] w-3/6 rounded-2xl hover:bg-white"
                  onClick={() => {
                    if (threadToDelete) {
                      deleteThread(threadToDelete.thread_id)
                    }
                    setAlertDialogOpen(false)
                    setThreadToDelete(null)
                  }}
                >
                  Delete
                </AlertDialogAction>
                <AlertDialogCancel className="hover:bg-[#4B4BC3] w-3/6 hover:text-[#F2F3FA] bg-[#4B4BC3] text-[#F2F3FA] rounded-2xl">
                  Cancel
                </AlertDialogCancel>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Edit Dialog */}
          <AlertDialog
            open={editDialogOpen}
            onOpenChange={(open) => {
              if (!open) {
                setEditDialogOpen(false)
                setThreadToEdit(null)
              }
            }}
          >
            <AlertDialogContent className="flex flex-col items-center">
              <AlertDialogHeader className="flex flex-col items-center">
                <div>
                  <img className="h-5" src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/edit-2-fill.svg" />
                </div>
                <AlertDialogTitle className="text-[#080236]">Edit trip name</AlertDialogTitle>
              </AlertDialogHeader>

              <Input
                value={threadToEdit?.thread_name || ""}
                onChange={(e) => setThreadToEdit({ ...threadToEdit!, thread_name: e.target.value })}
                placeholder="Enter new thread name"
                className="mt-2 border-[#B4BBE8] rounded-full text-[#080236]"
              />

              <AlertDialogFooter className="flex w-full items-center">
                <AlertDialogCancel className="bg-white w-3/6 text-[#080236] border border-[#1E1E76] rounded-2xl hover:bg-white">
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction
                  className="hover:bg-[#4B4BC3] hover:text-[#F2F3FA] bg-[#4B4BC3] rounded-2xl w-3/6 text-[#F2F3FA]"
                  onClick={() => {
                    if (threadToEdit && threadToEdit.thread_name.trim()) {
                      editThread(threadToEdit.thread_id, threadToEdit.thread_name.trim())
                    }
                    setEditDialogOpen(false)
                    setThreadToEdit(null)
                  }}
                >
                  Save
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default ChatMobileNav
