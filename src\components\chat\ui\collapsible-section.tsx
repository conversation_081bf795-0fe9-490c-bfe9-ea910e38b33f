"use client"

import { type ReactNode, useState } from "react"

interface CollapsibleSectionProps {
    title: string
    icon: ReactNode
    children: ReactNode
    defaultOpen?: boolean
}

export function CollapsibleSection({ title, icon, children, defaultOpen = true }: CollapsibleSectionProps) {
    const [isOpen, setIsOpen] = useState(defaultOpen)

    return (
        <div className="space-y-2">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center justify-between text-base font-medium text-[#080236] w-full"
            >
                <div className="flex items-center">
                    <div className="w-6 mr-3">{icon}</div>
                    {title}
                </div>
                <div>
                    {isOpen ? (
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
                            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z" />
                        </svg>
                    ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="#080236">
                            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                        </svg>
                    )}
                </div>
            </button>

            {isOpen && <div className="ml-9 space-y-2 max-h-[200px] overflow-y-auto">{children}</div>}
        </div>
    )
}
