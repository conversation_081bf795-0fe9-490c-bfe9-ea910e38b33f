"use client"

import { useState } from "react"
import { User, ChevronDown, ChevronUp } from "lucide-react"
import { formatFlightTime } from "@/lib/utils/flightTime"
import SummaryCard from "../chat_old/SummaryCard"

interface FlightSummaryProps {
    tripSummaryDetails: any
}

export const FlightSummary = ({ tripSummaryDetails }: FlightSummaryProps) => {
    const [activeTab, setActiveTab] = useState<"outbound" | "inbound">("outbound")
    const [expandedPassenger, setExpandedPassenger] = useState<number | null>(null)

    const hasInboundFlight =
        tripSummaryDetails?.selectedInboundFlight && Object.keys(tripSummaryDetails?.selectedInboundFlight).length > 0

    const togglePassenger = (passengerId: number) => {
        setExpandedPassenger(expandedPassenger === passengerId ? null : passengerId)
    }

    const dept =
        tripSummaryDetails?.selectedOutboundFlight &&
        formatFlightTime(
            tripSummaryDetails?.selectedOutboundFlight.departure,
            tripSummaryDetails?.selectedOutboundFlight.origin,
        )

    const arr =
        tripSummaryDetails?.selectedInboundFlight &&
        formatFlightTime(
            tripSummaryDetails?.selectedInboundFlight.departure,
            tripSummaryDetails?.selectedInboundFlight.origin,
        )

    return (
        <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Flight Summary</h2>

            {/* Tab Navigation */}
            <div className="flex bg-[#EBEBEB] rounded-lg p-1 mb-4">
                <button
                    onClick={() => setActiveTab("outbound")}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === "outbound" ? "bg-white text-brand-black shadow-sm" : "text-brand-grey hover:text-gray-900"
                        }`}
                >
                    Outbound Flight
                </button>
                {hasInboundFlight && (
                    <button
                        onClick={() => setActiveTab("inbound")}
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === "inbound" ? "bg-white text-brand-brand shadow-sm" : "text-brand-grey hover:text-gray-900"
                            }`}
                    >
                        Inbound Flight
                    </button>
                )}
            </div>

            {/* Flight Cards */}
            {activeTab === "outbound" && tripSummaryDetails?.selectedOutboundFlight && (
                <FlightCard
                    flight={tripSummaryDetails.selectedOutboundFlight}
                    type="outbound"
                    dayLabel={dept?.dayLabel}
                    passengers={tripSummaryDetails?.passengerDetails}
                    expandedPassenger={expandedPassenger}
                    togglePassenger={togglePassenger}
                    contactDetails={tripSummaryDetails?.paymentDetails}
                />
            )}

            {activeTab === "inbound" && hasInboundFlight && (
                <FlightCard
                    flight={tripSummaryDetails.selectedInboundFlight}
                    type="inbound"
                    dayLabel={arr?.dayLabel}
                    passengers={tripSummaryDetails?.passengerDetails}
                    expandedPassenger={expandedPassenger}
                    togglePassenger={togglePassenger}
                    contactDetails={tripSummaryDetails?.paymentDetails}
                />
            )}
        </div>
    )
}

interface FlightCardProps {
    flight: any
    type: "outbound" | "inbound"
    dayLabel?: string
    passengers: any[]
    expandedPassenger: number | null
    togglePassenger: (id: number) => void
    contactDetails: any
}

const FlightCard = ({
    flight,
    type,
    dayLabel,
    passengers,
    expandedPassenger,
    togglePassenger,
    contactDetails,
}: FlightCardProps) => (
    <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        {/* Flight Header */}
        <div className="bg-brand-50 p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white rounded-full p-1 flex items-center justify-center">
                    <img
                        src={flight?.segments[0].operator_logo || "/placeholder.svg"}
                        alt={flight?.airline}
                        className="w-10 h-10 object-contain"
                        onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/placeholder.svg?height=40&width=40"
                        }}
                    />
                </div>
                <div>
                    <h3 className="font-semibold text-lg">
                        {type === "outbound" ? "Outbound" : "Inbound"} • {dayLabel}
                    </h3>
                    <p className="text-sm text-brand-black">{flight?.airline}</p>
                </div>
            </div>
            <div className="text-sm text-brand-black">Flight {flight?.flight_number}</div>
        </div>

        {/* Flight Details using existing SummaryCard */}
        <div className="p-6 pt-0">
            {flight && <SummaryCard flight={flight} />}

            {/* Passengers */}
            <div className="mt-6 space-y-4">
                {passengers?.map((passenger: any, i: number) => (
                    <div key={i} className="border rounded-lg">
                        <button
                            onClick={() => togglePassenger(i)}
                            className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                        >
                            <div className="flex items-center gap-3">
                                <User className="w-5 h-5 text-brand-600" />
                                <span className="font-medium">
                                    Traveler {i + 1}: {passenger?.firstName} {passenger?.lastName}
                                </span>
                            </div>
                            {expandedPassenger === i ? (
                                <ChevronUp className="w-5 h-5 text-brand-black" />
                            ) : (
                                <ChevronDown className="w-5 h-5 text-brand-black" />
                            )}
                        </button>

                        {expandedPassenger === i && (
                            <div className="px-4 pb-4 border-t bg-[#EBEBEB]">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4">
                                    {/* Traveler Information */}
                                    <div>
                                        <h4 className="font-medium mb-3 text-brand-black">Traveler Information</h4>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Name:</span>
                                                <span className="font-medium">
                                                    {passenger?.firstName} {passenger?.lastName}
                                                </span>
                                            </div>
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Date of Birth:</span>
                                                <span className="font-medium">{passenger?.dob}</span>
                                            </div>
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Gender:</span>
                                                <span className="font-medium">{passenger?.gender}</span>
                                            </div>
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Phone:</span>
                                                <span className="font-medium">
                                                    +{contactDetails?.phone_int_code} {contactDetails?.contact_details_phone_number}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Seat & Luggage */}
                                    <div>
                                        <h4 className="font-medium mb-3 text-brand-black">Seat & Luggage</h4>
                                        <div className="space-y-2 text-sm">
                                            {/* <div className="flex justify-between">
                                                <span className="text-brand-600">Seat:</span>
                                                <span className="font-medium">{type === "outbound" ? "28F" : "52E"}</span>
                                            </div> */}
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Check-in Baggage:</span>
                                                <span className="font-medium">30 Kgs per Adult</span>
                                            </div>
                                            <div className="flex justify-between text-brand-black">
                                                <span className="text-brand-black">Cabin Baggage:</span>
                                                <span className="font-medium">7 Kgs per Adult</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    </div>
)
