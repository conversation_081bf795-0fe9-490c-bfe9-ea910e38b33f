import { setDefaultTimeout } from '@cucumber/cucumber';
import { <PERSON>, BrowserContext, Browser } from '@playwright/test';
import { fixture } from '../fixtures/Fixture';

setDefaultTimeout(80000);

export let browser: Browser;
export let context: BrowserContext;

export class BasePage {    static async goto(url: string) {
        await fixture.page.goto(url, {
            waitUntil: "domcontentloaded"
        });
        
        // Handle cookie consent dialog if it appears
        const { ConsentHelper } = require('../utils/ConsentHelper');
        await ConsentHelper.handleConsentDialog('accept');
    }

    static async clickButtonByName(name: string) {
        const button = fixture.page.getByRole('button', { name });
        await button.waitFor({ state: 'visible' });
        await button.click();
    }

    static async fillTextboxByName(name: string, value: string) {
        const textbox = fixture.page.getByRole('textbox', { name });
        await textbox.waitFor({ state: 'visible' });
        await textbox.fill(value);
    }

    static async fillInputByNameAttr(fieldName: string, value: string) {
        const input = fixture.page.locator(`input[name="${fieldName}"]`);
        await input.waitFor({ state: 'visible' });
        await input.fill(value);
    }

    static async clickButtonInsideForm(buttonText: string) {
        const button = fixture.page.locator('form').getByRole('button', { name: buttonText });
        await button.waitFor({ state: 'visible' });
        await button.click();
    }

    static async waitAndClick(locator: string) {
        const element = fixture.page.locator(locator);
        await element.waitFor({
            state: "visible"
        });
        await element.click();
    }

    /**
     * Checks for element presence using multiple selector strategies
     * Useful for complex UI components or when exact selectors are difficult to pin down
     */
    static async isElementPresent(selectors: string[]): Promise<boolean> {
        for (const selector of selectors) {
            try {
                const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
                if (isVisible) {
                    console.log(`Found element matching selector: ${selector}`);
                    return true;
                }
            } catch (error) {
                console.log(`Element not found with selector: ${selector}`);
            }
        }
        return false;
    }

    static async navigateTo(link: string) {
        await Promise.all([
            fixture.page.waitForNavigation(),
            fixture.page.click(link)
        ])
    }

    static async getTitle() {
        let title;
        await Promise.all([
            title = fixture.page.title()
        ])
        return title
    }

    static async getText(xpath: string) {
        let text;
        await Promise.all([
            text = await fixture.page.locator(xpath).textContent()
        ])
        return text
    }

    static async fillField(xpath: string, value: string) {
        let text;
        await Promise.all([
            await fixture.page.locator(xpath).fill(value),
            console.log(`Filled field with selector ${xpath} with value ${value}`)
        ])
        return text
    }

    static async getCurrentUrl() {
        let url;
        await Promise.all([
            url = fixture.page.url(),
            console.log(`Current URL is ${url}`)
        ])
        return url
    }

    static async getLocatorByXPath(xpath: string) {
        let loc;
        await Promise.all([
            loc = await fixture.page.locator(xpath),
            console.log(`Locator for XPath ${xpath} obtained`)
        ])
        return loc
    }

    static async pressKeyboardKeys(keysToEnter: string) {
        await Promise.all([
            await fixture.page.keyboard.press(keysToEnter)
        ])
    }
}