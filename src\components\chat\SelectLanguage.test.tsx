import React from "react";
import { SelectLanguage } from "./SelectLanguage";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";

describe("SelectLanguage", () => {
    it("renders the Select trigger with placeholder", () => {
        render(<SelectLanguage />);
        expect(screen.getByText("Choose Language")).toBeInTheDocument();
    });

    it("shows language options when trigger is clicked", async () => {
        render(<SelectLanguage />);
        const trigger = screen.getByText("Choose Language");
        fireEvent.mouseDown(trigger);
    });

    it("selects a language and updates the value", async () => {
        render(<SelectLanguage />);
        const trigger = screen.getByText("Choose Language");
        fireEvent.mouseDown(trigger);
    });

    it("matches snapshot", () => {
        const { asFragment } = render(<SelectLanguage />);
        expect(asFragment()).toMatchSnapshot();
    });

    it("renders all language options with correct values", async () => {
        render(<SelectLanguage />);
        const trigger = screen.getByText("Choose Language");
        fireEvent.mouseDown(trigger);
    });

    it("does not show options before trigger is clicked", () => {
        render(<SelectLanguage />);
        expect(screen.queryByText("English UK")).not.toBeInTheDocument();
        expect(screen.queryByText("Japanese")).not.toBeInTheDocument();
        expect(screen.queryByText("Chinese")).not.toBeInTheDocument();
    });
});