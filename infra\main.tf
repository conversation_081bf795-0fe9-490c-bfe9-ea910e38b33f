locals {
  environment = "${terraform.workspace == "dev" ? "dev" : (terraform.workspace == "qa" ? "qa" : (terraform.workspace == "ci_cd" ? "cicd" : (terraform.workspace == "pre_prod" ? "preprod" : (terraform.workspace == "prod" ? "prod" : "cicd"))))}"
  dns_name     = "${local.environment}.nxvoytrips.ai"
  replicas     = "${local.environment == "prod" || local.environment == "preprod" || local.environment == "dev" ? "2" : "1"}"
  max_replicas = "${local.environment == "prod" || local.environment == "preprod" ? "6" : (local.environment == "dev" ? "2" : "1")}"
  cpu_request  = "${local.environment == "prod" || local.environment == "preprod" || local.environment == "dev" ? var.CPU_REQUEST : "0"}"
  cpu_limit    = "${local.environment == "prod" || local.environment == "preprod" || local.environment == "dev" ? var.CPU_LIMIT : "0"}"
  mem_request  = "${local.environment == "prod" || local.environment == "preprod" || local.environment == "dev" ? var.MEM_REQUEST : "0"}"
  mem_limit    = "${local.environment == "prod" || local.environment == "preprod" || local.environment == "dev" ? var.MEM_LIMIT : "0"}"
}


data "terraform_remote_state" "gcp" {
  backend   = "gcs"
  workspace = terraform.workspace

  config = {
    bucket         = "nxvoy-terraform-states"
    prefix         = "nxvoy"
  }
}

## Create namespace 
resource "kubernetes_namespace" "service" {
  metadata {
    name = "${local.environment}-${var.SERVICE_NAME}"
  }
}

 resource "google_compute_global_address" "fe_ingress_ip" {
  name = "${local.environment}-fe-ingress-ip"
}