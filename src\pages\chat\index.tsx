import { useEffect } from "react";
import { useDispatch } from "react-redux";
import ChatLayout from "@/components/chat/ChatLayout";
import { ChatScreen } from "@/components/chat/ChatScreen";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";

export default function Chat() {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(
      updateCurrentThreadInfo({
        currentChatPageThreadId: "",
        currentThreadName: "",
        history: false,
        newThread: false,
        showMoreFlightsOption: false,
        chatResult: {},
        allFlights: {},
      })
    );
  }, [dispatch]);

  return (
    <ChatLayout>
      <ChatScreen />
    </ChatLayout>
  );
}