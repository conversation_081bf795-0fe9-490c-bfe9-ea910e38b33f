// flightService.ts
import { SeatMapGenerator } from "./SeatMapGenerator";
import { FlightSegment, Seat } from "./types";

export class FlightSeatService {
  private seatMapGenerator = new SeatMapGenerator();

  getSeatMap(flight: FlightSegment): Seat[] {
    return this.seatMapGenerator.generateSeatMap(
      flight.aircraftType,
      flight.bookedSeats
    );
  }

  getSeatPricing(seat: Seat): number {
    // Implement pricing logic based on seat type and features
    let price = 0;

    switch (seat.type) {
      case "first":
        price = 200;
        break;
      case "business":
        price = 150;
        break;
      case "premium":
        price = 75;
        break;
      case "economy":
        price = 30;
        break;
    }

    if (seat.features?.includes("extra-legroom")) price += 20;
    if (seat.features?.includes("window")) price += 5;
    if (seat.features?.includes("aisle")) price += 5;

    return price;
  }
}
