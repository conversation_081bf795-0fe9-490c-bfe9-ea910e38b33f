import type React from "react";
import ProfileDetails from "@/components/dashboard/ProfileDetails";
import AddressDetails from "@/components/dashboard/AddressDetails";
import PassengerProfileDetails from "@/components/dashboard/PassengerProfileDetails";
import type { User, UserDetailDelete } from "@/constants/user";
import ManageBooking from "./manageBooking";

interface UserProfileContentProps {
  activeSection: string;
  user: User | null;
  updateUser: (data: User) => Promise<void>; // Simplified to single function
  deleteApi: (data: UserDetailDelete) => Promise<void>;
  updateSuccessMsg: string | null;
}

const UserProfileContent: React.FC<UserProfileContentProps> = ({
  activeSection,
  user,
  updateUser,
  deleteApi,
  updateSuccessMsg,
}) => {
  const renderActiveSection = () => {
    switch (activeSection) {
      case "User Profile":
        return (
          <ProfileDetails
            user={user}
            updateUser={updateUser}
            updatedStatus={updateSuccessMsg}
          />
        );
      case "Address Details":
        return (
          <AddressDetails
            user={user}
            updateUser={updateUser} // Use the same function
            updatedStatus={updateSuccessMsg}
            deleteApi={deleteApi}
          />
        );
      case "Passenger Profiles":
        return (
          <PassengerProfileDetails
            user={user}
            updateUser={updateUser} // Use the same function
            deleteApi={deleteApi}
            updatedStatus={updateSuccessMsg}
          />
        );
      case "Manage Booking":
        return (
          <ManageBooking
            user={user}
            updateUser={updateUser} // Use the same function
            deleteApi={deleteApi}
            updatedStatus={updateSuccessMsg}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 p-1 md:p-4 h-screen border border-neutral rounded-[8px] overflow-hidden hover:overflow-auto transition-all">
      <div className="bg-brand-white">{renderActiveSection()}</div>
    </div>
  );
};

export default UserProfileContent;
