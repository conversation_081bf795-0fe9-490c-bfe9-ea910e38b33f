import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ShowMoreFlightsState {
  showMoreFlights: boolean;
}

const initialState: ShowMoreFlightsState = {
  showMoreFlights: false,
};

const showMoreFlightsSlice = createSlice({
  name: 'showMoreFlights',
  initialState,
  reducers: {
    setShowMoreFlights: (state, action: PayloadAction<boolean>) => {
      state.showMoreFlights = action.payload;
    },
    toggleShowMoreFlights: (state) => {
      state.showMoreFlights = !state.showMoreFlights;
    },
    resetShowMoreFlights: (state) => {
      state.showMoreFlights = false;
    },
  },
});

export const {
  setShowMoreFlights,
  toggleShowMoreFlights,
  resetShowMoreFlights,
} = showMoreFlightsSlice.actions;

export default showMoreFlightsSlice.reducer;
