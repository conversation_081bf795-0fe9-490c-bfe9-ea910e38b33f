@sample
@desktop
@mobile
@regression
@accessibility
Feature: Desktop: Sample Accessibility tests 

	Background: Start from a specific page
		Given The user starts from the "ui.sample.appUrl" page

	@sample1
	Scenario: Desktop: Sample - Login failure
		Given I check the accessbility of the page
		Given The user types the "sample.users.standard" username on the login page
		And The user types the "sample.users.standard" password on the login page
		When The user clicks on the login button
		Then The user is on the "sample.login.success" page
		Given I check the accessbility of the page
