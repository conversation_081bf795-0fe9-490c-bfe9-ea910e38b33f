import { Then, When } from '@cucumber/cucumber';
import { fixture } from '../../fixtures/Fixture';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';
import { WaitHelper } from '../../waits/WaitHelper';
import { BookingConfirmationPage } from '../../pages/BookingConfirmationPage';
import { ConsentHelper } from '../../utils/ConsentHelper';

/**
 * Step definition for asserting booking confirmation page
 * Verifies both the ticket booking confirmation message and payment summary elements
 */
Then('Assert user is on the booking confirmation page', async function() {
    try {
        console.log('Checking if user is on booking confirmation page...');
        
        // First take a screenshot as evidence
        await ScreenshotHelper.takeScreenshot('booking-confirmation-page');
        
        // Wait longer for the page to be stable due to payment processing
        await WaitHelper.waitForPageStable(10000);
        
        // Wait for any redirects to complete and page to stabilize
        await fixture.page.waitForTimeout(3000);
        
        // Use the BookingConfirmationPage to verify the page
        const isBookingConfirmed = await BookingConfirmationPage.isBookingConfirmationDisplayed();
        
        if (isBookingConfirmed) {
            // Additional info for reporting - capture order number and total
            try {
                const orderNumber = await BookingConfirmationPage.getOrderNumber();
                const totalAmount = await BookingConfirmationPage.getTotalAmount();
                const paymentMethod = await BookingConfirmationPage.getPaymentMethod();
                
                console.log(`Booking confirmed with order number: ${orderNumber}`);
                console.log(`Total amount: ${totalAmount}`);
                console.log(`Payment method: ${paymentMethod}`);
            } catch (infoError) {
                // Just log and continue, this is just additional info
                console.log(`Could not retrieve booking details: ${infoError}`);
            }
            
            console.log('Successfully confirmed user is on booking confirmation page');
            return true;
        } else {
            // If page object verification fails, try with direct element checks as fallback
            console.log('BookingConfirmationPage object verification failed, trying direct element verification');
              // Check for confirmation message directly with expanded selectors
            const confirmationMessageSelectors = [
                'div:has-text("Your Ticket was Booked Successfully!")',
                'div.text-\\[\\#080236\\].font-semibold:has-text("Your Ticket was Booked Successfully!")',
                'div.flex.flex-col.gap-4.pb-10.w-full.justify-center',
                'div:has-text("Booking Successful")',
                'div:has-text("Ticket Confirmation")',
                'div:has-text("Booking Confirmed")',
                'div:has-text(/ticket.*booked/i)',
                'div:has-text(/booking.*successful/i)',
                'div:has-text(/confirmation/i)'
            ];
            
            // Check for payment summary directly with expanded selectors
            const paymentSummarySelectors = [
                'div:has-text("Payment Summary")',
                'div.text-\\[\\#1E1E76\\]:has-text("Payment Summary")',
                'div:has-text("Order Number")',
                'div:has-text("Order Details")',
                'div:has-text("Payment Details")',
                'div:has-text(/payment.*summary/i)',
                'div:has-text(/order.*number/i)'
            ];
              // Implement retry logic with increased timeouts
            let confirmationMessageFound = false;
            let paymentSummaryFound = false;
            
            // Try up to 3 times with increasing timeouts
            for (let attempt = 1; attempt <= 3; attempt++) {
                console.log(`Fallback verification attempt ${attempt}/3`);
                const timeout = 3000 * attempt; // Increase timeout with each attempt
                
                // Check if confirmation message is present
                if (!confirmationMessageFound) {
                    for (const selector of confirmationMessageSelectors) {
                        const isVisible = await fixture.page.locator(selector).isVisible({ timeout })
                            .catch(() => false);
                            
                        if (isVisible) {
                            console.log(`Found confirmation message with selector: ${selector}`);
                            confirmationMessageFound = true;
                            break;
                        }
                    }
                }
                
                // Check if payment summary is present
                if (!paymentSummaryFound) {
                    for (const selector of paymentSummarySelectors) {
                        const isVisible = await fixture.page.locator(selector).isVisible({ timeout })
                            .catch(() => false);
                            
                        if (isVisible) {
                            console.log(`Found payment summary with selector: ${selector}`);
                            paymentSummaryFound = true;
                            break;
                        }
                    }
                }
                
                // If both are found, we can exit the retry loop
                if (confirmationMessageFound && paymentSummaryFound) {
                    console.log(`Successfully found all elements on attempt ${attempt}`);
                    break;
                }
                
                // Wait before next attempt if we haven't found everything yet
                if (attempt < 3 && (!confirmationMessageFound || !paymentSummaryFound)) {
                    console.log(`Waiting before next attempt (${3-attempt} attempts remaining)...`);
                    await fixture.page.waitForTimeout(2000);
                }
            }
            
            // If both elements were found, consider the verification successful
            if (confirmationMessageFound && paymentSummaryFound) {
                console.log('Successfully verified booking confirmation page with fallback method');
                return true;
            }
            
            // Take another screenshot as evidence of failure
            await ScreenshotHelper.takeScreenshot('booking-confirmation-failed', true);
            
            // List what was not found
            if (!confirmationMessageFound) {
                throw new Error('Booking confirmation message not found');
            } else {
                throw new Error('Payment summary section not found');
            }
        }
    } catch (error) {
        console.error(`Error verifying booking confirmation page: ${error}`);
        await ScreenshotHelper.takeScreenshot('booking-confirmation-error', true, true);
        throw error;
    }
});

/**
 * Step definition for clicking the "Download as PDF" button on the booking confirmation page
 * Handles the PDF download action and verifies its success
 */
When('user click the {string} button on the booking confirmation page', async function (buttonText) {
    if (buttonText !== "Download as PDF") {
        throw new Error(`Expected "Download as PDF" button, but got "${buttonText}"`);
    }
    try {
        console.log('Executing step: When user click the "Download as PDF" button');
        
        // Handle cookie consent dialog if it appears
        await ConsentHelper.handleConsentDialog();
        
        // Take a screenshot before attempting to click
        await ScreenshotHelper.takeScreenshot('before-download-pdf-action');
        
        // Wait a moment to ensure the page is fully loaded
        await fixture.page.waitForTimeout(2000);
        
        // Get the file download promise to handle the download event
        // This will allow us to detect if a download was initiated
        const downloadPromise = fixture.page.waitForEvent('download', { timeout: 10000 })
            .then(download => {
                console.log('Download started successfully');
                return true;
            })
            .catch(error => {
                console.warn('No download event detected, but this might be fine if PDF opens in new tab:', error);
                return true; // Consider it a success anyway since PDF might open in new tab
            });
        
        // Call the method to click the Download as PDF button
        const buttonClicked = await BookingConfirmationPage.clickDownloadAsPdfButton();
        
        if (!buttonClicked) {
            throw new Error('Failed to click Download as PDF button');
        }
        
        // Wait for the download to complete (or timeout)
        await downloadPromise;
        
        // Take a screenshot after the action
        await ScreenshotHelper.takeScreenshot('after-download-pdf-action');
        
        console.log('Successfully clicked the Download as PDF button');
    } catch (error) {
        console.error(`Error clicking Download as PDF button: ${error}`);
        await ScreenshotHelper.takeScreenshot('download-pdf-error', true, true);
        throw error;
    }
});

/**
 * Step definition to wait for booking confirmation page to be displayed
 * This step provides enhanced waiting functionality with better detection of the confirmation page
 * and extended timeout to account for payment processing
 */
/**
 * Step definition for waiting until the booking confirmation page is displayed
 * This step will wait with an extended timeout for the confirmation page to load fully
 */
When('Wait until booking confirmation page to displayed', { timeout: 240000 }, async function() {
    try {
        console.log('Waiting for booking confirmation page to be displayed with extended timeout...');
        
        // Take a screenshot before waiting
        await ScreenshotHelper.takeScreenshot('before-waiting-confirmation-page');
        
        // Handle any cookie consent dialog that might appear during page load
        await ConsentHelper.handleConsentDialog();
        
        // First, wait longer for any ongoing navigation to settle completely
        console.log('Waiting for initial navigation to settle (10 seconds)...');
        await fixture.page.waitForTimeout(10000).catch(e => {
            console.log(`Initial wait timeout error (non-critical): ${e.message}`);
        });
        
        // Enhanced waiting logic for booking confirmation page (180 seconds / 3 minutes timeout)
        console.log('Starting enhanced wait for booking confirmation page (78 seconds timeout)...');
        
        // Start time for timeout tracking
        const startTime = Date.now();
        const timeout = 78000; // 1.3 minute
        
        // Define key selectors to check for booking confirmation page
        const keySelectors = [
            // Success message elements
            'div.text-[#080236].font-semibold.text-xl:has-text("Your Ticket was Booked Successfully!")',
            'div.text-[#707FF5].text-lg:has-text("Booking details has been sent to:")',
            'div.flex.text-[#1E1E76].text-2xl.font-semibold:has-text("Payment Summary")',
            'div.font-semibold:has-text("Order Number")',
            
            // Payment and confirmation section elements
            'div.flex.flex-col.gap-4:has(div:has-text("Payment Summary"))',
            'div.text-[#1E1E76].text-2xl.font-semibold',
            'div.flex.text-[#1E1E76].text-2xl.font-semibold:has-text("Flight Summary")',
            
            // Download PDF button
            'button:has-text("Download as Pdf")',
            'button.px-4.py-1.text-xl.xs\\:text-base.w-max',
            'button[style*="background: linear-gradient"]:has-text("Download")',
            
            // Fallback indicators
            'div:has-text("Booking Successful")',
            'div:has-text("Ticket Confirmation")',
            'div:has-text("Booking Confirmed")',
            'div:has-text("Order Details")',
            'div:has-text("Payment Details")',
            'div:has-text("Order Number")'
        ];
        
        // Define interfaces for type safety
        type TextLocator = { type: 'text'; text: string; options?: { exact: boolean } };
        type RoleLocator = { type: 'role'; role: 'button' | 'heading' | 'checkbox'; options: { name: string | RegExp; exact: boolean } };
        type PlaywrightLocator = TextLocator | RoleLocator;
        
        // Define modern Playwright locators to check
        const playwriteLocators: PlaywrightLocator[] = [
            // Success message locators
            { type: 'text', text: 'Your Ticket was Booked Successfully', options: { exact: false } },
            { type: 'text', text: 'Booking details has been sent to', options: { exact: false } },
            { type: 'text', text: 'Payment Summary', options: { exact: false } },
            { type: 'text', text: 'Order Number', options: { exact: false } },
            { type: 'text', text: 'Flight Summary', options: { exact: false } },
            
            // Button locators
            { type: 'role', role: 'button', options: { name: 'Download as Pdf', exact: false } },
            { type: 'role', role: 'button', options: { name: 'Download', exact: false } },
            
            // Heading locators
            { type: 'role', role: 'heading', options: { name: /Booking Successful|Ticket Confirmation|Booking Confirmed/i, exact: false } }
        ];
        
        // Text patterns to look for in page content
        const confirmationTexts = [
            "Ticket was Booked Successfully",
            "Your Ticket was Booked Successfully",
            "Booking Successful",
            "Payment Summary",
            "Order Number",
            "Flight Summary",
            "Booking details has been sent to",
            "Do you want to keep all your travel details in one place",
            "Download as Pdf"
        ];
        
        // URL patterns that indicate we're on the confirmation page
        const urlPatterns = [
            'confirmation',
            'booking-success',
            'booking-confirmed',
            'ticket-confirmed',
            'success-summary',
            'checkout-success',
            'order-confirmation'
        ];
        
        let confirmationDetected = false;
        
        // Try multiple approaches until timeout occurs
        while (Date.now() - startTime < timeout) {
            console.log(`Checking for booking confirmation page... (${Math.floor((Date.now() - startTime) / 1000)}s elapsed)`);
            
            // STRATEGY 1: Check with modern Playwright locators
            try {
                const visiblePlaywrightLocators = await Promise.all(
                    playwriteLocators.map(async (locator) => {
                        try {
                            let element;
                            if (locator.type === 'text') {
                                element = fixture.page.getByText(locator.text, locator.options);
                            } else if (locator.type === 'role') {
                                element = fixture.page.getByRole(locator.role, locator.options);
                            }
                            
                            if (element && await element.isVisible({ timeout: 2000 })) {
                                if (locator.type === 'text') {
                                    return `${locator.type}:${locator.text}`;
                                } else {
                                    return `${locator.type}:${locator.role}`;
                                }
                            }
                            return null;
                        } catch (err) {
                            return null;
                        }
                    })
                ).then(results => results.filter(Boolean));
                
                if (visiblePlaywrightLocators.length > 0) {
                    console.log('✅ Booking confirmation detected with Playwright locators:', visiblePlaywrightLocators);
                    confirmationDetected = true;
                    break;
                }
            } catch (locatorError) {
                console.log('Playwright locator check error (non-critical):', locatorError.message);
            }
            
            // STRATEGY 2: Check all CSS selectors concurrently (fallback)
            const visibleSelectors = await Promise.all(
                keySelectors.map(async (selector) => {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                        .catch(() => false);
                    return isVisible ? selector : null;
                })
            ).then(results => results.filter(Boolean));
            
            if (visibleSelectors.length > 0) {
                console.log('✅ Booking confirmation detected with CSS selectors:', visibleSelectors);
                confirmationDetected = true;
                break;
            }
            
            // STRATEGY 2: Check page content for confirmation texts
            try {
                const pageContent = await fixture.page.content();
                const matchedTexts = confirmationTexts.filter(text => pageContent.includes(text));
                
                if (matchedTexts.length >= 2) { // Require at least 2 matches for confidence
                    console.log('✅ Booking confirmation detected via content matches:', matchedTexts);
                    confirmationDetected = true;
                    break;
                }
            } catch (error) {
                console.log(`Content check error (non-critical): ${error.message}`);
            }
            
            // STRATEGY 3: Check URL for confirmation indicators
            try {
                const currentUrl = await fixture.page.url();
                const matchedUrlPattern = urlPatterns.find(pattern => currentUrl.includes(pattern));
                
                if (matchedUrlPattern) {
                    console.log(`✅ Booking confirmation detected via URL pattern: ${matchedUrlPattern}`);
                    confirmationDetected = true;
                    break;
                }
            } catch (urlError) {
                console.log(`URL check error (non-critical): ${urlError.message}`);
            }
            
            // Take periodic screenshots
            if (Math.floor((Date.now() - startTime) / 1000) % 30 === 0) { // Every 30 seconds
                await ScreenshotHelper.takeScreenshot(`confirmation-wait-${Math.floor((Date.now() - startTime) / 1000)}`);
            }
            
            // Wait before next attempt
            await fixture.page.waitForTimeout(5000);
        }
        
        if (!confirmationDetected) {
            // Take a final screenshot to help with debugging
            await ScreenshotHelper.takeScreenshot('confirmation-page-not-detected', true, true);
            throw new Error('Booking confirmation page was not displayed within the expected time');
        }
        
        // Log success
        console.log('✅ Successfully detected booking confirmation page');
        
        // Take a success screenshot
        await ScreenshotHelper.takeScreenshot('booking-confirmation-wait-success');
        console.log('Successfully waited for booking confirmation page to display');
    } catch (error) {
        console.error(`Error waiting for booking confirmation page: ${error}`);
        await ScreenshotHelper.takeScreenshot('booking-confirmation-wait-error', true, true);
        throw error;
    }
});

/**
 * Step definition for clicking on the "Download as pdf" button and downloading the ticket
 * Handles the PDF download action with extended timeout and enhanced reliability
 */
Then('Click on the {string} button, download the ticket', { timeout: 60000 }, async function (buttonText) {
    try {
        console.log(`Executing step: Then Click on the "${buttonText}" button, download the ticket`);
        
        // Handle cookie consent dialog if it appears
        await ConsentHelper.handleConsentDialog();
        
        // Take a screenshot before attempting to click
        await ScreenshotHelper.takeScreenshot('before-download-pdf-ticket');
        
        // Wait a moment to ensure the page is fully loaded
        await fixture.page.waitForTimeout(3000);
        
        // Get the file download promise to handle the download event
        console.log('Setting up download event listener...');
        const downloadPromise = fixture.page.waitForEvent('download', { timeout: 20000 })
            .then(async download => {
                console.log('✅ Download started successfully');
                const fileName = download.suggestedFilename();
                console.log(`Suggested filename: ${fileName}`);
                return true;
            })
            .catch(error => {
                console.warn('⚠️ No download event detected, but this might be fine if PDF opens in new tab:', error.message);
                return true; // Consider it a success anyway since PDF might open in new tab
            });
        
        // Try to find and click the button using the exact CSS selector provided
        const buttonSelector = 'button.px-4.py-1.text-xl.xs\\:text-base.w-max[style*="background: linear-gradient"]';
        console.log(`Trying to find button with selector: ${buttonSelector}`);
        
        const buttonVisible = await fixture.page.isVisible(buttonSelector, { timeout: 5000 })
            .catch(() => false);
            
        if (buttonVisible) {
            console.log('Found button with exact selector, clicking...');
            await fixture.page.click(buttonSelector);
            await fixture.page.waitForTimeout(2000);
            await ScreenshotHelper.takeScreenshot('after-download-pdf-exact-selector');
        } else {
            console.log('Button not found with exact selector, trying text content...');
            // Try to find by text content
            const buttonByTextSelector = `button:has-text("${buttonText}")`;
            const buttonByTextVisible = await fixture.page.isVisible(buttonByTextSelector, { timeout: 3000 })
                .catch(() => false);
                
            if (buttonByTextVisible) {
                console.log(`Found button with text: "${buttonText}", clicking...`);
                await fixture.page.click(buttonByTextSelector);
                await fixture.page.waitForTimeout(2000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-text-selector');
            } else {
                // Last resort: JavaScript approach
                console.log('Button not found with text, trying JavaScript approach...');
                const buttonFound = await fixture.page.evaluate((buttonTextForEval) => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const pdfButton = buttons.find(button => 
                        button.textContent?.toLowerCase().includes(buttonTextForEval.toLowerCase()) ||
                        button.textContent?.toLowerCase().includes('download') ||
                        (button.style && button.style.background && 
                         button.style.background.includes('linear-gradient') && 
                         button.textContent?.toLowerCase().includes('download'))
                    );
                    
                    if (pdfButton) {
                        pdfButton.click();
                        return true;
                    }
                    return false;
                }, buttonText);
                
                if (!buttonFound) {
                    throw new Error(`Failed to find button with text "${buttonText}" using all available methods`);
                }
            }
        }
        
        // Wait for the download to complete (or timeout)
        console.log('Waiting for download to complete...');
        const downloadSucceeded = await downloadPromise;
        
        // Take a screenshot after the action
        await fixture.page.waitForTimeout(1000);
        await ScreenshotHelper.takeScreenshot('after-download-pdf-ticket');
        
        if (downloadSucceeded) {
            console.log('✅ Successfully downloaded the ticket PDF');
        } else {
            console.log('⚠️ Download may not have completed, but continuing the test');
        }
    } catch (error) {
        console.error(`❌ Error downloading the ticket: ${error}`);
        await ScreenshotHelper.takeScreenshot('download-ticket-error', true, true);
        throw error;
    }
});

/**
 * Step definition for clicking the user account icon
 * This step will click on the user account icon in the header
 */
Then('click the user account icon', { timeout: 30000 }, async function() {
    try {
        console.log('Executing step: Then click the user account icon');
        
        // Take a screenshot before attempting to click
        await ScreenshotHelper.takeScreenshot('before-click-user-account-icon');
        
        // Wait a moment to ensure the page is fully loaded
        await fixture.page.waitForTimeout(2000);
        
        // Multiple selectors for the user account icon to handle different UI states
        const accountIconSelectors = [
            // Exact selector provided in the requirement
            'span.flex.h-full.w-full.items-center.justify-center.rounded-full.bg-muted.text-white.bg-gradient-to-r.from-\\[\\#4B4BC3\\].to-\\[\\#707FF5\\]',
            
            // Container selector
            'span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer',
            
            // Alternative selectors for different UI states
            'div.avatar > span',
            'div.avatar > div',
            'div.flex.items-center.gap-2 > div.avatar',
            'div.rounded-full.cursor-pointer'
        ];
        
        // Try to find and click the user account icon using different selectors
        let accountIconClicked = false;
        
        for (const selector of accountIconSelectors) {
            console.log(`Trying to find user account icon with selector: ${selector}`);
            
            const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                .catch(() => false);
                
            if (isVisible) {
                console.log(`Found user account icon with selector: ${selector}, clicking...`);
                await fixture.page.click(selector);
                accountIconClicked = true;
                
                // Take a screenshot after clicking
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-user-account-icon');
                break;
            }
        }
        
        // If icon not found with direct selectors, try JavaScript approach
        if (!accountIconClicked) {
            console.log('User account icon not found with direct selectors, trying JavaScript approach...');
            
            // Use JavaScript to find and click the account icon by its appearance
            const jsClicked = await fixture.page.evaluate(() => {
                // Try to find by visual characteristics
                const possibleElements = [
                    // Look for gradient background
                    ...Array.from(document.querySelectorAll('span[class*="bg-gradient-to-r"]')),
                    // Look for rounded avatars
                    ...Array.from(document.querySelectorAll('span.rounded-full')),
                    ...Array.from(document.querySelectorAll('div.rounded-full')),
                    // Look by text content (could be user initials)
                    ...Array.from(document.querySelectorAll('span')).filter(el => 
                        el.textContent && el.textContent.length <= 2 && 
                        getComputedStyle(el).borderRadius.includes('50%')
                    )
                ];
                  // Find the most likely account icon by checking attributes and styles
                const accountIcon = possibleElements.find(el => {
                    const style = getComputedStyle(el);
                    // Check for avatar characteristics
                    const isRounded = style.borderRadius.includes('50%') || style.borderRadius === '9999px';
                    const hasGradient = el.className.includes('gradient') || style.background.includes('gradient');
                    const isClickable = style.cursor === 'pointer' || 
                                       (el.parentElement && getComputedStyle(el.parentElement).cursor === 'pointer');
                    const hasInitials = el.textContent && el.textContent.length <= 2;
                    
                    return (isRounded && (hasGradient || isClickable || hasInitials));
                });
                
                if (accountIcon) {
                    // Click the element or its closest clickable parent
                    let elementToClick = accountIcon;
                    // Find closest clickable parent if needed
                    let currentEl = accountIcon;
                    while (currentEl && getComputedStyle(currentEl).cursor !== 'pointer') {
                        currentEl = currentEl.parentElement;
                        if (currentEl && getComputedStyle(currentEl).cursor === 'pointer') {
                            elementToClick = currentEl;
                            break;
                        }
                    }
                      // Cast to HTMLElement to access click()
                    (elementToClick as HTMLElement).click();
                    return true;
                }
                
                return false;
            });
            
            if (jsClicked) {
                console.log('Successfully clicked user account icon with JavaScript approach');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-js-click-user-account-icon');
                accountIconClicked = true;
            }
        }
        
        if (!accountIconClicked) {
            throw new Error('Failed to find and click the user account icon using all available methods');
        }
        
        console.log('✅ Successfully clicked the user account icon');
    } catch (error) {
        console.error(`❌ Error clicking user account icon: ${error}`);
        await ScreenshotHelper.takeScreenshot('user-account-icon-click-error', true, true);
        throw error;
    }
});

/**
 * Step definition for clicking the logout button in the account menu
 * This step will click on the logout option in the account menu after it's expanded
 */
Then('click the logout button', { timeout: 30000 }, async function() {
    try {
        console.log('Executing step: And click the logout button');
        
        // Take a screenshot before attempting to click
        await ScreenshotHelper.takeScreenshot('before-click-logout-button');
        
        // Wait a moment for the account menu to fully expand
        await fixture.page.waitForTimeout(2000);
        
        // Try using the page object method first
        const logoutClicked = await BookingConfirmationPage.clickLogoutButton();
        
        if (logoutClicked) {
            console.log('✅ Successfully clicked the logout button using page object');
            return;
        }
        
        // If page object method fails, try direct approach with selectors from the provided HTML
        console.log('Page object approach failed, trying direct selectors...');
        
        // Try multiple selectors for the logout button based on provided HTML
        const logoutSelectors = [
            // Exact selector from provided HTML
            'div[role="menuitem"]:has(img[src*="sign-out.svg"][alt="Logout"])',
            'div[role="menuitem"]:has(span:text("Logout"))',
            'div.relative.flex.cursor-default.select-none.items-center.gap-2:has(img[src*="sign-out.svg"])',
            // More generic selectors as fallback
            'div[role="menuitem"]:has-text("Logout")',
            '[data-radix-menu-content] div:has-text("Logout")'
        ];
        
        let buttonClicked = false;
        
        // Try each selector
        for (const selector of logoutSelectors) {
            console.log(`Trying to find logout button with selector: ${selector}`);
            
            const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                .catch(() => false);
                
            if (isVisible) {
                console.log(`Found logout button with selector: ${selector}, clicking...`);
                await fixture.page.click(selector);
                buttonClicked = true;
                
                // Take a screenshot after clicking
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-logout-button');
                break;
            }
        }
        
        // If direct selector approach fails, try JavaScript approach
        if (!buttonClicked) {
            console.log('Logout button not found with direct selectors, trying JavaScript approach...');
            
            // Use JavaScript to find and click the logout button
            const jsClicked = await fixture.page.evaluate(() => {
                // First check for any open menus
                const menus = Array.from(document.querySelectorAll('[role="menu"], [data-radix-menu-content]'));
                let logoutButton = null;
                
                // Search through each menu
                for (const menu of menus) {
                    // Look for logout button by text content
                    const menuItems = Array.from(menu.querySelectorAll('[role="menuitem"]'));
                    logoutButton = menuItems.find(item => 
                        item.textContent?.toLowerCase().includes('logout') || 
                        item.querySelector('img[alt="Logout"]') ||
                        item.querySelector('img[src*="sign-out"]')
                    );
                    
                    if (logoutButton) break;
                }
                
                // If no menu found or no logout button in menu, look more broadly
                if (!logoutButton) {
                    // Find any element that looks like a logout button
                    const possibleLogoutButtons = [
                        // Look for elements with logout text
                        ...Array.from(document.querySelectorAll('div')).filter(el => 
                            el.textContent?.toLowerCase().includes('logout')
                        ),
                        // Look for elements with logout icon
                        ...Array.from(document.querySelectorAll('div:has(img[alt="Logout"])')),
                        ...Array.from(document.querySelectorAll('div:has(img[src*="sign-out"])')),
                    ];
                    
                    // Find the most likely logout button by checking attributes and styles
                    logoutButton = possibleLogoutButtons.find(el => {
                        const style = getComputedStyle(el);
                        // Check for button/menu item characteristics
                        const isClickable = style.cursor === 'pointer' || 
                                           el.getAttribute('role') === 'menuitem' ||
                                           el.getAttribute('role') === 'button';
                        return isClickable;
                    });
                }
                
                if (logoutButton) {
                    // Cast to HTMLElement to access click()
                    (logoutButton as HTMLElement).click();
                    return true;
                }
                
                return false;
            });
            
            if (jsClicked) {
                console.log('Successfully clicked logout button with JavaScript approach');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-js-click-logout-button');
                buttonClicked = true;
            }
        }
        
        if (!buttonClicked) {
            throw new Error('Failed to find and click the logout button using all available methods');
        }
        
        // Wait for logout to complete and redirect
        console.log('Waiting for logout to complete and redirect...');
        await fixture.page.waitForTimeout(5000);
        await ScreenshotHelper.takeScreenshot('after-logout-complete');
        
        console.log('✅ Successfully clicked the logout button');
    } catch (error) {
        console.error(`❌ Error clicking logout button: ${error}`);
        await ScreenshotHelper.takeScreenshot('logout-button-click-error', true, true);
        throw error;
    }
});
