import { useFlightContext } from '@/context/FlightContext';
import * as Dialog from '@radix-ui/react-dialog';
import Image from 'next/image';
import React, { useEffect, ReactNode } from 'react';

const ErrorBoundary = ({ children }: { children: ReactNode }) => {
    const {
        globalMessage,
        updateGlobalPopup,
    } = useFlightContext();

    const { isOpen, message } = globalMessage || {};

    const handleClose = () => {
        updateGlobalPopup({ isOpen: false, message: '', type: '' });
    }

    useEffect(() => {
        if (isOpen) {
        const timer = setTimeout(handleClose, 5000); // Auto-close after 5s
        return () => clearTimeout(timer);
        }
    }, [isOpen, handleClose]);

  return (
    <>
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-40" />
        <Dialog.Content className="fixed z-50 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-2xl shadow-lg w-full max-w-[650px]">
          <Dialog.Description className="text-gray-700">
            <div className='flex items-center gap-2 flex-col justify-center relative'>
                <Image className='absolute top-1 right-0 cursor-pointer' onClick={() => handleClose()} src="https://storage.googleapis.com/nxvoytrips-img/Error/close.svg" alt="error" height={50} width={30} />
                <Image src="https://storage.googleapis.com/nxvoytrips-img/Error/error.svg" alt="error" height={230} width={200} />
                <div className='text-[#1E1E76] font-medium text-[20px] py-5'>{message}</div> 
            </div>
          </Dialog.Description>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>

    {children}

    </>
  );
};

export default ErrorBoundary;
