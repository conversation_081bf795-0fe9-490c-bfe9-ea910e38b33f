import React from 'react';
import { cn } from '../../lib/utils';

interface InputFieldProps {
  label: string;
  placeholder: string;
  type?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  readonly?: boolean;
  disabled?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  placeholder,
  type = 'text',
  value,
  onChange,
  name,
  readonly = false,
  disabled = false
}) => {
  return (
    <div>
      <label className="text-brand-black font-semibold">{label}</label>
      <div className="relative font-proxima-nova w-full p-px rounded-sm h-auto border-[1px] border-neutral bg-brand-white shadow-sm">
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          name={name}
          className={cn(
            "placeholder-neutral-dark text-neutral-dark w-full px-4 py-2 bg-brand-white rounded-sm focus:outline-none",
            type === 'date' && "text-neutral-dark [&::-webkit-calendar-picker-indicator]:invert-[0.5] [&::-webkit-calendar-picker-indicator]:sepia-[1] [&::-webkit-calendar-picker-indicator]:saturate-[1000] [&::-webkit-calendar-picker-indicator]:hue-rotate-[240deg] [&::-webkit-calendar-picker-indicator]:brightness-[0.7] [&::-webkit-calendar-picker-indicator]:contrast-[0.8]",
            type === 'date' && "appearance-none [&::-webkit-datetime-edit]:text-neutral-dark [&::-webkit-datetime-edit-fields-wrapper]:text-neutral-dark [&::-webkit-datetime-edit-text]:text-neutral-dark [&::-webkit-datetime-edit-month-field]:text-neutral-dark [&::-webkit-datetime-edit-day-field]:text-neutral-dark [&::-webkit-datetime-edit-year-field]:text-neutral-dark"
          )}
          readOnly={readonly}
          disabled={disabled}
        />
      </div>
    </div>
  );
};

export default InputField; 