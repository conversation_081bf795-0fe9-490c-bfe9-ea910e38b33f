import { useEffect, useState } from "react";
import { InviteDialog } from "@/components/chat/InviteDialog";
import { SelectLanguage } from "@/components/chat/SelectLanguage";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronRight, User, LogOut, Undo2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AppState } from "@/store/store";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";
import { logoutMethod } from "@/utils/auth";
import { signOut } from "next-auth/react";
import { useCustomSession } from "@/hooks/use-custom-session";
import { getInitials } from "@/screens/dashboard/DashboardNavbar";
import { useChatContext } from "@/context/ChatContext";
import { clearReduxOnLogout } from "@/store/clearRedux";

type ChatHeaderProps = {};

function ProfileDropdown({ userDetails }: { userDetails: any }) {
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;
  const user = userDetails;

  const router = useRouter();

  const handleLogout = async () => {
    const response = await logoutMethod("auth/signout", token);
    if (response.success) {
      await clearReduxOnLogout();
      signOut({ redirect: false });
      handleNavigateHome();
    }
  };

  const handleNavigateHome = () => {
    router.push("/");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="h-[30px] w-[30px]">
          <AvatarImage src={user?.profile_picture || ""} alt="Profile" />
          <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
            {getInitials(user)}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px]">
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={() => router.push("/userprofile")}>
            <User />
            <span>My Account</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => router.push("/")}>
            <Undo2 />
            <span>Back to Home</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleLogout}>
            <LogOut />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const ChatHeader: React.FC<ChatHeaderProps> = () => {
  const chatThread = useSelector((state: AppState) => state.chatThread);
  const { currentThreadName = "" } = chatThread;
  const { currentUserDetails } = useSelector(
    (state: AppState) => state.userDetails
  );
  const [, forceUpdate] = useState(false);

  const { refreshChatHistory, setRefreshChatHistory } = useChatContext();


  // on new chat threadId  refresh
  useEffect(() => {
    if (refreshChatHistory) {
      forceUpdate((prev) => !prev);
      setRefreshChatHistory(false); // reset the flag
    }
  }, [refreshChatHistory]);

  const truncatedThreadTitle =
    currentThreadName && currentThreadName.length > 25
      ? currentThreadName.slice(0, 25) + "..."
      : currentThreadName;

  const currentUser = useSelector((state: AppState) => state.loggedInUser);

  return (
    <div className="w-full py-6 md:relative ">
      <div className="flex h-[42px] justify-between py-1 md:border md:border-[#707FF5] rounded-[12px] md:px-2 items-center items-center">
        <div className="w-full">
          <div className="flex items-center justify-start gap-2 text-[#080236]">
            <p className="font-[600] md:text-[12px] text-[10px] hidden md:block">
              {currentThreadName}
            </p>
            <p className="font-[600] md:text-[12px] text-[10px] md:hidden block">
              {truncatedThreadTitle}
            </p>
            <ChevronRight className="h-5" />
          </div>
        </div>
        <div className="flex w-full justify-end items-center md:gap-4 gap-2">
          <div className="">
            {/* <InviteDialog /> */}
          </div>
          <div className="">
            <SelectLanguage />
          </div>
          <div className="hidden md:block cursor-pointer">
            {currentUser.id && (
              <ProfileDropdown userDetails={currentUserDetails} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
