"use client"

import { But<PERSON> } from "@/components/ui/button"

interface HotelDescriptionProps {
    description: string
    fullDescription: string
    showFullDescription: boolean
    onToggleDescription: () => void
}

export default function HotelDescription({
    description,
    fullDescription,
    showFullDescription,
    onToggleDescription,
}: HotelDescriptionProps) {
    return (
        <div>
            <p className="text-[#080236] text-sm leading-relaxed">{showFullDescription ? fullDescription : description}</p>
            <Button
                variant="outline"
                className="mt-4 rounded-3xl text-[#080236] bg-[#E9E8FC] hover:bg-[#E9E8FC]"
                onClick={onToggleDescription}
            >
                {showFullDescription ? "Read Less" : "Read More"}
            </Button>
        </div>
    )
}
