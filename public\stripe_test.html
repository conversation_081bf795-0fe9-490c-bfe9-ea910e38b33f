<!DOCTYPE html>
<html>
<head>
  <title>Stripe Card Payment</title>
  <script src="https://js.stripe.com/v3/"></script>
  <style>
    #card-element.StripeElement--focus {
      border-color: #6b8eff;
      box-shadow: 0 0 5px rgba(107, 142, 255, 0.5);
    }

    #card-element.StripeElement--invalid {
      border-color: #fa755a;
    }
  </style>
</head>
<body>
  <h1>Pay with Card</h1>

  <form id="payment-form">
    <label>Name: <input type="text" id="name" required /></label><br />
    <label>Email: <input type="email" id="email" required /></label><br />
    <label>Phone: <input type="text" id="phone" required /></label><br />
    <label>Amount (USD): <input type="number" id="amount" required /></label><br /><br />

    <div id="card-element"></div><br />
    <button id="submit">Pay</button>
    <p id="payment-message"></p>
  </form>

  <script>
    const stripe = Stripe("pk_test_51R43lW2KQ2cS6r09euiE1EgYQuHu6A9uYyjLpedQovTg9hbhEOCFssO9Akbps83bxKo0sOkUih7UGJTmtCypKLv100zsx7fjDK");
    const elements = stripe.elements();
    const card = elements.create("card", {
                  style: {
                    base: {
                      color: "#32325d",
                      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                      fontSmoothing: "antialiased",
                      fontSize: "16px",
                      "::placeholder": {
                        color: "#aab7c4"
                      }
                    },
                    invalid: {
                      color: "#fa755a",
                      iconColor: "#fa755a"
                    }
                  }
                });
    card.mount("#card-element");

    const form = document.getElementById("payment-form");

    form.addEventListener("submit", async (e) => {
      e.preventDefault();

      const name = document.getElementById("name").value;
      const email = document.getElementById("email").value;
      const phone = document.getElementById("phone").value;
      const amount = parseFloat(document.getElementById("amount").value) * 100; // convert to cents

      document.getElementById("submit").disabled = true;
      const message = document.getElementById("payment-message");
      message.textContent = "Processing...";

      try {
        // Step 1: Get or create Stripe customer
        const customerRes = await fetch("http://localhost:8000/api/v1/payment/stripe-customer", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name, email, phone })
        });

        const customerData = await customerRes.json();
        const customerId = customerData.customerId;

        message.textContent = "Processing... (Customer fetched: " + customerId + ")";

        // Step 2: Create payment intent
        const intentRes = await fetch("http://localhost:8000/api/v1/payment/stripe-intent", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            amount: amount,
            currency: "usd",
            customer_id: customerId
          })
        });

        const intentData = await intentRes.json();
        const clientSecret = intentData.clientSecret;
        const paymentIntentId = intentData.paymentIntentId;

        message.textContent = "Processing... (Intent fetched: " + paymentIntentId + ")";

        // Step 3: Confirm card payment
        const { paymentIntent, error } = await stripe.confirmCardPayment(clientSecret, {
          payment_method: { card },
          setup_future_usage: "off_session"
        });

        if (error) {
          message.textContent = error.message;
          message.textContent = "Processing... (Error: " + error.message + ")";
          document.getElementById("submit").disabled = false;
          return;
        }

        message.textContent = "Processing... (Payment intent fetched: " + paymentIntent.id + ")";

        if (paymentIntent.status === "requires_capture") {
          message.textContent = "Processing... (Payment intent capture started)";
          // Step 4: Capture the payment intent
          const captureRes = await fetch("http://localhost:8000/api/v1/payment/stripe-capture", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ payment_intent_id: paymentIntentId })
          });

          const captureData = await captureRes.json();

          if (captureData.status === "succeeded") {
            message.textContent = "Payment captured successfully!";
          } else {
            message.textContent = `Payment not captured. Status: ${captureData.status}`;
          }
        } else {
          message.textContent = `Payment failed or is in unexpected status: ${paymentIntent.status}`;
        }

      } catch (err) {
        message.textContent = "Error: " + err.message;
      }
      document.getElementById("submit").disabled = false;
    });
  </script>
</body>
</html>