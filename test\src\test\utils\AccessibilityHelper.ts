import { Page } from "playwright";
import AxeBuilder from "@axe-core/playwright";
import path from "path";
import fs from 'fs';
import Utils from "./Utils";
import { FileUtils } from "./FileUtils";

export default class AccessibilityHelper {

    public static async checkAccessibility(page: Page): Promise<void> {
        await page.waitForLoadState("domcontentloaded");
        const axe = new AxeBuilder({ page });
        const results = await axe.analyze();
        FileUtils.createDirectories("test-reports/accessibility");
        this.saveAccessibilityViolationsResults(results.violations, "test-reports/accessibility/accessibility-results-" + Utils.getCurrentDateTime() + ".json");
    }

    public static async saveAccessibilityViolationsResults(results: any, filePath: string) {
        try {
            const jsonResults = JSON.stringify(results, null, 2);
            const fullPath = path.resolve(filePath);
            fs.writeFileSync(fullPath, jsonResults, 'utf-8');
            console.log(`Accessibility results saved to ${fullPath}`);
        } catch (error) {
            console.error('Error saving accessibility results:', error);
        }
    }
}