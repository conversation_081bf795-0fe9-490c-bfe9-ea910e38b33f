import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens: {
        xs: { max: "640px" },
        // => @media (max-width: 640px) { ... }
        // 'sm': {'min': '501px','max': '767px'},
        // => @media (max-width: 768px) { ... }

        // 'md': {'min': '768px','max': '1024px'},
        // // => @media (max-width: 1024px) { ... }

        // 'lg': {'min': '1025px','max': '1280px'},
        // // => @media (max-width: 1280px) { ... }

        // 'xl': {'min': '1281px','max': '1535px'},
        // // => @media (max-width: 1536px) { ... }

        // '2xl': {'min': '1536px'},
        // => @media (mix-width: 1536px) { ... }
      },

      container: {
        screens: {
          // xs: {'max':"450px"},
          // //xs: '320px',
          // sm: '640px',
          // md: '768px',
          // lg: '1024px',
          // xl: '1280px',
          // '2xl': '1536px',
        },
        padding: {
          DEFAULT: "1rem",
          sm: "2rem",
          lg: "4rem",
          xl: "5rem",
          "2xl": "6rem",
        },
      },
      fontFamily: {
        "proxima-nova": ["proxima-nova", "sans-serif"],
      },
      spacing: {
        "video-xs": "300px",
        "video-sm": "600px",
        "video-md": "700px",
        "video-lg": "800px",
        "video-xl": "920px",
      },
      fontSize: {
        "heading-xs": ["32px", "40px"],
        "heading-sm": ["42px", "52px"],
        "heading-md": ["52px", "63px"],
        "heading-lg": ["58px", "70px"],
      },
      keyframes: {
        marquee: {
          "0%": { transform: "translateX(0%)" },
          "100%": { transform: "translateX(-100%)" },
        },
      },
      animation: {
        marquee: "marquee 10s linear infinite",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        brand: {
          DEFAULT: "#1E1E76",
          white: "#FFFFFF",
          black: "#080236",
          grey: "#8C8C8C",
          border: "#EBEBEB",
        },

        accent: {
          DEFAULT: "#f2f2ff",
          foreground: "#707ff5",
        },

        neutral: {
          DEFAULT: "#EBEBEB",
          dark: "#999999",
          foreground: "#8C8C8C",
        },

        // Functional Colors
        success: {
          DEFAULT: "#84ebb4", // Green
          dark: "#1fc16b",
          foreground: "#1fc16b",
        },

        warning: {
          DEFAULT: "#f7c83b", // Yellow/Orange
          dark: "#e4ae0d",
          foreground: "#f7c83b",
        },

        error: {
          DEFAULT: "#fb3748", // Red
          dark: "#d00416",
          foreground: "#fb3748",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },

        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        "lucky-blue": "#1E1E76",
        portage: "#A195F9",
        cornflower: "#707FF5",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      backgroundImage: {
        "button-gradient":
          "linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)",
      },
    },
  },
  plugins: [],
} satisfies Config;

export default config;
