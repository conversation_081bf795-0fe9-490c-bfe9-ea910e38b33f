"use client";

import { useState } from "react";
import { generateUUID } from "@/lib/utils/uuid";
import { agentPostMethod } from "@/utils/api";
import { findAge } from "@/lib/utils/formValidation";
import tracker from "@/utils/posthogTracker";
import { useLoadingTransition } from "@/hooks/useLoadingTransition";
import type { BookingResponse } from "@/types/booking";

interface UseBookingAPIProps {
  tripSummaryDetails: any;
  token: string | undefined;
  updateGlobalPopup: (params: any) => void;
  handleBookingConfirmation: (success: boolean) => void;
  showErrorModal: (error: any) => void;
  dispatch: any;
  updateTripSummary: any;
}

export const useBookingAPI = ({
  tripSummaryDetails,
  token,
  updateGlobalPopup,
  handleBookingConfirmation,
  showErrorModal,
  dispatch,
  updateTripSummary,
}: UseBookingAPIProps) => {
  const [flightTermResponse, setFlightTermResponse] =
    useState<BookingResponse | null>(null);
  const [bookingResponse, setBookingResponse] = useState<any>(null);
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [bookingMessage, setBookingMessage] = useState<any>(null);
  const { isFetching, runWithLoading } = useLoadingTransition();

  // Prepare contact details for API calls
  const getContactDetails = () => {
    return {
      title: tripSummaryDetails?.paymentDetails?.title,
      name: tripSummaryDetails?.paymentDetails?.name,
      street: `${tripSummaryDetails?.paymentDetails?.street} ${tripSummaryDetails?.paymentDetails?.other || ""}`,
      city: tripSummaryDetails?.paymentDetails?.city,
      postcode: tripSummaryDetails?.paymentDetails?.postcode,
      country_code: tripSummaryDetails?.paymentDetails?.country_code,
      phone_int_code: tripSummaryDetails?.paymentDetails?.phone_int_code || "",
      phone_area_code: "",
      phone_number:
        tripSummaryDetails?.paymentDetails?.contact_details_phone_number ||
        "**********",
      email: tripSummaryDetails?.paymentDetails?.email,
      InternationalCode: "",
      AreaCode: "",
      Number: "",
      extension: "",
      province: tripSummaryDetails?.paymentDetails?.state || "OT",
    };
  };

  // Fetch flight terms
  const fetchFlightTerms = () => {
    runWithLoading(async () => {
      const contactDetails = getContactDetails();

      const payload: any = {
        routing_id: tripSummaryDetails?.sharedFlightResults?.routing_id,
        outward_id: tripSummaryDetails?.selectedOutboundFlight?.id,
        return_id: tripSummaryDetails?.selectedInboundFlight?.id,
        request_uuid: generateUUID(),
        country_of_user: tripSummaryDetails?.paymentDetails?.country_code,
        passengers: tripSummaryDetails?.passengerDetails?.map((p: any) => ({
          title: p.title,
          first_name: p.firstName,
          middle_name: p.middleName,
          last_name: p.lastName,
          age: findAge(
            p.dob,
            tripSummaryDetails?.selectedInboundFlight?.departure
          ),
          date_of_birth: p.dob,
        })),
        contact_details: contactDetails,
      };

      // Add luggage options if available
      let luggagePayload = {};
      const { is_per_passenger = false, option_names = {} } =
        tripSummaryDetails?.luggageOptions || {};
      const key = tripSummaryDetails?.luggageOptions?.is_per_passenger
        ? "passenger_level"
        : "booking_level";

      const { _outward, _return }: any =
        tripSummaryDetails?.selectedLuggageInfo || {};
      if (
        _outward &&
        _return &&
        (Object.keys(_outward).length > 0 || Object.keys(_return).length > 0)
      ) {
        luggagePayload = {
          [key]: {
            _outward,
            _return,
            _options: { is_per_passenger, option_names },
          },
        };

        if (Object.keys(luggagePayload).length > 0) {
          payload.luggage_options = luggagePayload;
        }
      }

      try {
        const response = await agentPostMethod(
          "flight/flight-terms",
          payload,
          token ?? ""
        );
        if (
          response?.detail?.status === "success" &&
          response.detail.data?.success
        ) {
          setFlightTermResponse(response.detail.data);

          // PostHog tracking
          const flowId = tracker.getFlowId();
          tracker.trackEvent("Step 4 - Completed", {
            page: "Completed",
            flowId,
          });
          tracker.trackEvent("flight_terms_success", {
            route: "flight/flight-terms",
            payloadSent: payload,
            responseData: response.detail.data,
            status: "success",
          });
          tracker.trackEvent("flight_terms_summary", {
            result: "success",
            reason: "N/A",
          });

        } else {
          const failureReason: string =
            response?.detail?.message || "Unknown server failure";
          handleBookingConfirmation(false);
          showErrorModal(response);

          // PostHog tracking
          tracker.trackEvent("flight_terms_failure", {
            route: "flight/flight-terms",
            payloadSent: payload,
            responseData: response,
            reason: failureReason,
            status: "failure",
          });
          tracker.trackEvent("flight_terms_summary", {
            result: "failure",
            reason: failureReason,
          });
        }
      } catch (error) {
        handleBookingConfirmation(false);
        const err = error as Error;
        const reason = err?.message || "Network or unknown error";

        // PostHog tracking
        tracker.trackEvent("flight_terms_failure", {
          route: "flight/flight-terms",
          payloadSent: payload,
          responseError: error,
          reason,
          status: "failure",
        });
        tracker.trackEvent("flight_terms_summary", {
          result: "failure",
          reason,
        });
      }
    });
  };

  // Book flight
  const bookFlight = (flightTerms: BookingResponse) => {
    runWithLoading(async () => {
      const bookingPayload = {
        tf_booking_reference: flightTerms.tf_booking_reference,
        expected_amount: flightTerms.expected_amount,
        expected_currency: flightTerms.expected_currency,
        request_uuid: generateUUID(),
        order_id: tripSummaryDetails?.paymentDetails?.orderId,
      };

      try {
        const response = await agentPostMethod(
          "flight/flight-book",
          bookingPayload,
          token ?? ""
        );
        if (
          response?.detail?.status === "success" &&
          response.detail.data?.success
        ) {
          setBookingMessage(response.detail.message);
          setBookingResponse(response.detail.data);
        } else {
          handleBookingConfirmation(false);
          showErrorModal(response);
        }
      } catch (error) {
        handleBookingConfirmation(false);
      }
    });
  };

  // Fetch booking details
  const fetchBookingDetails = (flightTerms: BookingResponse | null) => {
    runWithLoading(async () => {
      const bookingdetailsPayload = {
        tf_booking_reference: flightTerms?.tf_booking_reference,
        request_uuid: generateUUID(),
        order_id: tripSummaryDetails?.paymentDetails?.orderId,
      };

      try {
        const response = await agentPostMethod(
          "flight/booking-details",
          bookingdetailsPayload,
          token ?? ""
        );
        if (
          response?.detail?.status === "success" &&
          response.detail.data?.success
        ) {
          setBookingDetails(response.detail.data);
          dispatch(updateTripSummary({ bookingDetails: response.detail.data }));
        } else {
          updateGlobalPopup({
            isOpen: true,
            message:
              "There is a error while fetching booking details. Please try again",
            type: "error",
          });
        }
      } catch (error) {
        updateGlobalPopup({
          isOpen: true,
          message:
            "There is a error while fetching booking details. Please try again",
          type: "error",
        });
      }
    });
  };

  return {
    isFetching,
    flightTermResponse,
    bookingResponse,
    bookingDetails,
    bookingMessage,
    fetchFlightTerms,
    bookFlight,
    fetchBookingDetails,
  };
};
