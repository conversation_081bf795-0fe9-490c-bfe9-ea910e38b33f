"use client"

import { useState } from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

type FilterOption = {
    id: string
    label: string
    active: boolean
}

export default function RoomOptions() {
    const [filters, setFilters] = useState<FilterOption[]>([
        { id: "all-rooms", label: "All Rooms", active: true },
        { id: "fully-refundable", label: "Fully Refundable", active: true },
        { id: "pay-later", label: "Pay Later", active: false },
        { id: "kitchen", label: "Kitchen", active: false },
        { id: "free-breakfast", label: "Free Breakfast", active: false },
    ])

    const [bedsOpen, setBedsOpen] = useState(false)

    const toggleFilter = (id: string) => {
        setFilters(filters.map((filter) => (filter.id === id ? { ...filter, active: !filter.active } : filter)))
    }

    return (
        <div className="mb-6">
            <h2 className="text-[30px] font-bold text-[#080236] mb-4">Room Options</h2>
            <div className="flex flex-wrap gap-2">
                <div className="text-base text-[#959AC2] flex items-center mr-2">Filter by:</div>
                {filters.map((filter) => (
                    <Badge
                        key={filter.id}
                        variant={filter.active ? "default" : "outline"}
                        className={`text-sm font-medium px-3 py-1.5 cursor-pointer border border-[#B4BBE8] ${filter.active ? "hover:bg-[#4B4BC3] bg-[#4B4BC3] text:white" : "text:[#4B4BC3]"
                            }`}
                        onClick={() => toggleFilter(filter.id)}
                    >
                        {filter.label}
                        {filter.active && (
                            <X
                                className="ml-1 h-3 w-3"
                                onClick={(e) => {
                                    e.stopPropagation()
                                    toggleFilter(filter.id)
                                }}
                            />
                        )}
                    </Badge>
                ))}
                <div className="relative">
                    <Badge variant="outline" className="px-3 py-1.5 cursor-pointer text-sm font-medium text:[#4B4BC3]" onClick={() => setBedsOpen(!bedsOpen)}>
                        Number of Beds <span className="ml-1">▼</span>
                    </Badge>
                    {bedsOpen && (
                        <div className="absolute top-full left-0 mt-1 bg-white shadow-md rounded-md p-2 z-10 w-48 text:[#4B4BC3]">
                            <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => setBedsOpen(false)}>
                                1 Bed
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => setBedsOpen(false)}>
                                2 Beds
                            </Button>
                            <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => setBedsOpen(false)}>
                                3+ Beds
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}
