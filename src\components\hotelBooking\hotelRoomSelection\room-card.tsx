"use client"

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { AppState } from '@/store/store';
import { setSelectedRoom } from '@/store/slices/hotelBookingContext';
import ImageCarousel from "./image-carousel"
import { Button } from "@/components/ui/button"
import { Wifi, Car, Coffee, Phone, Wind, Dumbbell } from "lucide-react";

interface RoomCardProps {
    room: any // Enhanced room from Redux
    selectedHotel: any
    onViewDetails: (rateIndex: number) => void
}

export default function RoomCard({ room, selectedHotel, onViewDetails }: RoomCardProps) {
    const [selectedRateIndex, setSelectedRateIndex] = useState(0);
    const router = useRouter();
    const dispatch = useDispatch();
    const hotelBookingContext = useSelector((state: AppState) => state.hotelBookingContext.hotelBookingContext);

    if (!room.rate_options || room.rate_options.length === 0) {
        return null;
    }

    const selectedRate = room.rate_options[selectedRateIndex];

    const handleBooking = () => {
        // Store selected room in booking context
        dispatch(setSelectedRoom({
            hotel: selectedHotel,
            room: room,
            selectedRate: selectedRate,
            selectedRateIndex: selectedRateIndex,
        }));

        // Navigate to review page
        router.push('/hotel-review');
    };

    const formatCurrency = (amount: string | number) => {
        const currency = selectedHotel?.currency || 'INR';
        const currencySymbol = currency === 'EUR' ? '€' : '₹';
        const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        return `${currencySymbol}${numAmount.toLocaleString()}`;
    };

    const formatDate = (dateString?: string) => {
        if (!dateString) return '';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
            });
        } catch {
            return dateString;
        }
    };

    // Calculate discount
    const totalDiscountAmount = selectedRate.offers?.reduce((sum: number, offer: any) => 
        sum + Math.abs(parseFloat(offer.amount || '0')), 0
    ) || 0;

    const netPrice = parseFloat(selectedRate.net_price || '0');
    const discountPercentage = totalDiscountAmount > 0 && netPrice > 0
        ? Math.round((totalDiscountAmount / (netPrice + totalDiscountAmount)) * 100)
        : 0;

    const nights = hotelBookingContext?.nights || 2;
    const totalPrice = netPrice * nights;

    // Get amenities with icons
    const getAmenities = () => {
        const amenities = selectedRate.amenities || [];
        return amenities.map((amenityName: string) => {
            let icon = <span className="h-5 w-5"></span>;
            
            if (amenityName.toLowerCase().includes('breakfast')) {
                icon = <Coffee className="h-5 w-5" />;
            } else if (amenityName.toLowerCase().includes('wifi')) {
                icon = <Wifi className="h-5 w-5" />;
            } else if (amenityName.toLowerCase().includes('parking')) {
                icon = <Car className="h-5 w-5" />;
            } else if (amenityName.toLowerCase().includes('room service')) {
                icon = <Phone className="h-5 w-5" />;
            } else if (amenityName.toLowerCase().includes('air conditioning')) {
                icon = <Wind className="h-5 w-5" />;
            } else if (amenityName.toLowerCase().includes('fitness') || amenityName.toLowerCase().includes('gym')) {
                icon = <Dumbbell className="h-5 w-5" />;
            }

            return { icon, name: amenityName };
        });
    };

    const amenities = getAmenities();
    const images = room.room_images || [selectedHotel?.imageSrc || '/placeholder.jpg'];

    return (
        <div
            className="border border-[#E6E3FF] rounded-2xl overflow-hidden bg-transparent shadow-3xl hover:shadow-3xl transition-shadow h-[750px] w-full flex flex-col"
            style={{ boxShadow: "0 10px 25px -5px rgba(75, 75, 195, 0.1), 0 8px 10px -6px rgba(75, 75, 195, 0.1)" }}
        >
            <div className="p-4 pb-2">
                <h3 className="font-bold text-[#1E1E76] mb-1">{room.room_name}</h3>
                <div className="text-[#959AC2] text-sm mb-2">
                    Sleeps {room.room_size?.max_pax || 2}
                </div>
            </div>

            <div className="relative">
                <ImageCarousel images={images} height={180} />
            </div>

            <div className="p-4 flex flex-col flex-grow">
                <div className="flex-grow">
                    <button onClick={() => onViewDetails(selectedRateIndex)} className="text-[#4B4BC3] text-sm font-medium mb-2 hover:underline">
                        View Room Details and Photos
                    </button>

                    <h4 className="font-bold text-[#1E1E76] mb-3">{room.room_description || room.room_name}</h4>

                    <div className="flex flex-wrap gap-4 mb-4 ">
                        {amenities.map((amenity, index) => (
                            <div key={index} className="flex items-center text-xs text-[#4B4BC3] bg-[#E6E3FF] px-3 py-1 rounded-2xl">
                                <span className="mr-1 text-[#4B4BC3]">{amenity.icon}</span>
                                {amenity.name}
                            </div>
                        ))}
                    </div>

                    {/* Rate selector if multiple rates */}
                    {room.rate_options.length > 1 && (
                        <div className="mb-4">
                            <div className="text-xs text-[#1E1E76] mb-2">Board options:</div>
                            <div className="flex gap-2 flex-wrap">
                                {room.rate_options.map((rate: any, index: number) => (
                                    <button
                                        key={index}
                                        onClick={() => setSelectedRateIndex(index)}
                                        className={`text-xs px-2 py-1 rounded ${
                                            selectedRateIndex === index
                                                ? 'bg-[#4B4BC3] text-white'
                                                : 'bg-[#E6E3FF] text-[#4B4BC3]'
                                        }`}
                                    >
                                        {rate.board_name}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Divider after amenities */}
                    <div className="border-t border-[#B4BBE8] my-4"></div>

                    <div className="flex justify-between items-start">
                        <div>
                            <div className="text-[#4B4BC3] font-semibold text-lg">
                                {selectedRate.cancellation_policies?.length > 0 ? "Fully Refundable" : "Non Refundable"}
                            </div>
                            {selectedRate.cancellation_policies?.length > 0 && (
                                <div className="text-[#959AC2] text-sm">
                                    Until {formatDate(selectedRate.cancellation_policies[0].from)}
                                </div>
                            )}
                            <div className="text-[#1E1E76] font-medium mt-2">Book now, pay later</div>
                        </div>
                        <div className="flex flex-col items-end">
                            {discountPercentage > 0 && <div className="text-[#24C72F] font-bold text-2xl">{discountPercentage}% Off</div>}
                            <div className="text-[#1E1E76] font-bold text-3xl">{formatCurrency(netPrice)}</div>
                            <div className="text-[#959AC2] text-sm">
                                {formatCurrency(totalPrice)} for {nights} nights
                            </div>
                        </div>
                    </div>
                </div>

                {/* Fixed button position */}
                <div className="mt-auto">
                    <div className="flex justify-center mb-4">
                        <Button 
                            onClick={() => handleBooking()}
                            className="py-3 px-16 text-white rounded-full min-w-[200px] md:min-w-0 bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]"
                        >
                            Book
                        </Button>
                    </div>

                    {/* Divider after button */}
                    <div className="border-t border-[#B4BBE8]"></div>
                </div>
            </div>
        </div>
    )
}