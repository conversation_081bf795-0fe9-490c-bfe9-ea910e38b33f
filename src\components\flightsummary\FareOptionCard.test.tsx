import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FareOptionCard from "./FareOptionCard";

// src/components/flightsummary/FareOptionCard.test.tsx

// Mocks
jest.mock("@/context/FlightContext", () => ({
  useFlightContext: () => ({
    sharedFlightResults: {
      airline_data: [{ code: "AI", name: "Air India" }],
    },
    searchFilter: { travel_class: "Business" },
  }),
}));
jest.mock("react-redux", () => ({
  useSelector: jest.fn().mockReturnValue({
    chatResult: { input: { travel_class: "Economy" } },
  }),
}));
jest.mock("next/image", () => (props: any) => (
  // eslint-disable-next-line @next/next/no-img-element
  <img {...props} alt={props.alt || "mocked image"} />
));
jest.mock("@/lib/utils/airline", () => ({
  getAirlineName: (code: string) => (code === "AI" ? "Air India" : "Unknown"),
}));

const baseProps = {
  title: "Super Saver",
  price: "$123",
  discount: "10% off",
  selected: false,
  buttonLabel: "Upgrade",
  flight: {
    id: "f1",
    supplier_logo: "/logo.png",
    airline: "Air India",
    airline_code: "AI",
    origin: "DEL",
    destination: "BOM",
  },
  options: { "Baggage": true, "Meal": false },
  onClick: jest.fn(),
  fareFilght: { id: "f2" },
};

describe("FareOptionCard", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders with required props", () => {
    render(<FareOptionCard {...baseProps} />);
    expect(screen.getByText("Super Saver")).toBeInTheDocument();
    expect(screen.getByText("$123")).toBeInTheDocument();
    expect(screen.getByText("10% off")).toBeInTheDocument();
    expect(screen.getByText("Upgrade")).toBeInTheDocument();
    expect(screen.getByText("DEL - BOM")).toBeInTheDocument();
    expect(screen.getByText("Air India")).toBeInTheDocument();
  });

  it("renders selected state and disables button", () => {
    render(<FareOptionCard {...baseProps} selected={true} />);
    expect(screen.getByText("Selected")).toBeInTheDocument();
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
    expect(button).toHaveClass("text-white");
  });

  it("renders unselected state and enables button", () => {
    render(<FareOptionCard {...baseProps} selected={false} />);
    const button = screen.getByRole("button");
    expect(button).not.toBeDisabled();
    expect(button).toHaveTextContent("Upgrade");
  });

  it("calls onClick when button is clicked and not selected", () => {
    const onClick = jest.fn();
    render(<FareOptionCard {...baseProps} selected={false} onClick={onClick} />);
    fireEvent.click(screen.getByRole("button"));
    expect(onClick).toHaveBeenCalledWith(null);
  });

  it("does not call onClick when selected", () => {
    const onClick = jest.fn();
    render(<FareOptionCard {...baseProps} selected={true} onClick={onClick} />);
    fireEvent.click(screen.getByRole("button"));
    expect(onClick).not.toHaveBeenCalled();
  });

  it("renders correct benefit icons for options", () => {
    render(<FareOptionCard {...baseProps} />);
    // Should render both true and false icons
    expect(screen.getByText("Baggage")).toBeInTheDocument();
    expect(screen.getByText("Meal")).toBeInTheDocument();
  });


  it("handles missing/undefined props gracefully", () => {
    render(<FareOptionCard title="" price="" discount="" options={{}} />);
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("renders airline name using getAirlineName and sharedFlightResults", () => {
    render(<FareOptionCard {...baseProps} />);
    expect(screen.getByText("Air India")).toBeInTheDocument();
  });

  it("renders origin and destination if flight is present", () => {
    render(<FareOptionCard {...baseProps} />);
    expect(screen.getByText("DEL - BOM")).toBeInTheDocument();
  });

  it("does not break if flight or fareFilght is missing", () => {
    render(<FareOptionCard {...baseProps} flight={undefined} fareFilght={undefined} />);
    expect(screen.getByRole("button")).toBeInTheDocument();
  });
});