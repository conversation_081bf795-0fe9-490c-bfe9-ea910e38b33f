Feature: Flight Booking Journey\n  As a user\n  I want to book a flight using the NxVoy chat interface\n  So that I can complete my travel plans online\n\n  Scenario: Book a flight from Chennai to Delhi on June 20th\n    Given The user types the \"nxvoy.users.standard\" username on the login page\n    And The user types the \"nxvoy.users.standard\" password on the login page\n    And The user clicks on the login button\n    And the user should be logged in\n    And The user accepts all cookies\n    And The user enters \"Book a flight from Chennai to Delhi on June 20th\" in the chat\n    And The user solves the reCAPTCHA manually\n    And The user sends the chat message\n    Then The user should see available flight options\n    # Add more steps for selecting, booking, and confirming as needed 