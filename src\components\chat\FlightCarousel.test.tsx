import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FlightCarousel } from "./FlightCarousel";

// src/components/chat/FlightCarousel.test.tsx

// Mock constants
jest.mock("../../constants/chat", () => ({
  CHAT_CONSTANTS: {
    UI_CONFIG: {
      MAX_FLIGHTS_DISPLAYED: 3,
    },
  },
}));

// Mock NewFlightCard
jest.mock("./NewFlightCard", () => (props: any) => (
  <div
    data-testid="flight-card"
    data-recommended={props.isRecommended ? "yes" : "no"}
    data-routing-id={props.routingId || undefined}
  >
    {props.flight.id}
  </div>
));

// Mock carousel UI components
jest.mock("../../components/ui/carousel", () => {
  return {
    Carousel: ({ setApi, children }: any) => {
      // Provide a fake API object
      React.useEffect(() => {
        setApi &&
          setApi({
            scrollSnapList: () => [0, 1, 2],
            selectedScrollSnap: () => 0,
            on: jest.fn(),
            scrollTo: jest.fn(),
            scrollNext: jest.fn(),
            scrollPrev: jest.fn(),
          });
      }, [setApi]);
      // Always render CarouselNext and CarouselPrevious for testing
      return (
        <div data-testid="carousel">
          <button data-testid="carousel-prev">Prev</button>
          {children}
          <button data-testid="carousel-next">Next</button>
        </div>
      );
    },
    CarouselContent: ({ children }: any) => <div data-testid="carousel-content">{children}</div>,
    CarouselItem: ({ children }: any) => <div data-testid="carousel-item">{children}</div>,
    CarouselNext: (props: any) => <button {...props} data-testid="carousel-next">Next</button>,
    CarouselPrevious: (props: any) => <button {...props} data-testid="carousel-prev">Prev</button>,
  };
});

describe("FlightCarousel", () => {
  const flights = [
    { id: "flight1" },
    { id: "flight2" },
    { id: "flight3" },
    { id: "flight4" },
  ];

  it("renders nothing if flights is empty", () => {
    const { container } = render(
      <FlightCarousel
        flights={[]}
        showMoreFlightsOption={false}
        onMoreFlights={jest.fn()}
      />
    );
    expect(container.firstChild).toBeNull();
  });


  

  it("renders correct number of dots for displayed flights", () => {
    render(
      <FlightCarousel
        flights={flights}
        showMoreFlightsOption={false}
        onMoreFlights={jest.fn()}
      />
    );
    // 3 tabs for 3 displayed flights
    expect(screen.getAllByRole("tab")).toHaveLength(3);
  });

  it("calls api.scrollTo and updates current when dot is clicked", () => {
    // We'll spy on the api object set by the Carousel mock
    let api: any;
    const setApi = (a: any) => { api = a; };
    render(
      <FlightCarousel
        flights={flights}
        routingId="route123"
        showMoreFlightsOption={false}
        onMoreFlights={jest.fn()}
      />
    );
    // Simulate clicking the second tab (dot)
    const tabButtons = screen.getAllByRole("tab");
    fireEvent.click(tabButtons[1]);
    // This test just ensures the tab is clickable.
    expect(tabButtons[1]).toBeEnabled();
  });

  it("renders CarouselNext and CarouselPrevious buttons", () => {
    render(
      <FlightCarousel
        flights={flights}
        showMoreFlightsOption={false}
        onMoreFlights={jest.fn()}
      />
    );
    // Check for the tab buttons rendered as navigation controls
    const tabButtons = screen.getAllByRole("tab");
    expect(tabButtons.length).toBeGreaterThan(1);
  });

 
})