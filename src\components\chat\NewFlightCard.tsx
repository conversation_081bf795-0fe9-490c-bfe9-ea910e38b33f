"use client";

import type React from "react";
import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { updateTripSummary } from "@/store/slices/tripSummary";
import type { AppState } from "@/store/store";
import FlightHeader from "./flightCards/flight-header";
import FlightLegDesktop from "./flightCards/flight-leg-desktop";
import FlightLegMobile from "./flightCards/flight-leg-mobile";
import { TripOptions } from "@/constants/flight";
import { Button } from "@/components/ui/button";
import { formatFlightPrice } from "@/lib/utils/formatPrice";

interface TravelClass {
  class: string;
  supplier_class: string;
  supplier_base_code: string;
  supplier_rbd_code: string;
}

interface Segment {
  origin: string;
  destination: string;
  departure_time_ampm: string;
  departure_date: string;
  arrival_time_ampm: string;
  arrival_date: string;
  duration: string;
  operator: string;
  operator_code: string;
  flight_number: string;
  travel_class: TravelClass;
  supplier_logo: string;
  wait_time?: string;
  dept_airport_name: string;
  arr_airport_name: string;
}

interface Price {
  amount: any;
  currency: string;
}

interface Flight {
  id: string;
  airline: string;
  airline_code: string;
  origin: string;
  destination: string;
  departure: string;
  arrival: string;
  duration: string;
  departure_time_ampm: string;
  arrival_time_ampm: string;
  segments: Segment[];
  supplier_logo: string;
  price: Price;
  departure_date: string;
  arrival_date: string;
}

interface NewFlightCardProps {
  flight: {
    rank: number;
    outward_flight: Flight;
    return_flight?: Flight | null;
  };
  isRecommended?: boolean;
  routing_id?: string;
  showMoreFlightsOption: any;
  onMoreFlights: any;
}

const NewFlightCard: React.FC<NewFlightCardProps> = ({
  showMoreFlightsOption,
  onMoreFlights,
  flight,
  isRecommended = false,
  routing_id = "",
}) => {
  const [isOutboundOpen, setIsOutboundOpen] = useState(false);
  const [isInboundOpen, setIsInboundOpen] = useState(false);

  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
  const { chatResult } = chatThreadDetails;

  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const { selectedOutboundFlight, selectedInboundFlight } = tripSummaryDetails;

  const dispatch = useDispatch();

  const flightRankImage = new Map([
    [
      1,
      "https://storage.googleapis.com/nxvoytrips-img/ChatPage/default-flight.jpg",
    ],
    [2, "https://storage.googleapis.com/nxvoytrips-img/ChatPage/flight-2.png"],
    [3, "https://storage.googleapis.com/nxvoytrips-img/ChatPage/flight-3.png"],
  ]);

  const handleSelectFlight = (
    flightData: Flight,
    type: string,
    flightDatabound: Flight | null
  ) => {
    if (
      type === "outbound" &&
      flightData &&
      Object.keys(flightData).length > 0
    ) {
      dispatch(updateTripSummary({ selectedOutboundFlight: flightData }));
    }

    if (
      type === "inbound" &&
      flightData &&
      Object.keys(flightData).length > 0
    ) {
      dispatch(
        updateTripSummary({
          selectedOutboundFlight: flightDatabound,
          selectedInboundFlight: flightData,
        })
      );
    }
  };

  const renderFlightLeg = (
    label: string,
    flightData: Flight,
    flightDatabound: Flight | null,
    type: string,
    isOpen: boolean,
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    showButton: boolean
  ) => {
    const isSelected =
      (type === "outbound" && selectedOutboundFlight?.id === flightData.id) ||
      (type === "inbound" && selectedInboundFlight?.id === flightData.id);

    const onSelectFlight = () =>
      handleSelectFlight(flightData, type, flightDatabound);

    const getAirportDisplayName = (
      airportCode: any,
      airportOptions: any
    ): string => {
      if (!airportOptions || typeof airportOptions !== "object") {
        return airportCode;
      }
      const airport = airportOptions[airportCode];

      if (airport && isOpen) {
        return `${airportCode}, ${airport.airport_name}, ${airport.city_name_original}`;
      }

      return airportCode;
    };
    const { tripType } = chatThreadDetails;
    return (
      <div className={` w-full  ${type === "inbound" ? "border-t" : ""}`}>
        <FlightLegDesktop
          label={label}
          flightData={flightData}
          type={type}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          getAirportDisplayName={getAirportDisplayName}
          airportOptions={chatResult?.output?.airport_data}
          onSelectFlight={onSelectFlight}
          isSelected={isSelected}
        />

        {/* <FlightLegMobile
          label={label}
          flightData={flightData}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          getAirportDisplayName={getAirportDisplayName}
          airportOptions={chatResult?.output?.airport_data}
          onSelectFlight={onSelectFlight}
          isSelected={isSelected}
        /> */}

        {/* Right side: button */}
        {showButton && (
          <div className="items-center justify-center w-full">
            <div className="flex justify-between items-center bg-[#F2F2FF] px-4 py-3 rounded-b-2xl">
              <div className="grid w-5/6">
                <p className="font-semibold">
                  {formatFlightPrice(flightData.price)}
                </p>

                <p className="font-medium text-sm md:text-base">
                  For all Passengers
                </p>
              </div>
              <div className="flex flex-col md:flex-row gap-4 w-full">
                <div className="w-full md:order-2">
                  <Button
                    onClick={onSelectFlight}
                    className="w-full bg-brand text-white rounded-lg font-medium"
                  >
                    {tripType?.trim().length === 0
                      ? "Select Flight"
                      : isSelected
                        ? "Selected"
                        : "Select Flight"}
                  </Button>
                </div>

                {showMoreFlightsOption && (
                  <div className="w-full md:ml-4 order-2 md:order-1">
                    <Button
                      onClick={onMoreFlights}
                      variant="outline"
                      className="w-full rounded-lg border-brand text-brand font-medium"
                    >
                      Show more flights
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative mb-6 pt-4 overflow-visible">
      <div className="rounded-2xl  overflow-visible border-2 border-[#1E1E76]  ">
        {renderFlightLeg(
          `Outbound ${flight.outward_flight.departure_date}`,
          flight.outward_flight,
          null,
          "outbound",
          isOutboundOpen,
          setIsOutboundOpen,
          flight.return_flight ? false : true
        )}

        {flight.return_flight &&
          renderFlightLeg(
            `Inbound ${flight.return_flight.departure_date}`,

            flight.return_flight,
            flight.outward_flight,
            "inbound",
            isInboundOpen,
            setIsInboundOpen,
            true
          )}
      </div>
    </div>
  );
};

export default NewFlightCard;
