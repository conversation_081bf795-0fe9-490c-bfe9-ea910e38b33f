import OutboundFlight from "@/components/flightsummary/outboundFlight"
import InboundFlight from "@/components/flightsummary/inboundFlight"
import { formatAirportDisplayShort } from "@/lib/utils/formatAirport"
import FlightCard from "../flight/FlightCard"

interface FlightInfoSectionProps {
    tripSummaryDetails: any
}

const FlightInfoSection = ({ tripSummaryDetails }: FlightInfoSectionProps) => {
    return (
        <>
            <div className="">
                <div className=" text-brand-grey xs:w-[85%] xs:mx-auto xs:text-center mb-2 font-semibold">
                    Outbound |&nbsp;
                    {tripSummaryDetails?.selectedOutboundFlight && (
                        <span className="text-brand-black font-semibold">
                            {formatAirportDisplayShort(
                                tripSummaryDetails?.selectedOutboundFlight?.origin,
                                tripSummaryDetails?.sharedFlightResults?.airport_data,
                            )}
                            &nbsp;to&nbsp;
                            {formatAirportDisplayShort(
                                tripSummaryDetails?.selectedOutboundFlight?.destination,
                                tripSummaryDetails?.sharedFlightResults?.airport_data,
                            )}
                            &nbsp; |&nbsp;
                            <div className="text-brand-grey">{tripSummaryDetails?.flightSearch?.travel_class}</div>

                        </span>
                    )}
                </div>
            </div>
            <div className="mb-4">
                {/* <OutboundFlight /> */}
                <FlightCard
                    showPrice={false}
                    flightType="OUT-BOUND"
                    flight={tripSummaryDetails.selectedOutboundFlight}
                    key={tripSummaryDetails.selectedOutboundFlight.id}
                    airport={
                        tripSummaryDetails?.flightSearchRespnse?.airport_data
                    }
                />
            </div>
            {tripSummaryDetails?.selectedInboundFlight &&
                Object.keys(tripSummaryDetails?.selectedInboundFlight).length > 0 && (
                    <>
                        <div className="">
                            <div className="text-brand-grey xs:w-[85%] xs:mx-auto xs:text-center font-semibold">
                                Inbound |&nbsp;
                                <span className="text-[#080236] font-semibold">
                                    {tripSummaryDetails?.selectedInboundFlight && (
                                        <span className="text-[#080236] font-semibold">
                                            {formatAirportDisplayShort(
                                                tripSummaryDetails?.selectedInboundFlight?.origin,
                                                tripSummaryDetails?.sharedFlightResults?.airport_data,
                                            )}
                                            &nbsp;to&nbsp;
                                            {formatAirportDisplayShort(
                                                tripSummaryDetails?.selectedInboundFlight?.destination,
                                                tripSummaryDetails?.sharedFlightResults?.airport_data,
                                            )}
                                            &nbsp; |&nbsp;
                                            <div className="text-brand-grey">  {tripSummaryDetails?.flightSearch?.travel_class}</div>

                                        </span>
                                    )}
                                </span>
                            </div>
                        </div>
                        <div>
                            {/* <InboundFlight /> */}
                            <FlightCard
                                showPrice={false}
                                flightType="INBOUND"
                                flight={tripSummaryDetails.selectedInboundFlight}
                                key={tripSummaryDetails.selectedInboundFlight.id}
                                airport={
                                    tripSummaryDetails?.flightSearchRespnse?.airport_data
                                }
                            />
                        </div>
                    </>
                )}
        </>
    )
}

export default FlightInfoSection
