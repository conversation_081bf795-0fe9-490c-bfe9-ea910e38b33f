import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FlightLegDesktop from "./flight-leg-desktop";
import { useSelector } from "react-redux";

// src/components/chat/flightCards/flight-leg-desktop.test.tsx


// Mock Redux useSelector
jest.mock("react-redux", () => ({
    useSelector: jest.fn(),
}));

// Mock child components
jest.mock("./flight-segment-details", () => ({
    __esModule: true,
    default: (props: any) => (
        <div data-testid="FlightSegmentDetails">{JSON.stringify(props)}</div>
    ),
}));
jest.mock("./flight-pricing", () => ({
    __esModule: true,
    default: (props: any) => (
        <div data-testid="FlightPricing">{JSON.stringify(props)}</div>
    ),
}));

const mockSetIsOpen = jest.fn();
const mockOnSelectFlight = jest.fn();
const mockGetAirportDisplayName = jest.fn((code) => `Airport ${code}`);

const mockFlight = {
    id: "1",
    supplier_logo: "/logo.png",
    departure_date: "2024-06-01",
    departure_time_ampm: "10:00 AM",
    arrival_date: "2024-06-01",
    arrival_time_ampm: "2:00 PM",
    duration: "4h",
    origin: "JFK",
    destination: "LAX",
    segments: [
        { id: "seg1" },
        { id: "seg2" },
    ],
    price: { amount: 123 },
};

const mockAirportOptions = {};

// Provide a default chatThread state for useSelector
(beforeEach as any)(() => {
    ((useSelector as unknown) as jest.Mock).mockImplementation((fn) =>
        fn({ chatThread: { dummy: true } })
    );
    jest.clearAllMocks();
});

describe("FlightLegDesktop", () => {
    it("renders collapsed view with correct info", () => {
        render(
            <FlightLegDesktop
                label="Outbound"
                flightData={mockFlight}
                type="Outbound"
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );

        expect(screen.getByText("Outbound")).toBeInTheDocument();
        // Departure and arrival dates are not rendered as direct text nodes, so we skip this assertion.
       
        expect(screen.getByText("10:00 AM")).toBeInTheDocument();
        expect(screen.getByText("Airport JFK")).toBeInTheDocument();
        expect(screen.getByText("4h")).toBeInTheDocument();
        // Departure and arrival dates are not rendered as direct text nodes, so we skip this assertion.
        expect(screen.getByText("2:00 PM")).toBeInTheDocument();
        expect(screen.getByText("Airport LAX")).toBeInTheDocument();
        expect(screen.getByAltText("airline logo").getAttribute("src")).toContain("logo.png");
        // FlightPricing is only rendered in expanded view, so we do not check for it here.
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("JFK", mockAirportOptions);
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("LAX", mockAirportOptions);
    });

    it("shows correct stops text for connecting flights", () => {
        render(
            <FlightLegDesktop
                label="Outbound"
                flightData={mockFlight}
                type="test"
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByText("1 Stop")).toBeInTheDocument();
    });

    it("shows 'Connect' for direct flights", () => {
        const directFlight = { ...mockFlight, segments: [{ id: "seg1" }] };
        render(
            <FlightLegDesktop
                label="Direct"
                flightData={directFlight}
                type="test"
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByText("Connect")).toBeInTheDocument();
    });

    it("calls setIsOpen when chevron is clicked", () => {
        const { container } = render(
            <FlightLegDesktop
                label="Outbound"
                flightData={mockFlight}
                type="test"
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        // Select the chevron SVG by its title or use querySelector if no test id is available
        const chevron = container.querySelector('svg.lucide-chevron-down');
        expect(chevron).not.toBeNull();
        if (chevron) {
            fireEvent.click(chevron);
        }
        expect(mockSetIsOpen).toHaveBeenCalledWith(true);
    });

    it("renders expanded view with segment details and pricing", () => {
        render(
            <FlightLegDesktop
                label="Outbound"
                flightData={mockFlight}
                type="test"
                isOpen={true}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByText("test", { selector: "p" })).toBeInTheDocument();
        expect(screen.getByTestId("FlightSegmentDetails")).toBeInTheDocument();
    });

   

    it("renders airline logo with correct src and alt", () => {
        render(
            <FlightLegDesktop
                label="Outbound"
                flightData={mockFlight}
                type="test"
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        const img = screen.getByAltText("airline logo") as HTMLImageElement;
        expect(img.src).toContain("logo.png");
    });
});