import type React from "react"
import ScrollableTabs from "@/components/ScrollableTabs"

interface UserProfileMobileTabsProps {
    setActiveSection: (section: string) => void
}

const UserProfileMobileTabs: React.FC<UserProfileMobileTabsProps> = ({ setActiveSection }) => {
    const tabs = [
        { id: "User Profile", label: "User Profile" },
        { id: "Address Details", label: "Address Details" },
        // { id: "Payment Card Details", label: "Payment Card Details" },
        { id: "Passenger Profiles", label: "Passenger Profiles" },
    ]

    return (
        <div className="flex">
            <ScrollableTabs
                className="w-screen md:hidden block"
                tabs={tabs}
                defaultActiveTab="User Profile"
                showContent={false}
                onTabChange={(id: any) => setActiveSection(id)}
            />
        </div>
    )
}

export default UserProfileMobileTabs
