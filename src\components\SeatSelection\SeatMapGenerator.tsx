// seatMapGenerator.ts
import { AircraftSeatMap, Seat, SeatType } from "./types";

export class SeatMapGenerator {
  private seatMaps: Map<string, AircraftSeatMap>;

  constructor() {
    this.seatMaps = new Map();
    this.initializeDefaultSeatMaps();
  }

  private initializeDefaultSeatMaps() {
    // Boeing 737-800 typical configuration
    this.seatMaps.set("B738", {
      aircraftType: "Boeing 737-800",
      totalRows: 10,
      seatsPerRow: 7,
      cabinLayout: [
        {
          class: "business",
          startRow: 1,
          endRow: 10,
          seatLetters: ["A", "B", "C", "D", "E", "F", "G"],
          configuration: "2-3-2",
        }
      ],
    });

    // Airbus A380 typical configuration
    this.seatMaps.set("A388", {
      aircraftType: "Airbus A380",
      totalRows: 12,
      seatsPerRow: 10,
      cabinLayout: [
        {
          class: "first",
          startRow: 1,
          endRow: 2,
          seatLetters: ["A", "K"],
          configuration: "1-1",
        },
        {
          class: "business",
          startRow: 3,
          endRow: 5,
          seatLetters: ["A", "C", "D", "G", "H", "K"],
          configuration: "1-2-1",
        },
        {
          class: "premium",
          startRow: 6,
          endRow: 8,
          seatLetters: ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K"],
          configuration: "2-4-2",
        },
        {
          class: "economy",
          startRow: 9,
          endRow: 12,
          seatLetters: ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K"],
          configuration: "3-4-3",
        },
      ],
    });

    // Add more aircraft configurations as needed
  }

  private getSeatLettersFromConfiguration(configuration: string): string[] {
    const seatCount = configuration
      .split("-")
      .map((group) => parseInt(group))
      .reduce((a, b) => a + b, 0);
      
    // Generate letters from A onwards
    return Array.from({ length: seatCount }, (_, i) =>
      String.fromCharCode("A".charCodeAt(0) + i)
    );
  }
  
  generateSeatMap(aircraftType: string, bookedSeats: string[] = []): Seat[] {
    const seatMap = this.seatMaps.get(aircraftType);
    if (!seatMap)
      throw new Error(`Seat map not found for aircraft type: ${aircraftType}`);

    const seats: Seat[] = [];

    for (const cabin of seatMap.cabinLayout) {
        const letters = cabin.seatLetters.length
          ? cabin.seatLetters
          : this.getSeatLettersFromConfiguration(cabin.configuration);
      
        for (let row = cabin.startRow; row <= cabin.endRow; row++) {
          for (const letter of letters) {
            const seatId = `${row}${letter}`;
            seats.push({
              id: seatId,
              row,
              letter,
              type: cabin.class,
              status: bookedSeats.includes(seatId) ? "booked" : "available",
              features: this.getSeatFeatures(cabin.class, row, letter, seatMap),
            });
          }
        }
      }

    return seats;
  }

  private getSeatFeatures(
    seatClass: SeatType,
    row: number,
    letter: string,
    seatMap: AircraftSeatMap
  ): string[] {
    const features: string[] = [];
  
    const cabin = seatMap.cabinLayout.find(
      (c) => row >= c.startRow && row <= c.endRow
    );
  
    if (!cabin) return features;
  
    const letters = cabin.seatLetters;
    const letterIndex = letters.indexOf(letter);
  
    if (letterIndex === 0 || letterIndex === letters.length - 1) {
      features.push("window");
    }
  
    // Aisle detection based on configuration
    const configGroups = cabin.configuration.split("-").map(Number);
    console.log(configGroups);
    let aisleIndices: number[] = [];
    let index = 0;
    for (let i = 0; i < configGroups.length - 1; i++) {
      index += configGroups[i];
      aisleIndices.push(index - 1, index); // seats around aisle
    }
  
    if (aisleIndices.includes(letterIndex)) {
      features.push("aisle");
    }
  
    // Extra legroom on some rows
    if ([10, 20, 30].includes(row)) {
      features.push("extra-legroom");
    }
  
    if (row === cabin.startRow) {
      features.push("bulkhead");
    }
  
    return features;
  }  

  getAircraftTypes(): string[] {
    return Array.from(this.seatMaps.keys());
  }
}
