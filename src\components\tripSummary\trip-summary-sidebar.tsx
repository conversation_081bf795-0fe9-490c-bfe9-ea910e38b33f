"use client";

import TravelerSummary from "@/components/travelersummary/TravelerSummary";
import { formatFlightPrice } from "@/lib/utils/formatPrice";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import PriceBreakdownDialog from "../flightsummary/PriceBreakdown";

interface TripSummarySidebarProps {
    tripSummaryDetails: any;
    currentStep: number;
    onReviewAndPay: () => void;
}

const TripSummarySidebar = ({
    tripSummaryDetails,
    currentStep,
    onReviewAndPay,
}: TripSummarySidebarProps) => {
    const styles = {
        button: {
            background: "#1E1E76",
            color: "white",
            borderRadius: "8px",
        },
    };

    return (
        <div className="w-full ml-2 lg:w-1/4 lg:sticky lg:top-20 lg:h-fit lg:self-start lg:max-h-[calc(100vh-100px)] lg:overflow-y-auto order-2 lg:order-1">
            <div className="rounded-lg font-proxima-nova shadow-md border p-4 bg-brand-white mb-6">
                {/* <Accordion
                    type="multiple"
                    defaultValue={["departure", "return"]}
                    className="space-y-2"
                >
                    <AccordionItem value="departure" className="">
                        <AccordionTrigger className="py-2 text-brand-black font-semibold hover:no-underline">
                            Departure flight
                        </AccordionTrigger>
                        <AccordionContent className="pb-3">
                            <TravelerSummary
                                title=""
                                travelers={tripSummaryDetails?.outboundTravelers}
                                counts={tripSummaryDetails?.outboundCounts}
                            />
                        </AccordionContent>
                    </AccordionItem>

                    {Object.keys(tripSummaryDetails?.selectedInboundFlight).length >
                        0 && (
                            <AccordionItem
                                value="return"
                                className="border-b border-brand-grey"
                            >
                                <AccordionTrigger className="py-2 text-brand-black font-semibold hover:no-underline">
                                    Return flight
                                </AccordionTrigger>
                                <AccordionContent>
                                    <TravelerSummary
                                        title=""
                                        travelers={tripSummaryDetails?.inboundTravelers}
                                        counts={tripSummaryDetails?.inboundCounts}
                                    />
                                </AccordionContent>
                            </AccordionItem>
                        )}
                </Accordion> */}

                <div className="space-y-2 text-brand-black">
                    {/* <div className="flex justify-between text-sm">
                        <span className="text-sm font-normal">Banking Charges</span>
                        <span className="text-sm font-normal">
                            {formatFlightPrice({ amount: tripSummaryDetails?.priceBreakdown?.creditCardFees, currency: tripSummaryDetails?.selectedOutboundFlight?.price?.currency })}
                        </span>
                    </div>

                    <div className="flex justify-between text-sm">
                        <span className="text-sm font-normal">Service Fee</span>
                        <span className="text-sm font-normal">
                            {(
                                (tripSummaryDetails?.totalPrice || 0) *
                                ((tripSummaryDetails?.serviceFee || 0) / 100)
                            ).toFixed(2)}
                        </span>
                    </div>

                    {tripSummaryDetails?.selectedLuggageInfo?.totalPrice > 0 && (
                        <div className="flex justify-between items-center text-sm">
                            <span className="font-normal">Luggage Fee</span>
                            <span className="font-normal">
                                {formatFlightPrice({
                                    amount:
                                        tripSummaryDetails?.selectedLuggageInfo?.totalPrice || 0,
                                    currency:
                                        tripSummaryDetails?.selectedOutboundFlight?.price
                                            ?.currency || "",
                                })}
                            </span>
                        </div>
                    )} */}
                    <div className="flex justify-between">
                        <span className="text-xl font-semibold">Total Due</span>
                        <span className="text-xl  font-semibold">
                            {tripSummaryDetails?.selectedOutboundFlight &&
                                Object.keys(tripSummaryDetails?.selectedOutboundFlight).length >
                                0 &&
                                formatFlightPrice({
                                    amount:
                                        (tripSummaryDetails?.totalPrice || 0) +
                                        (tripSummaryDetails?.selectedLuggageInfo?.totalPrice || 0) +
                                        (tripSummaryDetails?.totalPrice || 0) *
                                        ((tripSummaryDetails?.serviceFee || 0) / 100),
                                    currency:
                                        tripSummaryDetails?.selectedOutboundFlight?.price?.currency,
                                })}
                        </span>
                    </div>
                    {(Object.keys(tripSummaryDetails.selectedInboundFlight).length === 0) &&
                        <PriceBreakdownDialog showServiceFee={true} outbound={tripSummaryDetails.selectedOutboundFlight} airport={
                            tripSummaryDetails?.flightSearchRespnse?.airport_data
                        } />}
                    {(Object.keys(tripSummaryDetails.selectedInboundFlight).length > 0) &&
                        <PriceBreakdownDialog showServiceFee={true} outbound={tripSummaryDetails.selectedOutboundFlight} airport={
                            tripSummaryDetails?.flightSearchRespnse?.airport_data
                        } inbound={tripSummaryDetails.selectedInboundFlight} />}
                </div>
                {/* <div className="w-full border-brand-grey border flex"></div> */}
                {currentStep === 1 && (
                    <div className="pt-4">
                        <button
                            onClick={onReviewAndPay}
                            style={styles.button}
                            className="w-full px-4 py-3 font-semibold text-white transition-opacity hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Review & Pay
                        </button>
                    </div>
                )}
            </div>

            <div className="my-10 xs:w-[90%] xs:mx-auto">
                <p className="text-brand-black text-sm mb-2">
                    Total price includes all taxes and fees. Prices are not guaranteed
                    until purchase is complete.
                </p>
            </div>
        </div>
    );
};

export default TripSummarySidebar;
