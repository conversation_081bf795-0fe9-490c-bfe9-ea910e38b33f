import React, { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import InputField from "@/components/input/InputField";
import { Address } from "./AddressDetails";
import { debounce } from "lodash";
import { agentPostMethod } from "@/utils/api";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { ChevronDown, Loader2 } from "lucide-react";
import { useCustomSession } from "@/hooks/use-custom-session"

interface CountryState {
  search: string;
  loading: boolean;
  countryList: Country[];
}

interface Country {
  code: string;
  name: string;
}

interface AddressFormProps {
  onCancel: () => void;
  onSave: (data: Address) => void;
  initialData?: Address;
  isEdit: boolean;
}

const AddressForm: React.FC<AddressFormProps> = ({ onCancel, onSave, initialData, isEdit }) => {
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const [form, setForm] = useState<Address>({
    address_id: initialData?.address_id || "",
    memorableName: initialData?.memorableName || "",
    userName: initialData?.userName || "",
    street: initialData?.street || "",
    city: initialData?.city || "",
    state: initialData?.state || "",
    country: initialData?.country || "",
    postalCode: initialData?.postalCode || "",
  });
  const [errors, setErrors] = useState<any>({});
  const [country, setCountry] = useState<CountryState>({ search: "", loading: false, countryList: [] });
  const [open, setOpen] = useState(false);

  const validate = () => {
    const newErrors: any = {};
    if (!form.memorableName) newErrors.memorableName = "Memorable Name is required";
    // if (!form.userName) newErrors.userName = "User Name is required";
    if (!form.street) newErrors.street = "Street address is required";
    if (!form.city) newErrors.city = "City is required";
    if (!form.country) newErrors.country = "Country is required";
    if (!form.postalCode) newErrors.postalCode = "Post code is required";
    else if (!/^[A-Za-z0-9\-,\s]+$/.test(form.postalCode)) newErrors.postalCode = "Post code can only contain letters (A-Z, a-z), Numbers (0-9), hyphens (-), commas (,), and spaces";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleCountryChange = (value: string) => {
    setForm({ ...form, country: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSave(form);
    }
  };

  const debouncedHandleCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod("flight/suggest-country", { query }, token ?? '');
            console.log(response);
            setCountry({ search: query, loading: false, countryList: response?.detail?.data });
          } catch (error) {
            console.log("Api error", error);
          } finally {
            console.log("Done");
          }
        }
      }, 500),
    []
  );

  const handleCountry = (query: string) => {
    setCountry({ ...country, loading: true, search: query });
    debouncedHandleCountry(query);
  }

  return (
    <form className="w-full bg-transparent" onSubmit={handleSubmit}>
      <div className="flex justify-between items-center gap-4 mb-4 md:mb-8">
        <h2 className="text-sm md:text-3xl font-bold text-brand-black">{isEdit ? "Edit Address" : "Add New Address"}</h2>
        <Button icon="save" className="text-sm text-brand-white bg-brand hover:text-brand-white hover:bg-brand px-6 py-4 rounded-[8px]" onClick={handleSubmit}>
          {isEdit ? "Update Changes" : "Add"}
        </Button>
      </div>
      <div className="flex flex-col gap-1">
        {/* First row - 2 fields in a 3-column grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-10">
          <div>
            <InputField
              label="Memorable Name"
              placeholder="Memorable Name"
              name="memorableName"
              value={form.memorableName}
              onChange={handleChange}

            />
            {errors.memorableName && <div className="text-red-500 text-sm mt-1">{errors.memorableName}</div>}
          </div>
          <div>
            <InputField
              label="Full Name"
              placeholder="Full Name"
              name="userName"
              value={form.userName}
              onChange={handleChange}
            />
            {errors.userName && <div className="text-red-500 text-sm mt-1">{errors.userName}</div>}
          </div>
          <div></div>
        </div>

        {/* Second row - 3 fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-10">
          <div>
            <InputField
              label="Street Address"
              placeholder="Street Address"
              name="street"
              value={form.street}
              onChange={handleChange}
            />
            {errors.street && <div className="text-red-500 text-sm mt-1">{errors.street}</div>}
          </div>
          <div>
            <InputField
              label="City"
              placeholder="City"
              name="city"
              value={form.city}
              onChange={handleChange}
            />
            {errors.city && <div className="text-red-500 text-sm mt-1">{errors.city}</div>}
          </div>
          <div>
            <InputField
              label="State / Province"
              placeholder="State / Province"
              name="state"
              value={form.state}
              onChange={handleChange}
            />
            {errors.state && <div className="text-red-500 text-sm mt-1">{errors.state}</div>}
          </div>
        </div>

        {/* Third row - 2 fields in a 3-column grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-10">
          <div>
            <label className="font-semibold text-brand-black block">Country</label>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full text-neutral-dark hover:text-neutral-dark hover:bg-brand-white justify-between  px-4 py-5 bg-white rounded-sm text-left font-normal border border-neutral flex"
                >
                  {form.country || "Select country"}
                  <ChevronDown className="h-4 w-4 shrink-0 text-neutral-dark stroke-[2.5]" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command className="bg-brand-white rounded-sm shadow-none">
                  <CommandInput
                    placeholder="Search country..."
                    onValueChange={handleCountry}
                  />
                  {country.loading ? (
                    <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </div>
                  ) : (
                    <>
                      <CommandEmpty>No country found.</CommandEmpty>
                      <CommandGroup>
                        {country.countryList.map((item: Country) => (
                          <CommandItem
                            key={item.code}
                            value={item.name}
                            onSelect={() => {
                              setForm({ ...form, country: item.name });
                              setOpen(false);
                            }}
                            className="data-[selected=true]:text-brand-black data-[selected=true]:bg-neutral aria-selected:bg-neutral aria-selected:text-brand-black data-[highlighted=true]:bg-neutral data-[highlighted=true]:text-brand-black hover:bg-neutral hover:text-brand-black"
                          >
                            {item.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </>
                  )}
                </Command>
              </PopoverContent>
            </Popover>

            {errors.country && <div className="text-red-500 text-sm mt-1">{errors.country}</div>}
          </div>
          <div>
            <InputField
              label="Post Code"
              placeholder="Post Code"
              name="postalCode"
              value={form.postalCode}
              onChange={handleChange}
            />
            {errors.postalCode && <div className="text-red-500 text-sm mt-1">{errors.postalCode}</div>}
          </div>
          <div></div>
        </div>

        {/* Cancel button row */}
        <div className="grid grid-cols-1 gap-8 mb-10">
          <Button className="text-sm text-brand-white bg-brand hover:bg-brand hover:text-brand-white px-6 py-4 rounded-[8px]] w-fit" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>
    </form>
  );
};

export default AddressForm;