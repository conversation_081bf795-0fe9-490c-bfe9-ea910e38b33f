import React from "react";
import { render, screen } from "@testing-library/react";
import ProgressStepper from "./progressSteps";

const steps = [
  { id: 1, label: "Traveler Details" },
  { id: 2, label: "Review & Pay" },
  { id: 3, label: "Confirmation" },
];

describe("ProgressStepper", () => {
  it("renders all step labels and numbers", () => {
    render(<ProgressStepper currentStep={1} steps={steps} />);
    steps.forEach((step) => {
      expect(screen.getByText(step.label)).toBeInTheDocument();
      expect(screen.getByText(step.id.toString())).toBeInTheDocument();
    });
  });

  it("shows correct styles for completed and current steps", () => {
    const { container } = render(<ProgressStepper currentStep={2} steps={steps} />);
    // Step 1 and 2 should have green circle and dark label
    const step1Circle = screen.getByText("1");
    const step2Circle = screen.getByText("2");
    expect(step1Circle).toHaveClass("bg-[#24C72F]", "text-white");
    expect(step2Circle).toHaveClass("bg-[#24C72F]", "text-white");
    expect(screen.getByText("Traveler Details")).toHaveClass("text-[#080236]");
    expect(screen.getByText("Review & Pay")).toHaveClass("text-[#080236]");
    // Step 3 should have gray styles
    const step3Circle = screen.getByText("3");
    expect(step3Circle).toHaveClass("bg-gray-200", "text-gray-600");
    expect(screen.getByText("Confirmation")).toHaveClass("text-gray-600");
  });

  it("shows correct connector line color", () => {
    const { container } = render(<ProgressStepper currentStep={2} steps={steps} />);
    // There should be two connector lines
    // Select connector lines by their role or a test id if available, otherwise fallback to className substring match
    const lines = Array.from(container.querySelectorAll("div")).filter(
      (div) =>
        div.className.includes("bg-[#4B4BC3]") || div.className.includes("bg-gray-200")
    );
    // First line should be blue (completed), second should be gray (not completed)
    expect(lines[0]).toHaveClass("bg-[#4B4BC3]");
    expect(lines[1]).toHaveClass("bg-gray-200");
  });

  it("renders 24-hour service and Secure payment with icons", () => {
    render(<ProgressStepper currentStep={1} steps={steps} />);
    expect(screen.getByText("24-hour service")).toBeInTheDocument();
    expect(screen.getByText("Secure payment")).toBeInTheDocument();
    // Check for images
    const imgs = screen.getAllByRole("presentation");
    expect(imgs.length).toBeGreaterThanOrEqual(2);
    expect(imgs[0]).toHaveAttribute("src", expect.stringContaining("Time.png"));
    expect(imgs[1]).toHaveAttribute("src", expect.stringContaining("Security.png"));
  });

  it("handles currentStep = 0 (no steps completed)", () => {
    render(<ProgressStepper currentStep={0} steps={steps} />);
    steps.forEach((step) => {
      const circle = screen.getByText(step.id.toString());
      expect(circle).toHaveClass("bg-gray-200");
    });
  });

  it("handles currentStep = last step (all steps completed)", () => {
    render(<ProgressStepper currentStep={3} steps={steps} />);
    steps.forEach((step) => {
      const circle = screen.getByText(step.id.toString());
      expect(circle).toHaveClass("bg-[#24C72F]");
      expect(screen.getByText(step.label)).toHaveClass("text-[#080236]");
    });
  });

  it("handles steps = 1 (single step)", () => {
    render(<ProgressStepper currentStep={1} steps={[{ id: 1, label: "Only Step" }]} />);
    expect(screen.getByText("Only Step")).toBeInTheDocument();
    expect(screen.queryByText("2")).not.toBeInTheDocument();
    // No connector lines
    expect(screen.queryByRole("separator")).not.toBeInTheDocument();
  });
});