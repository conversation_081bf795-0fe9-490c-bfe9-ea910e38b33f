import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { Check, CheckCircle } from "lucide-react";
import { AppState } from "@/store/store";
import ChatFooter from "@/components/footer/chatFooter";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/NavBar";
import HotelConfirmationCard from "@/components/hotel-confirmation/HotelConfirmationCard";
import BookingStepper from "@/components/hotelBooking/HotelBookStepper";
import Image from "next/image";

export default function HotelConfirmationPage() {
  const footerRef = useRef<HTMLDivElement | null>(null);
  const hotelBookingContext = useSelector((state: AppState) => state.hotelBookingContext.hotelBookingContext);

  const handleSignIn = () => {
    // setIsSignInClicked(true);
  };

  // Generate order number (note: to get this from your booking API)
  const generateOrderNumber = () => {
    return Math.floor(Math.random() * 90000) + 10000;
  };

  const formatCurrency = (amount: number) => {
    if (!hotelBookingContext?.selectedRoom?.hotel.currency) return `₹${amount.toLocaleString()}`;
    const currency = hotelBookingContext.selectedRoom.hotel.currency;
    const currencySymbol = currency === 'EUR' ? '€' : '₹';
    return `${currencySymbol}${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return new Date().toLocaleDateString('en-GB');
    try {
      return new Date(dateString).toLocaleDateString('en-GB');
    } catch {
      return new Date().toLocaleDateString('en-GB');
    }
  };

  // Calculate total amount (same logic as PriceBreakdown) need more clarity on discounts
  const calculateTotal = () => {
    if (!hotelBookingContext?.selectedRoom) return 0;
    
    const { selectedRate } = hotelBookingContext.selectedRoom;
    const nights = hotelBookingContext.nights;
    const pricePerNight = parseFloat(selectedRate.net_price || '0');
    const subtotal = pricePerNight * nights;
    
    // Tax calculation
    const currency = hotelBookingContext.selectedRoom.hotel.currency;
    const taxRate = currency === 'EUR' ? 0.10 : 0.18;
    const taxesAndFees = subtotal * taxRate;
    
    // Applicable discounts
    const discounts = selectedRate.offers?.reduce((sum, offer) => {
      if (offer.name.toLowerCase().includes('child') && hotelBookingContext.children === 0) {
        return sum;
      }
      return sum + Math.abs(parseFloat(offer.amount || '0'));
    }, 0) || 0;
    
    return subtotal + taxesAndFees - discounts;
  };

  if (!hotelBookingContext) {
    return (
      <div className="min-h-screen bg-[#F2F3FA] flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-[#1E1E76]">Loading booking confirmation...</p>
        </div>
      </div>
    );
  }

  const { adults, children } = hotelBookingContext;
  const totalAmount = calculateTotal();
  const orderNumber = generateOrderNumber();
  const bookingDate = formatDate();

  return (
    <div
      className={`md:relative h-screen xl:pb-10 lg:pb-5  bg-[#F2F3FA] flex flex-col gap-2 mx-auto font-proxima-nova`}
    >
      <div className="z-50 w-full mx-auto items-center flex flex-col justify-center">
        <div className="w-[95%] fixed top-0 mx-auto">
          <Navbar handleSignIn={handleSignIn} />
        </div>
      </div>

      <div className="md:relative top-10 bg-gray-100 md:overflow-y-auto md:hide-scrollbar">
        <div className="md:max-w-6xl md:mx-auto xs:pr-2 xs:pl-2">
          <div className="max-w-3xl mx-auto mt-6">
            <BookingStepper currentStep={3} />
          </div>

          <div className="flex flex-col items-center justify-center text-center px-4 py-8">
            <div className="bg-[#4B4BC3] rounded-full p-4 mb-4">
              <Check size={40} strokeWidth={2.5} color="white" />
            </div>

            <h2 className="text-xl sm:text-2xl font-semibold text-[#080236]">
              Your Hotel was Booked Successfully!
            </h2>

            <p className="text-sm sm:text-base text-[#B4BBE8] mt-1">
              Booking details has been sent to: <span><EMAIL></span>
            </p>
          </div>
          <div className="">
            <div className="mt-6 mb-6">
              <h2 className="text-[24px] text-[#1E1E76] font-bold">
                Payment Summary
              </h2>
            </div>
            {/* Main Content */}
            <div className="md:rounded-xl md:p-px mb-12 lg:col-span-2 space-y-6 bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76]">
              <div className="bg-gray-100 rounded-xl ">
                {/* Header */}
                <div className="rounded-xl md:p-6 border-gray-100">
                  <div className="flex justify-between">
                    <div>
                      <h2 className="font-bold text-[#080236]">Order Number</h2>
                      <p className="text-[#080236]">{orderNumber}</p>
                    </div>
                    <div>
                      <h2 className="font-bold text-[#080236]">Date</h2>
                      <p className="text-[#080236]">{bookingDate}</p>
                    </div>
                    <div>
                      <h2 className="font-bold text-[#080236]">Total</h2>
                      <p className="text-[#080236]">{formatCurrency(totalAmount)}</p>
                    </div>
                    <div>
                      <h2 className="font-bold text-[#080236]">
                        Payment Method
                      </h2>
                      <p className="text-[#080236]">Pay at Hotel</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-6 mb-6">
              <h2 className="text-[24px] text-[#1E1E76] font-bold">
                Hotel Summary
              </h2>
            </div>
            <div className="md:rounded-xl md:p-px mb-12 lg:col-span-2 space-y-6 bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76]">
              <div className="bg-gray-100 rounded-xl ">
                {/* Header */}
                <div className=" rounded-xl md:p-6 border-gray-100">
                  <div className=" mb-8">
                    <h1 className="text-[24px] font-bold text-[#4B4BC3] mb-2">
                      Guest Information
                    </h1>
                    <div className="flex gap-8">
                      <div>
                        <p className="text-[#080236]">Primary Guest</p>
                        <p className="font-medium">Guest Name</p>
                      </div>
                      <div>
                        <p className="text-[#080236]">Guests</p>
                        <p className="font-medium">
                          {adults} adult{adults > 1 ? 's' : ''} 
                          {children > 0 && ` | ${children} child${children > 1 ? 'ren' : ''}`}
                        </p>
                      </div>
                      <div>
                        <p className="text-[#080236]">Contact</p>
                        <p className="font-medium">+91 12345 67890</p>
                      </div>
                    </div>
                  </div>
                  <HotelConfirmationCard />
                </div>
              </div>
            </div>

            <div className="mt-12 mb-6">
              <div className=" px-4 py-10 sm:px-8 text-center sm:text-left">
                <div className="flex justify-center">
                  <div className=" text-center">
                    <h2 className="text-[30px] font-bold text-[#1E1E76]">
                      Do you want to keep all your travel details in one place?
                    </h2>
                    <p className="text-[#080236] mt-2 text-[24px]">
                      I've bundled everything from hotel details to booking confirmation
                      into a single PDF. Super easy to save, print, or
                      carry offline during your trip.
                    </p>
                    <p className="text-[#A195F9] mt-2 italic text-[24px]">
                      "Shasa, your travel-savvy sidekick."
                    </p>
                    
                    <Button className="w-[200px] mt-8 bg-indigo-600 text-white py-4 rounded-full text-[18px] font-medium bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)] h-[42px]">
                        Download as Pdf
                    </Button>
                  </div>
                </div>

                {/* Divider */}
                <div className="my-10 border-t border-gray-300 w-full max-w-3xl mx-auto"></div>

                {/* Bottom Section */}
                <div className="max-w-5xl mx-auto flex flex-col sm:flex-row justify-between items-center gap-6">

                  <div className="flex-1 text-center sm:text-left ">
                    <h3 className="text-[30px] font-bold text-[#1E1E76]">
                      Booking's done. Now let's move on to the next step!
                    </h3>
                    <p className="text-[#080236] mt-2 text-[24px]">
                      From real-time updates to all your upcoming trips, you'll
                      find it all in the NxVoy app.
                    </p>
                    <p className="mt-3 text-[30px] text-[#1E1E76] font-semibold">
                      Scan the QR code and download the app.
                      <br />
                      Shasa is waiting there to guide you!
                    </p>
                    <p className="text-[#080236] mt-2 text-[24px]">
                      Let's make your trip seamless, right from your phone.
                    </p>
                  </div>

                  <div className="flex flex-col items-center gap-2 w-[25%]">
                    <Image
                      src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/qr-scan.svg"
                      className="w-[160px]"
                      alt="QR Code"
                      width={100}
                      height={100}
                    />
                    <p className="text-[24px] font-semibold text-[#1E1E76]">
                      Download Our App
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div ref={footerRef}>
          <ChatFooter />
        </div>
      </div>
    </div>
  );
}