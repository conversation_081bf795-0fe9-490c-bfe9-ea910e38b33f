import { useState, useRef, useCallback } from "react";
import { useDispatch } from "react-redux";
import { getWebSocketBaseURL } from "@/utils/api";
import { CHAT_CONSTANTS } from "@/constants/chat";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { setShowMoreFlights } from "@/store/slices/showMoreFlights";
import { TripOptions } from "@/constants/flight";
import tracker from "@/utils/posthogTracker";

export interface Message {
  sender: "human" | "ai";
  text?: string;
}

export interface RawMessage {
  role: "human" | "ai";
  content?: string;
  timestamp: string;
  type?: "flights";
  flights?: any[];
  card?: {
    data?: {
      flights?: any[];
    };
  };
}

export type ChatMessage =
  | {
      sender: "human" | "ai";
      text: string;
      timestamp?: string;
      type?: undefined;
      messageId?: string;
    }
  | {
      sender: "ai";
      type: "flights";
      flights: any[];
      timestamp?: string;
      routingId?: string;
    };

export interface ChatScreenProps {}

export interface WebSocketMessage {
  type: string;
  message?: string;
  token?: string;
  content?: string;
  timestamp?: string;
  id?: string;
  name?: string;
  data?: any;
  routing_id?: string;
  cards?: {
    data?: {
      flights?: any[];
      routing_id?: string;
    };
  };
  tool?: string;
  thread_id?: string;
}

export interface FlightSearchPayload {
  flightSearch: {
    trip_type: string;
    departureLocation: string;
    departureValue: string;
    destinationLocation: string;
    destinationValue: string;
    dateRange: Array<{
      startDate: string;
      endDate: string;
    }>;
    travel_class: string;
    dept_airport_name: string;
    arr_airport_name: string;
    adults: number;
    children: number;
    infants: number;
  };
  flightSearchRespnse: any;
  sharedFlightResults: any;
}

export interface InitialMessage {
  message: string;
  time: string;
}

interface UseWebSocketProps {
  onMessageReceived: (message: ChatMessage) => void;
  onFlightDataReceived: (flights: any[], timestamp: string, routingId?: string) => void;
  onProcessingStatusChange: (isProcessing: boolean, message?: string) => void;
  onAgentEventChange: (message: string) => void;
  onStreamingChange: (isStreaming: boolean, message?: string) => void;
  onThreadNameUpdate: (name: string) => void;
  onFlightResultsUpdate: (results: any) => void;
  onError: (err:any) => void;
  setRefreshChatHistory: (refresh: boolean) => void;
  setCurrentThreadTitle: (title: string) => void;
}

// message counter for PostHog
let messageCount = 0;

export const useWebSocket = ({
  onMessageReceived,
  onFlightDataReceived,
  onProcessingStatusChange,
  onAgentEventChange,
  onStreamingChange,
  onThreadNameUpdate,
  onFlightResultsUpdate,
  onError,
  setRefreshChatHistory,
  setCurrentThreadTitle,
}: UseWebSocketProps) => {
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [socketStatus, setSocketStatus] = useState<"connected" | "disconnected">("disconnected");
  const [streamingMessage, setStreamingMessage] = useState("");
  
  const flightSearchData = useRef(null);
  const flightSearchDataRoutingId = useRef(null);
  
  const dispatch = useDispatch();

  const handleWebSocketMessage = useCallback((event: MessageEvent) => {
    const res: WebSocketMessage = JSON.parse(event.data);

    switch (res.type) {
      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.PROCESSING:
        onProcessingStatusChange(true, res.message);
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.AGENT_EVENT:
        onProcessingStatusChange(false);
        onAgentEventChange(res.message || "");
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.THREAD_NAME_UPDATED:
        setRefreshChatHistory(true);
        setTimeout(() => setRefreshChatHistory(false), 100);
        dispatch(updateCurrentThreadInfo({ currentThreadName: res.name || "" }));
        setCurrentThreadTitle(res.name || "");
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.TOKEN_STREAM:
        setStreamingMessage((prev) => prev + (res.token || ""));
        onStreamingChange(true, streamingMessage + (res.token || ""));
        onProcessingStatusChange(false);
        onAgentEventChange("");
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.FLIGHT_DATA:
        flightSearchData.current = res.data;
        flightSearchDataRoutingId.current = res.routing_id;
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.TOKEN_STREAM_START:
        onStreamingChange(true);
        setStreamingMessage("");
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.TOKEN_STREAM_END:
        onStreamingChange(false);
        setStreamingMessage("");
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.AGENT_RESPONSE:
        onMessageReceived({
          sender: "ai",
          text: res.content || "",
          timestamp: res.timestamp,
          messageId: res.id,
        });

        const hasFlights =
          res.cards &&
          res.cards.data &&
          Array.isArray(res.cards.data.flights) &&
          res.cards.data.flights.length > 0;

        if (hasFlights && res.cards?.data?.flights) {
          onFlightDataReceived(
            res.cards.data.flights,
            res.timestamp || "",
            res.cards.data.routing_id
          );
        }

        setStreamingMessage("");
        onStreamingChange(false);
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.TOOL_RESULT:
        if (res.tool === "search_flights") {
          handleFlightToolResult(res);
        }
        break;

      case CHAT_CONSTANTS.WEBSOCKET_MESSAGE_TYPES.COMPLETED:
        dispatch(
          updateCurrentThreadInfo({
            currentChatPageThreadId: res?.thread_id || "",
          })
        );
        break;

      default:
        break;
    }
  }, [dispatch, onMessageReceived, onFlightDataReceived, onProcessingStatusChange, onAgentEventChange, onStreamingChange, onThreadNameUpdate, setRefreshChatHistory, setCurrentThreadTitle, streamingMessage]);

  const handleFlightToolResult = (res: WebSocketMessage) => {
    const flightToolsData = res.data;
    let tripType = TripOptions[0];
    const chatOutputData =
      flightToolsData?.output?.content?.routing_id === flightSearchDataRoutingId.current
        ? flightSearchData.current
        : {};

    if (flightToolsData && typeof flightToolsData === "object" && Object.keys(flightToolsData).length > 0) {
      tripType =
        flightToolsData?.output?.content._return &&
        Object.keys(flightToolsData.output.content._return).length === 0
          ? TripOptions[0]
          : TripOptions[1];

      onFlightResultsUpdate({
        input: flightToolsData?.input?.params || {},
        output: chatOutputData || {},
      });
    }

    const onwardFlights =
      chatOutputData && "_outward" in chatOutputData
        ? (chatOutputData as { _outward?: { flights?: any[] } })._outward?.flights || []
        : [];

    if (onwardFlights && onwardFlights.length) {
      dispatch(
        updateCurrentThreadInfo({
          showMoreFlightsOption: true,
          chatResult: {
            input: flightToolsData?.input?.params || {},
            output: chatOutputData || {},
          },
          allFlights: chatOutputData || {},
          tripType,
        })
      );
    }

    dispatch(setShowMoreFlights(onwardFlights.length > 0));
  };

  const connectWebSocket = useCallback((threadId: string) => {
    // Close existing connection first
    if (ws) {
      ws.close();
      setWs(null);
      setSocketStatus(CHAT_CONSTANTS.SOCKET_STATUS.DISCONNECTED);
    }

    const wsUrl = `${getWebSocketBaseURL()}${CHAT_CONSTANTS.API_ENDPOINTS.WEBSOCKET_PATH}${threadId}`;
    const socket = new WebSocket(wsUrl);
    setWs(socket);

    socket.onopen = () => {
      setSocketStatus(CHAT_CONSTANTS.SOCKET_STATUS.CONNECTED);
      // console.log("poll Chat Opened", threadId)
      tracker.trackEvent("Chat Opened", { threadId: threadId });
    };

    socket.onmessage = (event) => {
      messageCount++;
      // console.log("poll WebSocket message received:", event, threadId, messageCount);
      handleWebSocketMessage(event);
      tracker.trackEvent("Chat Message Received", { threadId: threadId, messageCount });
    };

    socket.onerror = (err) => {
      console.error("[WebSocket] Error", err);
      onError(err);
    };

    socket.onclose = (event) => {
      // console.warn("[WebSocket] Closed", event);
      setSocketStatus(CHAT_CONSTANTS.SOCKET_STATUS.DISCONNECTED);
      tracker.trackEvent("Chat Closed", { threadId: threadId });
    };

    return socket;
  }, [handleWebSocketMessage, onError]);

  const sendMessage = useCallback((message: string, threadId: string) => {
    if (!ws || socketStatus !== CHAT_CONSTANTS.SOCKET_STATUS.CONNECTED) return false;

    ws.send(
      JSON.stringify({
        query: message,
        thread_id: threadId,
        message_type: "text",
      })
    );
    return true;
  }, [ws, socketStatus]);

  const closeConnection = useCallback(() => {
    if (ws) {
      ws.close();
      setWs(null);
      setSocketStatus(CHAT_CONSTANTS.SOCKET_STATUS.DISCONNECTED);
    }
  }, [ws]);

  return {
    ws,
    socketStatus,
    streamingMessage,
    connectWebSocket,
    sendMessage,
    closeConnection,
    isConnected: socketStatus === CHAT_CONSTANTS.SOCKET_STATUS.CONNECTED,
  };
};