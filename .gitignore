# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
#.env*

# lock files
package-lock.json
yarn.lock

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
lefthook.yml
.lintstagedrc.json
eslint.config.js
/.vs
/test/node_modules
/test/test-reports
test/.DS_Store
coverage
.DS_Store
