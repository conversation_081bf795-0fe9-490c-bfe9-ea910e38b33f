import { todayDate } from "@/lib/utils/flightUtils"
import { formatFlightPrice } from "@/lib/utils/formatPrice"
import type { BookingStatus } from "@/types/booking"

interface PaymentSummaryProps {
    bookingResponse: any
    flightTermResponse: any
    bookingStatus: BookingStatus
}

export const PaymentSummary = ({ bookingResponse, flightTermResponse, bookingStatus }: PaymentSummaryProps) => {
    const getStatusBadge = (status: BookingStatus) => {
        switch (status) {
            case "CONFIRMED":
                return (
                    <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Confirmed
                    </span>
                )
            case "UNCONFIRMED":
                return (
                    <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                    </span>
                )
            case "UNCONFIRMED_BY_SUPPLIER":
                return (
                    <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Unconfirmed
                    </span>
                )
            default:
                return (
                    <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Confirmed
                    </span>
                )
        }
    }

    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="bg-brand text-white p-4">
                <h2 className="text-lg font-semibold">Payment Summary</h2>
            </div>
            <div className="p-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                        <p className="text-sm text-brand-black mb-1">Order Number</p>
                        <p className="font-medium text-brand-black">
                            {bookingResponse?.supplier_booking_reference || "Processing..."}
                        </p>
                    </div>
                    <div>
                        <p className="text-sm text-brand-black mb-1">Date</p>
                        <p className="font-medium text-brand-black">{todayDate()}</p>
                    </div>
                    <div>
                        <p className="text-sm text-brand-black mb-1">Total</p>
                        <p className="font-medium text-brand-black">
                            {formatFlightPrice({
                                amount: flightTermResponse.expected_amount,
                                currency: flightTermResponse.expected_currency,
                            })}
                        </p>
                    </div>
                    <div>
                        <p className="text-sm text-brand-black mb-1">Status</p>
                        {getStatusBadge(bookingStatus)}
                    </div>
                </div>
            </div>
        </div>
    )
}
