import { capitalize } from "./capitalize";

type AirportData = {
    [code: string]: {
      iata_code: string;
      city_name: string;
      city_name_original: string;
      airport_name: string;
    };
  };
  
  export function formatAirportDisplay(code: string, airportData: AirportData): string {
    const airport = airportData?.[code];
    if (!airport) return code;
  
    const {
      city_name,
      city_name_original,
      airport_name,
      iata_code
    } = airport;
  
    const cityDisplay =
        city_name.trim().toLowerCase() === city_name_original.trim().toLowerCase()
        ? city_name
        : `${city_name} (${city_name_original})`;

    return `${capitalize(cityDisplay)} - ${airport_name} (${iata_code})`;
  }

  export function formatAirportDisplayShort(code: string, airportData: AirportData): string {
    const airport = airportData?.[code];
    if (!airport) return code;
  
    const {
      city_name,
      iata_code
    } = airport;


    return `${capitalize(city_name)} (${iata_code})`;
  }