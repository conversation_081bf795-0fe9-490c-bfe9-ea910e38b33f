"use client"

import type { ReactNode } from "react"

interface MenuButtonProps {
    icon: ReactNode
    label: string
    onClick: () => void,
    className?: string
}

export function MenuButton({ icon, label, onClick, className }: MenuButtonProps) {
    return (
        <button onClick={onClick} className={className ? className : "flex items-center text-base font-medium text-[#080236] w-full text-left"}>
            <div className="w-6 mr-3">{icon}</div>
            {label}
        </button>
    )
}
