"use client";

import TravellerCard from "@/components/traveller-card/TravellerCard";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation"; // ✅ Correct import
import { useEffect, useRef, useState, useMemo } from "react";

const TravellerPlan = () => {
  const sectionRef = useRef<HTMLElement | null>(null);
  const fullViewEnteredAtRef = useRef<number | null>(null); // ✅ Persistent ref
  const [phase, setPhase] = useState(-1);
  const phaseRef = useRef(-1);
  const [rotations, setRotations] = useState<number[]>([0, 0, 0, 0, 0]);
  const [isFullyInView, setIsFullyInView] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const headingRef = useRef(null);
  const router = useRouter();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        const ratio = entry.intersectionRatio;
        const isVisible = ratio > 0;
        const isFullyVisible = ratio >= 0.95;
        const isMostlyGone = ratio < 0.1;

        setIsVisible(isVisible);

        if (isFullyVisible && phaseRef.current === -1) {
          fullViewEnteredAtRef.current = performance.now();
          setIsFullyInView(true);
          setPhase(0);
          phaseRef.current = 0;

          setTimeout(() => {
            const duration =
              performance.now() - (fullViewEnteredAtRef.current || 0);
            const rect = sectionRef.current?.getBoundingClientRect();
            const stillFullyInView =
              rect && rect.top >= 0 && rect.bottom <= window.innerHeight;

            if (duration < 300 && phaseRef.current === 0 && stillFullyInView) {
              setPhase(3);
              phaseRef.current = 3;
              setRotations([0, 0, 0, 0, 0]);
            }
          }, 300);
        }

        if (isMostlyGone && phaseRef.current !== -1) {
          setPhase(-1);
          phaseRef.current = -1;
          setIsFullyInView(false);
          setRotations([0, 0, 0, 0, 0]);
          setIsVisible(false);
        }
      },
      {
        threshold: [0, 0.1, 0.25, 0.5, 0.75, 0.95, 1],
      }
    );

    const el = sectionRef.current;
    if (el) observer.observe(el);

    // ✅ Correct cleanup
    return () => {
      if (el) observer.unobserve(el);
    };
  }, []);

  useEffect(() => {
    phaseRef.current = phase;
  }, [phase]);

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (phase >= 3) return;
      e.preventDefault();
      goToNextPhase();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (phase >= 3) return;
      if (["ArrowDown", "ArrowRight", "ArrowUp", "ArrowLeft"].includes(e.key)) {
        e.preventDefault();
        goToNextPhase();
      }
    };

    const el = sectionRef.current;
    if (isFullyInView && phase >= 0 && phase < 3) {
      el?.addEventListener("wheel", handleWheel, { passive: false });
      window.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      el?.removeEventListener("wheel", handleWheel);
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isFullyInView, phase]);

  const goToNextPhase = () => {
    setPhase((prev) => Math.min(prev + 1, 3));
  };

  useEffect(() => {
    if (phase === 1) setRotations([-10, -10, 10, -10, 10]);
    if (phase === 3) setRotations([0, 0, 0, 0, 0]);
  }, [phase]);

  const handleMouseLeave = () => {
    if (phase === 2 || phase === 3) setRotations([-10, -10, 10, -10, 10]);
  };

  const handleMouseEnter = () => {
    if (phase === 2 || phase === 3) setRotations([0, 0, 0, 0, 0]);
  };

  const getCardMotionState = (index: number) => {
    if (phase === -1) {
      return { opacity: 0, scale: 0.5 };
    }

    if (phase === 0) {
      const entryPeek = [
        { x: -140, y: -80, rotate: -10, rotateY: 60 }, // TL
        { x: 140, y: -80, rotate: 10, rotateY: -60 }, // TR
        { x: -120, y: 80, rotate: 10, rotateY: 60 }, // BL
        { x: 0, y: 100, rotate: -10, rotateY: -60 }, // B
        { x: 120, y: 80, rotate: 15, rotateY: 60 }, // BR
      ];

      return {
        ...entryPeek[index],
        opacity: 0.4,
        scale: 0.7,
      };
    }

    if (phase === 1) {
      const directions = [
        { x: -200, y: -90, rotate: -15, rotateY: 180 }, // TL
        { x: 200, y: -90, rotate: 15, rotateY: -180 }, // TR
        { x: -210, y: 100, rotate: 10, rotateY: 180 }, // BL
        { x: 0, y: 100, rotate: -10, rotateY: -180 }, // B
        { x: 210, y: 100, rotate: 15, rotateY: 180 }, // BR
      ];
      return { ...directions[index], opacity: 1, scale: 0.9 };
    }

    if (phase === 2) {
      return {
        rotate: index % 2 === 0 ? -5 : 5,
        rotateY: 0,
        scale: 1.05,
        opacity: 1,
        x: 0,
        y: 0,
      };
    }

    if (phase === 3) {
      return { rotate: 0, x: 0, y: 0, scale: 1, opacity: 1, rotateY: 0 };
    }

    return {};
  };

  return (
    <section
      id="travellerplan"
      ref={sectionRef}
      className="relative w-full flex justify-center
        overflow-hidden mt-0 pt-[40px] pb-[40px] bg-brand-white"
    >
      <motion.div
        id="card-container"
        className="flex flex-col w-full h-full mx-[68px]"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="flex items-center justify-between">
          <TravellerCard
            imageUrl="weekend-getaway"
            title="Weekend Getaway"
            desc="A short and sweet trip to recharge your soul!"
            rotate={rotations[0]}
            animateState={getCardMotionState(0)}
          />

          <motion.div
            key={isVisible ? "visible" : "hidden"}
            ref={headingRef}
            initial={{ y: -200, opacity: 0 }}
            animate={
              isVisible ? { y: 88, opacity: 1 } : { y: -100, opacity: 0 }
            }
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="flex flex-col gap-2 max-w-[420px] w-2/5 
                    leading-tight items-center"
          >
            <h3
              className="font-proxima-nova font-bold 
                    text-center text-[24px] sm:text-[38px] lg:text-[52px] text-brand-black"
            >
              I Have a Plan for Every Traveller
            </h3>
            <p
              className="font-proxima-nova text-[#080236] 
                        font-medium text-center text-[14px] sm:text-[16px] lg:text-[18px] 
                        leading-normal"
            >
              I’ve got the perfect itinerary waiting for you. Just tell me your
              vibe, and I’ll curate the best plan for you!
            </p>
            <button
              className="flex items-center justify-center text-center rounded-[8px] px-3 py-2 sm:px-5 sm:py-3 font-proxima-nova text-base text-white font-semibold sm:font-normal w-[120px] sm:w-[194px] sm:h-[42px] bg-brand mt-5"
              onClick={() => router.push("/chat")}
            >
              Plan My Trip
            </button>
          </motion.div>

          <TravellerCard
            imageUrl="adventure"
            title="Adventure Escape"
            desc='Thrilling experiences that will leave you saying, "WOW!"'
            rotate={rotations[1]}
            animateState={getCardMotionState(1)}
          />
        </div>
        <div
          className="flex items-start sm:justify-between lg:justify-around 
                sm:max-xl:mt-0 sm:max-xl:mx-0 xl:mt-[45px] xl:mx-[65px] 
                sm:max-xl:w-full h-full mb-5"
        >
          <TravellerCard
            imageUrl="heritage"
            title="Heritage Visits"
            desc="Explore history like never before!"
            rotate={rotations[2]}
            animateState={getCardMotionState(2)}
          />
          <div
            className="flex flex-col sm:justify-center 
                    md:justify-end md:mt-[125px] lg:mt-[125px]
                    lg:justify-end h-full"
          >
            <TravellerCard
              imageUrl="luxury"
              title="Luxury Retreats"
              desc="Serene spots to pamper yourself in pure bliss!"
              rotate={rotations[3]}
              animateState={getCardMotionState(3)}
            />
          </div>
          <TravellerCard
            imageUrl="road-trip"
            title="Road Trips & Bike Tours"
            desc="Scenic drives and open roads to fuel your wanderlust!"
            rotate={rotations[4]}
            animateState={getCardMotionState(4)}
          />
        </div>
      </motion.div>
    </section>
  );
};

export default TravellerPlan;
