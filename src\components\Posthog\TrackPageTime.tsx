import { useEffect } from "react";
import { useRouter } from "next/router";
import tracker from "@/utils/posthogTracker";

const TrackPageTime = () => {
  const router = useRouter();
  const currentPath = router.pathname;

  useEffect(() => {
    const startTime = Date.now();

    const handleRouteChange = () => {
      const duration = Math.round((Date.now() - startTime) / 1000);
      tracker.trackEvent("time_spent", {
        path: currentPath,
        duration_seconds: duration,
      });
    };

    router.events.on("routeChangeComplete", handleRouteChange);
    return () => {
      router.events.off("routeChangeComplete", handleRouteChange);
    };
  }, [router.events, currentPath]);

  return null;
};

export default TrackPageTime;
