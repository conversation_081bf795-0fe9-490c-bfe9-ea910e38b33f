NxVoy Playwright Cucumber MasterFramework

📌 Overview

This repository contains the NxVoy Playwright Test Automation Framework, built using Playwright and Cucumber for end-to-end (E2E) testing and automated accessibility (A11y) testing. It ensures high test coverage, cross-browser compatibility, and strict compliance with accessibility standards.

🏗️ Tech Stack

Test Runner: Playwright
BDD Framework: Cucumber.js
Programming Language: TypeScript
Accessibility Testing: Axe-core integration
CI/CD Integration: GitHub Actions / GitLab CI

🚀 Getting Started

1️⃣ Prerequisites
Ensure you have the following installed:

Node.js (v16 or later)
npm (v8 or later) or yarn
Playwright browsers (Chromium, Firefox, WebKit)
To install Playwright with dependencies, run:

npm install
npx playwright install
2️⃣ Running Tests
▶️ Run All E2E Tests

npx cucumber-js
▶️ Run Specific Test (By Feature File)

npx cucumber-js tests/e2e/features/login.feature
▶️ Run Tests in Headed Mode (Debugging)

npx playwright test --headed
▶️ Generate & View HTML Report

npx playwright test --reporter=html
🎯 Accessibility (A11y) Testing

This framework includes automated accessibility testing using axe-core to ensure WCAG compliance.

▶️ Run Accessibility Tests

npx playwright test --grep @a11y
✅ Best Practices for A11y Testing
Ensure all images have alt attributes.
Maintain high color contrast between text and backgrounds.
Validate keyboard navigability and focus indicators.
Test all ARIA roles and labels.
📂 Project Structure

📦 nxvoy-playwright-framework
 ┣ 📂 tests
 ┃ ┣ 📂 e2e (End-to-End tests)
 ┃ ┣ 📂 accessibility (A11y tests)
 ┃ ┣ 📜 login.feature (Cucumber feature file)
 ┣ 📂 reports (HTML and JSON reports)
 ┣ 📜 playwright.config.ts
 ┣ 📜 package.json
 
🔁 CI/CD Integration

This framework supports automated testing in CI/CD pipelines using GitHub Actions or GitLab CI.

▶️ Run Tests in CI Pipeline

npm run test:ci
👥 Contributing

Fork the repository.
Create a feature branch: git checkout -b feature-name
Commit your changes: git commit -m 'Added new feature'
Push to the branch: git push origin feature-name
Open a Pull Request against development branch.
📜 License

This project is licensed under the MIT License.
