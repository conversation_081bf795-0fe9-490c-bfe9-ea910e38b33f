import { given, when, then, binding } from 'cucumber-tsflow';
import { DataTable, defineParameterType } from '@cucumber/cucumber';
import { ApiService } from '../../services/ApiService';
import { EndpointHelper } from '../../properties/EndpointHelper';
import { PayloadHelper } from '../../utils/PayloadHelper';
import { SchemaHelper } from '../../utils/SchemaHelper';
import { Properties } from '../../properties/Properties';
import { expect } from '@playwright/test';

defineParameterType({
    name: 'boolean',
    regexp: /true|false/,
    transformer: (s: any) => (s === 'true' ? true : false),
  });
@binding()
export class CommonApiSteps {
    @given('I set the base URL to the {string} URL')
    public iSetTheBaseUrlToTheUrl(url: string) {
        ApiService.setBaseUrl(Properties.getProperty(url));
    }

    @given('I make a {string} request to the {string} endpoint')
    public async makeGetRequest(method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE", url: string) {
        await ApiService.makeRequest(method, EndpointHelper.getEndpoint(url));
    }

    @given('The request query parameters are the following:')
    public theRequestQueryParamsAreTheFollowing(queryParams:  DataTable) {
        queryParams.rows().forEach((param: string[]) => { 
            ApiService.queryParams[param[0]] = param[1]; });
    }

    @when('The request path parameters are the following:')
    public theRequestPathParametersAreTheFollowing(pathParams: DataTable) {
        pathParams.rows().forEach((param: string[]) => { ApiService.pathParams[param[0]] = param[1]; });
    }

    @when('The request headers are the following:')
    public theRequestHeadersAreTheFollowing(headers:  DataTable) {
        headers.rows().forEach((header: string[]) => { 
            ApiService.headers[header[0]] = header[1]; });
    }

    @when('The request body is the {string} payload')
    public async theRequestBodyIsThePayload(payloadName: string) {
        ApiService.requestBody = JSON.stringify(await PayloadHelper.getPayload(payloadName));
    }

    @then('The response matches the {string} schema')
    public async theResponseMatchesTheSchema(schemaName: string) {
        await SchemaHelper.validateSchema(await SchemaHelper.getSchema(schemaName), await ApiService.response.json());
    }

    @then('The {string} response field value is {string}')
    public async theResponseFieldValueIsString(field: string, value: string) {
        expect(Properties.getJsonValueWithCompoundKey(await ApiService.response.json(), field.split("."))).toEqual(value);
    }

    @then('The {string} response field value is {int}')
    public async theResponseFieldValueIsNumber(field: string, value: number) {
        expect(Properties.getJsonValueWithCompoundKey(await ApiService.response.json(), field.split("."))).toEqual(value);
    }

    @then('The response is empty')
    public async theResponseIsEmpty() {
        expect(await ApiService.response.text()).toBe('');
    }

    @then('The status code is {int}')
    public theStatusCodeIs(statusCode: number) {
        expect(ApiService.response.status()).toBe(statusCode);
    }
}