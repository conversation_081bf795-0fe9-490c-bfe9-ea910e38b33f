# Use Alpine-based Nginx + Node.js image
FROM hoosin/alpine-nginx-nodejs:latest

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install --force

# Copy all project files
COPY . .

# Accept ENV variable for NODE_ENV
ARG NODE_ENV

# Set environment variable
ENV NODE_ENV=${NODE_ENV}
ENV NGINX_CONF_PATH=/etc/nginx

# Build Next.js only for production
RUN npm run build:$NODE_ENV

# Create Nginx config directory and copy config
RUN mkdir -p $NGINX_CONF_PATH
COPY nginx/nginx.conf $NGINX_CONF_PATH/nginx.conf

# Give execution permission
RUN chmod +x entrypoint.sh

# Expose port 80
EXPOSE 3000

# Run entrypoint script
ENTRYPOINT ["./entrypoint.sh"]
C<PERSON> ["foreman", "start"]

