"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

interface TabNavigationProps {
    activeTab: string
    onTabChange: (tab: string) => void
}

export default function TabNavigation({ activeTab, onTabChange }: TabNavigationProps) {
    return (
        <Tabs value={activeTab} className="mt-6">
            <TabsList className="justify-start rounded-none bg-transparent h-auto p-0 space-x-6">
                <TabsTrigger
                    value="overview"
                    onClick={() => onTabChange("overview")}
                    className="text-[#959AC2] rounded-none border-b-2 border-transparent data-[state=active]:border-[#1E1E76] data-[state=active]:text-[#1E1E76] px-0 pb-2 font-medium"
                >
                    Overview
                </TabsTrigger>
                <TabsTrigger
                    value="reviews"
                    onClick={() => onTabChange("reviews")}
                    className="text-[#959AC2] rounded-none border-b-2 border-transparent data-[state=active]:border-[#1E1E76] data-[state=active]:text-[#1E1E76] px-0 pb-2 font-medium"
                >
                    Reviews
                </TabsTrigger>
                <TabsTrigger
                    value="policies"
                    onClick={() => onTabChange("policies")}
                    className="text-[#959AC2] rounded-none border-b-2 border-transparent data-[state=active]:border-[#1E1E76] data-[state=active]:text-[#1E1E76] px-0 pb-2 font-medium"
                >
                    Policies
                </TabsTrigger>
                <TabsTrigger
                    value="amenities"
                    onClick={() => onTabChange("amenities")}
                    className="text-[#959AC2] rounded-none border-b-2 border-transparent data-[state=active]:border-[#1E1E76] data-[state=active]:text-[#1E1E76] px-0 pb-2 font-medium"
                >
                    Amenities
                </TabsTrigger>
                <TabsTrigger
                    value="location"
                    onClick={() => onTabChange("location")}
                    className="text-[#959AC2] rounded-none border-b-2 border-transparent data-[state=active]:border-[#1E1E76] data-[state=active]:text-[#1E1E76] px-0 pb-2 font-medium"
                >
                    Location
                </TabsTrigger>
            </TabsList>
        </Tabs>
    )
}
