import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import OtpVerificationScreen from "./OtpVerificationScreen";


// Mock OtpInput and Button to simplify tests
jest.mock("../../components/auth/OtpInput", () => (props: any) => (
  <div data-testid="otp-input">
    {Array.from({ length: props.inputCount }).map((_, i) => (
      <input
        key={i}
        data-testid={`otp-input-${i}`}
        value={props.otp[i] || ""}
        onChange={e => props.onChange(i, e.target.value)}
        onKeyDown={e => props.onKeyDown(i, e)}
        onPaste={props.onPaste}
        ref={el => { props.inputRefs.current[i] = el; }}
      />
    ))}
  </div>
));
jest.mock("../../components/ui/LoginButton", () => (props: any) => (
  <button {...props}>{props.children}</button>
));

describe("OtpVerificationScreen", () => {
  const onVerify = jest.fn();
  const handleResend = jest.fn();
  const close = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders all static texts and elements", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    expect(screen.getByText("OTP Verification")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Enter the verification code we just sent to your email/phone number."
      )
    ).toBeInTheDocument();
    expect(screen.getByText("Verification Code")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Change email/i })).toBeInTheDocument();
    expect(screen.getByText("Resend code")).toBeInTheDocument();
    expect(screen.getByLabelText("Close")).toBeInTheDocument();
    expect(screen.getByTestId("otp-input")).toBeInTheDocument();
  });

  it("shows error message from otpErrorMessage prop", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage="Invalid OTP"
        type="email"
        close={close}
      />
    );
    expect(screen.getByText("Invalid OTP")).toBeInTheDocument();
  });

  it("updates error message when otpErrorMessage changes", async () => {
    const { rerender } = render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage="First error"
        type="email"
        close={close}
      />
    );
    expect(screen.getByText("First error")).toBeInTheDocument();

    rerender(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    await waitFor(() => {
      expect(screen.queryByText("First error")).not.toBeInTheDocument();
    });
  });

  it("shows error if OTP is incomplete on submit", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    fireEvent.click(screen.getByRole("button", { name: /Change email/i }));
    expect(
      screen.getByText("Enter the verification code we just sent to your email/phone number.")
    ).toBeInTheDocument();
    expect(onVerify).not.toHaveBeenCalled();
  });

  it("calls onVerify when OTP is complete and form is submitted", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    // Fill all OTP inputs
    for (let i = 0; i < 6; i++) {
      fireEvent.change(screen.getByTestId(`otp-input-${i}`), {
        target: { value: String(i + 1) },
      });
    }
    fireEvent.click(screen.getByRole("button", { name: /Change email/i }));
    expect(onVerify).toHaveBeenCalledWith(["1", "2", "3", "4", "5", "6"]);
  });

  it("calls handleResend when Resend code is clicked", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    fireEvent.click(screen.getByText("Resend code"));
    expect(handleResend).toHaveBeenCalled();
  });

  it("shows loading states for verifying and resending", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={true}
        loadingEmailOtpVerify={true}
        otpErrorMessage={null}
        type="phone"
        close={close}
      />
    );
    expect(screen.getByRole("button", { name: /Verifying.../i })).toBeInTheDocument();
    expect(screen.getByText("Resending...")).toBeInTheDocument();
  });

  it("shows correct button label for type=phone", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="phone"
        close={close}
      />
    );
    expect(screen.getByRole("button", { name: /Change phone/i })).toBeInTheDocument();
  });

  it("calls close when close button is clicked", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
        close={close}
      />
    );
    fireEvent.click(screen.getByLabelText("Close"));
    expect(close).toHaveBeenCalled();
  });

  it("does not render close button if close prop is not provided", () => {
    render(
      <OtpVerificationScreen
        onVerify={onVerify}
        handleResend={handleResend}
        loadingEmailOtp={false}
        loadingEmailOtpVerify={false}
        otpErrorMessage={null}
        type="email"
      />
    );
    expect(screen.queryByLabelText("Close")).not.toBeInTheDocument();
  });
});