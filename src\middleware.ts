import { withAuth } from "next-auth/middleware";
import { NextRequest, NextResponse } from "next/server";

function isPublic(req: NextRequest) {
  const { pathname, searchParams } = req.nextUrl;

  const traveler = searchParams.get("traveler");

  const isLoginPage = pathname === "/chat" && traveler === "flight booking";

  const publicPaths = [
    "/",
    "/destination",
    "/stripe_test.html",
    "/favicon.ico",
    "/faq",
    "/about-us",
    "/contact-us",
    "/privacy-policy",
    "/terms-of-service",
    "/membership",
    "/coming-soon",
  ];

  return publicPaths.includes(pathname) || isLoginPage;
}

export default withAuth(
  function middleware(req) {
    if (isPublic(req)) {
      return NextResponse.next();
    }

    console.log("Protected route, checking auth...");
    return NextResponse.next(); // must return a value here
  },
  {
    pages: {
      signIn: "/chat", // exact login page for redirects
    },
    callbacks: {
      authorized({ req, token }) {
        const url = req.nextUrl;
        const accessToken = url.searchParams.get("accessToken");
        const refreshToken = url.searchParams.get("refreshToken");

        // Consider tokens from session or query params
        const isAllowed =
          isPublic(req) || !!token || !!accessToken || !!refreshToken;

        return isAllowed;
      },
    },
  }
);

// Exclude API/auth routes and static files
export const config = {
  matcher: ["/((?!api/auth|_next|favicon.ico).*)"],
};
