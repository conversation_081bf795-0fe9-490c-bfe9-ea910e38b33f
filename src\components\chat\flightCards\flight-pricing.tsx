import { formatFlightPrice } from "@/lib/utils/formatPrice"
import type React from "react"

interface FlightPricingProps {
    amount: any
    isMobile?: boolean
    showCancellation?: boolean
    isExpanded?: boolean
}

const FlightPricing: React.FC<FlightPricingProps> = ({
    amount,
    isMobile = false,
    showCancellation = true,
    isExpanded = false,
}) => {
    if (isMobile) {
        return (
            <div>
                <div className="flex justify-between items-center">
                    {showCancellation && <div className="text-sm text-[#FF3B3F]">*Free Cancellation within 24 hours of booking</div>}

                </div>
                <div className="text-right">
                    <div className="text-2xl font-bold text-[#24C72F]">{formatFlightPrice(amount)}</div>
                    <div className="text-sm text-[#B4BBE8]">per person</div>
                </div>
            </div>
        )
    }

    if (isExpanded) {
        return (
            <div className="flex justify-end">
                <div className="text-right">
                    <div className="text-2xl font-bold text-[#24C72F]"> {formatFlightPrice(amount)}</div>
                    <div className="text-sm text-[#B4BBE8]">for all passengers</div>
                </div>
            </div>
        )
    }

    return (
        <div className="text-sm text-[#B4BBE8] mt-3 flex justify-between relative">
            {/* {showCancellation && <div className="ml-[76px] text-[#FF3B3F]">*Free Cancellation within 24 hours of booking</div>} */}
            <div className="text-right absolute right-0 bottom-0">
                <div className="text-2xl font-bold text-[#24C72F]"> {formatFlightPrice(amount)}</div>
                <div className="text-sm text-[#B4BBE8]">for all passengers</div>
            </div>
        </div>
    )
}

export default FlightPricing
