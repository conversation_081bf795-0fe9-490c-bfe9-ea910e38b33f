export class DateHelper {
    /**
     * Returns a future date formatted as "DD MonthName" (e.g., "15 June")
     * @param daysFromNow Number of days from current date
     * @returns Formatted date string
     */
    public static getFutureDateFormatted(daysFromNow: number = 14): string {
        const date = new Date();
        date.setDate(date.getDate() + daysFromNow);
        
        // Format as "10th June" style
        const day = date.getDate();
        const month = date.toLocaleString('en-US', { month: 'long' });
        
        // Add proper ordinal suffix (st, nd, rd, th)
        let suffix = "th";
        if (day % 10 === 1 && day !== 11) suffix = "st";
        if (day % 10 === 2 && day !== 12) suffix = "nd";
        if (day % 10 === 3 && day !== 13) suffix = "rd";
        
        return `${day}${suffix} ${month}`;
    }
}