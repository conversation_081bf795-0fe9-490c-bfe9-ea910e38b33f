'use client'

import { FareOption, FareOptionCard, Flight, FlightResults, FlightSearchForm, PassengerPriceWrapperVariant, SelectedFlightOptions, LuggageOptionsResponse, LuggageSelection, ErrorProps } from '@/constants/models'
import React, { createContext, ReactNode, useContext, useEffect, useMemo, useState } from 'react'

interface TravelersType {
    adults: number;
    children: number;
    infants: number;
    pets: number;
}

interface FlightContextType {
    sharedFlightResults: FlightResults | null;
    searchedFlightResults: FlightResults | null;
    selectedOutboundFlight: Flight | null;
    selectedInboundFlight: Flight | null;
    recommendedFlight: Flight | undefined;
    isReturnTrip: boolean;
    travelers: TravelersType;
    totalPrice: number;
    fareOptions: FareOption[],
    passengersList: PassengerPriceWrapperVariant[],
    selectedOutboundFareOption: FareOptionCard | null;
    selectedInboundFareOption: FareOptionCard | null;
    searchFilter: FlightSearchForm | null;
    selectedFareOptions: SelectedFlightOptions;
    luggageOptions: LuggageOptionsResponse;
    supplierInfo: unknown;
    serviceFee: unknown;
    selectedLuggageInfo: LuggageSelection;
    filteredFareFlights: Flight[];
    globalMessage: ErrorProps;
    updateTravelers: (updated: TravelersType) => void;
    updateSharedFlightResults: (results: FlightResults) => void;
    updateSearchedFlightResults: (results: FlightResults) => void;
    selectOutboundFlight: (flight: Flight | null) => void;
    selectInboundFlight: (flight: Flight | null) => void;
    selectRecommendedFlight: (flight: Flight | undefined) => void;
    setTotalPrice: (price: number) => void;
    updateFareOptions: (options: FareOption[]) => void;
    updatePassengerList: (passengerList: PassengerPriceWrapperVariant[]) => void;
    updateOutboundFareOption: (fareOption: FareOptionCard | null) => void;
    updateInboundFareOption: (fareOption: FareOptionCard | null) => void;
    updateSearchFlights: (searchFlights: FlightSearchForm | null) => void;
    updateSelectedFareOptions: (fareOptions: SelectedFlightOptions) => void;
    updateLuggageOptions: (luggageOptions: LuggageOptionsResponse) => void;
    updateSelectedLuggageOptions: (luggageOptions: LuggageSelection) => void;
    updateFilteredFareFlights: (flights: Flight[]) => void;
    updateGlobalPopup: (error: ErrorProps) => void;
    updateSupplierInfo: unknown;
    updateServiceFee: unknown;

}

const FlightContext = createContext<FlightContextType | undefined>(undefined);

const FlightProvider = ({ children }: { children: ReactNode }) => {
    const [sharedFlightResults, setSharedFlightResults] = useState<FlightResults | null>(null);
    const [searchedFlightResults, setSearchedFlightResults] = useState<FlightResults | null>(null)
    const [selectedOutboundFlight, setSelectedOutboundFlight] = useState<Flight | null>(null);
    const [selectedInboundFlight, setSelectedInboundFlight] = useState<Flight | null>(null);
    const [selectedOutboundFareOption, setSelectedOutboundFareOption] = useState<FareOptionCard | null>(null);
    const [selectedInboundFareOption, setSelectedInboundFareOption] = useState<FareOptionCard | null>(null);
    const [recommendedFlight, setRecommendedFlight] = useState<Flight | undefined>(undefined);
    const [totalPrice, setTotalPrice] = useState(0);
    const [fareOptions, setFareOptions] = useState<FareOption[]>([]);
    const [passengersList, setPassengersList] = useState<PassengerPriceWrapperVariant[]>([]);
    const [travelers, setTravelers] = useState<TravelersType>({
        adults: 1,
        children: 0,
        infants: 0,
        pets: 0,
    });

    const [searchFilter, setSearchFilter] = useState<FlightSearchForm | null>({
        origin: '',
        destination: '',
        departure_date: '',
        return_date: '',
        adults: 1,
        children: 0,
        infants: 0,
        travel_class: '',
    });
    const [selectedFareOptions, setSelectedFareOptions] = useState<SelectedFlightOptions>({ outbound: null, inbound: null });
    const [filteredFareFlights, setFilteredFareFlights] = useState<Flight[]>([]);
    const [luggageOptions, setLuggageOptions] = useState<LuggageOptionsResponse>({
        _outward: [],
        _return: [],
        is_per_passenger: false,
        option_names: { generic: '', outward: '', return: '' },
    });

    const [selectedLuggageInfo, setSelectedLuggageInfo] = useState<LuggageSelection>({ outbound: [], inbound: [], inboundPrice: 0, outboundPrice: 0 });
    const [globalMessage, setGlobalMessage] = useState<ErrorProps>({ isOpen: false, message: '', type: '' });
    const [supplierInfo, setSupplierInfo] = useState([])
    const [serviceFee, setServiceFee] = useState(0)


    const isReturnTrip = useMemo(() => {
        return (
            Array.isArray(sharedFlightResults?.return_flights?.flights) &&
            sharedFlightResults.return_flights.flights.length > 0
        );
    }, [sharedFlightResults, searchedFlightResults]);

    const updateSharedFlightResults = (results: FlightResults) => {
        setSharedFlightResults(results);
    };

    const updateSearchedFlightResults = (results: FlightResults) => {
        setSharedFlightResults(results);
        setSearchedFlightResults(results);
    };

    const selectOutboundFlight = (flight: Flight | null) => {
        setSelectedOutboundFlight(flight);
    };

    const selectInboundFlight = (flight: Flight | null) => {
        setSelectedInboundFlight(flight);
    };

    const selectRecommendedFlight = (flight: Flight | undefined) => {
        setRecommendedFlight(flight);
    };

    const updateTravelers = (updated: TravelersType) => {
        setTravelers(updated);
    };

    const updateFareOptions = (fareOptions: FareOption[]) => {
        setFareOptions(fareOptions)
    }

    const updatePassengerList = (list: PassengerPriceWrapperVariant[]) => {
        setPassengersList(list);
    }

    const updateOutboundFareOption = (fareOption: FareOptionCard | null) => {
        setSelectedOutboundFareOption(fareOption);
    }

    const updateInboundFareOption = (fareOption: FareOptionCard | null) => {
        setSelectedInboundFareOption(fareOption);
    }

    const updateSearchFlights = (searchFlights: FlightSearchForm | null) => {
        setSearchFilter(searchFlights);
    }

    const updateSelectedFareOptions = (fareOptions: SelectedFlightOptions) => {
        setSelectedFareOptions(fareOptions);
    }

    const updateLuggageOptions = (luggageOptions: LuggageOptionsResponse) => {
        setLuggageOptions(luggageOptions);
    }

    const updateSelectedLuggageOptions = (luggageOptions: LuggageSelection) => {
        setSelectedLuggageInfo(luggageOptions);
    }

    const updateFilteredFareFlights = (flights: Flight[]) => {
        setFilteredFareFlights(flights);
    }

    const updateGlobalPopup = (error: ErrorProps) => {
        setGlobalMessage(error);
    }

    const updateSupplierInfo = (value: any) => {
        setSupplierInfo(value)
    }

    const updateServiceFee = (value: any) => {
        setServiceFee(value)
    }


    return (
        <FlightContext.Provider
            value={{
                sharedFlightResults,
                searchedFlightResults,
                selectedOutboundFlight,
                selectedInboundFlight,
                recommendedFlight,
                isReturnTrip,
                travelers,
                totalPrice,
                fareOptions,
                passengersList,
                selectedOutboundFareOption,
                selectedInboundFareOption,
                searchFilter,
                selectedFareOptions,
                luggageOptions,
                selectedLuggageInfo,
                filteredFareFlights,
                globalMessage,
                supplierInfo,
                serviceFee,
                updateTravelers,
                updateSharedFlightResults,
                updateSearchedFlightResults,
                selectOutboundFlight,
                selectInboundFlight,
                selectRecommendedFlight,
                setTotalPrice,
                updateFareOptions,
                updatePassengerList,
                updateOutboundFareOption,
                updateInboundFareOption,
                updateSearchFlights,
                updateSelectedFareOptions,
                updateLuggageOptions,
                updateSelectedLuggageOptions,
                updateFilteredFareFlights,
                updateGlobalPopup,
                updateSupplierInfo,
                updateServiceFee,
            }}
        >
            {children}
        </FlightContext.Provider>
    )
}

export default FlightProvider

export const useFlightContext = () => {
    const context = useContext(FlightContext);
    if (!context) {
        throw new Error('useFlightContext must be used within a FlightProvider');
    }
    return context;
}