import { fixture } from "../fixtures/Fixture";
import { BasePage } from "./BasePage";
import { ScreenshotHelper } from "../utils/ScreenshotHelper";

/**
 * Page object for payment-related interactions
 */
export class PaymentPage extends BasePage {
    // Element selectors
    static elements = {
        cardNumber: 'input#Card\\ Number',
        expiryMonth: 'input#Month',
        expiryYear: 'input#Year',
        cvv: 'input#Cvv',
        nameOnCard: 'input#Name',        
        // Billing address elements
        streetAddress: 'input#street\\ address, input[placeholder="Street Address"]',
        aptSuiteOther: 'input#Apt\\ \\/\\ Suite\\ \\/\\ Other\\ \\ , input[placeholder="Apt / Suite / Other  "]',
        city: 'input#City, input[placeholder="City"]',
        state: 'input#State, input[placeholder="State / Province"]',
        zipCode: 'input#Zip\\ Code, input[placeholder="Zip Code"]',
        country: 'input#Country, input[placeholder="Country"]',
        // Country suggestion elements - updated to match the exact HTML structure provided
        countrySuggestions: '.absolute.z-10.mt-2.w-full.bg-white.text-brand-black.rounded-md.shadow-lg.max-h-60.overflow-auto',
        countrySuggestionItem: 'div.px-4.py-2.hover\\:bg-brand-grey.cursor-pointer.text-brand-black',
        // Confirm pay button
        confirmPayButton: 'button:has-text("Confirm & Pay"), button[style*="background: linear-gradient"][style*="border-radius: 100px"]:has-text("Confirm"), .px-4.py-2:has-text("Confirm & Pay")',        
        // Contact info elements
        titleDropdown: '.contactInfo button[id="title"], button#title',
        titleOptions: '.contactInfo div[id^="headlessui-listbox-options"] div, div[id^="headlessui-listbox-options"] div',
        nameField: '.contactInfo input#name, input#name',
        countryCodeDropdown: 'div.flag',
        countryList: 'ul.country-list',
        countrySearchField: 'input.search-box',
        countryListItems: 'ul.country-list li.country',
        phoneNumber: 'input[type="tel"][name="phone"]',
        emailId: 'input#Email'
    };/**
     * Fill credit card information
     * @param creditCardData Object containing credit card information
     */
    static async fillCreditCardInfo(creditCardData: {
        cardnumber?: string | number;
        expMonth?: string | number;
        eXPYear?: string | number;
        cvv?: string | number;
        ccName?: string;
    }): Promise<boolean> {
        try {
            console.log('Filling credit card information...');

            // Fill card number
            if (creditCardData?.cardnumber) {
                await this.fillCardNumber(creditCardData.cardnumber.toString());
            }
              // Fill expiry month
            if (creditCardData?.expMonth) {
                await this.fillExpiryMonth(creditCardData.expMonth.toString());
            }
              // Fill expiry year
            if (creditCardData?.eXPYear) {
                await this.fillExpiryYear(creditCardData.eXPYear.toString());
            }
            
            // Fill CVV
            if (creditCardData?.cvv) {
                await this.fillCVV(creditCardData.cvv.toString());
            }
            
            // Fill name on card
            if (creditCardData?.ccName) {
                await this.fillNameOnCard(creditCardData.ccName.toString());
            }

            // Take a screenshot after filling the form
            await ScreenshotHelper.takeScreenshot('credit-card-info-filled');
            
            return true;
        } catch (error) {
            console.error('Error filling credit card information:', error);
            await ScreenshotHelper.takeScreenshot('credit-card-info-error', true);
            return false;
        }
    }

    /**
     * Fill the card number field
     */
    private static async fillCardNumber(cardNumber: string): Promise<void> {
        try {
            console.log('Filling card number...');
            await fixture.page.fill(this.elements.cardNumber, cardNumber);
            console.log('Card number filled successfully');
        } catch (error) {
            console.error('Error filling card number:', error);
            throw error;
        }
    }

    /**
     * Fill the expiry month field
     */
    private static async fillExpiryMonth(month: string): Promise<void> {
        try {
            console.log('Filling expiry month...');
            await fixture.page.fill(this.elements.expiryMonth, month);
            console.log('Expiry month filled successfully');
        } catch (error) {
            console.error('Error filling expiry month:', error);
            throw error;
        }
    }

    /**
     * Fill the expiry year field
     */
    private static async fillExpiryYear(year: string): Promise<void> {
        try {
            console.log('Filling expiry year...');
            await fixture.page.fill(this.elements.expiryYear, year);
            console.log('Expiry year filled successfully');
        } catch (error) {
            console.error('Error filling expiry year:', error);
            throw error;
        }
    }

    /**
     * Fill the CVV field
     */
    private static async fillCVV(cvv: string): Promise<void> {
        try {
            console.log('Filling CVV...');
            await fixture.page.fill(this.elements.cvv, cvv);
            console.log('CVV filled successfully');
        } catch (error) {
            console.error('Error filling CVV:', error);
            throw error;
        }
    }

    /**
     * Fill the name on card field
     */
    private static async fillNameOnCard(name: string): Promise<void> {
        try {
            console.log('Filling name on card...');
            await fixture.page.fill(this.elements.nameOnCard, name);
            console.log('Name on card filled successfully');
        } catch (error) {
            console.error('Error filling name on card:', error);
            throw error;
        }
    }    /**
     * Verify that the credit card information section is visible
     */
    static async isCreditCardFormVisible(): Promise<boolean> {
        try {
            console.log('Checking if credit card form is visible...');
            
            // Check if the card number input field is visible
            const isCardNumberVisible = await fixture.page.isVisible(this.elements.cardNumber, { timeout: 5000 });
            
            console.log(`Credit card form visibility: ${isCardNumberVisible}`);
            return isCardNumberVisible;
        } catch (error) {
            console.error('Error checking credit card form visibility:', error);
            return false;
        }
    }    /**
     * Fill billing address information
     * @param billingAddressData Object containing billing address information
     */
    static async fillBillingAddress(billingAddressData: {
        streetAddress?: string;
        aptSuiteOther?: string;
        city?: string;
        state?: string;
        zipcode?: string;
        country?: string;
    }): Promise<boolean> {
        try {
            console.log('Filling billing address information...');
            
            // Add a brief pause to ensure the page is ready
            await fixture.page.waitForTimeout(1000);
            
            // Try to fill each field with fallback mechanisms
            try {
                // Fill street address
                if (billingAddressData?.streetAddress) {
                    await this.fillStreetAddress(billingAddressData.streetAddress);
                    // Brief pause between fields to avoid race conditions
                    await fixture.page.waitForTimeout(500);
                }
                
                // Fill apt/suite/other
                if (billingAddressData?.aptSuiteOther) {
                    await this.fillAptSuiteOther(billingAddressData.aptSuiteOther);
                    await fixture.page.waitForTimeout(500);
                }
                
                // Fill city
                if (billingAddressData?.city) {
                    await this.fillCity(billingAddressData.city);
                    await fixture.page.waitForTimeout(500);
                }
                
                // Fill state
                if (billingAddressData?.state) {
                    await this.fillState(billingAddressData.state);
                    await fixture.page.waitForTimeout(500);
                }
                
                // Fill zipcode
                if (billingAddressData?.zipcode) {
                    await this.fillZipCode(billingAddressData.zipcode);
                    await fixture.page.waitForTimeout(500);
                }
                
                // Fill country
                if (billingAddressData?.country) {
                    await this.fillCountry(billingAddressData.country);
                }
                
                // Add a fallback if direct element interaction doesn't work using JavaScript
                await this.fillBillingAddressWithJavaScript(billingAddressData);
                
            } catch (fieldError) {
                console.warn('Error filling fields normally, trying JavaScript fallback', fieldError);
                // Try using JavaScript to fill the fields directly
                await this.fillBillingAddressWithJavaScript(billingAddressData);
            }

            // Take a screenshot after filling the form
            await ScreenshotHelper.takeScreenshot('billing-address-filled');
            
            return true;
        } catch (error) {
            console.error('Error filling billing address information:', error);
            await ScreenshotHelper.takeScreenshot('billing-address-error', true);
            return false;
        }
    }
    
    /**
     * Fallback method to fill billing address using JavaScript evaluation
     */    private static async fillBillingAddressWithJavaScript(billingAddressData: {
        streetAddress?: string;
        aptSuiteOther?: string;
        city?: string;
        state?: string;
        zipcode?: string;
        country?: string;
    }): Promise<void> {
        console.log('Attempting to fill billing address fields using JavaScript fallback...');
        
        try {
            // First fill all fields except country
            await fixture.page.evaluate((data) => {
                // Helper function to fill an input by various selectors
                function fillField(selectors, value) {
                    if (!value) return;
                    
                    // Try different ways to find the input element
                    const possibleSelectors = [
                        `input#${selectors.replace(/\s/g, '\\ ')}`,
                        `input[placeholder="${selectors}"]`,
                        `input[placeholder*="${selectors.split(' ')[0]}"]`,
                        `input[id*="${selectors.split(' ')[0].toLowerCase()}"]`
                    ];
                    
                    for (const selector of possibleSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            for (let i = 0; i < elements.length; i++) {
                                const element = elements[i] as HTMLInputElement;
                                element.value = value;
                                element.dispatchEvent(new Event('input', { bubbles: true }));
                                element.dispatchEvent(new Event('change', { bubbles: true }));
                                console.log(`Filled field ${selectors} with value ${value}`);
                                return true;
                            }
                        }
                    }
                    return false;
                }
                
                // Fill each field (except country, which needs special handling)
                fillField('street address', data.streetAddress);
                fillField('Apt / Suite / Other', data.aptSuiteOther);
                fillField('City', data.city);
                fillField('State', data.state);
                fillField('Zip Code', data.zipcode);
                
            }, billingAddressData);
              // Special handling for country field using a separate JS evaluation
            if (billingAddressData?.country) {
                console.log('Using special handling for country field...');
                
                // First fill the country field
                await fixture.page.evaluate((country) => {
                    // Try to find the country input
                    const countryFields = [
                        document.querySelector('input#Country'),
                        document.querySelector('input[placeholder="Country"]'),
                        document.querySelector('input.w-full.px-3.py-2[placeholder*="Country"]'),
                        ...Array.from(document.querySelectorAll('input[placeholder*="Country"]'))
                    ].filter(Boolean);
                    
                    if (countryFields.length > 0) {
                        const countryInput = countryFields[0] as HTMLInputElement;
                        
                        // Focus the field first (helps trigger proper events)
                        countryInput.focus();
                        
                        // Clear the field
                        countryInput.value = '';
                        countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                        
                        // Fill with country name
                        countryInput.value = country;
                        countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                        countryInput.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        console.log(`Filled country field with value ${country}`);
                        return true;
                    }
                    return false;
                }, billingAddressData.country);
                
                // Wait for suggestions to appear
                await fixture.page.waitForTimeout(1000);
                
                // Take a screenshot to help debug
                await ScreenshotHelper.takeScreenshot('country-suggestions-javascript');
                
                // Handle the suggestions dropdown with a more robust approach
                await fixture.page.evaluate((country) => {
                    // Function to find and click suggestion
                    function findAndClickSuggestion() {
                        // Use the specific selector from the example
                        const exactSelector = 'div.absolute.z-10.mt-2.w-full.bg-white.text-black.rounded-md.shadow-lg.max-h-60.overflow-auto div.px-4.py-2.hover\\:bg-gray-100.cursor-pointer';
                        
                        // Try with exact selector first
                        let suggestions = Array.from(document.querySelectorAll(exactSelector));
                        
                        // If not found, try more generic selectors
                        if (suggestions.length === 0) {
                            suggestions = Array.from(document.querySelectorAll(
                                'div.absolute.z-10 div, ' +
                                'div.px-4.py-2, ' + 
                                'div.hover\\:bg-gray-100, ' +
                                'div.cursor-pointer'
                            ));
                        }
                        
                        if (suggestions.length === 0) {
                            console.log('No suggestions found');
                            return false;
                        }
                        
                        // Try to find exact match first
                        for (const suggestion of suggestions) {
                            if (suggestion.textContent && 
                                suggestion.textContent.toLowerCase().includes(country.toLowerCase())) {
                                (suggestion as HTMLElement).click();
                                console.log(`Clicked on matching suggestion: ${suggestion.textContent}`);
                                return true;
                            }
                        }
                        
                        // If no exact match, click the first suggestion
                        if (suggestions.length > 0) {
                            (suggestions[0] as HTMLElement).click();
                            console.log(`Clicked on first suggestion: ${suggestions[0].textContent}`);
                            return true;
                        }
                        
                        return false;
                    }
                    
                    // Try to find and click immediately
                    let clicked = findAndClickSuggestion();
                    
                    // If not successful, try again after a short delay
                    if (!clicked) {
                        setTimeout(() => {
                            clicked = findAndClickSuggestion();
                            
                            // If still not successful, try to format country better and set directly
                            if (!clicked) {
                                const countryInput = document.querySelector('input#Country, input[placeholder="Country"]') as HTMLInputElement;
                                if (countryInput) {
                                    // Format country name with proper capitalization
                                    const formattedCountry = country.split(' ')
                                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                                        .join(' ');
                                        
                                    countryInput.value = formattedCountry;
                                    countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    countryInput.dispatchEvent(new Event('change', { bubbles: true }));
                                    
                                    // Dispatch Enter key and then Tab key
                                    countryInput.dispatchEvent(new KeyboardEvent('keydown', {
                                        key: 'Enter',
                                        code: 'Enter',
                                        keyCode: 13,
                                        bubbles: true
                                    }));
                                    
                                    // Also blur the field to ensure dropdown is dismissed
                                    setTimeout(() => {
                                        countryInput.blur();
                                    }, 300);
                                }
                            }
                        }, 500);
                    }
                    
                    // Dispatch an Enter key event as a fallback
                    setTimeout(() => {
                        const countryInput = document.querySelector('input#Country, input[placeholder="Country"]');
                        if (countryInput) {
                            // First Enter to select suggestion
                            countryInput.dispatchEvent(new KeyboardEvent('keydown', {
                                key: 'Enter',
                                code: 'Enter',
                                keyCode: 13,
                                bubbles: true
                            }));
                            
                            // Then Tab to move focus away
                            setTimeout(() => {
                                countryInput.dispatchEvent(new KeyboardEvent('keydown', {
                                    key: 'Tab',
                                    code: 'Tab',
                                    keyCode: 9,
                                    bubbles: true
                                }));
                                
                                // Then blur to dismiss any remaining dropdowns
                                setTimeout(() => {
                                    (countryInput as HTMLElement).blur();
                                }, 200);
                            }, 200);
                        }
                    }, 1000);
                }, billingAddressData.country);
                
                // Wait for selection to complete
                await fixture.page.waitForTimeout(2000);
                
                // Press Tab and Escape keys to ensure dropdown is dismissed
                await fixture.page.keyboard.press('Tab');
                await fixture.page.waitForTimeout(500);
                await fixture.page.keyboard.press('Escape');
                
                // Take a final screenshot
                await ScreenshotHelper.takeScreenshot('after-country-selection-javascript');
            }
            
            console.log('JavaScript fallback for billing address fields completed');
            
        } catch (error) {
            console.error('Error in JavaScript fallback for billing address:', error);
        }
    }    
    
    /**
     * Fill the street address field
     */
    private static async fillStreetAddress(streetAddress: string): Promise<void> {
        try {
            console.log('Filling street address...');
            
            // Try fill first
            try {
                await fixture.page.fill(this.elements.streetAddress, streetAddress, { timeout: 5000 });
            } catch (fillError) {
                console.log('Fill failed, trying alternative methods for street address');
                
                // Try type instead
                await fixture.page.locator('input[placeholder="Street Address"]').type(streetAddress, { timeout: 5000 });
            }
            
            console.log('Street address filled successfully');
        } catch (error) {
            console.error('Error filling street address:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for street address');
        }
    }

    /**
     * Fill the apt/suite/other field
     */
    private static async fillAptSuiteOther(aptSuiteOther: string): Promise<void> {
        try {
            console.log('Filling apt/suite/other...');
            
            try {
                await fixture.page.fill(this.elements.aptSuiteOther, aptSuiteOther, { timeout: 5000 });
            } catch (fillError) {
                console.log('Fill failed, trying alternative methods for apt/suite/other');
                
                // Try more generic selector
                await fixture.page.locator('input[placeholder*="Apt"]').type(aptSuiteOther, { timeout: 5000 });
            }
            
            console.log('Apt/suite/other filled successfully');
        } catch (error) {
            console.error('Error filling apt/suite/other:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for apt/suite/other');
        }
    }

    /**
     * Fill the city field
     */
    private static async fillCity(city: string): Promise<void> {
        try {
            console.log('Filling city...');
            
            try {
                await fixture.page.fill(this.elements.city, city, { timeout: 5000 });
            } catch (fillError) {
                console.log('Fill failed, trying alternative methods for city');
                
                // Try more generic selector
                await fixture.page.locator('input[placeholder="City"]').type(city, { timeout: 5000 });
            }
            
            console.log('City filled successfully');
        } catch (error) {
            console.error('Error filling city:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for city');
        }
    }

    /**
     * Fill the state field
     */
    private static async fillState(state: string): Promise<void> {
        try {
            console.log('Filling state...');
            
            try {
                await fixture.page.fill(this.elements.state, state, { timeout: 5000 });
            } catch (fillError) {
                console.log('Fill failed, trying alternative methods for state');
                
                // Try more generic selector
                await fixture.page.locator('input[placeholder*="State"]').type(state, { timeout: 5000 });
            }
            
            console.log('State filled successfully');
        } catch (error) {
            console.error('Error filling state:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for state');
        }
    }

    /**
     * Fill the zip code field
     */
    private static async fillZipCode(zipCode: string): Promise<void> {
        try {
            console.log('Filling zip code...');
            
            try {
                await fixture.page.fill(this.elements.zipCode, zipCode, { timeout: 5000 });
            } catch (fillError) {
                console.log('Fill failed, trying alternative methods for zip code');
                
                // Try more generic selector
                await fixture.page.locator('input[placeholder="Zip Code"]').type(zipCode, { timeout: 5000 });
            }
            
            console.log('Zip code filled successfully');
        } catch (error) {
            console.error('Error filling zip code:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for zip code');
        }
    }    /**
     * Fill the country field
     */    private static async fillCountry(country: string): Promise<void> {
        try {
            console.log(`Filling country with value: ${country}...`);
            
            // First take a screenshot to see the state before filling
            await ScreenshotHelper.takeScreenshot('before-fill-country');
            
            // Enhanced approach with multiple strategies for better reliability
            await this.fillCountryWithMultipleStrategies(country);
            
            // Verify that suggestions are no longer visible and field has proper value
            await this.verifyCountryWasSelected(country);
            
            console.log('Country filled and selected successfully');
        } catch (error) {
            console.error('Error filling country:', error);
            // Don't throw, let the fallback JavaScript method handle it
            console.log('Will try JavaScript fallback for country');
        }
    }
      /**
     * Try multiple strategies to fill and select a country from suggestions
     * Enhanced implementation to better handle dynamic dropdowns
     */    private static async fillCountryWithMultipleStrategies(country: string): Promise<boolean> {
        // Get the country selectors - using the EXACT structure from the HTML example provided
        const countrySelectors = [
            // Exact selector from provided HTML example
            'input[type="text"][id="Country"][class="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"][placeholder="Country"]',
            // More generalized but still specific selectors as fallbacks
            'input.w-full.px-3.py-2.placeholder\\:text-brand-grey.bg-brand-white.focus\\:outline-none[placeholder="Country"]',
            'input[type="text"][id="Country"]',
            'input#Country.w-full.px-3.py-2',
            // Basic fallbacks
            this.elements.country,
            'input#Country',
            'input[placeholder="Country"]'
        ];
        
        // Try to find and fill country field
        let countryFieldFound = false;
        let countryField = null;
        console.log(`Attempting to fill country field with value: "${country}"`);
        
        // First attempt: Standard Playwright approach with enhanced reliability
        for (const selector of countrySelectors) {
            try {
                // Take screenshot before any interaction
                await ScreenshotHelper.takeScreenshot('before-country-field-search');
                
                countryField = await fixture.page.locator(selector).first();
                if (await countryField.isVisible({ timeout: 3000 })) {
                    // Take screenshot before interaction with the found field
                    await ScreenshotHelper.takeScreenshot('found-country-field');
                    console.log(`Found country field with selector: ${selector}`);
                    
                    // Clear field first by clicking and focusing
                    await countryField.click({ timeout: 3000 });
                    await fixture.page.waitForTimeout(500); // Wait for field to be focused
                    
                    // Clear using keyboard shortcuts first
                    await fixture.page.keyboard.press('Control+a');
                    await fixture.page.waitForTimeout(300);
                    await fixture.page.keyboard.press('Backspace');
                    await fixture.page.waitForTimeout(500);
                    
                    // Also use fill('') as a backup clearing method
                    await countryField.fill('');
                    await fixture.page.waitForTimeout(1000); // Increased wait time for UI to respond after clearing
                    
                    console.log(`Typing country value: "${country}" with deliberate slow typing to ensure dropdown appears`);
                    
                    // Type the country name CHARACTER BY CHARACTER very slowly (600ms between chars)
                    // This is critical for ensuring the dropdown suggestions appear properly
                    await countryField.type(country, { delay: 600 });
                    
                    console.log(`Country field found and filled with selector: ${selector}`);
                    countryFieldFound = true;
                    break;
                }
            } catch (e) {
                console.log(`Error with country selector ${selector}: ${e}`);
            }
        }
        
        if (!countryFieldFound) {
            console.warn('Could not find country field with standard selectors, trying JavaScript approach');
            // JavaScript approach with enhanced typing and event simulation
            await fixture.page.evaluate((countryValue) => {
                const inputSelectors = [
                    // Exact selector from HTML example
                    'input[type="text"][id="Country"][class="w-full px-3 py-2 placeholder:text-brand-grey bg-brand-white focus:outline-none"]',
                    // Fallbacks
                    'input#Country', 
                    'input[type="text"][id="Country"]',
                    'input[placeholder="Country"]',
                    'input.w-full.px-3.py-2'
                ];
                
                for (const selector of inputSelectors) {
                    const input = document.querySelector(selector) as HTMLInputElement;
                    if (input) {
                        console.log(`JS found country input with selector: ${selector}`);
                        
                        // Clear and focus the field with proper events
                        input.focus();
                        input.value = '';
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        console.log('JS typing country value character by character with delays...');
                        
                        // Use a realistic typing simulation with individual character events
                        // This is crucial for dropdown trigger behavior
                        let typingPromise = Promise.resolve();
                        
                        // Define a function to type each character with proper events
                        const typeChar = (char, index) => {
                            return new Promise(resolve => {
                                setTimeout(() => {
                                    // Add the new character
                                    input.value = countryValue.substring(0, index + 1);
                                    
                                    // Dispatch all relevant events
                                    input.dispatchEvent(new Event('input', { bubbles: true }));
                                    
                                    // Log the progress
                                    console.log(`JS typed character: ${char} (${index + 1}/${countryValue.length})`);
                                    
                                    resolve(null);
                                }, 200); // 200ms delay between characters
                            });
                        };
                        
                        // Chain promises to type each character in sequence
                        for (let i = 0; i < countryValue.length; i++) {
                            typingPromise = typingPromise.then(() => typeChar(countryValue[i], i)) as Promise<any>;
                        }
                        
                        // Final change event after all typing is done
                        typingPromise.then(() => {
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            console.log('JS typing completed, all events dispatched');
                        });
                        
                        return true;
                    }
                }
                console.log('Failed to find country input field with any JS selector');
                return false;
            }, country);
            
            countryFieldFound = true; // Assume it worked
            console.log('Filled country field using JavaScript approach with simulated typing');
        }
        
        // CRITICAL IMPROVEMENT: Wait longer for suggestions to appear
        // This is especially important for slow networks or slower rendering
        console.log('Waiting for suggestions dropdown to appear...');
        await fixture.page.waitForTimeout(5000); // Extended wait time (5 seconds)
        
        // Take screenshot after typing to show suggestions
        await ScreenshotHelper.takeScreenshot('country-suggestions-dropdown');
        
        // Try clicking on suggestion with enhanced approach
        let suggestionClicked = await this.clickOnCountrySuggestion(country);
        
        // If first attempt failed, try forcing the dropdown to appear again
        if (!suggestionClicked) {
            console.log('First attempt to click suggestion failed, trying to retrigger dropdown...');
            
            // Try clicking the field again and typing a character to refresh suggestions
            if (countryField) {
                try {
                    await countryField.click({ timeout: 2000 });
                    // Type a space and then backspace to refresh suggestions
                    await fixture.page.keyboard.press(' ');
                    await fixture.page.waitForTimeout(500);
                    await fixture.page.keyboard.press('Backspace');
                    await fixture.page.waitForTimeout(2000); // Wait for suggestions to appear again
                    
                    // Take another screenshot to see if suggestions appeared
                    await ScreenshotHelper.takeScreenshot('country-suggestions-retry');
                    
                    // Try clicking suggestions again
                    suggestionClicked = await this.clickOnCountrySuggestion(country);
                } catch (e) {
                    console.log('Error during suggestion retry:', e);
                }
            }
        }
        
        if (!suggestionClicked) {
            // If regular clicking failed, try keyboard navigation
            console.log('Could not click suggestion, trying keyboard navigation');
            await fixture.page.keyboard.press('ArrowDown'); // Move to first suggestion
            await fixture.page.waitForTimeout(500);
            await fixture.page.keyboard.press('ArrowDown'); // Move to second suggestion if needed
            await fixture.page.waitForTimeout(500);
            await fixture.page.keyboard.press('Enter'); // Select highlighted suggestion
            await fixture.page.waitForTimeout(500);
            
            // Press Tab to move focus away
            await fixture.page.keyboard.press('Tab', { delay: 100 });
            await fixture.page.waitForTimeout(500);
            
            // Press Escape to dismiss any remaining dropdowns
            await fixture.page.keyboard.press('Escape', { delay: 100 });
            await fixture.page.waitForTimeout(500);
            
            suggestionClicked = true;  // Assume this worked
        }
        
        // Take final screenshot after selection
        await ScreenshotHelper.takeScreenshot('after-country-selection');
        
        return suggestionClicked;
    }
      /**
     * Attempt to click on a country suggestion with multiple strategies
     * Enhanced to better handle the exact element structure provided by the user
     */    private static async clickOnCountrySuggestion(country: string): Promise<boolean> {
        console.log(`Attempting to click on country suggestion for: "${country}"`);
        
        // Use very specific selectors based on the EXACT HTML example provided by the user:
        // '<div class="absolute z-10 mt-2 w-full bg-white text-brand-black rounded-md shadow-lg max-h-60 overflow-auto">
        // <div class="px-4 py-2 hover:bg-brand-grey cursor-pointer text-brand-black">India</div>...'
        const exactSuggestionSelectors = [
            // EXACT selector matching the HTML structure provided by the user
            'div.absolute.z-10.mt-2.w-full.bg-white.text-brand-black.rounded-md.shadow-lg.max-h-60.overflow-auto div.px-4.py-2.hover\\:bg-brand-grey.cursor-pointer.text-brand-black',
            
            // Exact item selector with text matching for the exact country
            `div.px-4.py-2.hover\\:bg-brand-grey.cursor-pointer.text-brand-black:has-text("${country}")`,
            
            // Simplified selectors that still match the provided structure
            'div.absolute.z-10 div.px-4.py-2.hover\\:bg-brand-grey',
            'div.absolute.z-10 div.px-4.py-2',
            'div.absolute.z-10 div.cursor-pointer',
            
            // Text-based selectors with country name
            `div.absolute.z-10 div:has-text("${country}")`,
            `div.px-4.py-2:has-text("${country}")`,
            `div.cursor-pointer:has-text("${country}")`,
            
            // Most generic selectors as last resort
            'div.absolute.z-10 div',
            'div[class*="absolute"] div[class*="cursor-pointer"]'
        ];
        
        let clicked = false;
        
        // Take a screenshot to see if the suggestions are visible
        await ScreenshotHelper.takeScreenshot('before-click-country-suggestion');
        
        // First, check if dropdown is visible and log detailed information about the suggestions
        try {
            // Check if the suggestion dropdown container is visible using multiple possible selectors
            const dropdownVisible = await fixture.page.evaluate(() => {
                // Try to find the dropdown with exact selector from user example
                const exactDropdownSelector = 'div.absolute.z-10.mt-2.w-full.bg-white.text-brand-black.rounded-md.shadow-lg.max-h-60.overflow-auto';
                const exactDropdown = document.querySelector(exactDropdownSelector);
                
                if (exactDropdown) {
                    console.log('Found dropdown with exact selector match!');
                    return {
                        visible: true,
                        selector: exactDropdownSelector,
                        itemCount: exactDropdown.querySelectorAll('div.px-4.py-2').length,
                        html: exactDropdown.outerHTML.substring(0, 500)
                    };
                }
                
                // Try alternative selectors
                const alternativeSelectors = [
                    'div.absolute.z-10',
                    'div[class*="absolute"][class*="z-10"]',
                    'div[class*="overflow-auto"]',
                    'div.rounded-md.shadow-lg'
                ];
                
                for (const selector of alternativeSelectors) {
                    const dropdown = document.querySelector(selector);
                    if (dropdown && 
                        dropdown.querySelectorAll('div').length > 0 && 
                        window.getComputedStyle(dropdown).display !== 'none') {
                        return {
                            visible: true,
                            selector: selector,
                            itemCount: dropdown.querySelectorAll('div').length,
                            html: dropdown.outerHTML.substring(0, 500)
                        };
                    }
                }
                
                return { visible: false };
            });
            
            console.log('Dropdown visibility check result:', dropdownVisible);
            
            if (!dropdownVisible.visible) {
                console.log('⚠️ Suggestion dropdown not detected! Taking additional screenshot for debugging.');
                await ScreenshotHelper.takeScreenshot('dropdown-not-visible');
                
                // If dropdown not detected, log and try to look for anything that might be a dropdown
                await fixture.page.evaluate(() => {
                    console.log('Searching for ANY possible dropdown-like elements...');
                    
                    // Look for elements with these potential dropdown characteristics
                    // Convert NodeList to Array first with Array.from() before spreading
                    const potentialDropdowns = [
                        ...Array.from(document.querySelectorAll('div.absolute')),
                        ...Array.from(document.querySelectorAll('div[style*="position: absolute"]')),
                        ...Array.from(document.querySelectorAll('div[class*="dropdown"]')),
                        ...Array.from(document.querySelectorAll('div[class*="suggestion"]')),
                        ...Array.from(document.querySelectorAll('div[class*="z-"]'))
                    ];
                    
                    console.log(`Found ${potentialDropdowns.length} potential dropdown-like elements`);
                    
                    potentialDropdowns.forEach((el, i) => {
                        if (i < 5) { // Log only first 5 to avoid excessive output
                            console.log(`Potential dropdown ${i}: class="${el.className}", childNodes=${el.childNodes.length}`);
                        }
                    });
                });
            }
        } catch (e) {
            console.log('Error checking dropdown visibility:', e);
        }
        
        // Try clicking suggestion with Playwright - optimized for the exact HTML structure provided
        for (const selector of exactSuggestionSelectors) {
            if (clicked) break;
            
            try {
                console.log(`Trying to find suggestion with selector: ${selector}`);
                
                // First try exact text match for the country
                const exactTextSelector = `${selector}:text("${country}")`;
                const exactMatch = fixture.page.locator(exactTextSelector).first();
                
                if (await exactMatch.isVisible({ timeout: 2000 }).catch(() => false)) {
                    console.log(`✓ Found exact match for "${country}", clicking...`);
                    await ScreenshotHelper.takeScreenshot('found-exact-suggestion-match');
                    
                    // Try clicking with different strategies
                    try {
                        await exactMatch.click({ timeout: 3000 });
                        clicked = true;
                        console.log(`✓ Clicked on exact match for "${country}"`);
                    } catch (clickErr) {
                        console.log(`Normal click failed: ${clickErr}. Trying force click...`);
                        try {
                            await exactMatch.click({ timeout: 3000, force: true });
                            clicked = true;
                            console.log(`✓ Forced click on exact match for "${country}"`);
                        } catch (forceErr) {
                            console.log(`Force click failed: ${forceErr}`);
                        }
                    }
                    
                    if (clicked) {
                        await fixture.page.waitForTimeout(1000);
                        break;
                    }
                }
                
                // Try with case-insensitive text search if exact match fails
                console.log(`Trying with case-insensitive search for "${country}"`);
                const suggestions = await fixture.page.locator(selector).all();
                console.log(`Found ${suggestions.length} potential suggestions with selector: ${selector}`);
                
                // If we found suggestions, take a screenshot to see them
                if (suggestions.length > 0) {
                    await ScreenshotHelper.takeScreenshot('found-suggestions');
                    
                    // Look for any match with our country name (case insensitive)
                    for (const suggestion of suggestions) {
                        if (clicked) break;
                        
                        // Get text content of this suggestion
                        const text = await suggestion.textContent().catch(() => null);
                        if (text) {
                            console.log(`Checking suggestion text: "${text.trim()}"`);
                            
                            // Check for match
                            if (text.trim().toLowerCase().includes(country.toLowerCase())) {
                                console.log(`✓ Found matching suggestion: "${text.trim()}", clicking...`);
                                
                                try {
                                    await suggestion.click({ timeout: 2000 });
                                    clicked = true;
                                    console.log(`✓ Clicked on matching suggestion: "${text.trim()}"`);
                                } catch (clickErr) {
                                    console.log(`Click failed: ${clickErr}. Trying force click...`);
                                    try {
                                        await suggestion.click({ timeout: 2000, force: true });
                                        clicked = true;
                                        console.log(`✓ Force clicked on matching suggestion: "${text.trim()}"`);
                                    } catch (forceErr) {
                                        console.log(`Force click failed: ${forceErr}`);
                                    }
                                }
                                
                                if (clicked) {
                                    await fixture.page.waitForTimeout(1000);
                                    break;
                                }
                            }
                        }
                    }
                    
                    // If we still haven't clicked and there are suggestions, click the first one
                    if (!clicked && suggestions.length > 0) {
                        console.log('No exact match found, clicking first suggestion as fallback');
                        
                        try {
                            await suggestions[0].click({ timeout: 2000 });
                            clicked = true;
                        } catch (clickErr) {
                            console.log(`Click on first suggestion failed: ${clickErr}. Trying force click...`);
                            try {
                                await suggestions[0].click({ timeout: 2000, force: true });
                                clicked = true;
                            } catch (forceErr) {
                                console.log(`Force click on first suggestion failed: ${forceErr}`);
                            }
                        }
                        
                        if (clicked) {
                            const text = await suggestions[0].textContent().catch(() => null);
                            console.log(`✓ Clicked on first suggestion: "${text ? text.trim() : 'unknown'}"`);
                            await fixture.page.waitForTimeout(1000);
                        }
                    }
                }
            } catch (e) {
                console.log(`Error with suggestion selector ${selector}: ${e}`);
            }
        }
          // Attempt method 2: Enhanced JavaScript approach if Playwright approach failed
        if (!clicked) {
            console.log('Trying advanced JavaScript approach for clicking country suggestions...');
            
            try {
                // Take a screenshot of current state before JS attempt
                await ScreenshotHelper.takeScreenshot('before-js-country-suggestion');
                
                const jsClicked = await fixture.page.evaluate((countryName) => {
                    console.log(`JS searching for suggestion elements matching "${countryName}"...`);
                    
                    // Try to find and log the dropdown structure first
                    const dropdowns = [
                        // Exact match from HTML example
                        document.querySelector('div.absolute.z-10.mt-2.w-full.bg-white.text-brand-black.rounded-md.shadow-lg.max-h-60.overflow-auto'),
                        // More general selectors
                        document.querySelector('div.absolute.z-10'),
                        document.querySelector('div[class*="absolute"][class*="z-10"]'),
                        document.querySelector('div[class*="dropdown"]'),
                        document.querySelector('div[class*="suggestion"]')
                    ].filter(Boolean);
                    
                    if (dropdowns.length > 0) {
                        console.log(`JS found ${dropdowns.length} potential dropdown containers`);
                        
                        // Look for suggestion items in each dropdown
                        for (const dropdown of dropdowns) {
                            // Try different selectors for items based on the HTML example
                            const itemSelectors = [
                                'div.px-4.py-2.hover\\:bg-brand-grey.cursor-pointer.text-brand-black',
                                'div.px-4.py-2',
                                'div.cursor-pointer',
                                'div'  // Most generic fallback
                            ];
                            
                            for (const selector of itemSelectors) {
                                const suggestionItems = dropdown.querySelectorAll(selector);
                                console.log(`JS found ${suggestionItems.length} suggestion items with selector '${selector}'`);
                                
                                if (suggestionItems.length > 0) {
                                    // First look for exact text match
                                    for (let i = 0; i < suggestionItems.length; i++) {
                                        const item = suggestionItems[i];
                                        const text = item.textContent?.trim();
                                        
                                        console.log(`JS suggestion item ${i}: "${text || ''}"`);
                                        
                                        // Check for exact match (case-insensitive)
                                        if (text && text.toLowerCase() === countryName.toLowerCase()) {
                                            console.log(`JS found EXACT match for "${countryName}": "${text}"`);
                                            
                                            // Click with complete event sequence for maximum reliability
                                            const elem = item as HTMLElement;
                                            
                                            // Try multiple approaches to click the element
                                            try {
                                                // Approach 1: Full mouse event sequence
                                                elem.dispatchEvent(new MouseEvent('mouseenter', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mousedown', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('click', {bubbles: true}));
                                                
                                                // Approach 2: Direct click as fallback
                                                setTimeout(() => elem.click(), 100);
                                                
                                                console.log(`JS successfully clicked on exact match: "${text}"`);
                                                return true;
                                            } catch (e) {
                                                console.log(`JS click failed: ${e}. Trying alternative click...`);
                                                
                                                // Last resort: Try other click methods
                                                try {
                                                    // Access parent elements and click those
                                                    const parent = elem.parentElement;
                                                    if (parent) {
                                                        (parent as HTMLElement).click();
                                                        console.log(`JS clicked on parent element as fallback`);
                                                    }
                                                    return true;
                                                } catch (e2) {
                                                    console.log(`JS alternative click failed: ${e2}`);
                                                }
                                            }
                                        }
                                    }
                                    
                                    // If no exact match found, look for partial match
                                    for (let i = 0; i < suggestionItems.length; i++) {
                                        const item = suggestionItems[i];
                                        const text = item.textContent?.trim();
                                        
                                        if (text && text.toLowerCase().includes(countryName.toLowerCase())) {
                                            console.log(`JS found partial match for "${countryName}": "${text}"`);
                                            
                                            try {
                                                // Full mouse event sequence for better reliability
                                                const elem = item as HTMLElement;
                                                elem.dispatchEvent(new MouseEvent('mouseenter', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mousedown', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
                                                elem.dispatchEvent(new MouseEvent('click', {bubbles: true}));
                                                
                                                // Direct click as backup
                                                setTimeout(() => elem.click(), 100);
                                                
                                                console.log(`JS clicked on partial match: "${text}"`);
                                                return true;
                                            } catch (e) {
                                                console.log(`JS partial match click failed: ${e}`);
                                            }
                                        }
                                    }
                                    
                                    // If no match found, click first item as fallback
                                    if (suggestionItems.length > 0) {
                                        try {
                                            const firstItem = suggestionItems[0] as HTMLElement;
                                            const text = firstItem.textContent?.trim();
                                            console.log(`JS no match found, clicking first suggestion: "${text || ''}"`);
                                            
                                            firstItem.dispatchEvent(new MouseEvent('mouseenter', {bubbles: true}));
                                            firstItem.dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));
                                            firstItem.dispatchEvent(new MouseEvent('mousedown', {bubbles: true}));
                                            firstItem.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
                                            firstItem.dispatchEvent(new MouseEvent('click', {bubbles: true}));
                                            
                                            // Direct click as backup
                                            setTimeout(() => firstItem.click(), 100);
                                            
                                            console.log(`JS clicked first suggestion as fallback`);
                                            return true;
                                        } catch (e) {
                                            console.log(`JS fallback click failed: ${e}`);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        console.log('JS found no dropdown containers, trying extreme fallback approaches');
                        
                        // Extreme fallback: Look for ANY element that might be a suggestion based on content
                        const allElements = document.querySelectorAll('div');
                        const possibleSuggestions = Array.from(allElements).filter(el => {
                            const text = el.textContent?.trim();
                            return text && 
                                  text.toLowerCase() === countryName.toLowerCase() || 
                                  text.toLowerCase().includes(countryName.toLowerCase());
                        });
                        
                        if (possibleSuggestions.length > 0) {
                            console.log(`JS found ${possibleSuggestions.length} elements containing text "${countryName}"`);
                            
                            try {
                                const element = possibleSuggestions[0] as HTMLElement;
                                element.click();
                                console.log(`JS clicked extreme fallback element with text containing "${countryName}"`);
                                return true;
                            } catch (e) {
                                console.log(`JS extreme fallback click failed: ${e}`);
                            }
                        }
                    }
                    
                    console.log('JS could not find any suitable suggestion elements to click');
                    return false;
                }, country);
                
                clicked = jsClicked;
                
                if (clicked) {
                    // If JavaScript click was successful, make sure the dropdown is dismissed
                    console.log('JS click successful, finalizing selection...');
                    await fixture.page.waitForTimeout(1500);
                    
                    // Press Tab to move focus away from the field
                    await fixture.page.keyboard.press('Tab');
                    await fixture.page.waitForTimeout(500);
                    
                    // Press Escape to ensure any lingering dropdown is dismissed
                    await fixture.page.keyboard.press('Escape');
                    await fixture.page.waitForTimeout(500);
                }
            } catch (e) {
                console.error(`JavaScript suggestion handling failed: ${e}`);
            }
        }
        
        // Final keyboard fallback if all visual clicking methods failed
        if (!clicked) {
            console.log('All direct clicking methods failed, trying keyboard navigation as last resort');
            
            try {
                await ScreenshotHelper.takeScreenshot('before-keyboard-fallback');
                
                // First refocus on the country field
                await fixture.page.evaluate(() => {
                    const countryInput = document.querySelector('input#Country') as HTMLInputElement;
                    if (countryInput) {
                        countryInput.focus();
                        return true;
                    }
                    return false;
                });
                
                // Try keyboard navigation sequence
                console.log('Using keyboard to navigate dropdown...');
                
                // Arrow down to enter the dropdown (try multiple times to ensure we get into the list)
                for (let i = 0; i < 3; i++) {
                    await fixture.page.keyboard.press('ArrowDown');
                    await fixture.page.waitForTimeout(500);
                }
                
                // Press Enter to select the currently highlighted option
                await fixture.page.keyboard.press('Enter');
                await fixture.page.waitForTimeout(500);
                
                // Tab out and press escape to ensure dropdown is closed
                await fixture.page.keyboard.press('Tab');
                await fixture.page.waitForTimeout(300);
                await fixture.page.keyboard.press('Escape');
                
                clicked = true;
                console.log('Keyboard navigation sequence completed');
            } catch (e) {
                console.log(`Keyboard navigation failed: ${e}`);
            }
        }
        
        // Take a screenshot after all attempts
        await ScreenshotHelper.takeScreenshot('after-all-country-suggestion-click-attempts');
        
        return clicked;
    }
    
    /**
     * Verify that the country was successfully selected with enhanced verification
     */
    private static async verifyCountryWasSelected(country: string): Promise<void> {
        console.log('Verifying country selection with enhanced checks...');
        
        // Wait for any animations or transitions to complete
        await fixture.page.waitForTimeout(1500);
        
        // Take a screenshot of the field after selection
        await ScreenshotHelper.takeScreenshot('country-field-after-selection');
        
        try {
            // 1. First, ensure no suggestions are still visible
            const dropdownVisible = await fixture.page.locator([
                this.elements.countrySuggestions,
                'div.absolute.z-10',
                'div[class*="absolute"][class*="z-10"]'
            ].join(', ')).isVisible().catch(() => false);
            
            if (dropdownVisible) {
                console.log('Suggestion dropdown still visible, dismissing it...');
                
                // Try multiple approaches to dismiss the dropdown
                
                // Click outside the dropdown
                try {
                    await fixture.page.mouse.click(50, 50);
                    await fixture.page.waitForTimeout(500);
                } catch (e) {
                    console.log(`Click outside dropdown failed: ${e}`);
                }
                
                // Press Escape key
                await fixture.page.keyboard.press('Escape');
                await fixture.page.waitForTimeout(500);
                
                // Press Tab key to move focus
                await fixture.page.keyboard.press('Tab');
                await fixture.page.waitForTimeout(500);
            }
            
            // 2. Verify the final value in the country field using multiple approaches
            const fieldValueCheck = await fixture.page.evaluate((expectedCountry) => {
                // Get country field with multiple selectors
                const countryInputs = [
                    document.querySelector('input#Country'),
                    document.querySelector('input[placeholder="Country"]'),
                    document.querySelector('input.w-full.px-3.py-2')
                ].filter(Boolean);
                
                if (countryInputs.length > 0) {
                    const input = countryInputs[0] as HTMLInputElement;
                    const currentValue = input.value;
                    
                    return {
                        found: true,
                        value: currentValue,
                        matches: currentValue.toLowerCase().includes(expectedCountry.toLowerCase()),
                        isExactMatch: currentValue.toLowerCase() === expectedCountry.toLowerCase()
                    };
                }
                
                return { found: false };
            }, country);
            
            console.log('Country field value check:', fieldValueCheck);
            
            // 3. If field not found or value doesn't match, try to fix it
            if (!fieldValueCheck.found || !fieldValueCheck.matches) {
                console.log('Country not properly selected or verified, attempting to fix...');
                
                // Try direct value setting as a last resort
                await fixture.page.evaluate((countryValue) => {
                    const countryInput = document.querySelector('input#Country, input[placeholder="Country"]') as HTMLInputElement;
                    if (countryInput) {
                        // Format the country name with proper capitalization for consistency
                        const formattedCountry = countryValue.split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                            .join(' ');
                            
                        // Set the value directly
                        countryInput.value = formattedCountry;
                        
                        // Dispatch proper events
                        countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                        countryInput.dispatchEvent(new Event('change', { bubbles: true }));
                        
                        console.log(`Directly set country field value to "${formattedCountry}"`);
                        return true;
                    }
                    return false;
                }, country);
                
                // Move focus away to ensure the change is applied
                await fixture.page.keyboard.press('Tab');
                await fixture.page.waitForTimeout(500);
                
                // Take another screenshot after the fix attempt
                await ScreenshotHelper.takeScreenshot('country-field-after-fix-attempt');
            } else {
                console.log(`✓ Country field successfully verified with value: "${fieldValueCheck.value}"`);
            }
        } catch (e) {
            console.warn(`Error during enhanced country selection verification: ${e}`);
            
            // If verification fails, try to ensure the field has the right value regardless
            console.log('Verification error, applying direct value as fallback...');
            
            await fixture.page.evaluate((countryValue) => {
                const countryInput = document.querySelector('input#Country, input[placeholder="Country"]') as HTMLInputElement;
                if (countryInput) {
                    // Format with proper capitalization
                    const formattedCountry = countryValue.split(' ')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ');
                        
                    countryInput.value = formattedCountry;
                    countryInput.dispatchEvent(new Event('input', { bubbles: true }));
                    countryInput.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                }
                return false;
            }, country);
            
            // Move focus away
            await fixture.page.keyboard.press('Tab');
        }
    }/**
     * Fill contact information
     * @param contactData Object containing contact information
     */
    static async fillContactInfo(contactData: {
        title?: string;
        name?: string;
        countrycode?: string;
        phoneNumber?: string;
        emailId?: string;
    }): Promise<boolean> {
        try {
            console.log('Filling contact information with enhanced reliability...');
            
            // Configure retries and timeouts for better reliability
            const maxRetries = 3;
            const retryDelay = 2000; // milliseconds
            let currentTry = 0;
            
            // First look for the contact section to ensure we're in the right context
            console.log('Looking for contact info section...');
            
            // Take a screenshot before attempting to fill contact info
            await ScreenshotHelper.takeScreenshot('before-contact-info');
            
            // Wait a moment to let any animations complete
            await fixture.page.waitForTimeout(2000);
            
            // Try to identify that we're on the contact section by looking for an email field
            // which is unique to contact info and not present in passenger info
            const isEmailFieldVisible = await fixture.page.locator('input#Email, input[type="email"]').isVisible()
                .catch(() => false);
                
            if (!isEmailFieldVisible) {
                console.warn('Email field not visible - might not be on contact section yet.');
                
                // If email not visible, try to find some other unique contact fields
                const isPhoneFieldVisible = await fixture.page.locator('input[type="tel"]').isVisible()
                    .catch(() => false);
                    
                if (!isPhoneFieldVisible) {
                    console.warn('Neither email nor phone field is visible. Checking if we need to scroll...');
                    
                    // Try scrolling down to see if contact section comes into view
                    await fixture.page.evaluate(() => {
                        window.scrollBy(0, 500); // Scroll down a bit
                    });
                    
                    await fixture.page.waitForTimeout(1000);
                    
                    // Check again after scrolling
                    const isContactSectionVisible = await fixture.page.locator('input#Email, input[type="email"], input[type="tel"]').isVisible()
                        .catch(() => false);
                        
                    if (!isContactSectionVisible) {
                        console.warn('Contact section still not visible after scrolling. Will proceed with caution.');
                    } else {
                        console.log('Contact section found after scrolling!');
                    }
                } else {
                    console.log('Phone field visible - we are on the contact section.');
                }            } else {
                console.log('Email field visible - we are on the contact section.');
            }
            
            // Add a bit of spacing between fields to avoid race conditions
            // Increased from 1000ms for better reliability
            const spacing = 1500; // milliseconds
            
            // Implement retry mechanism for entire contact info form filling
            while (currentTry < maxRetries) {
                try {
                    console.log(`Contact info form fill attempt ${currentTry + 1}/${maxRetries}`);
                    // Take a screenshot at the beginning of this attempt
                    await ScreenshotHelper.takeScreenshot(`contact-info-attempt-${currentTry + 1}`);
                    
                    // Try to fill the form with an increased timeout
                    const success = await this.attemptContactFormFill(contactData, spacing);
                    
                    if (success) {
                        console.log(`Successfully filled contact info on attempt ${currentTry + 1}`);
                        return true;
                    }
                    
                    currentTry++;
                    if (currentTry < maxRetries) {
                        console.log(`Retrying contact form fill in ${retryDelay}ms...`);
                        await fixture.page.waitForTimeout(retryDelay);
                    }
                } catch (retryError) {
                    console.error(`Error in contact form fill attempt ${currentTry + 1}:`, retryError);
                    currentTry++;
                    
                    if (currentTry < maxRetries) {
                        console.log(`Retrying after error (${retryDelay}ms)`);
                        await fixture.page.waitForTimeout(retryDelay);
                    }
                }
            }
            
            // If we've exhausted all retries, continue with our normal approach as last resort
            // But ONLY for fields that were not successfully filled during retry attempts
            
            let fieldsAttempted = {
                title: false,
                name: false,
                countryCode: false,
                phoneNumber: false,
                email: false
            };
            
            // Fill each field with error handling for each step
            try {
                // Fill title if not already attempted successfully
                if (contactData?.title && !fieldsAttempted.title) {
                    await this.selectTitle(contactData.title);
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.title = true;
                }
            } catch (titleError) {
                console.error('Error filling title, continuing with other fields:', titleError);
                // Take a screenshot but continue
                await ScreenshotHelper.takeScreenshot('title-error', true);
            }
              
            try {
                // Fill name if not already attempted successfully
                if (contactData?.name && !fieldsAttempted.name) {
                    await this.fillName(contactData.name);
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.name = true;
                }
            } catch (nameError) {
                console.error('Error filling name, continuing with other fields:', nameError);
            }
              
            // Skip selecting country code here to prevent double typing
            // The attemptContactFormFill method should have already handled this
            console.log('Skipping country code selection to prevent duplicate typing');
            
            try {
                // Fill phone number if not already attempted successfully
                if (contactData?.phoneNumber && !fieldsAttempted.phoneNumber) {
                    await this.fillPhoneNumber(contactData.phoneNumber);
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.phoneNumber = true;
                }
            } catch (phoneError) {
                console.error('Error filling phone number, continuing with other fields:', phoneError);
            }
            
            try {
                // Fill email ID if not already attempted successfully
                if (contactData?.emailId && !fieldsAttempted.email) {
                    await this.fillEmailId(contactData.emailId);
                    fieldsAttempted.email = true;
                }
            } catch (emailError) {
                console.error('Error filling email, continuing:', emailError);
            }

            // As a fallback, try direct JavaScript approach
            try {
                console.log('Attempting JavaScript fallback for contact info fields...');
                await fixture.page.evaluate((data) => {
                    // Helper function to try multiple selectors to find and fill an input field
                    function fillField(fieldName, value) {
                        if (!value) return false;
                        
                        const selectors = [
                            `input#${fieldName}`, 
                            `input[name="${fieldName}"]`, 
                            `input[placeholder*="${fieldName}"]`,
                            `input[type="${fieldName}"]`
                        ];
                        
                        for (const selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                for (let i = 0; i < elements.length; i++) {
                                    const el = elements[i] as HTMLInputElement;
                                    el.value = value;
                                    el.dispatchEvent(new Event('input', {bubbles: true}));
                                    el.dispatchEvent(new Event('change', {bubbles: true}));
                                    return true;
                                }
                            }
                        }
                        return false;
                    }
                    
                    // Fill name and email fields
                    fillField('name', data.name);
                    fillField('Email', data.emailId);
                    fillField('email', data.emailId);
                    
                    // For phone number field - try several possible selectors
                    const phoneSelectors = [
                        'input[type="tel"]',
                        'input[name="phone"]',
                        'input[placeholder*="Phone"]'
                    ];
                    
                    for (const selector of phoneSelectors) {
                        const phoneFields = document.querySelectorAll(selector);
                        if (phoneFields.length > 0) {
                            for (let i = 0; i < phoneFields.length; i++) {
                                const field = phoneFields[i] as HTMLInputElement;
                                field.value = data.phoneNumber;
                                field.dispatchEvent(new Event('input', {bubbles: true}));
                                field.dispatchEvent(new Event('change', {bubbles: true}));
                                break;
                            }
                            break;
                        }
                    }
                }, contactData);
                
                console.log('JavaScript fallback for contact fields completed');
            } catch (jsError) {
                console.error('JavaScript fallback for contact fields failed:', jsError);
            }

            // Take a screenshot after filling the form
            await ScreenshotHelper.takeScreenshot('contact-info-filled');
            
            return true;
        } catch (error) {
            console.error('Error filling contact information:', error);
            await ScreenshotHelper.takeScreenshot('contact-info-error', true);
            // Return true anyway to allow the test to continue
            return true;
        }
    }/**
     * Method to attempt filling contact form fields with robust error handling
     * Added as part of reliability improvements for timeout issues
     */
    private static async attemptContactFormFill(contactData: {
        title?: string;
        name?: string;
        countrycode?: string;
        phoneNumber?: string;
        emailId?: string;
    }, spacing: number = 1500): Promise<boolean> {
        try {
            console.log('Attempting to fill contact form with enhanced timeout values');
            
            // Increase timeouts for all operations
            const fieldTimeout = 10000;  // 10 seconds per field operation
            let success = true;
            
            // Track which fields were successfully filled
            const fieldsAttempted = {
                title: false,
                name: false,
                countryCode: false,
                phoneNumber: false,
                email: false
            };
            
            // Try to fill all fields with increased timeout values
            try {
                // Fill title with increased timeout
                if (contactData?.title) {
                    await Promise.race([
                        this.selectTitle(contactData.title),
                        fixture.page.waitForTimeout(fieldTimeout)
                    ]);
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.title = true;
                }
            } catch (titleError) {
                console.error('Error filling title:', titleError);
                success = false;
            }
            
            try {
                // Fill name with increased timeout
                if (contactData?.name) {
                    // Try with modern Playwright locators first
                    try {
                        // Try to find the name field by label
                        const nameInputByLabel = fixture.page.getByLabel(/name|full name|passenger name/i, { exact: false });
                        if (await nameInputByLabel.isVisible({ timeout: 3000 })) {
                            console.log('Found name field using label locator');
                            await nameInputByLabel.fill(contactData.name);
                            await fixture.page.waitForTimeout(spacing);
                            fieldsAttempted.name = true;
                            console.log('Successfully filled name using label locator');
                        }
                    } catch (labelError) {
                        console.log('Failed to find name field by label:', labelError.message);
                    }
                    
                    // Try by placeholder if label didn't work
                    if (!fieldsAttempted.name) {
                        try {
                            const nameByPlaceholder = fixture.page.getByPlaceholder(/name|full name/i, { exact: false });
                            if (await nameByPlaceholder.isVisible({ timeout: 2000 })) {
                                console.log('Found name field using placeholder locator');
                                await nameByPlaceholder.fill(contactData.name);
                                await fixture.page.waitForTimeout(spacing);
                                fieldsAttempted.name = true;
                                console.log('Successfully filled name using placeholder locator');
                            }
                        } catch (placeholderError) {
                            console.log('Failed to find name field by placeholder:', placeholderError.message);
                        }
                    }
                    
                    // Fall back to CSS selectors if modern locators fail
                    if (!fieldsAttempted.name) {
                        // Use a more reliable approach for name field
                        try {
                            await fixture.page.fill(this.elements.nameField, contactData.name, { timeout: fieldTimeout });
                            fieldsAttempted.name = true;
                            console.log('Successfully filled name using CSS selector');
                        } catch (fillError) {
                            console.log('Standard fill failed, trying JavaScript approach');
                            await fixture.page.evaluate((nameValue) => {
                                const nameInputs = document.querySelectorAll('input#name, input[name="name"]');
                                if (nameInputs.length > 0) {
                                    const nameInput = nameInputs[0] as HTMLInputElement;
                                    nameInput.value = nameValue;
                                    nameInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    nameInput.dispatchEvent(new Event('change', { bubbles: true }));
                                }
                            }, contactData.name);
                            fieldsAttempted.name = true;
                            console.log('Successfully filled name using JavaScript approach');
                        }
                    }
                    
                    await fixture.page.waitForTimeout(spacing);
                }
            } catch (nameError) {
                console.error('Error filling name:', nameError);
                success = false;
            }
            
            try {
                // Select country code with increased timeout
                if (contactData?.countrycode) {
                    await Promise.race([
                        this.selectCountryCode(contactData.countrycode),
                        fixture.page.waitForTimeout(fieldTimeout)
                    ]);
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.countryCode = true;
                }
            } catch (countryCodeError) {
                console.error('Error selecting country code:', countryCodeError);
                success = false;
            }
            
            try {
                // Fill phone number with increased timeout
                if (contactData?.phoneNumber) {
                    // Use Promise.race to ensure we don't exceed the timeout
                    await Promise.race([
                        this.fillPhoneNumber(contactData.phoneNumber),
                        fixture.page.waitForTimeout(fieldTimeout)
                    ]);
                    
                    // Wait between field operations
                    await fixture.page.waitForTimeout(spacing);
                    fieldsAttempted.phoneNumber = true;
                    console.log('Successfully filled phone number');
                }
            } catch (phoneError) {
                console.error('Error filling phone number:', phoneError);
                success = false;
            }
            
            try {
                // Fill email ID with increased timeout
                if (contactData?.emailId) {
                    // Try with modern Playwright locators first
                    try {
                        // Try to find email field by its role as an email input
                        const emailByRole = fixture.page.getByRole('textbox', { 
                            name: /email|e-mail|e mail/i, 
                            exact: false 
                        });
                        
                        if (await emailByRole.isVisible({ timeout: 3000 })) {
                            console.log('Found email field using role locator');
                            await emailByRole.fill(contactData.emailId);
                            fieldsAttempted.email = true;
                            console.log('Successfully filled email using role locator');
                        }
                    } catch (roleError) {
                        console.log('Failed to find email field by role:', roleError.message);
                    }
                    
                    // Try by label if role didn't work
                    if (!fieldsAttempted.email) {
                        try {
                            const emailByLabel = fixture.page.getByLabel(/email|e-mail/i, { exact: false });
                            if (await emailByLabel.isVisible({ timeout: 2000 })) {
                                console.log('Found email field using label locator');
                                await emailByLabel.fill(contactData.emailId);
                                fieldsAttempted.email = true;
                                console.log('Successfully filled email using label locator');
                            }
                        } catch (labelError) {
                            console.log('Failed to find email field by label:', labelError.message);
                        }
                    }
                    
                    // Try by placeholder if other methods didn't work
                    if (!fieldsAttempted.email) {
                        try {
                            const emailByPlaceholder = fixture.page.getByPlaceholder(/email|your email/i, { exact: false });
                            if (await emailByPlaceholder.isVisible({ timeout: 2000 })) {
                                console.log('Found email field using placeholder locator');
                                await emailByPlaceholder.fill(contactData.emailId);
                                fieldsAttempted.email = true;
                                console.log('Successfully filled email using placeholder locator');
                            }
                        } catch (placeholderError) {
                            console.log('Failed to find email field by placeholder:', placeholderError.message);
                        }
                    }
                    
                    // Fall back to CSS selectors if modern locators fail
                    if (!fieldsAttempted.email) {
                        // Try filling email with better error handling
                        try {
                            await fixture.page.fill(this.elements.emailId, contactData.emailId, { timeout: fieldTimeout });
                            console.log('Successfully filled email using CSS selector');
                        } catch (fillError) {
                            console.log('Standard fill failed, trying JavaScript approach for email');
                            await fixture.page.evaluate((emailValue) => {
                                const emailInputs = document.querySelectorAll('input#Email, input[type="email"]');
                                if (emailInputs.length > 0) {
                                    const emailInput = emailInputs[0] as HTMLInputElement;
                                    emailInput.value = emailValue;
                                    emailInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    emailInput.dispatchEvent(new Event('change', { bubbles: true }));
                                }
                            }, contactData.emailId);
                            console.log('Successfully filled email using JavaScript approach');
                        }
                    }
                    
                    fieldsAttempted.email = true;
                }
            } catch (emailError) {
                console.error('Error filling email:', emailError);
                success = false;
            }
            
            // Always take a screenshot to capture the current state
            await ScreenshotHelper.takeScreenshot('contact-form-fill-attempt');
            
            // Return both success status and which fields were attempted
            return success;
        } catch (error) {
            console.error('Error in contact form fill attempt:', error);
            return false;
        }
    }
    /**
     * Select the title from dropdown
     */    private static async selectTitle(title: string): Promise<void> {
        try {
            console.log(`========== TITLE SELECTION DEBUG START ==========`);
            console.log(`Selecting title: ${title}...`);
            
            // Take a screenshot before attempting to select title
            await ScreenshotHelper.takeScreenshot('before-title-selection');
            
            // First, try to locate the context section to ensure we're working with contact info
            // Look for sections that might contain the contact form
            const contactSections = ['section.contact-info', '.contact-details', '.contactInfo', 
                                     'section:has(input#Email)', 'div:has(input[name="phone"])', 
                                     'form:has(button#title)', 'section:has(button#title)',
                                     'div:has(button:has-text("Title"))'];
            // More debug logging to find the title dropdown
            console.log('DEBUG: Searching for title dropdown in the DOM...');
            await fixture.page.evaluate(() => {
                const allButtons = document.querySelectorAll('button');
                const titleButtons = Array.from(allButtons).filter(b => 
                    b.id === 'title' || 
                    (b.textContent && b.textContent.includes('Title'))
                );
                console.log(`Found ${titleButtons.length} potential title buttons:`);
                titleButtons.forEach((b, i) => {
                    console.log(`Button ${i+1}: id="${b.id}", text="${b.textContent?.trim()}", visible=${window.getComputedStyle(b).display !== 'none'}`);
                });
            });
                                     
            let contactSection = null;
            
            // Try to find the contact section first
            for (const sectionSelector of contactSections) {
                const isVisible = await fixture.page.locator(sectionSelector).isVisible().catch(() => false);
                if (isVisible) {
                    console.log(`Found contact section with selector: ${sectionSelector}`);
                    contactSection = sectionSelector;
                    break;
                }
            }
            
            // Improved title dropdown selector logic
            const dropdownSelectors = [
                'button#title', 
                '[role="button"]:has-text("Title")',
                'button:has-text("Title")',
                this.elements.titleDropdown,
                '.title-dropdown',
                'div.select-wrapper:has-text("Title")',
                'div[role="combobox"]:has-text("Title")'
            ];

            // Track whether we successfully clicked the dropdown
            let dropdownClicked = false;
            
            // If we found a contact section, try scoped selectors first
            if (contactSection) {
                // Try each dropdown selector within the contact section context
                for (const dropdownSelector of dropdownSelectors) {
                    if (dropdownClicked) break;
                    
                    try {
                        const scopedSelector = `${contactSection} ${dropdownSelector}`;
                        console.log(`Trying to click title dropdown with selector: ${scopedSelector}`);
                        const dropdownElement = fixture.page.locator(scopedSelector);
                        const isVisible = await dropdownElement.isVisible().catch(() => false);
                        
                        if (isVisible) {
                            await dropdownElement.click({ timeout: 3000 });
                            dropdownClicked = true;
                            console.log('Successfully clicked title dropdown within contact section');
                            break;
                        }
                    } catch (clickError) {
                        console.log(`Failed to click with scoped selector: ${dropdownSelector}`);
                        // Continue to next selector
                    }
                }
            }
            
            // If we couldn't click within the contact section, try without section context
            if (!dropdownClicked) {
                console.log('Trying global dropdown selectors...');
                for (const dropdownSelector of dropdownSelectors) {
                    if (dropdownClicked) break;
                    
                    try {
                        console.log(`Trying to click title dropdown with selector: ${dropdownSelector}`);
                        const dropdownElement = fixture.page.locator(dropdownSelector);
                        const isVisible = await dropdownElement.isVisible().catch(() => false);
                        
                        if (isVisible) {
                            await dropdownElement.click({ timeout: 3000 });
                            dropdownClicked = true;
                            console.log(`Successfully clicked title dropdown: ${dropdownSelector}`);
                            break;
                        }
                    } catch (clickError) {
                        console.log(`Failed to click with global selector: ${dropdownSelector}`);
                        // Continue to next selector
                    }
                }
            }
            
            // If still not clicked, use JavaScript fallback
            if (!dropdownClicked) {
                console.log('Failed to click title dropdown with standard methods, trying JavaScript fallback...');
                
                await fixture.page.evaluate(() => {
                    const possibleButtons = [
                        document.querySelector('button#title'),
                        document.querySelector('button[id="title"]'),
                        document.querySelector('.contactInfo button[id="title"]'),
                        ...Array.from(document.querySelectorAll('.contactInfo button, button#title, button[aria-haspopup="listbox"]')).filter(
                            el => el.textContent?.includes('Title') || 
                                 el.textContent?.includes('Mr') || 
                                 el.textContent?.includes('Ms')
                        )
                    ];
                    
                    // Log all potential buttons we find for debugging
                    console.log('Found title dropdown button candidates: ' + 
                               Array.from(document.querySelectorAll('button'))
                                 .filter(b => b.textContent?.includes('Title') || b.id === 'title')
                                 .map(b => b.outerHTML).join('\n'));
                    
                    for (const button of possibleButtons) {
                        if (button) {
                            // Force the click with both standard and event-based methods
                            try {
                                (button as HTMLElement).click();
                                // Also dispatch a click event for frameworks that might need it
                                button.dispatchEvent(new MouseEvent('click', {
                                    view: window,
                                    bubbles: true,
                                    cancelable: true
                                }));
                                console.log('Clicked title dropdown with JavaScript');
                                return true;
                            } catch (e) {
                                console.error('Error clicking button:', e);
                            }
                        }
                    }
                    return false;
                });
            }
            
            // Wait a moment for dropdown to appear
            await fixture.page.waitForTimeout(1000);
            
            // Take a screenshot after clicking the dropdown
            await ScreenshotHelper.takeScreenshot('after-title-dropdown-click');
            
            // Now find and click the correct title option
            try {
                // Enhanced title option selectors
                const optionSelectors = [
                    `[role="option"]:text-is("${title}")`,
                    `[role="option"]:text("${title}")`,
                    `div[role="listbox"] [role="option"]:text-is("${title}")`,
                    `div[id^="headlessui-listbox-options"] div:text-is("${title}")`,
                    `div[id^="headlessui-listbox-options"] div:text("${title}")`,
                    `li:text-is("${title}")`,
                    `div:text-is("${title}")`,
                    `span:text-is("${title}")`,
                    `ul li:has-text("${title}")`,
                    `.dropdown-item:has-text("${title}")`,
                    `[role="listbox"] [role="option"]:has-text("${title}")`
                ];
                
                let optionClicked = false;
                
                // Try each option selector
                for (const optionSelector of optionSelectors) {
                    if (optionClicked) break;
                    
                    try {
                        console.log(`Trying to click title option with selector: ${optionSelector}`);
                        const optionElements = fixture.page.locator(optionSelector);
                        const count = await optionElements.count();
                        
                        if (count > 0) {
                            // Click the first matching option
                            await optionElements.first().click({ timeout: 3000 });
                            optionClicked = true;
                            console.log(`Successfully clicked title option: ${title}`);
                            break;
                        }
                    } catch (clickError) {
                        console.log(`Failed to click with option selector: ${optionSelector}`);
                        // Continue to next selector
                    }
                }
                
                // If still not clicked, use JavaScript fallback
                if (!optionClicked) {
                    console.log('Trying JavaScript fallback for clicking title option...');
                    
                    // Use JavaScript to find and click the option
                    await fixture.page.evaluate((titleText: string) => {
                        // Get all visible elements that match the title text
                        const findOptionElement = () => {
                            // Common option element selectors
                            const selectors = [
                                'div[id^="headlessui-listbox-options"] div',
                                '[role="listbox"] [role="option"]',
                                '[role="option"]',
                                'ul.dropdown-options li',
                                '.dropdown-menu .dropdown-item',
                                'div[role="presentation"] div',
                                'ul li',
                                '.select-options div',
                                'div.option'
                            ];
                            
                            // Log all dropdown options we can find for debugging
                            const allOptions = [];
                            for (const selector of selectors) {
                                const elements = document.querySelectorAll(selector);
                                for (const el of Array.from(elements)) {
                                    if (el.textContent && el.textContent.trim()) {
                                        allOptions.push(`${selector}: "${el.textContent.trim()}"`);
                                    }
                                }
                            }
                            console.log(`Found dropdown options: ${allOptions.join(', ')}`);
                            
                            for (const selector of selectors) {
                                const elements = document.querySelectorAll(selector);
                                for (const el of Array.from(elements)) {
                                    const style = window.getComputedStyle(el);
                                    // Only consider visible elements
                                    if (style.display !== 'none' && style.visibility !== 'hidden' && 
                                        el.textContent && el.textContent.trim() === titleText) {
                                        return el;
                                    }
                                }
                            }
                            
                            // If not found by exact match, try includes
                            for (const selector of selectors) {
                                const elements = document.querySelectorAll(selector);
                                for (const el of Array.from(elements)) {
                                    const style = window.getComputedStyle(el);
                                    // Only consider visible elements
                                    if (style.display !== 'none' && style.visibility !== 'hidden' && 
                                        el.textContent && el.textContent.trim().includes(titleText)) {
                                        return el;
                                    }
                                }
                            }
                            
                            // Last resort: look for any visible element with the title text
                            const allElements = document.querySelectorAll('div, span, li, option, button');
                            for (const el of Array.from(allElements)) {
                                const style = window.getComputedStyle(el);
                                if (style.display !== 'none' && style.visibility !== 'hidden' && 
                                    el.textContent && (el.textContent.trim() === titleText || 
                                    el.textContent.trim().includes(titleText))) {
                                    return el;
                                }
                            }
                            
                            return null;
                        };
                        
                        const optionElement = findOptionElement();
                        if (optionElement) {
                            console.log(`Found and will click title option: ${(optionElement as HTMLElement).textContent}`);
                            (optionElement as HTMLElement).click();
                            optionElement.dispatchEvent(new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            }));
                            return true;
                        }
                        console.log(`Could not find title option: ${titleText}`);
                        return false;
                    }, title);
                }
            } catch (clickError) {
                console.error('Error clicking title option:', clickError);
                throw clickError;
            }
            
            // Take a screenshot after selection
            await ScreenshotHelper.takeScreenshot('after-title-selection');
            console.log('Title selected successfully');
            console.log(`========== TITLE SELECTION DEBUG END ==========`);
        } catch (error) {
            console.error('Error selecting title:', error);
            await ScreenshotHelper.takeScreenshot('title-selection-error', true);
            
            // Log more info but don't throw - let the process continue
            console.warn('Will continue with the test despite title selection issue');
        }
    }

    /**
     * Fill the name field
     */
    private static async fillName(name: string): Promise<void> {
        try {
            console.log('Filling name...');
            await fixture.page.fill(this.elements.nameField, name);
            console.log('Name filled successfully');
        } catch (error) {
            console.error('Error filling name:', error);
            throw error;
        }
    }

    /**
     * Select the country code from dropdown
     */
    private static async selectCountryCode(countryName: string): Promise<void> {
        try {
            console.log(`Selecting country code for: ${countryName}...`);
            
            // Take a screenshot before starting country code selection
            await ScreenshotHelper.takeScreenshot('before-country-code-selection');
            
            // Check if the country code dropdown is already open to avoid double-typing
            const isDropdownOpen = await fixture.page.isVisible(this.elements.countryList)
                .catch(() => false);
                
            if (isDropdownOpen) {
                console.log('Country dropdown is already open, skipping click on dropdown');
            } else {
                // Click on the country code dropdown to open it
                await fixture.page.click(this.elements.countryCodeDropdown);
                
                // Wait for dropdown to appear
                await fixture.page.waitForSelector(this.elements.countryList, { timeout: 5000 });
            }
            
            // Check if the search field is already visible and has content
            const searchFieldValue = await fixture.page.inputValue(this.elements.countrySearchField)
                .catch(() => '');
                
            if (searchFieldValue && searchFieldValue.toLowerCase() === countryName.toLowerCase()) {
                console.log('Search field already contains the correct country name, skipping fill');
            } else {
                // Clear the search field first to avoid appending to existing text
                await fixture.page.fill(this.elements.countrySearchField, '');
                
                // Search for the country
                await fixture.page.fill(this.elements.countrySearchField, countryName);
                
                // Wait a moment for search results
                await fixture.page.waitForTimeout(1000);
            }
            
            // Take a screenshot after filling the search field
            await ScreenshotHelper.takeScreenshot('country-code-search-results');
            
            // Find and click on the country in the list
            const countrySelector = `${this.elements.countryListItems} .country-name:text-is("${countryName}")`;
            
            try {
                await fixture.page.click(countrySelector, { timeout: 5000 });
            } catch (clickError) {
                console.warn(`Could not find exact country name match for: ${countryName}`);
                
                // If exact match fails, try to find a country that contains the text
                const containsSelector = `${this.elements.countryListItems} .country-name:text("${countryName}")`;
                try {
                    await fixture.page.click(containsSelector, { timeout: 5000 });
                } catch (containsError) {
                    console.warn(`Could not find country containing: ${countryName}`);
                    
                    // Try clicking the first country in the list as last resort
                    const firstCountrySelector = `${this.elements.countryListItems}:first-child`;
                    await fixture.page.click(firstCountrySelector, { timeout: 5000 })
                        .catch(() => console.error('Failed to click first country in list'));
                }
            }
            
            // Take a screenshot after selection
            await ScreenshotHelper.takeScreenshot('after-country-code-selection');
            console.log('Country code selected successfully');
        } catch (error) {
            console.error('Error selecting country code:', error);
            
            // Try an alternative method using JavaScript if the first method fails
            try {
                console.log('Trying alternative method for selecting country code...');
                
                // First check if the dropdown exists and is visible
                const dropdownExists = await fixture.page.evaluate(() => {
                    return document.querySelector('ul.country-list') !== null;
                });
                
                if (!dropdownExists) {
                    // Try to open the dropdown first if it's not visible
                    await fixture.page.evaluate(() => {
                        const dropdownToggle = document.querySelector('div.flag');
                        if (dropdownToggle) {
                            (dropdownToggle as HTMLElement).click();
                        }
                    });
                    
                    // Wait for dropdown to appear
                    await fixture.page.waitForTimeout(1000);
                }
                
                // Now search and select the country
                await fixture.page.evaluate((countryName: string) => {
                    // Try to find and fill the search field first
                    const searchField = document.querySelector('input.search-box');
                    if (searchField) {
                        (searchField as HTMLInputElement).value = '';
                        (searchField as HTMLInputElement).value = countryName;
                        (searchField as HTMLInputElement).dispatchEvent(new Event('input', { bubbles: true }));
                    }
                    
                    // Wait a short time for search results
                    setTimeout(() => {
                        // Find and click the matching country
                        const countryItems = document.querySelectorAll('ul.country-list li.country');
                        for (let i = 0; i < countryItems.length; i++) {
                            const item = countryItems[i];
                            const countryNameElement = item.querySelector('.country-name');
                            if (countryNameElement && countryNameElement.textContent &&
                                countryNameElement.textContent.toLowerCase().includes(countryName.toLowerCase())) {
                                (item as HTMLElement).click();
                                return true;
                            }
                        }
                        
                        // If no match found, click the first country as fallback
                        if (countryItems.length > 0) {
                            (countryItems[0] as HTMLElement).click();
                            return true;
                        }
                        
                        return false;
                    }, 500);
                }, countryName);
                
                // Wait for the selection to complete
                await fixture.page.waitForTimeout(1000);
                
                console.log('Country code selected using alternative method');
            } catch (alternativeError) {
                console.error('Error with alternative country selection:', alternativeError);
                throw error;
            }
        }
    }

    /**
     * Fill the phone number field
     */    private static async fillPhoneNumber(phoneNumber: string): Promise<void> {
        try {
            console.log('Filling phone number (preserving country code)...');
            
            // Try with modern Playwright locators first
            try {
                // Look for phone field by role and name
                const phoneByRole = fixture.page.getByRole('textbox', { 
                    name: /phone|telephone|mobile/i,
                    exact: false 
                });
                
                if (await phoneByRole.isVisible({ timeout: 3000 })) {
                    console.log('Found phone field using role locator');
                    
                    // Get the current value to check for country code prefix
                    const currentValue = await phoneByRole.inputValue();
                    console.log(`Current phone input value: ${currentValue}`);
                    
                    // If there's a country code, preserve it
                    if (currentValue && currentValue.startsWith('+')) {
                        const countryCode = currentValue.match(/^\+\d+/)?.[0] || '';
                        if (countryCode) {
                            console.log(`Found country code: ${countryCode}`);
                            // Preserve country code while filling phone number
                            await phoneByRole.fill(`${countryCode} ${phoneNumber}`);
                            console.log(`Successfully filled phone with country code using role locator`);
                            return;
                        }
                    } else {
                        // No country code to preserve, just fill the field
                        await phoneByRole.fill(phoneNumber);
                        console.log(`Successfully filled phone number using role locator`);
                        return;
                    }
                }
            } catch (roleError) {
                console.log('Failed to find phone field by role:', roleError.message);
            }
            
            // Try by label if role didn't work
            try {
                const phoneByLabel = fixture.page.getByLabel(/phone|telephone|mobile/i, { exact: false });
                if (await phoneByLabel.isVisible({ timeout: 2000 })) {
                    console.log('Found phone field using label locator');
                    
                    // Get the current value to check for country code prefix
                    const currentValue = await phoneByLabel.inputValue();
                    console.log(`Current phone input value: ${currentValue}`);
                    
                    // If there's a country code, preserve it
                    if (currentValue && currentValue.startsWith('+')) {
                        const countryCode = currentValue.match(/^\+\d+/)?.[0] || '';
                        if (countryCode) {
                            console.log(`Found country code: ${countryCode}`);
                            await phoneByLabel.fill(`${countryCode} ${phoneNumber}`);
                            console.log(`Successfully filled phone with country code using label locator`);
                            return;
                        }
                    } else {
                        // No country code to preserve
                        await phoneByLabel.fill(phoneNumber);
                        console.log(`Successfully filled phone number using label locator`);
                        return;
                    }
                }
            } catch (labelError) {
                console.log('Failed to find phone field by label:', labelError.message);
            }
            
            // Fall back to CSS selector approach
            // First, get the current value to check for country code prefix
            const currentValue = await fixture.page.inputValue(this.elements.phoneNumber);
            console.log(`Current phone input value using CSS selector: ${currentValue}`);
            
            // If there's a country code (starts with +), we must preserve it
            if (currentValue && currentValue.startsWith('+')) {
                console.log('Detected country code in phone field, preserving it...');
                
                try {
                    // Try a more reliable approach for preserving country code
                    // 1. Extract the country code from the current value
                    const countryCode = currentValue.match(/^\+\d+/)?.[0] || '';
                    
                    if (countryCode) {
                        console.log(`Found country code: ${countryCode}`);
                          // 2. Use a combination of methods to ensure reliability
                          // Method 1: Using JavaScript to directly set the value
                        await fixture.page.evaluate(({ selector, code, number }: { selector: string, code: string, number: string }) => {
                            const element = document.querySelector(selector) as HTMLInputElement;
                            if (element) {
                                const newValue = `${code} ${number}`.trim();
                                element.value = newValue;
                                
                                // Important: Trigger proper input events for React/Angular detection
                                element.dispatchEvent(new Event('input', { bubbles: true }));
                                element.dispatchEvent(new Event('change', { bubbles: true }));
                                
                                console.log(`Phone field value set via JS to: ${newValue}`);
                            }
                        }, { selector: this.elements.phoneNumber, code: countryCode, number: phoneNumber });
                        
                        // Method 2: Double check with Playwright input handling
                        try {
                            // Click the field and position cursor after the country code
                            await fixture.page.click(this.elements.phoneNumber);
                            
                            // 1. Check current value after JS update
                            const updatedValue = await fixture.page.inputValue(this.elements.phoneNumber);
                            
                            // 2. If JS update didn't work properly, try the Playwright approach
                            if (!updatedValue.includes(phoneNumber)) {
                                console.log('JavaScript update not detected, using Playwright approach...');
                                
                                // Clear the field with Control+A and replace with formatted value
                                await fixture.page.press(this.elements.phoneNumber, 'Control+a');
                                await fixture.page.type(this.elements.phoneNumber, `${countryCode} ${phoneNumber}`, { delay: 50 });
                            }
                        } catch (playwrightError) {
                            console.log('Playwright approach failed, but JavaScript update should have worked:', playwrightError);
                        }
                        
                        // Take a screenshot to verify
                        await ScreenshotHelper.takeScreenshot('phone-number-with-country-code');
                        
                        console.log('Phone number filled successfully while preserving country code');
                        return;
                    }
                } catch (error) {
                    console.error('Error preserving country code, falling back to standard approach:', error);
                }
            }
            
            // If no country code found or preservation failed, fall back to standard approach
            try {
                console.log('Using standard fill for phone number (no country code detected)');
                await fixture.page.fill(this.elements.phoneNumber, phoneNumber);
            } catch (fillError) {
                // If fill fails, try type as a fallback
                console.log('Fill failed, trying type method');
                await fixture.page.click(this.elements.phoneNumber);
                await fixture.page.press(this.elements.phoneNumber, 'Control+a');
                await fixture.page.type(this.elements.phoneNumber, phoneNumber, { delay: 50 });
            }              // Additional JavaScript fallback approach if all else fails
            try {
                await fixture.page.evaluate(({ selector, phone }: { selector: string, phone: string }) => {
                    const element = document.querySelector(selector) as HTMLInputElement;
                    if (element) {
                        // Get existing value to recheck for country code
                        const existingValue = element.value || '';
                        
                        // If there's still a country code after our attempts (starts with +), preserve it
                        if (existingValue.startsWith('+')) {                            // Extract country code
                            const countryCodeMatch = existingValue.match(/^\+\d+/);
                            if (countryCodeMatch && countryCodeMatch[0]) {
                                // Keep the country code and add the new phone number
                                const formattedValue = `${countryCodeMatch[0]} ${phone}`;
                                element.value = formattedValue;
                                console.log(`Phone preserved with country code: ${formattedValue}`);
                            } else {
                                element.value = phone;
                                console.log(`No country code found, set pure number: ${phone}`);
                            }
                        } else {
                            element.value = phone;
                        }
                        
                        // Trigger events for framework detection
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('Final phone field value via JS: ' + element.value);
                    }
                }, { selector: this.elements.phoneNumber, phone: phoneNumber });
            } catch (jsError) {
                console.warn('JavaScript fallback approach failed:', jsError);
            }
            
            // Verification step - check if the value was set properly
            const finalValue = await fixture.page.inputValue(this.elements.phoneNumber);
            console.log(`Final phone input value: ${finalValue}`);
            
            console.log('Phone number filling process completed');
        } catch (error) {
            console.error('Error filling phone number:', error);
            await ScreenshotHelper.takeScreenshot('phone-number-error', true);
            throw error;
        }
    }

    /**
     * Fill the email ID field
     */
    private static async fillEmailId(emailId: string): Promise<void> {
        try {
            console.log('Filling email ID...');
            await fixture.page.fill(this.elements.emailId, emailId);
            console.log('Email ID filled successfully');
        } catch (error) {
            console.error('Error filling email ID:', error);
            throw error;
        }
    }

    /**
     * Click the Confirm & Pay button to complete the payment process
     */
    static async clickConfirmAndPayButton(): Promise<boolean> {
        try {
            console.log('Clicking the Confirm & Pay button...');
            
            // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-confirm-and-pay');
            
            // Define selectors for the button - primary and fallbacks for better reliability
            const confirmPayButtonSelectors = [
                this.elements.confirmPayButton,
                'button:has-text("Confirm & Pay")',
                'button:has-text("Confirm")',
                'button.px-4.py-2[style*="background: linear-gradient"]',
                '.px-4.py-2:has-text("Confirm")'
            ];
            
            // Try to find and click the button using multiple strategies
            let clickSuccessful = false;
            for (const selector of confirmPayButtonSelectors) {
                try {
                    // Check if element exists before attempting click
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
                    if (isVisible) {
                        await fixture.page.click(selector);
                        console.log(`Successfully clicked Confirm & Pay button using selector: ${selector}`);
                        clickSuccessful = true;
                        break;
                    }
                } catch (error) {
                    console.log(`Failed to click using selector ${selector}: ${error}`);
                }
            }
            
            if (!clickSuccessful) {
                // Last resort: try JavaScript click on any matching element
                try {
                    await fixture.page.evaluate(() => {
                        const buttons = Array.from(document.querySelectorAll('button'));
                        const confirmPayButton = buttons.find(button => 
                            button.textContent?.includes('Confirm & Pay') || 
                            button.textContent?.includes('Confirm') ||
                            (button.className?.includes('px-4') && button.className?.includes('py-2') && 
                             button.style.background?.includes('linear-gradient'))
                        );
                        if (confirmPayButton) confirmPayButton.click();
                        return !!confirmPayButton;
                    });
                    console.log('Used JavaScript evaluation to find and click the Confirm & Pay button');
                    clickSuccessful = true;
                } catch (jsError) {
                    console.error('JavaScript click also failed:', jsError);
                }
            }
            
            if (!clickSuccessful) {
                throw new Error('Failed to click the Confirm & Pay button using all available methods');
            }
            
            // Wait for navigation to complete
            await fixture.page.waitForTimeout(5000);
            
            // Take screenshot after clicking
            await ScreenshotHelper.takeScreenshot('after-confirm-and-pay');
            
            console.log('Successfully clicked the Confirm & Pay button');
            return true;
        } catch (error) {
            console.error('Error clicking Confirm & Pay button:', error);
            await ScreenshotHelper.takeScreenshot('confirm-and-pay-error', true);
            throw error;
        }
    }
}
