import { BasePage } from './BasePage';
import { fixture } from '../fixtures/Fixture';
import { ScreenshotHelper } from '../utils/ScreenshotHelper';

export class ChatPage extends BasePage {    private static elements = {
        // Chat with Shasa button - using modern Playwright locators
        // These will be used with page.getByRole, getByText, etc. methods
        
        // Modern locator references for quick usage
        chatWithShasaButtonRole: { role: 'button', name: 'Chat with Shasa' },
        chatWithShasaButtonAltRole: { role: 'button', name: /chat with shasa/i },
        
        // Legacy selectors kept for backward compatibility
        chatWithShasaButton: 'button.relative.flex.flex-row.gap-2.items-center.text-center.px-4.py-1.rounded-full.justify-around.bg-\\[linear-gradient\\(to_right\\,\\#4B4BC3\\,\\#707FF5\\,\\#A195F9\\)\\].text-white',
        chatWithShasaButtonAlt1: 'button:has-text("Chat with Shasa")',
        chatWithShasaButtonAlt2: 'button.rounded-full:has-text("Chat with Shasa")',
        chatWithShasaButtonAlt3: 'button:has(div:text("Chat with Shasa"))',
        chatWithShasaButtonAlt4: 'button:has(img[alt="Chat"])',
        
        // Chat interface elements
        chatContainer: 'div.flex.flex-col.gap-1, div.flex.flex-col.h-full.bg-white.overflow-hidden.pb-\\[50px\\]',
        chatWindow: 'div[role="dialog"], div.flex.flex-col.h-full.tk-proxima-nova.overflow-hidden',
        messageSendButton: 'button:has(svg.lucide-send), button:has(svg.lucide.lucide-send)',
        
        // Input elements - with modern locators
        messageInputPlaceholder: 'Ask me anything...',
        messageTextareaPlaceholders: ['Type a message...', 'Ask me anything.', 'Find'],
        messageInput: 'input[placeholder="Ask me anything..."]',
        messageTextarea: 'textarea[placeholder="Type a message..."], textarea[placeholder="Ask me anything."], textarea[placeholder="Find"]',
        
        // Chat interface main container - based on the provided element
        chatInputContainer: 'div.fixed.bottom-0.right-0.z-50, div.absolute.fixed.bottom-0.pb-6.md\\:left-20.md\\:right-20.left-0.right-0.fixed.bottom-0.left-0.right-0.px-4.py-3.z-50',
        chatInputBox: 'div.flex.flex-col.bg-white.rounded-2xl.border-2, div.flex.items-center.w-full.border.border-\\[\\#707FF5\\].rounded-lg.px-4.py-2.bg-white.shadow-sm',
        sendButton: 'button[type="submit"]:has(svg.lucide-send), div.h-8.w-8.cursor-pointer.flex.items-center.justify-center.rounded-full.text-white.bg-\\[linear-gradient\\(267.79deg\\,_\\#F2A1F2_-60.95\\%\\,_\\#A195F9_6.26\\%\\,_\\#707FF5_73.47\\%\\,_\\#4B4BC3_140.67\\%\\,_\\#1E1E76_207.88\\%\\)\\]',
        
        // New Chat button - using modern locator references
        newChatButtonRole: { role: 'button', name: 'New Chat' },
        newChatButtonRoleAlt: { role: 'button', name: /new chat/i },
        
        // Legacy selectors kept for backward compatibility
        newChatButton: 'button.bg-\\[linear-gradient\\(267.79deg\\,_\\#F2A1F2_-60.95\\%\\,_\\#A195F9_6.26\\%\\,_\\#707FF5_73.47\\%\\,_\\#4B4BC3_140.67\\%\\,_\\#1E1E76_207.88\\%\\)\\]:has-text("New Chat")',
        newChatButtonAlt1: 'button:has-text("New Chat")',
        newChatButtonAlt2: 'button.rounded-xl:has-text("New Chat")',
        newChatButtonAlt3: 'button:has(svg.lucide-square-pen)',
        newChatButtonAlt4: 'button.text-\\[\\#F2F3FA\\]:has-text("New Chat")',
        
        // New UI elements based on the provided HTML
        welcomeMessage: 'h2.text-\\[14px\\].md\\:text-\\[30px\\].font-bold:has-text("Ready to Explore?")',
        chatVideo: 'video.w-full.md\\:h-\\[207px\\].h-\\[120px\\]',
        chatForm: 'form.relative'
    }

    /**
     * Click on the Chat with Shasa button
     * Enhanced with Playwright's modern locators
     */    
    public static async clickChatWithShasaButton(): Promise<boolean> {
        console.log('Attempting to click Chat with Shasa button...');
        
        try {
            // Take a screenshot before clicking for debugging
            await ScreenshotHelper.takeScreenshot('before-chat-button-click', false);
            
            // First try using modern Playwright locators (preferred method)
            console.log('Trying to locate button with modern getByRole locator');
            
            // Try with getByRole - most semantic approach
            const buttonByRole = fixture.page.getByRole('button', { name: 'Chat with Shasa', exact: false });
            if (await buttonByRole.isVisible({ timeout: 3000 }).catch(() => false)) {
                console.log('Found Chat with Shasa button with getByRole locator');
                await buttonByRole.click();
                console.log('Successfully clicked Chat with Shasa button using getByRole');
                
                // Add a longer wait after clicking to allow chat interface to fully load
                console.log('Adding additional wait time after clicking the button...');
                await fixture.page.waitForTimeout(5000);
                
                // Take another screenshot after waiting
                await ScreenshotHelper.takeScreenshot('after-chat-button-click-wait', false);
                
                return true;
            }
            
            // Try with getByText as fallback
            console.log('Trying with getByText locator');
            const buttonByText = fixture.page.getByText('Chat with Shasa', { exact: false });
            if (await buttonByText.isVisible({ timeout: 2000 }).catch(() => false)) {
                // Make sure we're clicking a button containing this text, not just any element
                const buttonWithText = buttonByText.filter({ has: fixture.page.locator('button') });
                if (await buttonWithText.isVisible({ timeout: 2000 }).catch(() => false)) {
                    console.log('Found Chat with Shasa button with getByText locator');
                    await buttonWithText.click();
                    
                    // Add a longer wait after clicking
                    await fixture.page.waitForTimeout(5000);
                    await ScreenshotHelper.takeScreenshot('after-chat-button-click-wait', false);
                    
                    return true;
                }
            }
            
            // If modern locators failed, fall back to legacy selectors for backward compatibility
            console.log('Modern locators failed, trying legacy CSS selectors as fallback');
            
            // Try multiple selectors to find and click the button
            const selectors = [
                this.elements.chatWithShasaButton,
                this.elements.chatWithShasaButtonAlt1,
                this.elements.chatWithShasaButtonAlt2,
                this.elements.chatWithShasaButtonAlt3,
                this.elements.chatWithShasaButtonAlt4,
                // Additional generic selectors
                'button:has(img[src*="Chat White.png"])',
                'button:has(div:contains("Chat with Shasa"))'
            ];
            
            for (const selector of selectors) {
                try {
                    console.log(`Trying to locate button with selector: ${selector}`);
                    
                    // Check if the button is visible
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`Found Chat with Shasa button with selector: ${selector}`);
                        
                        // Click the button
                        await this.waitAndClick(selector);
                        
                        // Add a longer wait after clicking to allow chat interface to fully load
                        console.log('Adding additional wait time after clicking the button...');
                        await fixture.page.waitForTimeout(5000);
                        
                        // Take another screenshot after waiting
                        await ScreenshotHelper.takeScreenshot('after-chat-button-click-wait', false);
                        
                        console.log('Successfully clicked Chat with Shasa button');
                        return true;
                    }
                } catch (error) {
                    console.log(`Failed to interact with selector ${selector}: ${error}`);
                }
            }
        } catch (error) {
            console.error('Error in clickChatWithShasaButton:', error);
        }
        
        // If we get here, we couldn't find or click the button
        console.error('Failed to find or click the Chat with Shasa button');
        await ScreenshotHelper.takeScreenshot('chat-button-not-found', true);
        return false;
    }

    /**
     * Verify that the chat interface is visible after clicking the chat button
     */
    public static async isChatInterfaceVisible(): Promise<boolean> {
        try {
            console.log('Checking if chat interface is visible...');
            
            // Try multiple approaches to verify chat interface is visible
            const chatInterfaceSelectors = [
                this.elements.chatContainer,
                'div.fixed.bottom-0.right-0',
                'div.fixed.inset-0.bg-background/80.backdrop-blur-sm',
                'div:has(input[placeholder="Ask me anything..."])',
                'div:has(textarea[placeholder="Type a message..."])',
                // New selectors based on HTML reference
                'div.flex.h-screen.bg-white',
                'div.w-80.md\\:block',
                'div.rounded-2xl.my-2.relative',
                'div:has(img[src*="main-logo-white.svg"])',
                'div.absolute.fixed.bottom-0.pb-6',
                'div.flex-1.overflow-hidden.mx-4',
                'div.relative.h-\\[90\\%\\].sm\\:h-full',
                'form.flex.items-center.w-full.border',
                'textarea[placeholder="Type a message..."]',
                'button.bg-\\[linear-gradient\\(267.79deg',
                'div:has(h3:text("Chat History"))',
                'video.w-full.md\\:h-\\[207px\\]',
                'div:has(h2:text("Where to today?"))'
            ];
            
            // Check if any of the chat interface selectors are visible with increased timeout
            for (const selector of chatInterfaceSelectors) {
                console.log(`Checking selector: ${selector}`);
                const isVisible = await fixture.page.isVisible(selector, { timeout: 5000 })
                    .catch(() => {
                        console.log(`Error or timeout checking selector: ${selector}`);
                        return false;
                    });
                
                if (isVisible) {
                    console.log(`Found chat interface with selector: ${selector}`);
                    return true;
                }
            }
            
            // If none of the general selectors worked, try checking specific elements
            console.log('Checking specific chat elements...');
            const isInputVisible = await fixture.page.isVisible(this.elements.messageInput, { timeout: 5000 })
                .catch(() => false);
            
            const isTextareaVisible = await fixture.page.isVisible(this.elements.messageTextarea, { timeout: 5000 })
                .catch(() => false);
                
            const isSendButtonVisible = await fixture.page.isVisible(this.elements.messageSendButton, { timeout: 5000 })
                .catch(() => false);
                
            const isNewChatButtonVisible = await fixture.page.isVisible(this.elements.newChatButton, { timeout: 5000 })
                .catch(() => {
                    return fixture.page.isVisible(this.elements.newChatButtonAlt1, { timeout: 3000 })
                        .catch(() => false);
                });
            
            if (isInputVisible || isTextareaVisible || isSendButtonVisible || isNewChatButtonVisible) {
                console.log('Chat interface is visible based on input field, textarea, send button, or New Chat button');
                return true;
            }
            
            console.log('Chat interface is not visible with any selector');
            await ScreenshotHelper.takeScreenshot('chat-interface-not-visible', true);
            return false;
        } catch (error) {
            console.error('Error checking if chat interface is visible:', error);
            await ScreenshotHelper.takeScreenshot('chat-interface-error', true);
            return false;
        }
    }
    
    /**
     * Verify that the user is on the chat page by checking for the presence of chat interface elements
     * Enhanced with Playwright's modern locators
     * This is a comprehensive check specifically for the "Then User is on Chat page" step
     */
    public static async verifyChatPageIsOpen(): Promise<boolean> {
        console.log('Verifying user is on the Chat page...');
        try {
            // Take screenshot for reference
            await ScreenshotHelper.takeScreenshot('chat-page-verification');
            
            // First try using modern locators to find chat interface elements
            console.log('Checking for chat interface elements using modern locators...');
            
            // Check for input with placeholder
            const inputByPlaceholder = fixture.page.getByPlaceholder(this.elements.messageInputPlaceholder);
            const inputVisible = await inputByPlaceholder.isVisible({ timeout: 3000 }).catch(() => false);
            
            // Check for textarea with various placeholders
            let textareaVisible = false;
            for (const placeholder of this.elements.messageTextareaPlaceholders) {
                const textarea = fixture.page.getByPlaceholder(placeholder);
                if (await textarea.isVisible({ timeout: 2000 }).catch(() => false)) {
                    textareaVisible = true;
                    break;
                }
            }
            
            // Check for send button (look for button with icon)
            const sendButton = fixture.page.getByRole('button').filter({
                has: fixture.page.getByTestId('lucide-send').or(fixture.page.locator('svg.lucide-send'))
            });
            const sendButtonVisible = await sendButton.isVisible({ timeout: 2000 }).catch(() => false);
            
            // Check for New Chat button
            const newChatButton = fixture.page.getByRole('button', { name: /new chat/i });
            const newChatButtonVisible = await newChatButton.isVisible({ timeout: 2000 }).catch(() => false);
            
            console.log(`Modern locator check results - Input: ${inputVisible}, Textarea: ${textareaVisible}, Send button: ${sendButtonVisible}, New Chat button: ${newChatButtonVisible}`);
            
            // If any two elements are visible, consider chat page open
            if ((inputVisible && sendButtonVisible) ||
                (textareaVisible && sendButtonVisible) ||
                (inputVisible && newChatButtonVisible) ||
                (textareaVisible && newChatButtonVisible)) {
                console.log('Chat page is confirmed open with modern locators');
                return true;
            }
            
            // Fall back to legacy selectors if modern locators failed
            console.log('Modern locators did not confirm chat page, trying legacy selectors...');
            
            // Check for the main chat container with the specific selectors from the provided HTML
            const chatContainerVisible = await fixture.page.isVisible(this.elements.chatInputContainer, { timeout: 5000 })
                .catch(() => false);
                
            if (chatContainerVisible) {
                console.log('Chat input container is visible');
                
                // Further verification by checking the input box and send button
                const inputBoxVisible = await fixture.page.isVisible(this.elements.chatInputBox, { timeout: 2000 })
                    .catch(() => false);
                    
                const sendButtonVisible = await fixture.page.isVisible(this.elements.sendButton, { timeout: 2000 })
                    .catch(() => false);
                    
                // Check for the placeholder text in the input field or textarea
                const inputField = fixture.page.locator(this.elements.messageInput);
                const inputFieldVisible = await inputField.isVisible()
                    .catch(() => false);
                
                // Check for textarea as well
                const textareaField = fixture.page.locator(this.elements.messageTextarea);
                const textareaFieldVisible = await textareaField.isVisible()
                    .catch(() => false);
                    
                console.log(`Legacy check results - Input box: ${inputBoxVisible}, Send button: ${sendButtonVisible}, Input field: ${inputFieldVisible}, Textarea field: ${textareaFieldVisible}`);
                
                // If at least two of the elements are visible, consider the chat page open
                if ((inputBoxVisible && sendButtonVisible) || 
                    (inputBoxVisible && inputFieldVisible) || 
                    (inputBoxVisible && textareaFieldVisible) ||
                    (sendButtonVisible && inputFieldVisible) ||
                    (sendButtonVisible && textareaFieldVisible)) {
                    console.log('Chat page is confirmed open with legacy selectors');
                    return true;
                }
            }
            
            // Alternative approach - check for any elements that might indicate chat interface
            const chatInterfaceElements = [
                // Main container
                'div.absolute.fixed.bottom-0',
                'div[class*="fixed bottom-0"]',
                
                // Chat input
                'input[placeholder="Ask me anything..."]',
                'textarea[placeholder="Type a message..."]',
                'div.flex.items-center.w-full.border',
                'form.flex.items-center.w-full.border',
                
                // Send button
                'svg.lucide.lucide-send',
                'div.h-8.w-8.cursor-pointer.flex.items-center.justify-center.rounded-full'
            ];
            
            for (const selector of chatInterfaceElements) {
                const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                    .catch(() => false);
                    
                if (isVisible) {
                    console.log(`Chat page confirmed open with element: ${selector}`);
                    return true;
                }
            }
            
            console.log('Failed to verify chat page is open - required elements not found');
            await ScreenshotHelper.takeScreenshot('chat-page-not-found', true);
            return false;
        } catch (error) {
            console.error('Error verifying chat page:', error);
            await ScreenshotHelper.takeScreenshot('chat-page-verification-error', true);
            return false;
        }
    }
    
    /**
     * Types a message in the chat input field
     * @param message The message to type
     */
    public static async typeMessage(message: string): Promise<boolean> {
        console.log('Attempting to type message in chat input field');
        
        try {
            // Try modern Playwright locators first
            try {
                // First try using getByPlaceholder
                for (const placeholder of this.elements.messageTextareaPlaceholders) {
                    try {
                        const inputField = fixture.page.getByPlaceholder(placeholder);
                        if (await inputField.isVisible({ timeout: 2000 }).catch(() => false)) {
                            console.log(`Found chat input using getByPlaceholder("${placeholder}")`);
                            await inputField.fill('');
                            await inputField.fill(message);
                            console.log(`Successfully typed message using modern locator: "${message}"`);
                            await ScreenshotHelper.takeScreenshot('after-typing-message');
                            return true;
                        }
                    } catch (error) {
                        console.log(`Error with placeholder "${placeholder}": ${error}`);
                    }
                }
                
                // Try using getByRole for textbox
                try {
                    const textbox = fixture.page.getByRole('textbox');
                    if (await textbox.isVisible({ timeout: 2000 }).catch(() => false)) {
                        console.log('Found chat input using getByRole("textbox")');
                        await textbox.fill('');
                        await textbox.fill(message);
                        console.log(`Successfully typed message using getByRole: "${message}"`);
                        await ScreenshotHelper.takeScreenshot('after-typing-message');
                        return true;
                    }
                } catch (error) {
                    console.log(`Error with getByRole("textbox"): ${error}`);
                }
            } catch (error) {
                console.log('Error with modern locators, falling back to legacy selectors:', error);
            }
            
            // Fall back to legacy selectors if modern locators fail
            const inputSelectors = [
                this.elements.messageInput,
                this.elements.messageTextarea,  // Add the textarea selector
                'input[placeholder*="Ask me anything"]',
                'textarea[placeholder*="Type a message"]', // Add a more generalized textarea selector
                'textarea[placeholder*="message"]',
                'input[placeholder*="ask"]',
                'input.w-full',
                'div.flex input',
                'textarea.w-full', // Add generic textarea selectors
                'div.flex textarea'
            ];
            
            // Try each selector until we find one that works
            for (const selector of inputSelectors) {
                try {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`Found chat input element with selector: ${selector}`);
                        
                        // Clear any existing text
                        await fixture.page.fill(selector, '');
                        
                        // Type the message
                        await fixture.page.fill(selector, message);
                        console.log(`Successfully typed message: "${message}"`);
                        
                        // Take a screenshot for verification
                        await ScreenshotHelper.takeScreenshot('after-typing-message');
                        
                        return true;
                    }
                } catch (error) {
                    console.log(`Error with selector ${selector}: ${error}`);
                }
            }
            
            // If we get here, we couldn't find the input field
            console.error('Failed to find chat input field or textarea');
            await ScreenshotHelper.takeScreenshot('chat-input-not-found', true);
            return false;
            
        } catch (error) {
            console.error('Error typing message in chat input:', error);
            await ScreenshotHelper.takeScreenshot('message-typing-error', true);
            return false;
        }
    }
    
    /**
     * Clicks the send button to send a message
     */
    public static async clickSendButton(): Promise<boolean> {
        console.log('Attempting to click send button');
        
        try {
            // First check if a cookie consent dialog is present and handle it
            await this.handleCookieConsentIfPresent();
            
            // Dismiss any overlays that might be blocking the send button
            await this.dismissAnyOverlays();
            
            // Try modern Playwright locators first
            try {
                // Try finding the button by role with appropriate icons or names
                console.log('Trying to locate send button with modern locators...');
                
                // Try getByRole button with "Send" name or containing an icon
                const sendButton = fixture.page.getByRole('button').filter({ hasText: 'Send' });
                if (await sendButton.isVisible({ timeout: 2000 }).catch(() => false)) {
                    console.log('Found send button with getByRole("button") and "Send" text');
                    await sendButton.click();
                    console.log('Successfully clicked send button using modern locator');
                    await ScreenshotHelper.takeScreenshot('after-send-button-click-modern');
                    return true;
                }
                
                // Try finding button with an icon role
                const iconButton = fixture.page.getByRole('button').filter({ has: fixture.page.locator('svg') });
                if (await iconButton.isVisible({ timeout: 2000 }).catch(() => false)) {
                    console.log('Found send button with getByRole("button") and svg icon');
                    await iconButton.click();
                    console.log('Successfully clicked send button using modern icon locator');
                    await ScreenshotHelper.takeScreenshot('after-send-button-click-modern-icon');
                    return true;
                }
            } catch (modernError) {
                console.log('Modern locators approach failed:', modernError);
                console.log('Falling back to legacy selectors...');
            }
            
            // Fall back to legacy selectors if modern locators fail
            const sendButtonSelectors = [
                this.elements.sendButton,
                this.elements.messageSendButton,
                'div.cursor-pointer:has(svg.lucide-send)',
                'button:has(svg.lucide-send)',
                'div.h-8.w-8.cursor-pointer.rounded-full',
                'div.rounded-full:has(svg)',
                // Additional selectors that might be more specific to this application
                'div[role="button"]:has(svg.lucide-send)',
                'button[type="submit"]:has(svg)',
                'div.rounded-full.bg-\\[linear-gradient',
                'div[role="button"].cursor-pointer',
                // More specific selectors based on app structure
                '.chat-input-box button[type="submit"]',
                '.chat-input-container div.rounded-full',
                'form button:has(svg)',
                'form div[role="button"]'
            ];
            
            // First, log all visible buttons to help debug if selector isn't working
            console.log('Checking for visible button elements before trying specific selectors');
            const buttonCount = await fixture.page.locator('button, div[role="button"]').count();
            console.log(`Found ${buttonCount} potential button elements`);
            
            // Try each selector until we find one that works
            for (const selector of sendButtonSelectors) {
                try {
                    console.log(`Checking send button with selector: ${selector}`);
                    
                    // Use a longer timeout for checking visibility
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 5000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`Found send button with selector: ${selector}`);
                        
                        // Take a screenshot before clicking
                        await ScreenshotHelper.takeScreenshot('before-send-button-click');
                        
                        // Attempt click with multiple strategies
                        let clickSuccess = false;
                        
                        // Strategy 1: Normal waitAndClick
                        try {
                            await this.waitAndClick(selector);
                            clickSuccess = true;
                            console.log('Standard click successful');
                        } catch (clickError) {
                            console.log(`Standard click failed: ${clickError}. Trying alternative methods...`);
                        }
                        
                        // Strategy 2: Force click with JavaScript if normal click failed
                        if (!clickSuccess) {
                            try {
                                await fixture.page.evaluate((sel) => {
                                    const element = document.querySelector(sel);
                                    if (element) {
                                        (element as HTMLElement).click();
                                        return true;
                                    }
                                    return false;
                                }, selector);
                                clickSuccess = true;
                                console.log('JavaScript click successful');
                            } catch (jsClickError) {
                                console.log(`JavaScript click failed: ${jsClickError}. Trying next method...`);
                            }
                        }
                        
                        // Strategy 3: Try direct click at element coordinates
                        if (!clickSuccess) {
                            try {
                                // Get element bounding box
                                const boundingBox = await fixture.page.locator(selector).boundingBox();
                                if (boundingBox) {
                                    // Click in the center of the element
                                    const x = boundingBox.x + boundingBox.width / 2;
                                    const y = boundingBox.y + boundingBox.height / 2;
                                    
                                    await fixture.page.mouse.click(x, y);
                                    clickSuccess = true;
                                    console.log(`Coordinate click successful at (${x}, ${y})`);
                                }
                            } catch (coordClickError) {
                                console.log(`Coordinate click failed: ${coordClickError}`);
                            }
                        }
                        
                        // Strategy 4: Try pressing Enter key as a fallback
                        if (!clickSuccess) {
                            try {
                                // Check for input field first
                                const inputVisible = await fixture.page.isVisible('input[placeholder="Ask me anything..."]', 
                                    { timeout: 2000 }).catch(() => false);
                                    
                                if (inputVisible) {
                                    // Focus on the input field first
                                    await fixture.page.focus('input[placeholder="Ask me anything..."]');
                                    await fixture.page.keyboard.press('Enter');
                                    clickSuccess = true;
                                    console.log('Used Enter key as fallback for input field');
                                } else {
                                    // Check for textarea
                                    const textareaVisible = await fixture.page.isVisible('textarea[placeholder="Type a message..."]',
                                        { timeout: 2000 }).catch(() => false);
                                        
                                    if (textareaVisible) {
                                        // Focus on the textarea
                                        await fixture.page.focus('textarea[placeholder="Type a message..."]');
                                        await fixture.page.keyboard.press('Enter');
                                        clickSuccess = true;
                                        console.log('Used Enter key as fallback for textarea');
                                    } else {
                                        console.log('Neither input nor textarea could be found for Enter key fallback');
                                    }
                                }
                            } catch (enterKeyError) {
                                console.log(`Enter key fallback failed: ${enterKeyError}`);
                            }
                        }
                        
                        if (clickSuccess) {
                            console.log('Successfully sent message with one of the click strategies');
                            
                            // Take a screenshot after clicking
                            await ScreenshotHelper.takeScreenshot('after-send-button-click');
                            
                            // Wait a moment to ensure the click had time to register
                            await fixture.page.waitForTimeout(1500);
                            
                            return true;
                        }
                    }
                } catch (error) {
                    console.log(`Error with selector ${selector}: ${error}`);
                }
            }
            
            // If we get here, we couldn't find or click the send button
            console.error('Failed to find or click the send button with all strategies');
            await ScreenshotHelper.takeScreenshot('send-button-not-found-or-clickable', true);
            
            // Last resort: Try to submit any form on the page
            try {
                console.log('Attempting last resort: form submission');
                await fixture.page.evaluate(() => {
                    const form = document.querySelector('form');
                    if (form) {
                        form.submit();
                        return true;
                    }
                    return false;
                });
                console.log('Attempted form submission as last resort');
                await fixture.page.waitForTimeout(1000);
                return true; // Assume the form submission worked
            } catch (formSubmitError) {
                console.log(`Form submission failed: ${formSubmitError}`);
            }
            
            return false;
        } catch (error) {
            console.error('Error clicking send button:', error);
            await ScreenshotHelper.takeScreenshot('send-button-click-error', true);
            return false;
        }
    }
    
    /**
     * Verifies if a new chat has been started
     */
    public static async verifyNewChatStarted(): Promise<boolean> {
        console.log('Verifying new chat has been started');
        
        try {
            // Wait for the new chat interface to stabilize
            await fixture.page.waitForTimeout(3000);
            
            // Take a screenshot of the current state for debugging
            await ScreenshotHelper.takeScreenshot('new-chat-verification-start');
            
            // Check for updated welcome message based on new UI structure
            const welcomeMessageSelectors = [
                // Exact selector from the provided HTML
                'h2.text-\\[14px\\].md\\:text-\\[30px\\].font-bold.text-\\[#2a1a6e\\]:has-text("Ready to Explore?")',
                // More general selectors as fallback
                'h2:has-text("Ready to Explore?")',
                'p:has-text("I\'m Sasha, your travel assistant")',
                'p.text-gray-600.text-lg:has-text("travel assistant")',
                'div:has-text("Ready to Explore?")',
                '.text-\\[#2a1a6e\\]:has-text("Ready to Explore?")'
            ];

            let welcomeMessageFound = false;
            for (const selector of welcomeMessageSelectors) {
                try {
                    console.log(`Checking for welcome message with selector: ${selector}`);
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
                    if (isVisible) {
                        console.log(`✅ Welcome message found with selector: ${selector}`);
                        welcomeMessageFound = true;
                        break;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            console.log(`Welcome message detection: ${welcomeMessageFound}`);
            
            // Check for the video element which is part of the new chat screen in the new UI
            const videoSelectors = [
                // Exact selector from provided HTML
                'video.w-full.md\\:h-\\[207px\\].h-\\[120px\\]',
                // More generic video selectors
                'video[autoplay][loop]',
                'video:has(source[src*="SHASA"])',
                'div.flex video'
            ];
            
            for (const selector of videoSelectors) {
                try {
                    console.log(`Checking for video with selector: ${selector}`);
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
                    if (isVisible) {
                        console.log(`✅ Found video element in the new chat screen with selector: ${selector}`);
                        await ScreenshotHelper.takeScreenshot('new-chat-video-element-found');
                        return true;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            // Check for the textarea element with updated placeholder text from provided HTML
            const textareaSelectors = [
                // Exact selector from provided HTML
                'textarea.w-full.py-1.px-1.text-base.text-gray-900.placeholder-gray-400.bg-transparent[placeholder="Find"]',
                'textarea[placeholder="Find"]',
                'textarea[placeholder="Ask me anything."]',
                // Generic textarea selectors as fallback
                'textarea.w-full.py-1.px-1',
                'div.flex-1.px-2 textarea',
                'form textarea'
            ];
            
            for (const selector of textareaSelectors) {
                try {
                    console.log(`Checking for textarea with selector: ${selector}`);
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
                    if (isVisible) {
                        console.log(`✅ Found textarea with selector: ${selector}`);
                        await ScreenshotHelper.takeScreenshot('new-chat-textarea-found');
                        return true;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            // Check for send message button/icon from provided HTML
            const sendButtonSelectors = [
                // Exact selector from provided HTML
                'button[type="submit"]:has(svg.lucide.lucide-send)',
                // More generic selectors
                'button:has(svg.lucide-send)',
                'svg.lucide.lucide-send.h-5.w-5',
                '.flex.items-center.pr-4.pb-3 button'
            ];
            
            for (const selector of sendButtonSelectors) {
                try {
                    console.log(`Checking for send button with selector: ${selector}`);
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
                    if (isVisible) {
                        console.log(`✅ Found send button with selector: ${selector}`);
                        await ScreenshotHelper.takeScreenshot('new-chat-send-button-found');
                        return true;
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            // Check for other distinctive elements of the new chat UI
            const otherUiSelectors = [
                // Form container from provided HTML
                'form.relative',
                'div.flex.flex-col.bg-white.rounded-2xl.border-2',
                // Chat container elements
                'div.flex.flex-col.h-full.bg-white.overflow-hidden',
                'div.flex.flex-col.h-full.tk-proxima-nova.overflow-hidden',
                'div.overflow-y-auto.bg-white.h-full',
                // Input container
                'div.fixed.bottom-0.right-0.z-50',
                'div.flex.justify-center.px-12.py-4',
                // Empty chat indicators
                'div.flex.flex-col.items-center.justify-center.min-h-\\[60vh\\].text-center',
                'div.space-y-6',
                // Any visible chat UI element
                'div.max-w-3xl.mx-auto.md\\:px-4.py-6.px-2'
            ];
            
            for (const selector of otherUiSelectors) {
                try {
                    console.log(`Checking for UI element with selector: ${selector}`);
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
                    if (isVisible) {
                        console.log(`✅ Found chat UI element with selector: ${selector}`);
                        
                        // If we already found the welcome message, this is definitely a success
                        if (welcomeMessageFound) {
                            console.log('New chat confirmed via welcome message and chat UI elements');
                            await ScreenshotHelper.takeScreenshot('new-chat-ui-elements-with-welcome');
                            return true;
                        } else {
                            // Even without welcome message, if we found the chat UI elements it's likely a new chat
                            console.log('New chat confirmed via chat UI elements (no welcome message)');
                            await ScreenshotHelper.takeScreenshot('new-chat-ui-elements-only');
                            return true;
                        }
                    }
                } catch (error) {
                    // Continue to next selector
                }
            }
            
            // Fall back to checking if a previous chat was cleared
            try {
                // Check for absence of chat messages which would indicate a new chat
                const messageCount = await fixture.page.evaluate(() => {
                    // Look for common message elements
                    const messages = document.querySelectorAll('.chat-message, div.chat-message, div.flex.justify-start, div.flex.justify-end');
                    return messages.length;
                });
                
                console.log(`Found ${messageCount} message elements in the chat`);
                if (messageCount === 0) {
                    console.log('New chat confirmed by absence of previous messages');
                    await ScreenshotHelper.takeScreenshot('new-chat-no-messages');
                    return true;
                }
            } catch (error) {
                console.log(`Error counting messages: ${error}`);
            }
            
            console.log('Could not confirm new chat was started');
            await ScreenshotHelper.takeScreenshot('new-chat-verification-failed', true);
            return false;
        } catch (error) {
            console.error('Error verifying new chat:', error);
            await ScreenshotHelper.takeScreenshot('new-chat-verification-error', true);
            return false;
        }
    }
      /**
     * Types a message and sends it
     * @param message The message to send
     */
    public static async sendMessage(message: string): Promise<boolean> {
        console.log(`Sending message: "${message}"`);
        
        try {
            // Number of retry attempts if the first try fails
            const maxRetries = 3;
            let success = false;
            
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    if (attempt > 1) {
                        console.log(`Retry attempt ${attempt} of ${maxRetries} to send message`);
                        await fixture.page.waitForTimeout(1000); // Wait before retry
                    }
                    
                    // Type the message
                    const messageTyped = await this.typeMessage(message);
                    if (!messageTyped) {
                        console.error('Failed to type message');
                        await ScreenshotHelper.takeScreenshot(`failed-type-message-attempt-${attempt}`, true);
                        continue; // Try again
                    }
                    
                    // Wait a moment before clicking send
                    await fixture.page.waitForTimeout(1000);
                    
                    // Click the send button
                    const sendClicked = await this.clickSendButton();
                    if (!sendClicked) {
                        console.error('Failed to click send button');
                        await ScreenshotHelper.takeScreenshot(`failed-click-send-attempt-${attempt}`, true);
                        continue; // Try again
                    }
                    
                    // Wait for message to be sent and potentially for a response
                    await fixture.page.waitForTimeout(2000);
                    
                    // Verify message was actually sent by checking for elements that would indicate success
                    // This could be checking for the message in the chat history or other indicators
                    const messageSent = await this.verifyMessageWasSent(message);
                    
                    if (messageSent) {
                        console.log('Message sent successfully');
                        success = true;
                        break; // Exit the retry loop
                    } else {
                        console.log('Could not verify message was sent, may retry');
                    }
                } catch (attemptError) {
                    console.error(`Error in send attempt ${attempt}:`, attemptError);
                    await ScreenshotHelper.takeScreenshot(`send-attempt-${attempt}-error`, true);
                    
                    // If it's not the last attempt, we'll try again
                    if (attempt < maxRetries) {
                        continue;
                    }
                }
            }
            
            return success;
        } catch (error) {
            console.error('Error sending message:', error);
            await ScreenshotHelper.takeScreenshot('message-send-error', true);
            return false;
        }
    }
    
    /**
     * Verifies that a message was successfully sent by checking the chat interface
     * @param message The message that was sent
     */
    private static async verifyMessageWasSent(message: string): Promise<boolean> {
        try {
            console.log('Verifying message was sent...');
            
            // Wait a moment for any animations or UI updates
            await fixture.page.waitForTimeout(1000);
            
            // Look for user message in the chat history
            // This is a basic check - might need refinement based on actual app behavior
            const userMessageVisible = await fixture.page.isVisible(`div:has-text("${message.substring(0, 20)}")`, 
                { timeout: 3000 }).catch(() => false);
                
            // Look for any indication that the AI is responding or has responded
            const responseIndicators = [
                'div:has-text("processing")',
                'div.typing-indicator',
                'div:has-text("looking for flights")',
                'div.chat-message.ai',
                'div[aria-label="AI response"]',
                // Additional indicators from UI
                'div:has-text("searching")',
                'div.flex.gap-2.items-start.justify-end',
                'div.w-8.h-8.rounded-full.bg-white.flex.items-center.justify-center.ml-2',
                'img[src*="/SHASA.png"]'
            ];
            
            let responseDetected = false;
            for (const indicator of responseIndicators) {
                const isVisible = await fixture.page.isVisible(indicator, { timeout: 2000 })
                    .catch(() => false);
                    
                if (isVisible) {
                    console.log(`Response detected with indicator: ${indicator}`);
                    responseDetected = true;
                    break;
                }
            }
            
            // Check if input field or textarea is now empty (indicating message was sent)
            const inputEmpty = await this.isInputFieldEmpty();
            
            // If we can see the message or a response indicator or input is now empty, consider it successful
            const success = userMessageVisible || responseDetected || inputEmpty;
            
            if (success) {
                console.log('Message verification successful');
                await ScreenshotHelper.takeScreenshot('message-sent-verified');
            } else {
                console.log('Could not verify message was sent');
                await ScreenshotHelper.takeScreenshot('message-verification-failed');
            }
            
            return success;
        } catch (error) {
            console.error('Error verifying message was sent:', error);
            return false;
        }
    }
    
    /**
     * Helper method to check if input field or textarea is empty
     * @returns true if either input field or textarea is visible and empty
     */
    private static async isInputFieldEmpty(): Promise<boolean> {
        try {
            // Check input field
            const inputField = await fixture.page.locator(this.elements.messageInput);
            const inputVisible = await inputField.isVisible().catch(() => false);
            
            if (inputVisible) {
                const inputValue = await inputField.inputValue().catch(() => null);
                if (inputValue === '') {
                    console.log('Input field is empty');
                    return true;
                }
            }
            
            // Check textarea
            const textareaField = await fixture.page.locator(this.elements.messageTextarea);
            const textareaVisible = await textareaField.isVisible().catch(() => false);
            
            if (textareaVisible) {
                const textareaValue = await textareaField.inputValue().catch(() => null);
                if (textareaValue === '') {
                    console.log('Textarea is empty');
                    return true;
                }
            }
            
            return false;
        } catch (error) {
            console.error('Error checking if input field is empty:', error);
            return false;
        }
    }

    /**
     * Clicks the New Chat button
     * Enhanced with Playwright's modern locators
     */
    public static async clickNewChatButton(): Promise<boolean> {
        console.log('Attempting to click New Chat button');
        
        try {
            // First handle any cookie consent or overlays that might be present
            await this.handleCookieConsentIfPresent();
            await this.dismissAnyOverlays();
            
            // Take a screenshot of the current state
            await ScreenshotHelper.takeScreenshot('before-searching-new-chat-button');
            
            // Try modern Playwright locators first (preferred method)
            console.log('Trying to locate New Chat button with modern locators');
            
            // Try with getByRole - most semantic approach
            const buttonByRole = fixture.page.getByRole('button', { name: 'New Chat', exact: true });
            if (await buttonByRole.isVisible({ timeout: 3000 }).catch(() => false)) {
                console.log('Found New Chat button with getByRole locator');
                await buttonByRole.click();
                console.log('Successfully clicked New Chat button using getByRole');
                
                // Wait for the UI to update
                await fixture.page.waitForTimeout(2000);
                await ScreenshotHelper.takeScreenshot('after-new-chat-button-click');
                
                return true;
            }
            
            // Try with case-insensitive match as fallback
            const buttonByRoleInsensitive = fixture.page.getByRole('button', { name: /new chat/i });
            if (await buttonByRoleInsensitive.isVisible({ timeout: 2000 }).catch(() => false)) {
                console.log('Found New Chat button with case-insensitive getByRole locator');
                await buttonByRoleInsensitive.click();
                console.log('Successfully clicked New Chat button using case-insensitive getByRole');
                
                // Wait for the UI to update
                await fixture.page.waitForTimeout(2000);
                await ScreenshotHelper.takeScreenshot('after-new-chat-button-click');
                
                return true;
            }
            
            // Try with getByText as another fallback
            const buttonByText = fixture.page.getByText('New Chat', { exact: true });
            if (await buttonByText.isVisible({ timeout: 2000 }).catch(() => false)) {
                // Check if this is actually a button containing the text
                const parentButton = buttonByText.locator('xpath=./ancestor::button');
                if (await parentButton.isVisible({ timeout: 1000 }).catch(() => false)) {
                    console.log('Found New Chat button with getByText and ancestor locator');
                    await parentButton.click();
                    
                    // Wait for the UI to update
                    await fixture.page.waitForTimeout(2000);
                    await ScreenshotHelper.takeScreenshot('after-new-chat-button-click');
                    
                    return true;
                } else {
                    // Click the text element directly if it's clickable
                    console.log('Found New Chat text element, attempting to click');
                    await buttonByText.click();
                    
                    // Wait for the UI to update
                    await fixture.page.waitForTimeout(2000);
                    await ScreenshotHelper.takeScreenshot('after-new-chat-text-click');
                    
                    return true;
                }
            }
            
            // If modern locators failed, fall back to legacy selectors for backward compatibility
            console.log('Modern locators failed, trying legacy CSS selectors as fallback');
            
            // Try multiple selectors for the New Chat button based on the new UI
            const selectors = [
                // Original selectors
                this.elements.newChatButton,
                this.elements.newChatButtonAlt1,
                this.elements.newChatButtonAlt2,
                this.elements.newChatButtonAlt3,
                this.elements.newChatButtonAlt4,
                // Updated selectors for new UI
                'button:has-text("New Chat")',
                'button:has-text("New chat")',
                'button:has(svg.lucide-square-pen)',
                // More generic selectors that might work with new UI
                'button.inline-flex',
                'nav button:first-child',
                'nav button:first-of-type',
                'button:has(svg[data-lucide="plus-square"])', 
                'button:has(svg[data-lucide="square-pen"])',
                // Selectors based on class patterns in new UI
                'button.rounded-md',
                'button.bg-\\[linear-gradient',
                'button.text-\\[\\#F2F3FA\\]'
            ];
            
            // Log all visible buttons to help with debugging
            console.log('Scanning for all possible buttons...');
            const allButtons = await fixture.page.locator('button').count();
            console.log(`Found ${allButtons} total buttons on page`);
            
            // If we see at least one button, take a more detailed inventory
            if (allButtons > 0) {
                await fixture.page.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    console.log('Button inventory:', 
                        buttons.map(btn => ({
                            text: btn.textContent?.trim(),
                            classes: btn.className,
                            hasIcon: btn.querySelector('svg') !== null
                        }))
                    );
                });
            }
            
            for (const selector of selectors) {
                try {
                    console.log(`Trying to locate New Chat button with selector: ${selector}`);
                    
                    // Use a longer timeout to ensure we find the button
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`✅ Found New Chat button with selector: ${selector}`);
                        
                        // Take a screenshot before clicking for debugging
                        await ScreenshotHelper.takeScreenshot('before-new-chat-button-click');
                        
                        // Click the button with a more reliable click method
                        try {
                            // First try the waitAndClick helper
                            await this.waitAndClick(selector);
                            
                            // Take a screenshot after clicking
                            await ScreenshotHelper.takeScreenshot('after-new-chat-button-click');
                            
                            // Add a wait to let the UI update
                            await fixture.page.waitForTimeout(2000);
                            
                            console.log('Successfully clicked New Chat button');
                            return true;
                        } catch (clickError) {
                            console.log(`Standard click failed: ${clickError}, trying force click`);
                            
                            // If the helper failed, try a force click directly through evaluate
                            await fixture.page.evaluate((sel) => {
                                const btn = document.querySelector(sel);
                                if (btn && btn instanceof HTMLElement) {
                                    (btn as HTMLElement).click();
                                    console.log('Force clicked button via JavaScript');
                                    return true;
                                }
                                return false;
                            }, selector);
                            
                            // Add a wait after force click
                            await fixture.page.waitForTimeout(2000);
                            
                            console.log('Attempted force click on New Chat button');
                            await ScreenshotHelper.takeScreenshot('after-force-click-new-chat');
                            return true;
                        }
                    }
                } catch (error) {
                    console.log(`Failed to interact with selector ${selector}: ${error}`);
                }
            }
            
            // If we get here, we couldn't find or click the button using standard selectors
            console.log('Standard selectors failed, trying generic UI navigation approach');
            
            // Try to find any button that might be the new chat button based on position or appearance
            try {
                // Look for buttons in the left sidebar or navigation area
                const navButtons = await fixture.page.$$('nav button, aside button, [data-sidebar] button');
                if (navButtons && navButtons.length > 0) {
                    console.log(`Found ${navButtons.length} buttons in navigation areas`);
                    
                    // Try clicking the first button which is often the "New" action
                    await navButtons[0].click();
                    console.log('Clicked first navigation button as fallback');
                    await fixture.page.waitForTimeout(2000);
                    await ScreenshotHelper.takeScreenshot('after-nav-button-click');
                    return true;
                }
            } catch (navError) {
                console.log(`Navigation button approach failed: ${navError}`);
            }
            
            // If we get here, we couldn't find or click the button
            console.error('Failed to find or click the New Chat button after trying all approaches');
            await ScreenshotHelper.takeScreenshot('new-chat-button-not-found', true);
            return false;
        } catch (error) {
            console.error('Error clicking New Chat button:', error);
            await ScreenshotHelper.takeScreenshot('new-chat-button-click-error', true);
            return false;
        }
    }

    /**
     * Handles any cookie consent dialog that might be present and blocking UI elements
     */
    private static async handleCookieConsentIfPresent(): Promise<void> {
        console.log('Checking for cookie consent dialog');
        
        // Common selectors for cookie consent buttons
        const cookieConsentSelectors = [
            'button:has-text("Accept")',
            'button:has-text("Accept All")',
            'button:has-text("I Agree")',
            'button:has-text("Allow All")',
            'button:has-text("OK")',
            'button:has-text("Got it")',
            'button.cookie-consent-button',
            'div[role="dialog"] button:has-text("Accept")',
            'div.cookie-banner button',
            'div[id*="cookie"] button',
            '[aria-label="Cookie Consent"] button'
        ];
        
        // Try each selector
        for (const selector of cookieConsentSelectors) {
            try {
                // Check if the consent button is visible with a short timeout
                const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                    .catch(() => false);
                
                if (isVisible) {
                    console.log(`Found cookie consent button with selector: ${selector}`);
                    
                    // Take a screenshot before clicking
                    await ScreenshotHelper.takeScreenshot('before-cookie-consent-click');
                    
                    // Click the button
                    await fixture.page.click(selector);
                    
                    console.log('Successfully clicked cookie consent button');
                    
                    // Wait for the dialog to disappear
                    await fixture.page.waitForTimeout(1000);
                    return;
                }
            } catch (error) {
                console.log(`Error with cookie consent selector ${selector}: ${error}`);
            }
        }
        
        console.log('No cookie consent dialog found or it was already handled');
    }

    /**
     * Attempts to dismiss any overlays, modals or other elements that might be blocking interaction
     * This method tries various approaches to ensure we can interact with the UI
     */
    private static async dismissAnyOverlays(): Promise<void> {
        console.log('Checking for and dismissing any overlays or modals');
        
        try {
            // Common selectors for close buttons, overlay dismissal, etc.
            const dismissSelectors = [
                // Close buttons
                'button:has-text("Close")',
                'button:has-text("Dismiss")',
                'button[aria-label="Close"]',
                '[role="dialog"] button:has(svg)',
                '.modal-close',
                '.close-button',
                
                // Cookie consent specific
                'button:has-text("Accept")',
                'button:has-text("Accept All Cookies")',
                'button:has-text("I Agree")',
                'button:has-text("Allow All")',
                
                // Other common dismissal patterns
                'button:has(svg.lucide-x)',
                'div[role="button"]:has(svg.lucide-x)',
                'div.modal-backdrop',
                '.overlay'
            ];
            
            // Try each selector
            for (const selector of dismissSelectors) {
                try {
                    // Check if the element is visible
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 1000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`Found overlay/modal element with selector: ${selector}`);
                        
                        // Take a screenshot before dismissing
                        await ScreenshotHelper.takeScreenshot('before-overlay-dismiss');
                        
                        // Click the element
                        await fixture.page.click(selector);
                        
                        console.log('Clicked overlay dismissal element');
                        
                        // Wait for any animations to complete
                        await fixture.page.waitForTimeout(500);
                    }
                } catch (error) {
                    // Just log and continue trying other selectors
                    console.log(`Error with overlay selector ${selector}: ${error}`);
                }
            }
            
            // Check for backdrop/overlay element that might need an ESC key
            const hasBackdrop = await fixture.page.isVisible('div.modal-backdrop, div.overlay, div[role="dialog"]', 
                { timeout: 1000 }).catch(() => false);
                
            if (hasBackdrop) {
                console.log('Detected backdrop/overlay, trying to dismiss with ESC key');
                await fixture.page.keyboard.press('Escape');
                await fixture.page.waitForTimeout(500);
            }
            
            // Check for any other blocking overlays, try clicking away from them
            const hasAnyModal = await fixture.page.isVisible('[role="dialog"], .modal, .popup', 
                { timeout: 1000 }).catch(() => false);
                
            if (hasAnyModal) {
                console.log('Detected modal/popup, trying alternative dismissal approaches');
                
                // Try clicking elsewhere on the page (background)
                await fixture.page.mouse.click(10, 10);
                await fixture.page.waitForTimeout(500);
            }
            
            console.log('Finished checking for overlays');
        } catch (error) {
            console.error('Error in dismissing overlays:', error);
        }
    }

    /**
     * Waits for flight search results to be shown in the chat interface
     * This method will wait until Shasa's flight search results are completely loaded
     */
    public static async waitForFlightSearchResults(timeoutMs: number = 30000): Promise<boolean> {
        console.log('Waiting for flight search results to be shown...');
        
        try {
            // Take an initial screenshot
            await ScreenshotHelper.takeScreenshot('before-flight-search-results');
            
            // Start time for our custom wait implementation
            const startTime = Date.now();
            let resultsShown = false;
            const checkInterval = 2000; // Check every 2 seconds
            
            // Define modern locators for flight results detection
            const resultIndicators = {
                // Text indicators using getByText
                texts: ['Best Value', 'More Flex', 'Fully Flexible', 'Duration', 
                       'Connect', 'for all passengers', 'per person', 'Show more flights'],
                
                // Button indicators using getByRole
                buttons: ['Show more flights', 'Select Flight', 'Select'],
                
                // Labels that might be present in flight results
                labels: ['Flight', 'Airline', 'Departure', 'Arrival', 'Price']
            };
            
            // Also define legacy CSS selectors as fallbacks
            const legacySelectors = [
                '.rounded-2xl.shadow-lg.overflow-hidden',
                'div:has-text("Best Value")',
                'div:has-text("More Flex")',
                'div:has-text("Fully Flexible")',
                'img[src*="flight-travel.svg"]',
                'div:has-text("Duration")',
                'div:has-text("Connect")',
                'button:has-text("Show more flights")'
            ];
            
            while (Date.now() - startTime < timeoutMs && !resultsShown) {
                console.log(`Checking for flight results (elapsed: ${Math.floor((Date.now() - startTime)/1000)}s)...`);
                
                // Try text-based indicators first (most reliable)
                for (const text of resultIndicators.texts) {
                    const textElement = fixture.page.getByText(text, { exact: false });
                    if (await textElement.isVisible({ timeout: 1000 }).catch(() => false)) {
                        console.log(`Found flight search results with text indicator: "${text}"`);
                        resultsShown = true;
                        break;
                    }
                }
                
                // If text indicators didn't find anything, try buttons
                if (!resultsShown) {
                    for (const buttonName of resultIndicators.buttons) {
                        const buttonElement = fixture.page.getByRole('button', { name: buttonName, exact: false });
                        if (await buttonElement.isVisible({ timeout: 1000 }).catch(() => false)) {
                            console.log(`Found flight search results with button: "${buttonName}"`);
                            resultsShown = true;
                            break;
                        }
                    }
                }
                
                // If still not found, try legacy selectors
                if (!resultsShown) {
                    for (const selector of legacySelectors) {
                        if (await fixture.page.isVisible(selector, { timeout: 1000 }).catch(() => false)) {
                            console.log(`Found flight search results with legacy selector: ${selector}`);
                            resultsShown = true;
                            break;
                        }
                    }
                }
                
                // If found results, break out of the loop
                if (resultsShown) break;
                
                // Wait before checking again
                await fixture.page.waitForTimeout(checkInterval);
                
                // Take periodic screenshots
                if ((Date.now() - startTime) % 10000 < checkInterval) {
                    await ScreenshotHelper.takeScreenshot(`flight-search-waiting-${Math.floor((Date.now() - startTime)/1000)}s`);
                }
            }
            
            // Result after timeout or successful detection
            if (resultsShown) {
                console.log('Flight search results successfully found');
                await ScreenshotHelper.takeScreenshot('flight-search-results-found');
                return true;
            } else {
                console.log('Flight search results not found within timeout period');
                await ScreenshotHelper.takeScreenshot('flight-search-results-timeout', true);
                return false;
            }
        } catch (error) {
            console.error('Error waiting for flight search results:', error);
            await ScreenshotHelper.takeScreenshot('flight-search-results-error', true);
            return false;
        }
    }
    
    /**
     * Check if the "Show more flights" button is enabled
     * @returns Promise resolving to true if button is found and enabled, false otherwise
     */
    public static async isShowMoreFlightsButtonEnabled(): Promise<boolean> {
        console.log('Checking if "Show more flights" button is enabled');
        
        try {
            // First try with modern locators
            const showMoreButton = fixture.page.getByRole('button', { name: /show more flights/i });
            if (await showMoreButton.isVisible({ timeout: 3000 }).catch(() => false)) {
                // Check if button is not disabled
                const isDisabled = await showMoreButton.getAttribute('disabled').catch(() => null);
                const isEnabled = isDisabled === null || isDisabled !== 'true';
                
                if (isEnabled) {
                    console.log('"Show more flights" button is visible and enabled');
                    await ScreenshotHelper.takeScreenshot('show-more-flights-button-enabled');
                    return true;
                } else {
                    console.log('"Show more flights" button is visible but disabled');
                    await ScreenshotHelper.takeScreenshot('show-more-flights-button-disabled');
                    return false;
                }
            }
            
            // If modern locator failed, try with CSS selectors
            const buttonSelectors = [
                'button:has-text("Show more flights")',
                'button.text-white.bg-gradient-to-r:has-text("Show more flights")',
                'div.ml-16 button'
            ];
            
            for (const selector of buttonSelectors) {
                try {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                        .catch(() => false);
                        
                    if (isVisible) {
                        console.log(`Found "Show more flights" button with selector: ${selector}`);
                        
                        // Check if the button is enabled (not disabled)
                        const isDisabled = await fixture.page.locator(selector).getAttribute('disabled')
                            .catch(() => null);
                            
                        const isEnabled = isDisabled === null || isDisabled !== 'true';
                        
                        if (isEnabled) {
                            console.log('"Show more flights" button is enabled');
                            await ScreenshotHelper.takeScreenshot('show-more-flights-button-enabled');
                            return true;
                        } else {
                            console.log('"Show more flights" button is disabled');
                            await ScreenshotHelper.takeScreenshot('show-more-flights-button-disabled');
                            return false;
                        }
                    }
                } catch (error) {
                    console.log(`Error checking selector ${selector}:`, error);
                }
            }
            
            console.log('Could not find "Show more flights" button');
            await ScreenshotHelper.takeScreenshot('show-more-flights-button-not-found', true);
            return false;
        } catch (error) {
            console.error('Error checking if "Show more flights" button is enabled:', error);
            await ScreenshotHelper.takeScreenshot('show-more-flights-button-check-error', true);
            return false;
        }
    }

    /**
     * Clicks the "Show more flights" button if it's enabled
     * @returns true if button was found and clicked successfully, false otherwise
     */
    public static async clickShowMoreFlightsButton(): Promise<boolean> {
        console.log('Attempting to click "Show more flights" button');
        
        try {
            // Take a screenshot before attempting to click
            await ScreenshotHelper.takeScreenshot('before-show-more-flights-click');
            
            // First try with modern locators
            const showMoreButton = fixture.page.getByRole('button', { name: /show more flights/i });
            if (await showMoreButton.isVisible({ timeout: 3000 }).catch(() => false)) {
                // Check if button is enabled
                const isDisabled = await showMoreButton.getAttribute('disabled').catch(() => null);
                const isEnabled = isDisabled === null || isDisabled !== 'true';
                
                if (!isEnabled) {
                    console.log('"Show more flights" button is visible but disabled');
                    await ScreenshotHelper.takeScreenshot('show-more-flights-button-disabled', true);
                    return false;
                }
                
                // Click the button
                await showMoreButton.click({ timeout: 3000 });
                console.log('Successfully clicked "Show more flights" button with modern locator');
                await fixture.page.waitForTimeout(2000);
                await ScreenshotHelper.takeScreenshot('after-show-more-flights-click');
                return true;
            }
            
            // If modern locator failed, try with the method that checks if the button is enabled
            const isEnabled = await this.isShowMoreFlightsButtonEnabled();
            
            if (!isEnabled) {
                console.error('"Show more flights" button is not enabled or not found');
                await ScreenshotHelper.takeScreenshot('show-more-flights-button-not-clickable', true);
                return false;
            }
            
            // Wait a moment for any UI updates or animations to complete
            await fixture.page.waitForTimeout(2000);
            
            // Define selectors for the Show more flights button
            const buttonSelectors = [
                'button:has-text("Show more flights")',
                'button.text-white.bg-gradient-to-r:has-text("Show more flights")',
                'button.rounded-full.text-white:has-text("Show more flights")',
                'button.px-4.py-2:has-text("Show more flights")',
                // More generalized selectors as fallbacks
                'button:has-text("more flights")',
                'button.bg-gradient-to-r'
            ];
            
            // Try each selector until we find and click the button
            for (const selector of buttonSelectors) {
                try {
                    console.log(`Trying to locate "Show more flights" button with selector: ${selector}`);
                    
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 })
                        .catch(() => false);
                    
                    if (isVisible) {
                        console.log(`Found "Show more flights" button with selector: ${selector}`);
                        
                        // Click the button
                        await this.waitAndClick(selector);
                        
                        console.log('Successfully clicked "Show more flights" button');
                        
                        // Take a screenshot after clicking
                        await ScreenshotHelper.takeScreenshot('after-show-more-flights-click');
                        
                        // Wait for a moment for any loading or UI updates to complete
                        await fixture.page.waitForTimeout(2000);
                        
                        return true;
                    }
                } catch (error) {
                    console.log(`Failed to interact with selector ${selector}: ${error}`);
                }
            }
            
            // If we get here, we couldn't find or click the button
            console.error('Failed to find or click the "Show more flights" button');
            await ScreenshotHelper.takeScreenshot('show-more-flights-click-failed', true);
            return false;
            
        } catch (error) {
            console.error('Error clicking "Show more flights" button:', error);
            await ScreenshotHelper.takeScreenshot('show-more-flights-click-error', true);
            return false;
        }
    }
}