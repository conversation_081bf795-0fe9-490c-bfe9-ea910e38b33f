import { fixture } from '../fixtures/Fixture';
import { WaitHelper } from '../waits/WaitHelper';

/**
 * Helper class to handle cookie consent dialogs during test automation
 */
export class ConsentHelper {
    // Selectors for the cookie consent dialog
    private static readonly CONSENT_DIALOG_SELECTOR = '.cky-consent-bar';
    private static readonly ACCEPT_BUTTON_SELECTOR = '.cky-btn-accept';
    private static readonly REJECT_BUTTON_SELECTOR = '.cky-btn-reject';
    private static readonly CUSTOMIZE_BUTTON_SELECTOR = '.cky-btn-customize';

    /**
     * Handles the cookie consent dialog if it appears
     * @param action - The action to take: 'accept', 'reject', or 'customize'. Default is 'accept'
     * @param timeout - Maximum time to wait for the consent dialog in milliseconds
     * @returns true if the consent dialog was handled, false if it wasn't found
     */
    public static async handleConsentDialog(action: 'accept' | 'reject' | 'customize' = 'accept', timeout: number = 5000): Promise<boolean> {
        try {
            // Wait for the consent dialog with a reasonable timeout
            const isDialogVisible = await <PERSON>H<PERSON>per.waitForSelectorWithRetry(
                this.CONSENT_DIALOG_SELECTOR, 
                { state: 'visible', timeout },
                2
            );

            if (!isDialogVisible) {
                console.log('No cookie consent dialog detected');
                return false;
            }

            console.log(`Cookie consent dialog detected, handling with action: ${action}`);

            // Select the appropriate button based on the requested action
            let buttonSelector: string;
            switch (action) {
                case 'accept':
                    buttonSelector = this.ACCEPT_BUTTON_SELECTOR;
                    break;
                case 'reject':
                    buttonSelector = this.REJECT_BUTTON_SELECTOR;
                    break;
                case 'customize':
                    buttonSelector = this.CUSTOMIZE_BUTTON_SELECTOR;
                    break;
                default:
                    buttonSelector = this.ACCEPT_BUTTON_SELECTOR;
            }

            // Click the button and wait for the dialog to disappear
            await fixture.page.click(buttonSelector);
            
            // Verify the dialog is gone
            await fixture.page.waitForSelector(this.CONSENT_DIALOG_SELECTOR, {
                state: 'hidden',
                timeout: 5000
            });
            
            console.log('Successfully handled cookie consent dialog');
            return true;
        } catch (error) {
            console.log(`Error handling cookie consent dialog: ${error}`);
            return false;
        }
    }
}