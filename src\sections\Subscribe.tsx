import { footerLinks, socialMedia } from "@/constants";
import Image from "next/image";
import { useState } from "react";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { postMethod } from "@/utils/api";

type SubscriptionType = {
  subscribed: boolean;
  success: boolean;
  error?: string;
};

const Subscribe = () => {
  const [email, setEmail] = useState("");
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [subscription, setSubscription] = useState<SubscriptionType>({
    subscribed: false,
    success: false,
    error: "",
  });

  const handleSubscribe = async () => {
    if (!validateEmail(email)) {
      setSubscription({
        subscribed: false,
        success: false,
        error: "Invalid email address. Please enter a valid email.",
      });
      return;
    }

    if (!executeRecaptcha) {
      setSubscription({
        subscribed: false,
        success: false,
        error: "Something went wrong. Please try again.",
      });
      return;
    }

    try {
      setSubscription({
        subscribed: false,
        success: false,
        error: "",
      });
      const token = await executeRecaptcha("search_submit");
      await submitEmail(token);
    } catch (error: any) {
      if (error?.detail?.message === "Email already subscribed") {
        setSubscription({
          subscribed: false,
          success: false,
          error: "Email already subscribed.",
        });
        return;
      }
      setSubscription({
        subscribed: false,
        success: false,
        error: "Something went wrong. Please try again.",
      });
    }
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const submitEmail = async (token: string) => {
    return new Promise((resolve, reject) => {
      const method = "join-now";
      const param = { email: email, sessionid: token };
      postMethod(method, param)
        .then((response) => {
          if (response?.detail?.status === "success") {
            setSubscription({
              subscribed: true,
              success: true,
              error: "",
            });
            resolve(response);
          } else {
            reject(response);
          }
        })
        .catch((error) => reject(error));
    });
  };

  return (
    <section
      id="subscribe"
      className="relative flex justify-center 
      min-h-screen bg-cover bg-center"
      style={{
        backgroundImage:
          "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/background.png')",
      }}
    >
      <div className="flex flex-col md:w-[85%] sm:w-[90%] xs:w-[90%] mx-auto">
        <div className="flex flex-row w-full lg:gap-6 md:gap-2 sm:gap-2 xs:gap-2 sm:mx-2">
          <div className="lg:w-1/3 md:w-1/3 sm:w-1/3 xs:w-1/3 mt-5">
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/Hello-Shasa%202.png"
              alt="shasa"
              className="w-full h-full"
            />
          </div>
          <div className="flex lg:w-2/3 md:w-2/3 sm:w-2/3 xs:w-2/3 items-center">
            <div className="bg-gradient-to-r from-[#4D4DC5] via-[#707FF5] text-transparent to-[#A195F9] bg-clip-text w-[85%] mx-auto font-proxima-nova font-bold xs:text-[15px] text-[13px] xs:w-[90%] sm:w-[85%] flex items-center sm:text-[20px] 2xl:text-[40px] xl:text-[36px] lg:text-[32px] md:text-[24px] leading-[1] xs:leading-[1] tracking-normal mt-12 xs:mt-8">
              “<br />
              I travel with you at every moment. I’m your map, your memo, and
              your personal travel assistant! After all, every great adventure
              needs a sidekick, and I’m yours! <br />
              ”<br />- SHASA
            </div>
          </div>
        </div>
        <div
          className={`w-full md:w-[90%] lg:w-[80%] bg-gradient-to-r from-[#1E1E76] from-0% via-[#4B4BC3] via-[49%] to-[#707FF5] to-100%
          border-spacing-1 rounded-2xl h-auto mx-auto xs:p-2 mt-10 sm:mt-10 lg:mt-20`}
        >
          <p
            className="text-white font-proxima-nova font-bold text-sm 
              sm:text-lg lg:text-2xl xs:text-sm text-center sm:py-2 py-2"
          >
            Shasa’s Travel Newsletter - Stay in the Loop with ME!
          </p>
          <p className="text-white font-proxima-nova font-medium px-[15px] lg:text-base md:text-sm sm:text-[13px] xs:text-sm text-center">
            Hey traveller! I’ve got the best deals, smart travel hacks, and
            insider tips waiting for you.
          </p>
          <p className="text-white font-proxima-nova font-medium lg:text-base md:text-sm sm:text-[13px] xs:text-sm text-center">
            No spam, just pure wanderlust! Hop in?
          </p>
          {
            <p
              className={`lg:text-lg md:text-base font-medium text-sm text-center px-2 my-2 ${subscription.success ? "text-[#11FF25]" : "text-[#FFC917]"
                }`}
            >
              {subscription.success
                ? "You're all set! Thanks for subscribing. Get ready for travel tips, deals, and updates!"
                : subscription.error}
            </p>
          }
          <div className="flex justify-center my-5">
            <div
              className="sm:w-[70%] lg:w-[60%] w-full items-center flex
                gap-2 sm:gap-5 sm:border sm:border-white
                rounded-[8px] bg-white pl:2.5 mx-5"
            >
              <input
                type="text"
                placeholder="Enter Your e-mail id"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="outline-none sm:border-none ml-2 sm:pl-5
                  sm:flex-1 xs:w-full max-sm:w-full leading-normal
                  font-proxima-nova font-medium text-sm sm:text-base text-neutral-dark"
              />
              <div
                className="flex justify-end max-sm:justify-end w-max items-center
                  bg-gradient-to-tr  rounded-[8px] from-[#4B4BC3] via-[#707FF5] to-[#A195F9]"
              >
                <button
                  className="rounded-[8px] px-3 py-2 sm:px-5 sm:py-3 font-proxima-nova text-base text-white 
                    font-semibold sm:font-normal w-[120px] sm:w-[170px] bg-brand"
                  onClick={handleSubscribe}
                >
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
        {/* <hr
          className="w-full mt-14 border-0 h-0.5"
          style={{
            background:
              "linear-gradient(to left, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
          }}
        /> */}
        <div className="mt-10 xs:mt-5 xs:gap-0 mx-auto w-full md:flex md:flex-row xs:flex-col sm:flex-col gap-5 flex-wrap">
          <div className="flex flex-col md:w-1/3 xs:w-full sm:w-full items-center xs:text-center sm:text-center md:text-start md:items-start">
            <a href="/">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png"
                alt="logo"
                className="w-52 xs:w-40 h-10 object-contain"
              />
            </a>
            <p className="mt-6 pl-4 xs:mt-3 sm:mt-4 font-proxima-nova font-medium max-md:text-center text-sm lg:text-lg md:text-base xs:w-[80%] sm:w-[80%] md:w-[100%] xs:mx-auto md:max-w-lg text-white">
              Plan smarter; travel better! You now have your personal AI trip
              planner, travel assistant, and itinerary expert at your service!
            </p>
          </div>
          <hr
            className="md:hidden w-full mt-5 border-0 h-[0.5px]"
            style={{
              background:
                "linear-gradient(to left, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
            }}
          />
          <div className="flex flex-1 flex-col md:w-2/3 xs:gap-6 sm:gap-6 md:gap-0 sm:w-full xs:w-full sm:justify-around xs:justify-center xs:items-center md:flex-row gap-2">
            <div className="flex justify-around md:w-1/2 xs:w-full sm:w-full xs:items-center sm:items-center xs:justify-center sm:justify-center lg:gap-10 gap-20 flex-wrap">
              {footerLinks.map((item) => (
                <div
                  key={item.title}
                  className="max-md:w-full xs:mt-2 sm:mt-2 md:mt-0 xs:w-full sm:w-full xs:justify-center sm:justify-center xs:items-center sm:items-center xs:flex xs:flex-col sm:flex sm:flex-col"
                >
                  <div className="flex flex-col w-max xs:w-full mx-auto md:items-start xs:justify-center sm:justify-center xs:items-center sm:items-center">
                    <h4
                      className="font-proxima-nova w-full sm:text-2xl xs:text-xl leading-normal 
              font-bold text-base sm:font-semibold
              text-white max-md:text-center xs:text-center"
                    >
                      {item.title}
                    </h4>
                    <ul className="max-md:flex xs:w-[90%] max-md:flex-row md:flex-col xs:flex-row xs:flex sm:flex sm:flex-row sm:justify-around w-max sm:mx-auto xs:mx-auto xs:justify-around xs:items-center items-start max-md:gap-2 max-md:justify-center max-md:items-center">
                      {item.links.map((link) => (
                        <li
                          className="mt-3 font-proxima-nova leading-normal text-white lg:text-xl md:text-lg
                            font-norml max-sm:text-sm xs:text-base xs:flex sm:flex xs:justify-center sm:justify-start xs:items-center sm:items-center cursor-pointer"
                          key={link.name}
                        >
                          {link.externalPage ? (
                            <a
                              href={link.link}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {link.name}
                            </a>
                          ) : (
                            <a href={link.link}>{link.name}</a>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
            <div className="md:hidden sm:flex xs:flex text-white sm:w-[100%] xs:w-[100%] text-center justify-center">
              Privacy Policy | Terms of Service.
            </div>
            <div className="flex flex-start md:w-1/2 sm:w-full xs:w-full xs:justify-center sm:justify-center md:items-start">
              <div className="flex flex-col xs:gap-4 sm:gap-4 md:gap-0 max-md:w-full sm:w-full xs:w-full xs:justify-center xs:items-center sm:justify-center sm:items-center ">
                <h4
                  className="font-proxima-nova sm:text-2xl xs:text-xl leading-normal 
                 text-white max-md:text-center
                font-bold text-base max-md:mt-5"
                >
                  Follow Us
                </h4>
                <div className="flex flex-row xs:w-[80%] sm:w-[80%] sm:mx-auto xs:mx-auto xs:justify-around gap-2">
                  {socialMedia.map((item) => (
                    <div
                      className="justify-center items-center flex w-full mt-2"
                      key={item.alt}
                    >
                      <Image
                        src={item.src}
                        alt={item.alt}
                        width={24}
                        height={24}
                        className="bg-none cursor-pointer"
                        onClick={() => window.open(item.link)}
                      />
                    </div>
                  ))}
                </div>
                <div className="text-white mt-10 md:flex sm:hidden xs:hidden md:text-sm lg:text-base justify-center font-medium w-full max-md:text-center">
                  <a
                    href="/privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </a>
                  <p className="px-2">|</p>
                  <a
                    href="/terms-of-service"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Terms of Service.
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* <hr
          className="w-full mt-10 border-0 h-0.5"
          style={{
            background:
              "linear-gradient(to left, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
          }}
        /> */}
        <div className="w-full my-5 flex flex-col gap-5 sm:flex-row justify-between items-center">
          <p
            className="text-white
              font-medium w-full mx-auto max:text-sm max-sm:text-center xs:text-center"
          >
            &copy; 2025 Nxvoy & Shasa. All Rights Reserved.
          </p>
          <p
            className="text-[#707FF5] sm:text-sm
            text-sm font-medium whitespace-nowrap"
          >
            Design by Unified Web Services Ltd.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Subscribe;
