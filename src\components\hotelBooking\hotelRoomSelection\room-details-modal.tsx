"use client"

import { useEffect, useRef } from "react"
import { X } from "lucide-react"
import ImageCarousel from "./image-carousel"

interface RoomDetailsModalProps {
    room: any // Enhanced room from Redux
    selectedRateIndex: number
    selectedHotel: any
    isOpen: boolean
    onClose: () => void
}

export default function RoomDetailsModal({ room, selectedRateIndex, selectedHotel, isOpen, onClose }: RoomDetailsModalProps) {
    const modalRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") onClose()
        }

        const handleClickOutside = (e: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("keydown", handleEscape)
            document.addEventListener("mousedown", handleClickOutside)
            document.body.style.overflow = "hidden"
        }

        return () => {
            document.removeEventListener("keydown", handleEscape)
            document.removeEventListener("mousedown", handleClickOutside)
            document.body.style.overflow = "auto"
        }
    }, [isOpen, onClose])

    if (!isOpen) return null

    const selectedRate = room.rate_options?.[selectedRateIndex] || room.rate_options?.[0];
    const images = room.room_images || [selectedHotel?.imageSrc || '/placeholder.jpg'];
    const facilities = room.room_facilities || [];

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
            <div ref={modalRef} className="bg-[#F2F3FA] rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
                <div className="sticky top-0 bg-[#F2F3FA] z-10 p-4 border-b flex justify-between items-center">
                    <div>
                        <h2 className="text-[30px] font-bold text-[#1E1E76]">Room Details</h2>
                        <p className="text-[#080236] font-medium text-[24px]">{room.room_name}</p>
                    </div>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                        <X className="h-6 w-6" />
                    </button>
                </div>

                <div className="p-4">
                    <div className="mb-6">
                        <ImageCarousel images={images} height={400} />
                    </div>

                    <div className="mb-6">
                        <p className="text-[#080236] mb-4 font-medium text-base">
                            {room.room_description || selectedHotel?.hotel_content?.full_description || selectedHotel?.description}
                        </p>

                        <div className="mb-4">
                            <h3 className="text-[30px] font-bold text-[#080236] mb-2">
                                128 Verified Reviews
                            </h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-1.5">
                                        <img
                                            src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                                            width={16}
                                            height={16}
                                            alt="rating star"
                                            className="object-contain m-0"
                                        />
                                        <span className="font-bold text-[#4B4BC3] text-sm">{selectedHotel?.rating || 4.0}</span>
                                    </div>
                                    <div className="text-sm font-bold text-[#080236]">Exceptional</div>
                                </div>
                                <div className="text-center">
                                    <div className="font-medium text-[#4B4BC3] text-sm">{selectedHotel?.rating || 4.0}</div>
                                    <div className="text-sm text-[#080236]">Service</div>
                                </div>
                                <div className="text-center">
                                    <div className="font-medium text-[#4B4BC3] text-sm">{selectedHotel?.rating || 4.0}</div>
                                    <div className="text-sm text-[#080236]">Location</div>
                                </div>
                                <div className="text-center">
                                    <div className="font-medium text-[#4B4BC3] text-sm">{selectedHotel?.rating || 4.0}</div>
                                    <div className="text-sm text-[#080236]">Cleanliness</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 className="text-[30px] font-bold text-[#080236] mb-4">Room Facilities</h3>
                        <div className="grid grid-cols-2 md:grid-cols-3">
                            {facilities.map((facility: string, index: number) => (
                                <div key={index} className="text-base font-medium text-[#080236]">
                                    {facility}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}