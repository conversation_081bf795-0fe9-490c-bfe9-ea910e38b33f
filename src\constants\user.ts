// types/User.ts
export type UserResponse = {
  detail: {
    status: string;
    message: string;
    data: User;
  };
};

export type User = {
  firstName: string;
  middleName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  nationality: string;
  title: string;
  email: string;
  alternatePhone: string;
  filedata: string; // base64 encoded profile image
  addresses: Address[];
  paymentCards: PaymentCard[];
  passengers: Passenger[];
  phone: string;
  profile_picture?: string;
  avatarUrl?: string;
};

export type Address = {
  address_id: string;
  memorableName: string;
  userName: string;
  streetAddress: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
};

export type PaymentCard = {
  card_id: string;
  cardHolderName: string;
  cardNumber: string;
  expiryDate: string;
  cardCVV: string;
  cardType: string;
  nickName: string;
  billingAddress: string; // refers to address_id
};

export type Passenger = {
  passenger_id?: string;
  info: PassengerInfo;
  contact: PassengerContact;
  preference: PassengerPreference;
  document: PassengerDocument;
  profile?: PassengerInfo;
};

export type PassengerInfo = {
  info_id?: string;
  firstName: string;
  middleName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  title: string;
  nationality: string;
};

export type PassengerContact = {
  contact_id?: string;
  primaryEmail: string;
  Phone: string;
};

export type PassengerPreference = {
  preference_id?: string;
  travelerType: string;
  preferredSeat: string;
  mealPreference: string;
  cabinClass: string;
  frequentFlyerNumber: string;
};

export type PassengerDocument = {
  document_id?: string;
  passport: {
    passportNumber: string;
    passportExpiryDate: string;
    issueCountry: string;
  };
  aadhar: {
    aadharNumber: string;
  };
};

export type UserDetailDelete = {
  address_id?: string;
  card_id?: string;
  contact_id?: string;
  document_id?: string;
  info_id?: string;
  passenger_id?: string;
  preference_id?: string;
};

export type UpdatePassword = {
  oldPassword: string;
  newPassword: string;
  confirm_password: string;
};
