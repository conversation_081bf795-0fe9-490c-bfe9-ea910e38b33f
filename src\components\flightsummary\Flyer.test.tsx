import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Flyer from "./Flyer";

// Mock ResizeObserver for test environment
beforeAll(() => {
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

describe("Flyer component", () => {
  it("renders collapsed by default", () => {
    render(<Flyer />);
    expect(
      screen.getByText(
        "Frequent Flyer Numbers, Known Traveler Number, Redress and more"
      )
    ).toBeInTheDocument();
    // Details section should not be visible
    expect(screen.queryByPlaceholderText("Number")).not.toBeInTheDocument();
    expect(screen.queryByPlaceholderText("Enter KTN")).not.toBeInTheDocument();
    expect(
      screen.queryByPlaceholderText("Enter Redress Number")
    ).not.toBeInTheDocument();
  });

  it("expands details section on button click", () => {
    render(<Flyer />);
    const toggleBtn = screen.getByRole("button");
    fireEvent.click(toggleBtn);
    expect(screen.getByPlaceholderText("Number")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter KTN")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter Redress Number")).toBeInTheDocument();
    expect(screen.getByText("Select Program")).toBeInTheDocument();
  });

  it("collapses details section on second button click", () => {
    render(<Flyer />);
    const toggleBtn = screen.getByRole("button");
    fireEvent.click(toggleBtn); // open
    fireEvent.click(toggleBtn); // close
    expect(screen.queryByPlaceholderText("Number")).not.toBeInTheDocument();
  });

  it("allows typing into Known Traveler Number and Redress Number fields", () => {
    render(<Flyer />);
    fireEvent.click(screen.getByRole("button"));
    const ktnInput = screen.getByPlaceholderText("Enter KTN") as HTMLInputElement;
    const redressInput = screen.getByPlaceholderText("Enter Redress Number") as HTMLInputElement;
    fireEvent.change(ktnInput, { target: { value: "KTN123" } });
    fireEvent.change(redressInput, { target: { value: "RED456" } });
    expect(ktnInput.value).toBe("KTN123");
    expect(redressInput.value).toBe("RED456");
  });

  it("allows typing into Frequent Flyer Number field", () => {
    render(<Flyer />);
    fireEvent.click(screen.getByRole("button"));
    const ffInput = screen.getByPlaceholderText("Number") as HTMLInputElement;
    fireEvent.change(ffInput, { target: { value: "78910" } });
    expect(ffInput.value).toBe("78910");
  });

  it("renders dropdown options and allows selection", async () => {
    render(<Flyer />);
    fireEvent.click(screen.getByRole("button"));
    // Open the Listbox dropdown
    const listboxBtn = screen.getByText("Select Program");
    fireEvent.mouseDown(listboxBtn); // Use mouseDown for headlessui compatibility
    // Wait for options to appear
    expect(await screen.findByText((content, element) => content.includes("SkyTeam"))).toBeInTheDocument();
    expect(await screen.findByText((content, element) => content.includes("Star Alliance"))).toBeInTheDocument();
    expect(await screen.findByText((content, element) => content.includes("Oneworld"))).toBeInTheDocument();
    // Select an option
    fireEvent.click(screen.getByText((content, element) => content.includes("SkyTeam")));
    // The button should still show "Select Program" (since no state is managed for selection)
    expect(screen.getByText("Select Program")).toBeInTheDocument();
  });

  it("renders all expected UI text and placeholders", () => {
    render(<Flyer />);
    fireEvent.click(screen.getByRole("button"));
    expect(screen.getByPlaceholderText("Number")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter KTN")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter Redress Number")).toBeInTheDocument();
    expect(screen.getByText("Select Program")).toBeInTheDocument();
  });
});