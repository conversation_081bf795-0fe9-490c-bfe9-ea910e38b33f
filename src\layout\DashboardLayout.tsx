import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import {
  SidebarInset,
  SidebarProvider,
  useSidebar,
} from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/sidebarlayout/AppSidebar";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { AppState } from "@/store/store";
import CaptachaCookieHide from "@/components/captachacookiehide/CaptachaCookieHide";

// Define page title mappings
const PAGE_TITLES: Record<string, string> = {
  "/userprofile": "My Profile",
  "/flights": "Flight Search",
  "/flightsummary": "Flights Summary",
};

// Function to get page title based on route
const getPageTitleFromRoute = (pathname: string): string => {
  // Check exact matches first
  if (PAGE_TITLES[pathname]) {
    return PAGE_TITLES[pathname];
  }

  // Check for dynamic routes (e.g., /flight/[id], /booking/[id])
  for (const [route, title] of Object.entries(PAGE_TITLES)) {
    if (pathname.startsWith(route) && route !== "/") {
      return title;
    }
  }

  // Handle special cases
  if (pathname.startsWith("/flight/")) {
    return "Flight Details";
  }
  if (pathname.startsWith("/booking/")) {
    return "Booking Details";
  }
  if (pathname.startsWith("/user/")) {
    return "User Profile";
  }

  // Default fallback
  const segments = pathname.split("/").filter(Boolean);
  if (segments.length > 0) {
    return segments[segments.length - 1]
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  return "Dashboard";
};

// Function to check if current page is chat-related
const isChatPage = (pathname: string): boolean => {
  return (
    pathname === "/chat" ||
    pathname.startsWith("/chat/") ||
    pathname.includes("/chat")
  );
};

// Function to get chat page title based on route and thread data
const getChatPageTitle = (
  pathname: string,
  currentThreadName: string | null
): string => {
  // Base chat page (/chat)
  if (pathname === "/chat") {
    return "New Chat";
  }

  console.log("pathname=====", pathname);

  // Thread-specific chat page (/chat/[chatThreadId])
  if (pathname.startsWith("/chat/[chatThreadId]")) {
    if (currentThreadName && currentThreadName.trim()) {
      // Truncate long thread names
      return currentThreadName;
    } else {
      return "New Chat";
    }
  }

  return "New Chat";
};

interface DashboardLayoutProps {
  children: React.ReactNode;
  sectionChangeHandler?: (section: string) => void;
}

export default function DashboardLayout({
  children,
  sectionChangeHandler,
}: DashboardLayoutProps) {
  const router = useRouter();
  const [pageTitle, setPageTitle] = useState<string>("Dashboard");

  // Get chat thread data from Redux
  const chatThread = useSelector((state: AppState) => state.chatThread);
  const { currentThreadName } = chatThread;

  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    const { pathname } = router;

    if (isChatPage(pathname)) {
      setSidebarOpen(true);
      // For chat pages, use specific chat title logic
      const chatTitle = getChatPageTitle(pathname, currentThreadName);
      setPageTitle(chatTitle);
    } else {
      // For other pages, use route-based title
      setSidebarOpen(false);
      const title = getPageTitleFromRoute(pathname);
      setPageTitle(title);
    }
  }, [router.pathname, currentThreadName]);

  // for shadcn glitch
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const body = document.body;
      if (
        body.style.overflow === "unset" ||
        body.style.pointerEvents === "none"
      ) {
        body.style.overflow = "";
        body.style.pointerEvents = "";
      }
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ["style"],
    });

    return () => observer.disconnect();
  }, []);

  // const { toggleSidebar } = useSidebar();

  return (
    <SidebarProvider open={sidebarOpen} onOpenChange={setSidebarOpen}>
      <CaptachaCookieHide />
      <AppSidebar variant="inset" />
      <SidebarInset className="flex flex-col">
        {/* Sticky Header */}
        <header className="md:bg-white bg-[#080236] sticky top-0 z-40 group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear ">
          <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
            <div className="w-5/6 flex items-center">
              <div className="">
                <SidebarTrigger className="-ml-1 md:block hidden" />
                <SidebarToggleButton />
              </div>

              <Separator
                orientation="vertical"
                className="mx-2 data-[orientation=vertical]:h-4 md:block hidden"
              />
              
              <h1 className="md:block hidden text-base font-medium text-foreground">
                {pageTitle}
              </h1>
            </div>
          </div>
        </header>

        <div className="md:hidden block px-4 w-full shadow border-b py-2">
          <h1 className="text-base font-medium text-foreground">
            {pageTitle}
          </h1>
        </div>

        {/* Main Content Area - Allow natural scrolling */}
        <div className="flex-1 bg-background overflow-y-auto">
          <div className="max-w-8xl  px-1 py-4 sm:p-6 lg:p-8">{children}</div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

export function SidebarToggleButton() {
  const { toggleSidebar } = useSidebar();

  return (
    <div className="flex md:hidden block">
      <div>
        <button onClick={toggleSidebar} className="-ml-1">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M5.5 7H9H12.5"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
        />
        <path
          d="M5.5 12H12H18.5"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
        />
        <path
          d="M5.5 17H10.5H15.5"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
        />
      </svg>
    </button>
      </div>
      <div className="ml-4">
        <img className="h-6" src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png" />
      </div>
    </div>
  );
}
