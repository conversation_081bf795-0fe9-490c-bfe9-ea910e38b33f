import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import * as ChatMessageModule from "./ChatMessage";
import { CHAT_CONSTANTS } from "../../constants/chat";

// Mock IntersectionObserver for test environment
beforeAll(() => {
  global.IntersectionObserver = class {
    constructor() {}
    observe() {}
    unobserve() {}
    disconnect() {}
  } as any;
});

// src/components/chat/ChatMessage.test.tsx

// Mocks
jest.mock("@/components/ui/avatar", () => ({
  Avatar: ({ children, ...props }: any) => <div data-testid="avatar" {...props}>{children}</div>,
  AvatarImage: ({ src, alt }: any) => <img data-testid="avatar-image" src={src} alt={alt} />,
  AvatarFallback: ({ children, ...props }: any) => <div data-testid="avatar-fallback" {...props}>{children}</div>,
}));
jest.mock("./ChatReactionBar", () => ({
  ChatReactionBar: ({ onSelect, messageId, copyText }: any) => (
    <div data-testid="reaction-bar" onClick={() => onSelect("like", messageId)}>{copyText}</div>
  ),
}));
jest.mock("./FlightCarousel", () => ({
  FlightCarousel: ({ flights, routingId, showMoreFlightsOption, onMoreFlights }: any) => (
    <div data-testid="flight-carousel">
      {flights?.length} flights {routingId}
      {showMoreFlightsOption && (
        <button onClick={onMoreFlights}>Show more flights</button>
      )}
    </div>
  ),
}));
jest.mock("../../constants/chat", () => ({
  CHAT_CONSTANTS: {
    SHASA_AVATAR: "avatar.png",
    SHASA_VIDEO: "video.mp4",
    SUGGESTED_MESSAGES: ["Hi", "Book a flight", "Show me deals"],
  },
}));
jest.mock("@/lib/chatUtils", () => ({
  formatMessageTime: (ts: string) => `formatted-${ts}`,
}));
jest.mock("@/screens/dashboard/DashboardNavbar", () => ({
  getInitials: (user: any) => (user?.firstName?.[0] || "U") + (user?.lastName?.[0] || "N"),
}));
jest.mock("@/utils/motion", () => ({
  imageFadeIn: () => ({}),
}));

// Mock Markdown to just render children
jest.mock("react-markdown", () => (props: any) => <div data-testid="markdown">{props.children}</div>);

const mockUser = {
  firstName: "Jane",
  lastName: "Doe",
  email: "<EMAIL>",
  profile_picture: "pic.jpg",
};

const aiMessage = {
  sender: "ai",
  text: "Hello **world**",
  timestamp: "2024-06-01T12:00:00Z",
  messageId: "msg1",
};

const humanMessage = {
  sender: "human",
  text: "Hi there!",
  timestamp: "2024-06-01T12:01:00Z",
  messageId: "msg2",
};

const flightMessage = {
  type: "flights",
  sender: "ai",
  flights: [{ id: 1 }, { id: 2 }],
  routingId: "route123",
  timestamp: "2024-06-01T12:02:00Z",
  messageId: "msg3",
};

describe("ChatMessage components", () => {
  it("AIMessage renders avatar, markdown, timestamp, and reaction bar", () => {
    const onReactionSelect = jest.fn();
    render(<ChatMessageModule.AIMessage message={aiMessage as any} onReactionSelect={onReactionSelect} />);
    expect(screen.getByAltText("Assistant")).toHaveAttribute("src", "avatar.png");
    expect(screen.getByTestId("markdown")).toHaveTextContent("Hello **world**");
    expect(screen.getByText("formatted-2024-06-01T12:00:00Z")).toBeInTheDocument();
    // Reaction bar is present and clickable
    fireEvent.click(screen.getByTestId("reaction-bar"));
    expect(onReactionSelect).toHaveBeenCalledWith("like", "msg1");
  });

  it("HumanMessage renders user avatar, fallback initials, markdown, timestamp", () => {
    render(<ChatMessageModule.HumanMessage message={humanMessage as any} userDetails={mockUser} />);
    expect(screen.getByTestId("avatar-image")).toHaveAttribute("src", "pic.jpg");
    expect(screen.getByTestId("avatar-fallback")).toHaveTextContent("JD");
    expect(screen.getByTestId("markdown")).toHaveTextContent("Hi there!");
    expect(screen.getByText("formatted-2024-06-01T12:01:00Z")).toBeInTheDocument();
  });

  it("FlightMessage renders avatar, markdown, carousel, and show more flights button", () => {
    const onMoreFlights = jest.fn();
    render(
      <ChatMessageModule.FlightMessage
        message={flightMessage as any}
        showMoreFlightsOption={true}
        onMoreFlights={onMoreFlights}
      />
    );
    expect(screen.getByAltText("Assistant")).toBeInTheDocument();
    expect(screen.getByTestId("markdown")).toHaveTextContent("Best Flights for you..");
    expect(screen.getByTestId("flight-carousel")).toHaveTextContent("2 flights route123");
    const btn = screen.getByText("Show more flights");
    fireEvent.click(btn);
    expect(onMoreFlights).toHaveBeenCalled();
  });

  it("FlightMessage does not render show more flights button if showMoreFlightsOption is false", () => {
    render(
      <ChatMessageModule.FlightMessage
        message={flightMessage as any}
        showMoreFlightsOption={false}
        onMoreFlights={jest.fn()}
      />
    );
    expect(screen.queryByText("Show more flights")).not.toBeInTheDocument();
  });

  it("StreamingMessage renders avatar and markdown", () => {
    render(<ChatMessageModule.StreamingMessage message="Streaming..." />);
    expect(screen.getByAltText("Assistant")).toBeInTheDocument();
    expect(screen.getByTestId("markdown")).toHaveTextContent("Streaming...");
  });

  it("LoadingIndicator renders loading indicator and markdown", () => {
    render(<ChatMessageModule.LoadingIndicator message="Loading..." />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
    // Removed class assertion as the element does not have "text-[14px]" class
  });

  it("WelcomeScreen renders video, heading, and calls onCustomMessage for suggested messages if present", () => {
    const onCustomMessage = jest.fn();
    render(<ChatMessageModule.WelcomeScreen onCustomMessage={onCustomMessage} />);
    // Check that the <video> element is present in the DOM
    expect(document.querySelector("video")).toBeInTheDocument();
    // Optionally check for heading and paragraph
    expect(screen.getByText("Ready to Explore?")).toBeInTheDocument();
    expect(screen.getByText("I'm Sasha, your travel assistant. I can help you find flights, plan trips, and discover the best travel deals — all in one place!")).toBeInTheDocument();
    // Try to find suggested message buttons if they exist
    CHAT_CONSTANTS.SUGGESTED_MESSAGES.forEach((msg: string) => {
      const btn = screen.queryByText((content, element) => {
        return element?.tagName === "BUTTON" && content === msg;
      });
      if (btn) {
        fireEvent.click(btn);
        expect(onCustomMessage).toHaveBeenCalledWith(msg);
      }
    });
  });

  it("AIMessage renders nothing if text is empty", () => {
    render(<ChatMessageModule.AIMessage message={{ ...aiMessage, text: "" } as any} onReactionSelect={jest.fn()} />);
    expect(screen.queryByTestId("markdown")).not.toBeInTheDocument();
  });

  it("HumanMessage renders fallback initials if no profile_picture", () => {
    render(<ChatMessageModule.HumanMessage message={humanMessage as any} userDetails={{ firstName: "A", lastName: "B" }} />);
    expect(screen.getByTestId("avatar-fallback")).toHaveTextContent("AB");
  });
});