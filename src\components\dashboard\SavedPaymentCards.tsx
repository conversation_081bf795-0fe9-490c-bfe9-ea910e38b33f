import React, { useState } from "react";
import { PencilLine, CircleX } from "lucide-react";
import { Button } from "@/components/ui/button";
import ConfirmDeleteModal from "./ConfirmDeleteModal";
import { Address, PaymentCard, User, UserDetailDelete } from "@/constants/user";

interface SavedPaymentCardsProps {
  onAddNewCard: () => void;
  onEdit: (card: PaymentCard) => void;
  onRemove?: (cardId: string) => void;
  user: User | null;
  deleteCard: (deleteCard: UserDetailDelete) => void;
}

function getAddress(addId: string, user: User) {
  let address = "";
  const filterAdd: Address[] = user.addresses.filter(
    (add) => add.address_id === addId
  );

  filterAdd.forEach((add) => {
    address = `${add?.streetAddress}, ${add.city}, ${add.state}, ${add.country}`;
  });

  return address;
}

const SavedPaymentCards: React.FC<SavedPaymentCardsProps> = ({
  onAddNewCard,
  onEdit,
  onRemove,
  user,
  deleteCard,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteCardId, setDeleteCardId] = useState<string | null>(null);

  const handleRemoveClick = (cardId: string) => {
    setDeleteCardId(cardId);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteCardId) {
      deleteCard({ card_id: deleteCardId });
    }
    setModalOpen(false);
    setDeleteCardId(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteCardId(null);
  };

  console.log("payment card:", user?.paymentCards);
  if (!user || user.paymentCards.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <div className="text-xl font-semibold text-center text-brand-black w-full">
          There's No Payment Method Registered For Your Account.
        </div>
        <div className="text-xl font-bold text-brand-black my-8 text-center">
          You can add new payment card here
        </div>
        <Button
          icon="edit"
          className="text-sm text-brand-white bg-brand hover:text-brand-white hover:bg-brand  px-6 py-4 rounded-[8px]"
          onClick={() => onAddNewCard()}
        >
          Add New Payment
        </Button>
      </div>
    );
  }

  return (
    <div>
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are Sure you want to delete?"
      />
      <div className="flex flex-row justify-between items-center gap-2 mb-8">
        <h1 className="text-sm sm:text-md md:text-2xl lg:text-3xl font-bold text-brand-black truncate max-w-[60%]">
          Manage Payment Card Details
        </h1>
        <Button

          icon="edit"
          className="text-xs sm:text-sm text-brand-white bg-brand hover:text-brand-white hover:bg-brand px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 rounded-[8px] whitespace-nowrap"
          onClick={onAddNewCard}
          disabled={user.paymentCards.length === 3}
        >
          Add New Payment
        </Button>
      </div>
      <div className="md:flex flex-wrap gap-8 grid sm:grid-cols-1 md:grid-cols-2">
        {user.paymentCards.map((card) => (
          <div
            key={card.card_id}
            className="w-full max-w-md bg-brand-white rounded-xl shadow flex flex-col justify-between"
          >
            <div className="flex justify-between items-center mb-4 bg-brand-white border-neutral py-2 px-6 rounded-t-xl">
              <div className="flex items-center gap-2">
                <span className="font-bold text-brand-black">
                  {card.cardType}
                </span>
                <span className="text-sm">
                  Credit Card ending in {card.cardNumber.slice(-4)}
                </span>
              </div>
              <span className="text-sm">{card.expiryDate}</span>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="font-semibold text-brand-black">
                  Name on card:
                </div>
                <div className="font-normal text-brand-black">
                  {card.cardHolderName}
                </div>
                <div className="font-semibold text-brand-black">
                  Billing Address:
                </div>
                <div className="font-normal text-brand-black">
                  {getAddress(card.billingAddress, user)}
                </div>
              </div>
            </div>
            <div className="flex justify-end gap-4 p-4">
              <button
                className="flex items-center gap-1 px-4 py-1 rounded-[8px] border border-brand text-brand text-sm"
                onClick={() => handleRemoveClick(card.card_id)}
              >
                <CircleX size={16} className="mr-1" /> Remove
              </button>
              <button
                className="flex items-center gap-1 px-4 py-1 rounded-[8px]   bg-brand text-white text-sm"
                onClick={() => {
                  onEdit(card);
                }}
              >
                <PencilLine size={16} className="mr-1" /> Edit
              </button>
            </div>
          </div>
        ))}
      </div>
      <p className="text-sm text-muted-foreground italic mt-6 ml-1">
        * You can store a maximum of 3 payment cards.
      </p>
    </div>
  );
};

export default SavedPaymentCards;
