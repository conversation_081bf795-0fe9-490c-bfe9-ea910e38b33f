"use client"

interface SeatsSectionProps {
    currentStep: number
    tripSummaryDetails: any
    setFlightSeatModal: (open: boolean) => void
}

const SeatsSection = ({ currentStep, tripSummaryDetails, setFlightSeatModal }: SeatsSectionProps) => {
    return (
        <div className="flex flex-col gap-4 mb-4">
            <div className="flex text-2xl xs:text- xs:justify-center xs:items-center xs:w-full text-[#1E1E76] font-semibold">
                Seats
            </div>
            <div className="relative font-proxima-nova w-full p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
                <div className="flex flex-col items-center justify-between w-full 2xl:p-4 xl:p-5 lg:p-5 md:p-3 sm:p-3 xs:p-3 border rounded-2xl shadow-md bg-[#F8F9FF] relative">
                    {currentStep === 1 && (
                        <div className="flex flex-row xs:flex-col xs:gap-5 xs:items-center w-full justify-between">
                            <div className="flex flex-row gap-5 items-center">
                                <img
                                    className="w-6 h-6"
                                    src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Seat%20Blue.png"
                                    alt=""
                                />
                                <div className="flex font-semibold text-lg text-[#080236]">Seat Choice Included</div>
                            </div>
                            <div className="flex">
                                <button
                                    onClick={() => setFlightSeatModal(true)}
                                    className="px-4 py-1 lg:text-lg md:text-base sm:text-sm xs:text-sm text-[#080236] font-bold rounded-full bg-[#E9E8FC]"
                                >
                                    Choose Your Seat
                                </button>
                            </div>
                        </div>
                    )}
                    {currentStep === 2 && (
                        <div className="flex flex-row xs:flex-col xs:gap-4 xs:items-center w-full justify-between">
                            <div className="flex flex-col gap-2">
                                <div className="flex flex-row gap-5 items-center">
                                    <img
                                        className="w-6 h-6"
                                        src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Seat%20Blue.png"
                                    />
                                    <div className="flex flex-col lg:text-lg md:text-base xs:text-sm text-[#080236]">
                                        <div>
                                            {tripSummaryDetails?.selectedOutboundFlight?.segments[0].origin} to{" "}
                                            {tripSummaryDetails?.selectedOutboundFlight?.segments[0].destination} :{" "}
                                            <strong>Seat 28F | 32F</strong>
                                        </div>
                                        <div>
                                            {tripSummaryDetails?.selectedOutboundFlight?.segments[1]?.origin} to{" "}
                                            {tripSummaryDetails?.selectedOutboundFlight?.segments[1]?.destination} : <strong>Seat 52E</strong>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-row gap-5 items-center">
                                    <img
                                        className="w-6 h-6"
                                        src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Seat%20Blue.png"
                                    />
                                    {tripSummaryDetails?.selectedInboundFlight &&
                                        Object.keys(tripSummaryDetails?.selectedInboundFlight).length > 0 && (
                                            <div className="flex flex-col lg:text-lg md:text-base xs:text-sm text-[#080236]">
                                                <div>
                                                    {tripSummaryDetails?.selectedInboundFlight?.segments[0].origin} to{" "}
                                                    {tripSummaryDetails?.selectedInboundFlight?.segments[0].destination} :{" "}
                                                    <strong>Seat 28F | 32F</strong>
                                                </div>
                                                <div>
                                                    {tripSummaryDetails?.selectedInboundFlight?.segments[1]?.origin} to{" "}
                                                    {tripSummaryDetails?.selectedInboundFlight?.segments[1]?.destination} :{" "}
                                                    <strong>Seat 52E</strong>
                                                </div>
                                            </div>
                                        )}
                                </div>
                            </div>
                            <div className="flex h-max">
                                <button
                                    onClick={() => setFlightSeatModal(true)}
                                    className="px-4 py-1 bg-[#E9E8FC] rounded-full lg:text-lg md:text-sm xs:text-sm"
                                >
                                    Change Your Seat
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default SeatsSection
