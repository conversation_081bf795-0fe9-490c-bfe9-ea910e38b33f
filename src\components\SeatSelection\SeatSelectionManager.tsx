import { Seat } from "./types";

export class SeatSelectionManager {
    private seatMap: Seat[];
    private selectedSeatsByTraveler: Map<number, string>; // key = traveler index
  
    constructor(seatMap: Seat[]) {
      this.seatMap = seatMap;
      this.selectedSeatsByTraveler = new Map();
    }
  
    selectSeat(seatId: string, travelerIndex: number): void {
      // Deselect previously selected seat for this traveler
      const prevSeatId = this.selectedSeatsByTraveler.get(travelerIndex);
      if (prevSeatId) {
        this.setSeatStatus(prevSeatId, 'available');
      }
  
      // Set new selection
      this.setSeatStatus(seatId, 'selected');
      this.selectedSeatsByTraveler.set(travelerIndex, seatId);
    }
  
    deselectSeat(seatId: string, travelerIndex: number): void {
      this.setSeatStatus(seatId, 'available');
      this.selectedSeatsByTraveler.delete(travelerIndex);
    }
  
    getTravelerSeat(travelerIndex: number): string | undefined {
      return this.selectedSeatsByTraveler.get(travelerIndex);
    }
  
    getAllSelectedSeats(): string[] {
      return Array.from(this.selectedSeatsByTraveler.values());
    }
  
    private setSeatStatus(seatId: string, status: 'available' | 'selected') {
      const seat = this.seatMap.find((s) => s.id === seatId);
      if (seat) seat.status = status;
    }
  }
