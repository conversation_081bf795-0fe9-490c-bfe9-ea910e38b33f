"use client"

import { useFlightContext } from "@/context/FlightContext"
import { getAirlineName } from "@/lib/utils/airline"
import type React from "react"
import { useEffect } from "react"
import type { AppState } from "@/store/store"
import { useSelector } from "react-redux"
import type { FareFeature } from "@/utils/fare-processor"
import { Briefcase, Luggage, RefreshCw, Armchair, UtensilsCrossed, FastForward, Edit, FileText, X } from "lucide-react"

const FareOptionCards: React.FC<any> = ({
  title,
  price,
  discount,
  selected = false,
  buttonLabel = "Upgrade",
  flight,
  options,
  onClick,
  fareFilght,
}) => {
  const { sharedFlightResults, searchFilter } = useFlightContext()
  const chatThreadDetails = useSelector((state: AppState) => state.chatThread)
  const { chatResult } = chatThreadDetails

  const handleClick = () => {
    onClick && onClick(null)
  }

  useEffect(() => {
    if (flight && fareFilght) {
      if (flight.id === fareFilght.id) {
        onClick && onClick(null)
      }
    }
  }, [flight])

  const getIconComponent = (iconName: string) => {
    const iconProps = { size: 18, className: "text-green-600" }

    switch (iconName) {
      case "Briefcase":
        return <Briefcase {...iconProps} />
      case "Luggage":
        return <Luggage {...iconProps} />
      case "RefreshCw":
        return <RefreshCw {...iconProps} />
      case "IndianRupee":
        return <X {...iconProps} />
      case "Armchair":
        return <Armchair {...iconProps} />
      case "UtensilsCrossed":
        return <UtensilsCrossed {...iconProps} />
      case "FastForward":
        return <FastForward {...iconProps} />
      case "Edit":
        return <Edit {...iconProps} />
      default:
        return <FileText {...iconProps} />
    }
  }

  // Group features by section for better organization
  const groupFeaturesBySection = (features: FareFeature[]) => {
    const baggage = features.filter(
      (f) => f.type === "SmallCabinBag" || f.type === "LargeCabinBag" || f.type === "HoldBag",
    )
    const policies = features.filter((f) => f.type === "FlightChange" || f.type === "Cancellation")
    const services = features.filter(
      (f) => f.type === "Meal" || f.type === "Seat" || f.type === "Product" || f.type === "NameChange",
    )

    return { baggage, policies, services }
  }

  const renderFeatureSection = (features: FareFeature[], showDivider = false) => {
    if (features.length === 0) return null

    return (
      <>
        {features.map((feature: FareFeature, index: number) => (
          <div
            key={`${feature.type}-${feature.maxWeight || feature.weight || index}`}
            className="flex items-start gap-3 py-2"
          >
            <div className="flex-shrink-0 mt-0.5">{getIconComponent(feature.icon)}</div>
            <div className="flex-1">
              <p className="text-sm text-neutral-dark leading-relaxed">{feature.description}</p>
              {/* Show dimensions below baggage items */}
              {(feature.type === "SmallCabinBag" || feature.type === "LargeCabinBag" || feature.type === "HoldBag") &&
                feature.dimensions && <p className="text-xs text-neutral-dark mt-1">{feature.dimensions}</p>}
            </div>
          </div>
        ))}
        {showDivider && (
          <div className="my-3">
            <div className="border-t border-dotted border-neutral"></div>
          </div>
        )}
      </>
    )
  }

  return (
    <div className="w-full md:w-[371px] bg-white rounded-2xl shadow-lg border border-neutral overflow-hidden h-[520px] flex flex-col">
      {/* Header */}
      <div className="bg-brand text-white p-4 text-center flex-shrink-0">
        <h3 className="text-lg font-bold uppercase tracking-wide">{title}</h3>
      </div>

      {/* Content */}
      <div className="p-6 flex flex-col flex-1 overflow-auto">
        {/* Airline Info */}
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center border">
              <img
                src={flight?.segments?.[0]?.operator_logo || "/placeholder.svg"}
                alt={flight?.airline}
                className="w-4 h-4 object-contain"
              />
            </div>
            <span className="text-sm font-medium text-neutral-dark">
              {flight && sharedFlightResults && getAirlineName(flight?.airline_code, sharedFlightResults.airline_data)}
            </span>
          </div>
          {flight && (
            <span className="text-sm text-neutral-dark">
              {flight?.origin} - {flight?.destination}
            </span>
          )}
        </div>

        {/* Features List - Clean design matching the image */}
        <div className="flex-1 min-h-0 mb-4">
          <div
            className="h-full overflow-y-auto pr-2"
            style={{ scrollbarWidth: "thin", scrollbarColor: "#cbd5e1 #f1f5f9" }}
          >
            {options && Array.isArray(options)
              ? (() => {
                const { baggage, policies, services } = groupFeaturesBySection(options)
                return (
                  <>
                    {/* Baggage Section */}
                    {renderFeatureSection(baggage, policies.length > 0 || services.length > 0)}

                    {/* Policies Section */}
                    {renderFeatureSection(policies, services.length > 0)}

                    {/* Services Section */}
                    {renderFeatureSection(services, false)}
                  </>
                )
              })()
              : // Fallback for old format
              options &&
              Object.entries(options).map(([featureText, isIncluded]) => (
                <div key={featureText} className="flex items-start gap-3 py-2">
                  <div className="flex-shrink-0 mt-0.5">
                    <FileText size={18} className="text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-neutral-dark leading-relaxed">{featureText}</p>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Price & Action - Fixed at bottom */}
        <div className="text-center flex-shrink-0">
          {discount && <div className="text-green-600 font-semibold mb-1">{discount}</div>}
          <div className="text-2xl font-bold text-brand-black mb-1">{price}</div>
          <div className="text-xs text-neutral-dark mb-4">per adult</div>

          <button
            className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${selected
              ? "bg-brand text-white cursor-default"
              : "bg-white text-brand border-2 border-brand hover:bg-brand hover:text-brand-white"
              }`}
            disabled={selected}
            onClick={handleClick}
          >
            {selected ? "Selected" : buttonLabel}
          </button>
        </div>
      </div>
    </div>
  )
}

export default FareOptionCards
