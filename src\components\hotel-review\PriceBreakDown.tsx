import { useSelector } from "react-redux";
import { AppState } from "@/store/store";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/router";

const PriceBreakdown = () => {
  const router = useRouter();
  const hotelBookingContext = useSelector((state: AppState) => state.hotelBookingContext.hotelBookingContext);

  const handleBookAndPay = () => {
    router.push('/hotel-confirmation');
  };

  if (!hotelBookingContext || !hotelBookingContext.selectedRoom) {
    return (
      <div className="">
        <div className="bg-[#E9E8FC] rounded-2xl px-6 pt-6 shadow-sm border border-gray-100">
          <h3 className="text-[24px] font-semibold mb-4 text-[#1E1E76]">Price Details</h3>
          <p className="text-sm text-[#707FF5] mb-4">Loading pricing information...</p>
        </div>
      </div>
    );
  }

  const { selectedRoom, nights, rooms } = hotelBookingContext;
  const { hotel, selectedRate } = selectedRoom;

  const formatCurrency = (amount: number) => {
    const currency = hotel.currency || 'INR';
    const currencySymbol = currency === 'EUR' ? '€' : '₹';
    return `${currencySymbol}${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Calculate pricing
  const pricePerNight = parseFloat(selectedRate.net_price || '0');
  const subtotal = pricePerNight * nights;
  
  // Calculate taxes and fees (approximately 18% GST for Indian hotels, 10% for EUR)
  const taxRate = hotel.currency === 'EUR' ? 0.10 : 0.18;
  const taxesAndFees = subtotal * taxRate;
  
  const totalAmount = subtotal + taxesAndFees;

  // Calculate discount with conditional child discount
  const calculateApplicableDiscounts = () => {
    if (!selectedRate.offers) return 0;
    
    return selectedRate.offers.reduce((sum, offer) => {
      // Only apply child discount if there are children in the booking
      if (offer.name.toLowerCase().includes('child') && hotelBookingContext.children === 0) {
        return sum; // Skip child discount if no children
      }
      
      return sum + Math.abs(parseFloat(offer.amount || '0'));
    }, 0);
  };

  const totalDiscountAmount = calculateApplicableDiscounts();

  const finalTotal = totalAmount - totalDiscountAmount;

  return (
    <div className="">
      <div className="bg-[#E9E8FC] rounded-2xl px-6 pt-6 shadow-sm border border-gray-100">
        <h3 className="text-[24px] font-semibold mb-4 text-[#1E1E76]">Price Details</h3>
        <p className="text-sm text-[#707FF5] mb-4">{rooms} room{rooms > 1 ? 's' : ''}</p>

        <div className="space-y-3 mb-6 border-t border-[#080236]">
          <div className="flex justify-between pt-2 text-[#080236] text-[18px]">
            <span className="font-medium">Price per night</span>
            <span className="font-medium">{formatCurrency(pricePerNight)}</span>
          </div>
          <div className="flex justify-between text-[#080236]">
            <span className="text-sm">Number of nights</span>
            <span className="font-medium">{nights}</span>
          </div>
          <div className="flex justify-between text-[#080236]">
            <span className="text-sm">Subtotal</span>
            <span className="font-medium">{formatCurrency(subtotal)}</span>
          </div>

          {/* Show discounts if any */}
          {totalDiscountAmount > 0 && (
            <div className="flex justify-between text-[#24C72F]">
              <span className="text-sm">Total Discounts</span>
              <span className="font-medium">-{formatCurrency(totalDiscountAmount)}</span>
            </div>
          )}

          <div className="flex justify-between text-[#080236]">
            <span className="text-sm">Taxes and fees</span>
            <span className="font-medium">{formatCurrency(taxesAndFees)}</span>
          </div>

          <div className="border-t border-[#080236] pt-3 text-[24px] text-[#1E1E76]">
            <div className="flex justify-between">
              <span className="font-semibold">You pay today</span>
              <span className="font-semibold text-[#24C72F]">{formatCurrency(0)}</span>
            </div>
          </div>

          <div className="flex justify-between border-t border-[#080236] text-[24px] text-[#1E1E76] pt-3">
            <span className="font-medium">Due later</span>
            <span className="font-medium">{formatCurrency(finalTotal)}</span>
          </div>

          <div className="border-t border-[#080236] border-b pt-3 pb-2 text-[24px] text-[#1E1E76]">
            <div className="flex justify-between">
              <span className="font-bold">Total</span>
              <span className="font-bold text-[#24C72F]">{formatCurrency(finalTotal)}</span>
            </div>
          </div>
        </div>

        <Button onClick={handleBookAndPay} className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 h-[42px] rounded-full text-[18px] font-medium bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)] mb-4">
          Book & Pay
        </Button>
      </div>
      <p className="text-[14px] text-[#080236] px-2 mt-16">
        Prices in {hotel.currency || 'INR'}. Total price includes all taxes and fees. Prices are not guaranteed until purchase is complete.
      </p>
    </div>
  );
};

export default PriceBreakdown;