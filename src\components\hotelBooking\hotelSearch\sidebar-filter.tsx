"use client";

import { useState, useEffect } from "react";
import { ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetClose } from "@/components/ui/sheet";
import { useIsMobile } from "@/hooks/use-mobile";

type FilterOption = {
    id: string;
    label: string;
    count?: number;
    checked?: boolean;
    filter?: any;
};

type FilterSection = {
    id: string;
    title: string;
    options: FilterOption[];
    expandable?: boolean;
    expanded?: boolean;
    showCount?: number;
};

type SidebarFilterProps = {
    sections: FilterSection[];
    className?: string;
    width?: string | number;
    onChange?: (sectionId: string, optionId: string, checked: boolean) => void;
    onReset?: () => void;
    onDone?: () => void;
    isOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
};

export function SidebarFilter({
    sections,
    className,
    width = 280,
    onChange,
    onReset,
    onDone,
    isOpen,
    onOpenChange,
}: SidebarFilterProps) {
    const [localSections, setLocalSections] = useState(sections);
    const isMobile = useIsMobile();
    const [activeFilters, setActiveFilters] = useState(0);

    // Update local sections when sections prop changes
    useEffect(() => {
        setLocalSections(sections);
    }, [sections]);

    // Calculate active filters count
    useEffect(() => {
        const count = localSections.reduce((acc, section) => {
            const sectionCount = section.options.filter(
                (option) => option.checked
            ).length;
            return acc + sectionCount;
        }, 0);
        setActiveFilters(count);
    }, [localSections]);

    const toggleSection = (sectionId: string) => {
        setLocalSections(
            localSections.map((section) =>
                section.id === sectionId
                    ? { ...section, expanded: !section.expanded }
                    : section
            )
        );
    };

    const handleCheckboxChange = (
        sectionId: string,
        optionId: string,
        checked: boolean
    ) => {
        console.log(`SidebarFilter: handleCheckboxChange called - ${sectionId} - ${optionId} - ${checked}`);
        
        // Update local state for immediate UI feedback
        setLocalSections(
            localSections.map((section) =>
                section.id === sectionId
                    ? {
                        ...section,
                        options: section.options.map((option) =>
                            option.id === optionId ? { ...option, checked } : option
                        ),
                    }
                    : section
            )
        );

        // Notify parent component
        if (onChange) {
            onChange(sectionId, optionId, checked);
        }
    };

    const handleReset = () => {
        console.log("SidebarFilter: handleReset called");
        
        // Update local state
        setLocalSections(
            localSections.map((section) => ({
                ...section,
                options: section.options.map((option) => ({
                    ...option,
                    checked: false,
                })),
            }))
        );

        // Notify parent component
        if (onReset) {
            onReset();
        }
    };

    const handleDone = () => {
        console.log("SidebarFilter: handleDone called");
        
        if (onOpenChange) {
            onOpenChange(false);
        }

        if (onDone) {
            onDone();
        }
    };

    // Handle dynamic width
    const getWidthStyle = () => {
        if (typeof width === "number") {
            return { width: `${width}px` };
        }

        if (typeof width === "string") {
            if (
                [
                    "xs",
                    "sm",
                    "md",
                    "lg",
                    "xl",
                    "2xl",
                    "3xl",
                    "4xl",
                    "5xl",
                    "6xl",
                    "7xl",
                ].includes(width)
            ) {
                return {}; // Will be handled by Tailwind classes
            }
            return { width };
        }

        return {};
    };

    const getWidthClass = () => {
        if (
            typeof width === "string" &&
            [
                "xs",
                "sm",
                "md",
                "lg",
                "xl",
                "2xl",
                "3xl",
                "4xl",
                "5xl",
                "6xl",
                "7xl",
            ].includes(width)
        ) {
            return `max-w-${width}`;
        }
        return "";
    };

    const FilterSections = () => (
        <div className="overflow-y-auto flex-1 font-proxima-nova">
            {localSections.map((section, index) => (
                <div key={section.id} className={cn("pb-2", index !== 0 && "pt-2")}>
                    <div className="px-4 pb-3">
                        <div className="flex justify-between items-center">
                            <h3 className="text-sm font-bold text-[#080236]">
                                {section.title}
                            </h3>
                        </div>
                    </div>

                    <div id={`section-${section.id}`} className="px-4 space-y-2">
                        {section.options
                            .slice(0, section.expanded ? undefined : section.showCount || 5)
                            .map((option) => (
                                <div
                                    key={option.id}
                                    className="flex items-center justify-between"
                                >
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            id={`${section.id}-${option.id}`}
                                            checked={option.checked || false}
                                            onChange={(e) =>
                                                handleCheckboxChange(
                                                    section.id,
                                                    option.id,
                                                    e.target.checked
                                                )
                                            }
                                            className="h-4 w-4 rounded border-[#B4BBE8] accent-[#4B4BC3]"
                                        />
                                        <label
                                            htmlFor={`${section.id}-${option.id}`}
                                            className="ml-2 text-sm text-[#080236]"
                                        >
                                            {option.label}
                                        </label>
                                    </div>
                                    {option.count !== undefined && (
                                        <span className="text-xs text-[#B4BBE8]">
                                            {option.count}
                                        </span>
                                    )}
                                </div>
                            ))}

                        {section.expandable &&
                            section.options.length > (section.showCount || 5) && (
                                <button
                                    onClick={() => toggleSection(section.id)}
                                    className="text-xs text-[#4B4BC3] hover:underline py-1 flex items-center"
                                >
                                    {section.expanded
                                        ? "Show less"
                                        : `Show all (${section.options.length})`}
                                    <ChevronDown
                                        className={cn(
                                            "ml-1 h-3 w-3",
                                            section.expanded && "rotate-180"
                                        )}
                                    />
                                </button>
                            )}
                    </div>

                    {index < sections.length - 1 && (
                        <div className="px-4 mt-4">
                            <div className="h-px bg-[#B4BBE8]"></div>
                        </div>
                    )}
                </div>
            ))}
        </div>
    );

    const FilterActions = () => (
        <div className="p-4 flex justify-start items-center gap-4 shadow-[0_-2px_10px_rgba(0,0,0,0.05)] bg-white rounded-b-xl">
            <p className=" text-[#1E1E76] font-medium cursor-pointer" onClick={handleReset}>
                Reset
            </p>
            <button
                className="py-2 px-4 text-white rounded-full bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]"
                onClick={handleDone}
            >
                Done
            </button>
        </div>
    );

    // Desktop view
    if (!isMobile) {
        return (
            <aside
                className={cn(
                    "relative bg-white flex flex-col h-full overflow-hidden",
                    getWidthClass(), // e.g., 'w-64'
                    className
                )}
                style={getWidthStyle()}
            >
                <div className="absolute left-0 top-0 h-full w-1 bg-[#707FF5] pointer-events-none" />
                <div className="pt-4">
                    <h2 className="text-center text-xl font-semibold text-[#080236]">
                        Filters
                    </h2>
                </div>
                <div className="px-4 mt-4">
                    <div className="h-px bg-[#B4BBE8] mb-2"></div>
                </div>
                <FilterSections />
                <FilterActions />
            </aside>
        );
    }

    // Mobile view uses Sheet component
    return (
        <Sheet open={isOpen} onOpenChange={onOpenChange}>
            <SheetContent
                side="right"
                className="p-0 w-[85vw] max-w-md border-l font-proxima-nova"
            >
                <div className="flex flex-col h-full">
                    <div className="p-4 flex justify-between items-center">
                        <h2 className="text-xl font-semibled text-[#080236]">Filters</h2>

                        <SheetClose asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                                <span className="sr-only">Close</span>
                            </Button>
                        </SheetClose>
                    </div>
                    <div className="px-4 mt-0">
                        <div className="h-px bg-[#B4BBE8] mb-2"></div>
                    </div>
                    <FilterSections />
                    <FilterActions />
                </div>
            </SheetContent>
        </Sheet>
    );
}