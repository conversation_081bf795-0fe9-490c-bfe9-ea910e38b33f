import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from "@headlessui/react";
import { ChevronDown } from "lucide-react";
import React, { useState } from "react";

const Flyer = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="rounded-lg overflow-hidden my-4 text-brand-black">
      <button
        className="w-full flex gap-5 px-4 py-3 text-left hover:bg-gray-100 transition-colors duration-200 justify-start items-center"
        onClick={() => setIsOpen(!isOpen)}
      >
        <svg
          className={`w-5 h-5 text-gray-500 transform transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="#1E1E76"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
        <span className="text-brand-black font-semibold">
          Frequent Flyer Numbers, Known Traveler Number, Redress and more
        </span>
      </button>

      {isOpen && (
        <div className="p-4">
          <div className="space-y-4">
            {/* Frequent Flyer Program */}
            <div>
              <div className="flex flex-row xs:flex-col xs:gap-3 gap-2">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto  shadow-sm">
                  <Listbox>
                    <ListboxButton className="w-full flex justify-between h-full text-brand-grey items-center focus:outline-none rounded-full px-4 xs:px-2 py-2 bg-brand-white text-left">
                      <div>Select Program</div>
                      <ChevronDown />
                    </ListboxButton>
                    <ListboxOptions className="bg-brand-white  border-2 border-[#EBEBEB]  rounded-xl flex flex-col gap-2 mt-1 cursor-pointer absolute z-10 w-full max-h-60 overflow-auto focus:outline-none">
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="">Select Program</ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="skyteam">SkyTeam</ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="staralliance">Star Alliance</ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="oneworld">Oneworld</ListboxOption>
                    </ListboxOptions>
                  </Listbox>
                </div>
                <div className="w-full space-y-1">
                  <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto  shadow-sm">
                    <input
                      type="number"
                      id="Number"
                      className="w-full px-3 py-2 placeholder:text-brand-grey bg-white rounded-full focus:outline-none"
                      placeholder="Number"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Known Traveler Number */}
            <div className="w-full space-y-1">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto  shadow-sm">
                <input
                  type="text"
                  id="ktn"
                  placeholder="Enter KTN"
                  className="w-full px-3 py-2 placeholder:text-brand-grey rounded-full bg-white focus:outline-none"
                />
              </div>
            </div>

            {/* Redress Number */}
            <div className="w-full space-y-1">
              <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto  shadow-sm">
                <input
                  type="text"
                  id="redress"
                  placeholder="Enter Redress Number"
                  className="w-full px-3 py-2 placeholder:text-brand-grey bg-white focus:outline-none"
                />
              </div>
            </div>

            {/* Additional Info */}
            {/* <div className="text-sm text-gray-500">
              <p>This information is optional and can help expedite your travel experience.</p>
            </div> */}
          </div>
        </div>
      )}
    </div>
  );
};

export default Flyer;
