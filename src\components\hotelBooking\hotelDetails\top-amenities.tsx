"use client"

import { Waves, Utensils, Wifi, Car, Building, PawPrint, Dumbbell } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"

interface TopAmenitiesProps {
    topAmenities: string[]
    onShowAllAmenities: () => void
}

export default function TopAmenities({ topAmenities, onShowAllAmenities }: TopAmenitiesProps) {
    const getIcon = (amenity: string) => {
        switch (amenity) {
            case "Swimming Pool":
                return <Waves className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "8 Restaurant(s)":
                return <Utensils className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Free Internet Access":
                return <Wifi className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Free Parking":
                return <Car className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Airport Shuttle":
                return <Building className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Pets Allowed":
                return <PawPrint className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Spa":
                return <Waves className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            case "Fitness Center":
                return <Dumbbell className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
            default:
                return <Waves className="h-5 w-5 text-[#080236]" stroke="#1E1E76" />
        }
    }

    return (
        <div className="col-span-12 md:col-span-7 bg-white p-4 rounded-lg border border-[#B4BBE8] flex flex-col">
            <h3 className="font-bold text-lg mb-4 text-[#080236]">Top Amenities</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-y-4 mb-4">
                {topAmenities.map((amenity, index) => (
                    <div key={index} className="flex items-center gap-1">
                        {getIcon(amenity)}
                        <span className="text-xs text-center text-[#080236]">{amenity}</span>
                    </div>
                ))}
            </div>
            <div className="mt-auto pt-2">
                <Button variant="link" className="text-[#4B4BC3] text-base p-0 h-auto underline" onClick={onShowAllAmenities}>
                    Show All Amenities
                </Button>
            </div>
        </div>
    )
}
