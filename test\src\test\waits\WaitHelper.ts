import { fixture } from '../fixtures/Fixture';

/**
 * Helper class for managing waits in tests
 */
export class WaitHelper {
    /**
     * Wait for the page to be stable (no network activity, animations completed)
     */
    public static async waitForPageStable(timeout: number = 5000): Promise<void> {
        // Wait for network to be idle
        await fixture.page.waitForLoadState('networkidle', { timeout });
        // Add a small buffer to allow any JS animations to complete
        await fixture.page.waitForTimeout(500);
    }

    /**
     * Wait for a selector with exponential backoff retry
     * @param selector - CSS selector to wait for
     * @param options - Wait options
     * @param maxRetries - Maximum number of retries
     */
    public static async waitForSelectorWithRetry(
        selector: string, 
        options: { state?: 'attached' | 'detached' | 'visible' | 'hidden', timeout?: number } = {}, 
        maxRetries: number = 3
    ): Promise<boolean> {
        let retries = 0;
        let baseTimeout = options.timeout || 5000;
        
        while (retries < maxRetries) {
            try {
                await fixture.page.waitForSelector(selector, {
                    ...options,
                    timeout: baseTimeout * Math.pow(2, retries)
                });
                return true;
            } catch (error) {
                retries++;
                if (retries >= maxRetries) {
                    console.log(`Failed to find selector "${selector}" after ${maxRetries} retries`);
                    return false;
                }
                console.log(`Retry ${retries}/${maxRetries} waiting for "${selector}"`);
            }
        }
        
        return false;
    }    /**
     * Wait for navigation to complete with a check for specific URL pattern
     */
    public static async waitForNavigationToUrlPattern(urlPattern: string | RegExp, timeout: number = 10000): Promise<boolean> {
        try {
            await Promise.race([
                fixture.page.waitForURL(urlPattern, { timeout }),
                fixture.page.waitForNavigation({ url: urlPattern, timeout })
            ]);
            return true;
        } catch (error) {
            console.log(`Navigation to ${urlPattern} failed or timed out`);
            return false;
        }
    }

    /**
     * Wait for navigation to a specific URL with retries
     * @param expectedUrl - The URL to wait for (can be partial)
     * @param maxRetries - Maximum number of retries
     * @param timeout - Timeout in milliseconds for each attempt
     */
    public static async waitForNavigationToUrl(
        expectedUrl: string,
        maxRetries: number = 3,
        timeout: number = 10000
    ): Promise<boolean> {
        let retries = 0;
        
        while (retries < maxRetries) {
            // Wait a moment for navigation
            await fixture.page.waitForTimeout(Math.min(1000 * retries, 3000));
            
            try {
                // Check if current URL contains expected URL
                const currentUrl = fixture.page.url();
                console.log(`Current URL: ${currentUrl}, Expected URL: ${expectedUrl}`);
                
                if (currentUrl.includes(expectedUrl)) {
                    console.log(`Successfully navigated to URL containing: ${expectedUrl}`);
                    return true;
                }
                
                // If not matching, wait for navigation event
                try {
                    await fixture.page.waitForNavigation({ timeout: timeout / (retries + 1) });
                } catch (navError) {
                    // Navigation timeout is expected if we're already on the page
                    console.log(`Navigation wait timed out on retry ${retries + 1}`);
                }
                
                retries++;
                console.log(`Retry ${retries}/${maxRetries} waiting for URL: ${expectedUrl}`);
            } catch (error) {
                retries++;
                console.log(`Error on retry ${retries}: ${error}`);
            }
        }
        
        // One final check
        const finalUrl = fixture.page.url();
        return finalUrl.includes(expectedUrl);
    }

    /**
     * Enhanced wait utility that attempts various strategies with exponential backoff
     * @param checkFn - Function that returns true when condition is met
     * @param options - Wait options
     */
    public static async waitWithBackoff(
        checkFn: () => Promise<boolean>,
        options: { 
            maxAttempts?: number, 
            initialTimeout?: number,
            maxTimeout?: number,
            description?: string 
        } = {}
    ): Promise<boolean> {
        const {
            maxAttempts = 5,
            initialTimeout = 2000,
            maxTimeout = 30000,
            description = 'condition'
        } = options;
        
        let attempt = 0;
        
        while (attempt < maxAttempts) {
            try {
                console.log(`Attempt ${attempt + 1}/${maxAttempts} waiting for ${description}...`);
                
                // Check if the condition is met
                const result = await checkFn();
                if (result) {
                    console.log(`✓ ${description} satisfied on attempt ${attempt + 1}`);
                    return true;
                }
                
                // Condition not met, wait with exponential backoff
                attempt++;
                if (attempt < maxAttempts) {
                    // Calculate timeout with exponential backoff, capped at maxTimeout
                    const timeout = Math.min(initialTimeout * Math.pow(2, attempt), maxTimeout);
                    console.log(`Waiting ${timeout}ms before next attempt`);
                    await fixture.page.waitForTimeout(timeout);
                }
            } catch (error) {
                console.log(`Error on attempt ${attempt + 1}: ${error}`);
                attempt++;
                
                // If we still have attempts left, wait before retrying
                if (attempt < maxAttempts) {
                    const timeout = Math.min(initialTimeout * Math.pow(2, attempt), maxTimeout);
                    console.log(`Waiting ${timeout}ms before next attempt`);
                    await fixture.page.waitForTimeout(timeout);
                }
            }
        }
        
        console.log(`× Failed to satisfy ${description} after ${maxAttempts} attempts`);
        return false;
    }

    /**
     * Waits for any of the provided selectors to be visible with backoff
     * @param selectors - Array of selectors to check
     * @param options - Wait options
     * @returns Object with success flag and the selector that was found
     */
    public static async waitForAnySelector(
        selectors: string[],
        options: { 
            maxAttempts?: number, 
            initialTimeout?: number,
            maxTimeout?: number,
            state?: 'attached' | 'detached' | 'visible' | 'hidden'
        } = {}
    ): Promise<{ success: boolean, selector: string }> {
        const {
            maxAttempts = 5,
            initialTimeout = 2000,
            maxTimeout = 30000,
            state = 'visible'
        } = options;
        
        let attempt = 0;
        
        while (attempt < maxAttempts) {
            console.log(`Attempt ${attempt + 1}/${maxAttempts} checking ${selectors.length} selectors...`);
            
            // Try each selector in sequence
            for (const selector of selectors) {
                try {
                    const timeout = Math.min(initialTimeout * Math.pow(1.5, attempt), maxTimeout / selectors.length);
                    console.log(`Checking selector: ${selector} with timeout ${timeout}ms`);
                    
                    await fixture.page.waitForSelector(selector, { 
                        state, 
                        timeout
                    });
                    
                    console.log(`✓ Found selector: ${selector}`);
                    return { success: true, selector };
                } catch (error) {
                    // This selector failed, try the next one
                    console.log(`× Selector not found: ${selector}`);
                }
            }
            
            // None of the selectors worked, increment attempt and wait
            attempt++;
            if (attempt < maxAttempts) {
                const waitTime = Math.min(initialTimeout * Math.pow(2, attempt), maxTimeout);
                console.log(`All selectors failed. Waiting ${waitTime}ms before retry...`);
                await fixture.page.waitForTimeout(waitTime);
            }
        }
        
        console.log(`× All selectors failed after ${maxAttempts} attempts`);
        return { success: false, selector: '' };
    }
}
