"use client";

import type React from "react";
import { useEffect, useState, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import type { FareOptionCard, Flight } from "@/constants/models";
import FareOptionCards from "./FareOptionCard";
import { formatFlightPrice } from "@/lib/utils/formatPrice";
import { motion } from "framer-motion";
import type { AppState } from "@/store/store";
import { updateTripSummary } from "@/store/slices/tripSummary";
import {
  processFareFeatures,
  type ProcessedFareOptions,
} from "@/utils/fare-processor";

interface FareCarouselProps {
  flight: Flight | null;
  onClick: (card: FareOptionCard | null) => void;
  selected: string | undefined;
  searchFlightResults?: any;
}

const FareCarousel: React.FC<FareCarouselProps> = ({
  flight,
  onClick,
  selected,
  searchFlightResults,
}) => {
  const segments = flight?.segments || [];
  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const dispatch = useDispatch();

  const [processedFareOptions, setProcessedFareOptions] =
    useState<ProcessedFareOptions>({});
  const [fareFilteredFlights, setFareFilteredFlights] = useState([]);
  const [visibleCount, setVisibleCount] = useState(3);

  const supplier_feature =
    tripSummaryDetails?.sharedFlightResults?.supplier_features || {};

  const carouselRef = useRef<HTMLDivElement>(null);

  const [startIndex, setStartIndex] = useState(0);

  const canGoLeft = startIndex > 0;
  const canGoRight = startIndex + visibleCount < fareFilteredFlights.length;

  // ✅ Handle screen size for visible cards
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setVisibleCount(1);
      } else if (window.innerWidth >= 768 && window.innerWidth < 1024) {
        setVisibleCount(2);
      } else {
        setVisibleCount(3);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // ✅ Fetch Fare Options and Filter Flights together
  useEffect(() => {
    const supplier = flight?.supplier || "";
    if (supplier !== "") {
      const supplierData = supplier_feature?.[supplier];
      if (supplierData && Object.keys(supplierData).length > 0) {
        const processed = processFareFeatures(supplierData);
        setProcessedFareOptions(processed);

        if (segments.length) {
          const flightCode = segments[0]?.flight_code || "";
          const flightResults: any = [];

          if (
            flightCode !== "" &&
            searchFlightResults?.flights?.length
          ) {
            const latestFlights = searchFlightResults.flights.filter(
              (flight: any) => flight?.segments[0]?.flight_code === flightCode
            );

            latestFlights.forEach((flight: any) => {
              flightResults.push({
                ...flight,
                fareOption:
                  flight?.segments[0]?.travel_class?.supplier_class || "",
              });
            });

            const filtered = flightResults.filter(
              (ele: any) => processed[ele.fareOption]
            );

            //console.log("helllllo", flightResults, latestFlights, flight?.supplier, searchFlightResults, flightCode)

            setFareFilteredFlights(filtered);

            dispatch(
              updateTripSummary({
                selectedFareOption: filtered,
              })
            );
          }
        }
      }
    }
  }, [flight, segments, supplier_feature, searchFlightResults, dispatch]);

  // ✅ Scroll handlers
  const handlePrev = () => {
    if (window.innerWidth < 768) {
      if (canGoLeft) {
        const newIndex = startIndex - 1;
        scrollToIndex(newIndex);
        setTimeout(() => setStartIndex(newIndex), 50);
      }
    } else {
      if (carouselRef.current) {
        const cardWidth = 371;
        const cardGap = 16;
        carouselRef.current.scrollBy({
          left: -(cardWidth + cardGap),
          behavior: "smooth",
        });
        setTimeout(() => {
          const newIndex = Math.max(0, startIndex - 1);
          setStartIndex(newIndex);
        }, 300);
      }
    }
  };

  const handleNext = () => {
    if (window.innerWidth < 768) {
      if (canGoRight) {
        const newIndex = startIndex + 1;
        scrollToIndex(newIndex);
        setTimeout(() => setStartIndex(newIndex), 50);
      }
    } else {
      if (carouselRef.current) {
        const cardWidth = 371;
        const cardGap = 16;
        carouselRef.current.scrollBy({
          left: cardWidth + cardGap,
          behavior: "smooth",
        });
        setTimeout(() => {
          const newIndex = Math.min(
            fareFilteredFlights.length - visibleCount,
            startIndex + 1
          );
          setStartIndex(newIndex);
        }, 300);
      }
    }
  };

  const scrollToIndex = (index: number) => {
    if (carouselRef.current) {
      const cardWidth = window.innerWidth < 768 ? window.innerWidth - 32 : 371;
      const cardGap = window.innerWidth < 768 ? 8 : 16;
      const scrollPosition = index * (cardWidth + cardGap);

      setTimeout(() => {
        if (carouselRef.current) {
          carouselRef.current.scrollTo({
            left: scrollPosition,
            behavior: "smooth",
          });
        }
      }, 10);
    }
  };

  const updateIndexFromScroll = () => {
    if (carouselRef.current) {
      const cardWidth = window.innerWidth < 768 ? window.innerWidth - 32 : 371;
      const cardGap = window.innerWidth < 768 ? 8 : 16;
      const scrollLeft = carouselRef.current.scrollLeft;
      const newIndex = Math.round(scrollLeft / (cardWidth + cardGap));
      const clampedIndex = Math.max(
        0,
        Math.min(newIndex, fareFilteredFlights.length - 1)
      );

      if (clampedIndex !== startIndex) {
        setStartIndex(clampedIndex);
      }
    }
  };

  // ✅ Scroll listener
  useEffect(() => {
    const carousel = carouselRef.current;
    if (carousel) {
      const handleScrollEnd = () => {
        requestAnimationFrame(updateIndexFromScroll);
      };

      carousel.addEventListener("scrollend", handleScrollEnd, {
        passive: true,
      });

      if (!("onscrollend" in window)) {
        let scrollTimeout: NodeJS.Timeout;
        const handleScroll = () => {
          clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(updateIndexFromScroll, 100);
        };

        carousel.addEventListener("scroll", handleScroll, { passive: true });
        return () => {
          clearTimeout(scrollTimeout);
          carousel.removeEventListener("scroll", handleScroll);
        };
      }

      return () => {
        carousel.removeEventListener("scrollend", handleScrollEnd);
      };
    }
  }, [startIndex, fareFilteredFlights.length]);

  return (
    <div className="relative w-full h-full">
      <div className="w-full h-full grid grid-cols-1 sm:grid-cols-1 gap-4">
        {Object.keys(processedFareOptions).length > 0 ? (
          <>
            <div className="relative w-full overflow-hidden">
              <div
                ref={carouselRef}
                className="flex w-full md:max-w-[1147px] md:mx-auto overflow-x-auto scrollbar-hide snap-x snap-mandatory scroll-smooth py-4"
                style={{
                  scrollbarWidth: "none",
                  msOverflowStyle: "none",
                  cursor: "grab",
                }}
                onScroll={updateIndexFromScroll}
              >
                <div className="flex gap-2 md:gap-4 w-full">
                  {fareFilteredFlights.length > 0 &&
                    fareFilteredFlights.map((flights: any, index: number) => (
                      <motion.div
                        key={index}
                        className="snap-center flex-shrink-0 w-full md:w-auto"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <FareOptionCards
                          title={flights?.fareOption}
                          flight={flight}
                          onClick={() =>
                            onClick({
                              title: flights?.fareOption,
                              amount: flights.price.amount,
                              currency: flights.price.currency,
                              flights: flights,
                            })
                          }
                          options={
                            processedFareOptions[flights?.fareOption] || []
                          }
                          price={formatFlightPrice(flights.price)}
                          selected={selected === flights?.fareOption}
                          fareFilght={flights}
                        />
                      </motion.div>
                    ))}
                </div>
              </div>
            </div>

            {fareFilteredFlights.length > visibleCount && (
              <>
                <div className="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none">
                  <button
                    onClick={handlePrev}
                    className="p-1 ml-[-16px] pointer-events-auto rounded-full shadow bg-white hover:bg-gray-100 transition"
                    aria-label="Previous card"
                  >
                    <ChevronLeft color="#1E1E76" />
                  </button>
                  <button
                    onClick={handleNext}
                    className="p-1 mr-[-16px] pointer-events-auto rounded-full shadow bg-white hover:bg-gray-100 transition"
                    aria-label="Next card"
                  >
                    <ChevronRight color="#1E1E76" />
                  </button>
                </div>

                <div className="absolute inset-y-0 left-0 right-0 flex items-center justify-between md:hidden pointer-events-none">
                  <button
                    onClick={handlePrev}
                    disabled={!canGoLeft}
                    className={`p-1 ml-[-16px] rounded-full shadow bg-white hover:bg-gray-100 transition pointer-events-auto ${!canGoLeft ? "opacity-30 cursor-not-allowed" : ""
                      }`}
                    aria-label="Previous card"
                  >
                    <ChevronLeft color="#1E1E76" />
                  </button>
                  <button
                    onClick={handleNext}
                    disabled={!canGoRight}
                    className={`p-1 mr-[-16px] rounded-full shadow bg-white hover:bg-gray-100 transition pointer-events-auto ${!canGoRight ? "opacity-30 cursor-not-allowed" : ""
                      }`}
                    aria-label="Next card"
                  >
                    <ChevronRight color="#1E1E76" />
                  </button>
                </div>
              </>
            )}
          </>
        ) : null}
      </div>
    </div>
  );
};

export default FareCarousel;
