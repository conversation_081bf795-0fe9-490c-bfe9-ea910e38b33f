{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build:dev": "env-cmd -f .env.development next build", "build:stage": "env-cmd -f .env.stage next build", "build:qa": "env-cmd -f .env.qa next build", "build:preprod": "env-cmd -f .env.pre-production next build", "build:prod": "env-cmd -f .env.production next build", "start:dev": "env-cmd -f .env.development next start", "start:stage": "env-cmd -f .env.stage next start", "start:qa": "env-cmd -f .env.qa next start", "start:preprod": "env-cmd -f .env.pre-production next start", "start:prod": "env-cmd -f .env.production next start", "lint": "next lint", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^2.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.8.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@types/react-lottie": "^1.2.10", "autoprefixer": "^10.4.20", "axios": "^1.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "env-cmd": "^10.1.0", "framer-motion": "^12.4.2", "gsap": "^3.12.7", "input-otp": "1.4.1", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "next": "14.2.25", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "posthog-js": "^1.249.4", "react": "^18", "react-date-range": "^2.0.1", "react-day-picker": "8.10.1", "react-dom": "^18", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.54.1", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "redux-mock-store": "^1.5.5", "redux-persist": "^6.0.0", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss": "latest", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.24.0", "@evilmartians/lefthook": "^1.11.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22", "@types/react": "^18", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18", "@types/react-google-recaptcha": "^2.1.9", "@types/react-redux": "^7.1.34", "@types/redux-mock-store": "^1.5.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lefthook": "^1.11.7", "lint-staged": "^15.5.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5"}}