import React, { useState } from "react";

import SavedPaymentCards from "./SavedPaymentCards";
import PaymentCardForm from "./PaymentCardForm";

const PaymentCardDetails: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  const handleEdit = () => {
    setShowForm(true);
  };

  return (
    <div className="w-full p-8">
      {showForm ? (
        <PaymentCardForm onCancel={() => setShowForm(false)} />
      ) : (
        <SavedPaymentCards onAddNewCard={() => setShowForm(true)} onEdit={handleEdit} />
      )}

      <p className="text-green-600 text-sm w-full p-2 text-center my-6">New Card Added Successfully</p>

      <div className="flex w-full h-[1px] bg-[#B4BBE8]"></div>
    </div>
  );
};

export default PaymentCardDetails;
