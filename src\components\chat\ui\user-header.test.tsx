import React from "react";
import { render, screen } from "@testing-library/react";
import { UserHeader } from "./user-header";

const mockGetInitials = jest.fn();

const user = { firstName: "<PERSON>", lastName: "Doe", email: "<EMAIL>" };

describe("UserHeader", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders avatar image when profilePicture is provided", () => {
        render(
            <UserHeader
                profilePicture="pic.jpg"
                firstName="John"
                lastName="Doe"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        // Check that the profile picture is rendered as a background or avatar, not as an <img>
        // For example, check for an element with a background image or a specific class
        const avatar = screen.getByText("John Doe").closest("div")?.querySelector("span");
        expect(avatar).toBeInTheDocument();
    });

    it("renders initials in fallback when profilePicture is not provided", () => {
        mockGetInitials.mockReturnValue("JD");
        render(
            <UserHeader
                firstName="John"
                lastName="Doe"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        expect(screen.getByText("JD")).toBeInTheDocument();
        expect(mockGetInitials).toHaveBeenCalledWith(user);
    });

    it("renders full name when firstName and lastName are provided", () => {
        render(
            <UserHeader
                profilePicture="pic.jpg"
                firstName="Jane"
                lastName="Smith"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        expect(screen.getByText("Jane Smith")).toBeInTheDocument();
    });

    it("renders only first name if lastName is missing", () => {
        render(
            <UserHeader
                profilePicture="pic.jpg"
                firstName="Jane"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        expect(screen.getByText("Jane")).toBeInTheDocument();
    });

    it('renders "Loading..." if firstName is missing', () => {
        render(
            <UserHeader
                profilePicture="pic.jpg"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("passes correct props to AvatarImage and AvatarFallback", () => {
        mockGetInitials.mockReturnValue("XY");
        render(
            <UserHeader
                profilePicture=""
                firstName="X"
                lastName="Y"
                getInitials={mockGetInitials}
                user={user}
            />
        );
        // There should be no image when profilePicture is an empty string
        expect(screen.queryByAltText("Profile")).not.toBeInTheDocument();
        expect(screen.getByText("XY")).toBeInTheDocument();
    });
});