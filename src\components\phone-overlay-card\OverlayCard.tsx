import React from "react";

interface CardProps {
  title?: string;
  message?: string;
  overlayImageUrl?: string;
  classname?: string;
}

const OverlayCard = ({
  title,
  message,
  overlayImageUrl,
  classname,
}: CardProps) => {
  return (
    <div
      className="absolute rounded-[24px] bg-[#E9E8FC80] border-2 border-red-500
        transform -translate-x-1/2 -translate-y-1/2 lg:w-[546px] md:w-[500px] sm:w-[425px] xs:w-[350px] xs:h-[200px] sm:h-[225px] md:h-[276px] top-0 left-0"
      style={{
        border: "1px solid transparent",
        backgroundImage:
          "linear-gradient(white, white), linear-gradient(315deg, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
        backgroundOrigin: "border-box",
        backgroundClip: "content-box, border-box",
      }}
    >
      <div
        className="relative w-full flex flex-col 
            items-center justify-center gap-2 px-5 h-[90%]"
      >
        <h2 className="font-proxima-nova font-medium text-lucky-blue xl:text-4xl lg:text-3xl md:text-3xl sm:text-2xl xs:text-2xl text-center">
          {title}
        </h2>
        <p className="font-bold font-proxima-nova text-[#080236] text-center xl:text-2xl lg:text-xl md:text-lg sm:text-base xs:text-base">
          {message}
        </p>
        <div className={`absolute ${classname}`}>
          <img
            src={overlayImageUrl}
            alt="alert"
            className="w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
};

export default OverlayCard;
