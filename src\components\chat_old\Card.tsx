import React, { useEffect, useState } from "react";
import {
  Plane,
  PlaneTakeoff,
  PlaneLanding,
  ChevronDown,
  InfoIcon,
} from "lucide-react";
import { useRouter } from "next/router";
import { Flight } from "@/constants/models";
import { formatFlightTime } from "@/lib/utils/flightTime";
import { useFlightContext } from "@/context/FlightContext";
import { formatAirportDisplay } from "@/lib/utils/formatAirport";
import { getFlightDescription } from "@/lib/utils/flightDescription";
import { formatFlightPrice } from "@/lib/utils/formatPrice";
import { getFormattedLayoverTime } from "@/lib/utils/layover";
import { useSelector } from "react-redux";
import { AppState } from "@/store/store";

interface CardProps {
  onSelect?: () => void;
  flight: Flight;
  value?: number;
}

const Card: React.FC<CardProps> = ({ onSelect, flight, value }) => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const { sharedFlightResults, searchedFlightResults } = useFlightContext();
  const depOrigin = formatFlightTime(flight.departure, flight.origin);
  const arrDest = formatFlightTime(flight.arrival, flight.destination);
  const firstSegment = flight.segments?.[0];
  const lastSegment = flight.segments?.[flight.segments.length - 1];
  const isConnecting = flight.segments && flight.segments.length > 1;
  const dep1 = formatFlightTime(firstSegment.depart_date, firstSegment.origin);
  const arr1 = formatFlightTime(
    firstSegment.arrive_date,
    firstSegment.destination
  );
  const dep2 = formatFlightTime(lastSegment.depart_date, lastSegment.origin);
  const arr2 = formatFlightTime(
    lastSegment.arrive_date,
    lastSegment.destination
  );

  const getAirportDisplayName = (
    airportCode: any,
    airportOptions: any
  ): string => {
    // console.log("getAirportDisplayName2=======", airportCode, airportOptions);
    if (!airportOptions || typeof airportOptions !== "object") {
      return airportCode;
    }

    const airport = airportOptions[airportCode];

    if (airport && isOpen) {
      return `${airportCode}, ${airport.airport_name}, ${airport.city_name_original}`;
    }

    return airportCode; // fallback if not found
  };
  const airportData =
    sharedFlightResults?.airport_data || searchedFlightResults?.airport_data;

  const firstSegmentOriginDisplay = getAirportDisplayName(
    firstSegment.origin,
    airportData
  );
  const firstSegmentDestDisplay = getAirportDisplayName(
    firstSegment.destination,
    airportData
  );
  const lastSegmentOriginDisplay = getAirportDisplayName(
    lastSegment.origin,
    airportData
  );
  const lastSegmentDestDisplay = getAirportDisplayName(
    lastSegment.destination,
    airportData
  );
  const flightDescription1 = getFlightDescription(firstSegment);
  const flightDescription2 = getFlightDescription(lastSegment);
  const layoverStr = getFormattedLayoverTime(flight);

  const handleClick = () => {
    if (onSelect) onSelect();
  };

  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);

  const { allFlights, chatResult } = chatThreadDetails;

  useEffect(() => {
    if (value === 0) {
      setIsOpen(true);
    }
  }, [value]);

  return (
    <div
      className={`flex flex-col gap-5 xl:p-4 lg:px-0 lg:py-4 md:py-4 sm:py-4 xs:py-4 ${isOpen ? "max-h-screen overflow-y-scroll" : ""}`}
    >
      <div className="relative font-proxima-nova p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
        <div className="flex flex-col items-center justify-between w-full 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-3 xs:p-3 border rounded-2xl shadow-md bg-[#F8F9FF] relative">
          {/* Badges */}
          {flight.recommended && (
            <div className="absolute -top-3 left-8 flex gap-2">
              <span className="bg-[#4B4BC3] text-white xl:text-base sm:text-sm xs:text-sm px-4 py-0 rounded-full">
                Best
              </span>
              <span className="bg-[#24C72F] text-white xl:text-base sm:text-sm xs:text-sm px-4 py-0 rounded-full">
                Lowest
              </span>
            </div>
          )}
          <div className="flex w-full justify-end xs:hidden">
            {/* Dropdown Icon */}
            <ChevronDown
              onClick={() => setIsOpen((prev) => !prev)}
              className={`text-[#070708] w-5 h-5 cursor-pointer ${isOpen ? "rotate-180" : ""} `}
            />
          </div>
          <div
            className={`flex flex-row xs:flex-col gap-8 lg:justify-around 2xl:gap-4 xs:gap-2 xs:py-1 2xl:justify-center sm:justify-center xs:justify-center xs:items-center w-full mx-auto`}
          >
            <div className="ml-14 w-1/6">
              <img
                src={flight.supplier_logo}
                alt={flight.airline}
                className="w-28 h-16 rounded-full object-contain"
              />
            </div>
            <div className="flex flex-col w-full justify-center xs:items-center 2xl:gap-2 xl:gap-1 xs:gap-1">
              {!isOpen ? (
                <div className="flex flex-row justify-around gap-4 w-full">
                  <div className="w-full xs:flex xs:flex-col xs:justify-center xs:items-center">
                    <h2 className="font-bold 2xl:text-lg xl:text-base lg:text-sm md:text-lg sm:text-base xs:text-base text-[#1E1E76]">
                      {flight.departure_date}
                    </h2>
                    <p className="xl:text-sm lg:text-sm md:text-sm sm:text-sm xs:text-sm text-[#1E1E76]">
                      {flight.departure_time_ampm} |{" "}
                      <span className="text-[#707FF5]">
                        {getAirportDisplayName(
                          depOrigin.airport,
                          chatResult?.output?.airport_data
                        )}
                      </span>
                    </p>
                  </div>
                  {/* Flight Duration & Connection */}
                  <div className="text-center flex w-full xs:flex xs:justify-center xs:items-center items-center">
                    <div className="flex flex-col">
                      <p className="xl:text-sm lg:text-xs sm:text-sm xs:text-sm tracking-wide text-[#707FF5] font-medium">
                        {flight.duration}
                      </p>
                      <div className="h-0.5 2xl:w-40 xl:w-32 lg:w-24 md:w-36 sm:w-28 bg-gradient-to-r from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76]"></div>
                      <p className="text-[#3CBC5C] tracking-wide xl:text-sm lg:text-xs sm:text-sm xs:text-sm font-medium">
                        {isConnecting ? "Connect" : "Direct"}
                      </p>
                    </div>
                    <Plane
                      fill="#1E1E76"
                      className="text-[#161B49] xl:w-5 xl:h-5 lg:w-4 lg:h-4 xs:w-4 xs:h-4 rotate-45"
                    />
                  </div>
                  {/* Destination */}
                  <div className="flex w-full xs:flex xs:justify-center xs:items-center items-center gap-3">
                    <div>
                      <h2 className="font-bold 2xl:text-lg xl:text-base lg:text-sm md:text-lg sm:text-base xs:text-base text-[#1E1E76]">
                        {flight.arrival_date}
                      </h2>
                      <p className="xl:text-sm lg:text-sm sm:text-sm xs:text-sm text-[#1E1E76]">
                        {flight.arrival_time_ampm} |{" "}
                        <span className="text-[#707FF5]">
                          {getAirportDisplayName(
                            arrDest.airport,
                            chatResult?.output?.airport_data
                          )}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex text-brand w-max 2xl:text-xl xl:text-lg lg:text-base sm:text-lg font-bold">
                  Departure {depOrigin.dayLabel}
                </div>
              )}
              <p
                className={`2xl:text-sm xl:text-xs lg:text-xs md:text-sm sm:text-sm ${isOpen ? "xs:hidden" : "xs:text-sm"} text-[#FF3B3F]`}
              >
                *Free Cancellation within 24 hours of booking
              </p>
            </div>
          </div>
          {isOpen && (
            <div className=" py-3 w-full h-full flex flex-col gap-2 justify-center items-center xs:hidden">
              <div className="flex flex-row w-[90%] mx-auto justify-center xl:gap-2 lg:gap-1 sm:gap-2 h-auto xl:p-2 lg:p-0">
                {/* First Leg: MAA → DEL */}
                <div className="flex flex-col xl:w-1/5 sm:w-1/4 justify-between text-left xl:py-2 lg:py-1 md:py-0">
                  <div className="flex flex-col">
                    <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                      {firstSegment?.departure_time_ampm}
                    </p>
                    <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                      Departure - {firstSegment?.departure_date}
                    </p>
                  </div>
                  <div className="flex flex-col">
                    <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                      {firstSegment?.arrival_time_ampm}
                    </p>
                    <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                      Arrival - {firstSegment?.arrival_date}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-center justify-center p-2">
                  <img
                    src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg"
                    alt="takeoff"
                  />
                </div>
                <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
                  <div className="flex flex-col">
                    <p className="font-bold flex-wrap capitalize 2xl:text-lg xl:text-lg sm:text-base text-[#080236]">
                      {firstSegmentOriginDisplay}
                    </p>
                    <p className="2xl:text-sm xl:text-sm capitalize lg:text-xs sm:text-sm text-[#B4BBE8]">
                      {flightDescription1}
                    </p>
                  </div>
                  <p className="xl:text-sm lg:text-xs capitalize sm:text-sm text-[#080236] mt-1">
                    {firstSegment.duration}
                  </p>
                  <div>
                    <p className="text-[#080236] capitalize 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                      {firstSegmentDestDisplay}
                    </p>
                  </div>
                </div>
              </div>
              {isConnecting && (
                <>
                  <div className="flex w-1/2">
                    <div className="flex items-center gap-3 p-2 rounded-lg border border-[#B4BBE8] w-max">
                      <InfoIcon color="#4B4BC3" />
                      <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#4B4BC3] font-medium">
                        {`${layoverStr} ${lastSegmentOriginDisplay}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-row justify-center xl:gap-2 md:gap-1 sm:gap-2 w-[90%] mx-auto h-auto xl:p-2 lg:p-0">
                    {/* Second Leg: DEL → LHR */}
                    <div className="flex flex-col xl:w-1/5 sm:w-1/4 text-left justify-between xl:py-2 lg:py-1 md:py-0">
                      <div className="flex flex-col">
                        <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                          {lastSegment.departure_time_ampm}
                        </p>
                        <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                          Departure - {lastSegment.departure_date}
                        </p>
                      </div>
                      <div className="flex flex-col">
                        <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                          {lastSegment.arrival_time_ampm}
                        </p>
                        <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                          Arrival - {lastSegment.arrival_date}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col-reverse items-center justify-center p-2">
                      <img
                        src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                        alt="Landing"
                      />
                    </div>
                    <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
                      <div>
                        <p className="font-bold 2xl:text-lg capitalize xl:text-lg sm:text-base text-[#080236]">
                          {lastSegmentOriginDisplay}
                        </p>
                        <p className="2xl:text-sm capitalize xl:text-sm lg:text-xs sm:text-sm text-[#B4BBE8]">
                          {flightDescription2}
                        </p>
                      </div>
                      <p className="xl:text-sm capitalize lg:text-xs sm:text-sm text-[#080236] mt-1">
                        {lastSegment.duration}
                      </p>
                      <div>
                        <p className="text-[#080236] capitalize 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                          {lastSegmentDestDisplay}
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
          {isOpen && (
            <div className="sm:hidden xs:flex w-full h-full flex flex-col gap-4">
              <div className="flex flex-row gap-2 w-full p-2">
                <div className="flex flex-col w-2/6 justify-center items-center gap-1">
                  <div className="flex flex-col gap-0.5 w-full mx-auto">
                    <div className="text-[#080236] mx-auto text-sm text-start w-full font-bold">
                      {dep1.timeLabel}
                    </div>
                    <div className="text-[#707FF5] mx-auto w-full text-start text-xs">
                      Departure - {dep1.dayLabel}
                    </div>
                  </div>
                  <div className="flex items-center w-4/6">
                    <div className="flex flex-col justify-center items-center gap-0.5">
                      <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

                    </div>
                    <div className="flex text-sm">{flight.duration}</div>
                  </div>
                  <div className="flex flex-col gap-0.5 w-full mx-auto">
                    <div className="text-[#080236] mx-auto text-sm text-start w-full font-bold">
                      {arr1.timeLabel}
                    </div>
                    <div className="text-[#707FF5] mx-auto w-full text-start text-xs">
                      Arrival - {arr1.dayLabel}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-1 justify-between w-4/6">
                  <div className="flex flex-col w-full gap-0.5">
                    <div className="text-sm capitalize font-semibold">
                      {firstSegmentOriginDisplay}
                    </div>
                    <div className="text-xs capitalize">
                      {flightDescription1}
                    </div>
                  </div>
                  <div className="flex flex-col w-full gap-0.5">
                    <div className="text-sm capitalize font-semibold">
                      {firstSegmentDestDisplay}
                    </div>
                    {/* <div className="text-xs">
                        Air India · Economy · AI 892 · Airbus A320neo - Jet
                      </div> */}
                  </div>
                </div>
              </div>
              {isConnecting && (
                <>
                  <div className="flex w-full">
                    <div className="flex items-center gap-3 p-2 rounded-lg border border-[#B4BBE8] w-full">
                      <InfoIcon color="#4B4BC3" />
                      <p className="text-xs text-[#4B4BC3] font-medium">
                        {`${layoverStr} ${lastSegmentOriginDisplay}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-row gap-2 w-full p-2">
                    <div className="flex flex-col w-2/6 justify-center items-center gap-1">
                      <div className="flex flex-col gap-0.5 w-full mx-auto">
                        <div className="text-[#080236] mx-auto text-sm text-start w-full font-bold">
                          {dep2.timeLabel}
                        </div>
                        <div className="text-[#707FF5] mx-auto w-full text-start text-xs">
                          Departure - {dep2.dayLabel}
                        </div>
                      </div>
                      <div className="flex items-center w-4/6">
                        <div className="flex flex-col-reverse justify-center items-center gap-0.5">
                          <img
                            src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                            alt="Landing"
                          />
                          <div className="flex h-20 w-0.5 bg-[#A195F9]"></div>
                          <div className="flex w-2 h-2 bg-[#A195F9] rounded-full"></div>
                        </div>
                        <div className="flex text-sm">
                          {lastSegment.duration}
                        </div>
                      </div>
                      <div className="flex flex-col gap-0.5 w-full mx-auto">
                        <div className="text-[#080236] mx-auto text-sm text-start w-full font-bold">
                          {arr2.timeLabel}
                        </div>
                        <div className="text-[#707FF5] mx-auto w-full text-start text-xs">
                          Arrival - {arr2.dayLabel}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1 justify-between w-4/6">
                      <div className="flex flex-col gap-0.5">
                        <div className="text-sm font-semibold capitalize">
                          {lastSegmentOriginDisplay}
                        </div>
                        <div className="text-xs capitalize">
                          {flightDescription2}
                        </div>
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <div className="text-sm capitalize font-semibold">
                          {lastSegmentDestDisplay}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-[#B4BBE8] text-sm">
                    *Free Cancelation within 24 hours of booking
                  </div>
                </>
              )}
            </div>
          )}
          <div className="flex w-full" onClick={() => setIsOpen((prev) => !prev)}>
            <div className="flex xs:w-1/2 justify-end items-center md:hidden">
              <ChevronDown className={`text-[#161B49] w-5 h-5 cursor-pointer ${isOpen ? "rotate-180" : ""}`} />
            </div>
            <div className="sm:w-full xs:w-1/2 flex flex-col items-end">
              <div className="p-2">
                <p className="text-[#24C72F] 2xl:text-xl xl:text-lg sm:text-base xs:text-base font-bold">
                  {formatFlightPrice(flight.price)}
                </p>
                <p className="text-[#B4BBE8] xl:text-sm sm:text-xs xs:text-xs">
                  For All Passengers
                </p>
              </div>
              {isOpen && (
                <div className="flex w-full justify-end items-center xs:hidden">
                  <button
                    onClick={handleClick}
                    className="flex 2xl:px-4 2xl:py-2 sm:px-4 sm:py-2  text-xl rounded-full text-[#080236] bg-[#A195F9] shadow-[0_4px_6px_-1px_#00000040]"
                  >
                    Select Flight
                  </button>
                </div>
              )}
            </div>
          </div>
          <div
            className={`items-center sm:hidden ${isOpen ? "xs:flex" : " xs:hidden"}`}
          >
            <button
              onClick={() => {
                if (onSelect) onSelect();
              }}
              className="flex px-4 py-1 rounded-full text-[#080236] bg-[#A195F9] shadow-[0_4px_6px_-1px_#00000040]"
            >
              Select Flight
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Card;
