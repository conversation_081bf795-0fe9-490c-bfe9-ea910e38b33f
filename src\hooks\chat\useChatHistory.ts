// hooks/useChatHistory.ts
import { useCallback, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useCustomSession } from "@/hooks/use-custom-session";
import { AppState } from "@/store/store";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { format } from "date-fns";
import axios from "axios";

type Thread = {
  thread_id: string;
  thread_name: string;
  updated_at: string;
  created_at: string;
  message_count: number;
};

type GroupedThreads = Record<string, Thread[]>;

export const useChatHistory = () => {
  const dispatch = useDispatch();
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  // Redux state
  const chatHistory = useSelector((state: AppState) => state.chatThread.chatHistory || {});
  const chatHistoryLoaded = useSelector((state: AppState) => state.chatThread.chatHistoryLoaded || false);
  const isRefreshingHistory = useSelector((state: AppState) => state.chatThread.isRefreshingHistory || false);
  const shouldRefreshHistory = useSelector((state: AppState) => state.chatThread.shouldRefreshHistory || false);

  // Track initial mount
  const isInitialMount = useRef(true);

  // Load chat history function
  const loadChatHistory = useCallback(async (forceRefresh = false) => {
    // If we have cached data and this isn't a forced refresh, use cache
    if (!forceRefresh && chatHistoryLoaded && Object.keys(chatHistory).length > 0) {
      return;
    }

    try {
      // Dispatch loading state
      dispatch(updateCurrentThreadInfo({ isRefreshingHistory: true }));

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/list`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to fetch user chat history");
      }

      const data: Thread[] = response.data.detail.data.data;
      
      const grouped: GroupedThreads = data.reduce(
        (acc: GroupedThreads, thread: Thread) => {
          const month = format(new Date(thread.created_at), "MMMM yyyy");
          if (!acc[month]) acc[month] = [];
          acc[month].push(thread);
          return acc;
        },
        {}
      );

      // Update Redux store with new data
      dispatch(updateCurrentThreadInfo({
        chatHistory: grouped,
        chatHistoryLoaded: true,
        isRefreshingHistory: false,
      }));

    } catch (error) {
      console.error("Chat history error:", error);
      // On error, just stop loading state but keep existing cache
      dispatch(updateCurrentThreadInfo({ isRefreshingHistory: false }));
    }
  }, [token, dispatch, chatHistory, chatHistoryLoaded]);

  // Load chat history on authentication
  useEffect(() => {
    if (status === "authenticated" && session?.user?.email && token) {
      // Only force refresh on initial mount if we don't have any cached data
      const shouldForceRefresh = isInitialMount.current && !chatHistoryLoaded;
      loadChatHistory(shouldForceRefresh);
      isInitialMount.current = false;
    }
  }, [status, session?.user?.email, token, loadChatHistory, chatHistoryLoaded]);

  // Listen for refresh signals
  useEffect(() => {
    if (shouldRefreshHistory && chatHistoryLoaded) {
      console.log('Received signal to refresh chat history');
      loadChatHistory(true);
      
      // Clear the flag after processing
      dispatch(updateCurrentThreadInfo({ shouldRefreshHistory: false }));
    }
  }, [shouldRefreshHistory, chatHistoryLoaded, loadChatHistory, dispatch]);

  // Helper functions
  const editThreadName = useCallback(async (threadId: string, newName: string) => {
    if (!newName.trim() || !token) return false;

    try {
      await axios.put(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread-name`,
        { thread_id: threadId, thread_name: newName.trim() },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      // Optimistically update the cache
      const updatedHistory = { ...chatHistory };
      Object.keys(updatedHistory).forEach(month => {
        if (updatedHistory[month] && Array.isArray(updatedHistory[month])) {
          const threadIndex = updatedHistory[month].findIndex(t => t.thread_id === threadId);
          if (threadIndex !== -1) {
            updatedHistory[month] = [...updatedHistory[month]];
            updatedHistory[month][threadIndex] = {
              ...updatedHistory[month][threadIndex],
              thread_name: newName.trim()
            };
          }
        }
      });
      
      dispatch(updateCurrentThreadInfo({ chatHistory: updatedHistory }));

      // Then refetch to ensure server consistency
      loadChatHistory(true);
      return true;
    } catch (error) {
      console.error("Rename error", error);
      // Revert optimistic update on error
      loadChatHistory(true);
      return false;
    }
  }, [token, chatHistory, dispatch, loadChatHistory]);

  const deleteThread = useCallback(async (threadId: string) => {
    if (!token) return false;

    try {
      await axios.delete(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread`,
        {
          data: { thread_id: threadId },
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      // Optimistically remove from cache
      const updatedHistory = { ...chatHistory };
      Object.keys(updatedHistory).forEach(month => {
        if (updatedHistory[month] && Array.isArray(updatedHistory[month])) {
          updatedHistory[month] = updatedHistory[month].filter(t => t.thread_id !== threadId);
          // Remove empty months
          if (updatedHistory[month].length === 0) {
            delete updatedHistory[month];
          }
        }
      });
      
      dispatch(updateCurrentThreadInfo({ chatHistory: updatedHistory }));

      // Then refetch to ensure server consistency
      loadChatHistory(true);
      return true;
    } catch (error) {
      console.error("Delete error", error);
      // Revert optimistic update on error
      loadChatHistory(true);
      return false;
    }
  }, [token, chatHistory, dispatch, loadChatHistory]);

  // Calculate derived data safely
  const allThreads = Object.values(chatHistory).flat();
  const showSkeleton = !chatHistoryLoaded && isRefreshingHistory;

  return {
    // Data
    chatHistory,
    allThreads,
    chatHistoryLoaded,
    isRefreshingHistory,
    showSkeleton,
    
    // Actions
    loadChatHistory,
    editThreadName,
    deleteThread,
  };
};