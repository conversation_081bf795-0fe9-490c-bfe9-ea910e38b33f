import { ChatSidebar } from "@/components/chat/ChatSidebar";
import { ChatHeader } from "@/components/chat/ChatHeader";
import ChatMobileNav from "@/components/chat/ChatMobileNav";
import { ChatProvider } from "@/context/ChatContext";
import DashboardLayout from "@/layout/DashboardLayout";

export default function ChatLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
     <DashboardLayout>
      <div className="">
      <div className="">
        <ChatProvider>
        <div className=" tk-proxima-nova overflow-hidden">
          {/* Chat Content Area - Takes full remaining height */}
          <div className="flex flex-col h-full max-h-screen">
            {children}
          </div>
        </div>
      </ChatProvider>
      </div>
    </div>
    </DashboardLayout>
  );
}