import React, { useContext, useRef } from 'react'
import { useAccordionContext } from './AccordionContext';

interface AccordionItemProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    value: string;
    trigger: string;
}

const AccordionItem = ({
    value,
    children,
    trigger,
    ...props
}: AccordionItemProps) => {
    const { selected, setSelected } = useAccordionContext();
    const open = selected === value

    const ref = useRef<HTMLDivElement | null>(null);

    return (
        <li
            className="grid grid-cols-[auto_1fr] gap-2 w-full items-center px-4"
            onClick={() => setSelected(open ? null : value)}
        >
            <div className="w-[14.48px] h-[14.48px] 
                sm:w-[22.8px] sm:h-[22.8px] md:w-[38px] md:h-[38px] 
                flex justify-center items-center">
                <img
                    src={`${open ? 'https://storage.googleapis.com/nxvoytrips-img/Homepage/faq/up-arrow.png' : 'https://storage.googleapis.com/nxvoytrips-img/Homepage/faq/down-arrow.png'}`}
                    alt="chevron"
                    className='w-[11.43px] h-[4.19px] max-w-[14.48px] max-h-[14.48px]
                    sm:max-w-[38px] sm:max-h-[38px] sm:w-[30px] sm:h-[11px] cursor-pointer'
                    onClick={() => setSelected(open ? null : value)}
                />
            </div>

            <header
                onClick={() => setSelected(open ? null : value)}
                className="flex items-center font-medium p-2 cursor-pointer w-full"
            >
                <span className="font-proxima-nova font-bold text-[#1E1E76] text-2xl sm:text-xl xs:text-lg leading-normal">
                    {trigger}
                </span>
            </header>

            <div className={`${open && 'w-[14.48px] h-[14.48px] sm:w-[30px]'}`}>
            </div>

            <div
                className="overflow-hidden transition-all duration-300 ease-in-out"
                style={{
                    height: open ? ref.current?.offsetHeight || 0 : 0
                }}            >
                <div ref={ref} className="px-4 flex items-start pb-2">
                    <span className="font-normal text-xl sm:text-lg xs:text-base leading-normal w-full font-proxima-nova text-[#080236]">
                        {children}
                    </span>
                </div>
            </div>

            <div className="col-span-2 flex justify-center mb-5">
                <div className="w-full border-b-[0.76px] border-neutral 
                sm:border-b-[2px]"></div>
            </div>
        </li>


    )
}

export default AccordionItem