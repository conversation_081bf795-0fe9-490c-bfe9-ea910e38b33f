import { motion } from "framer-motion";
import React, { useRef } from "react";
import Image from "next/image";
import { partners } from "@/constants";
import { textVariant } from "@/utils/motion";

const TrustedPartners = () => {
  return (
    <section className="flex bg-brand-white w-full h-full">
      <main className="flex flex-col bg-brand-white gap-10 w-[85%] mx-auto my-[50px] xs:my-[40px]">
        <div className="flex flex-col gap-2 items-center justify-center xs:mx-[30px]">
          <motion.h1
            initial="hidden"
            whileInView="show"
            variants={textVariant(0.25)}
            className="font-bold text-brand-black 
                      font-proxima-nova text-center xs:mt-2 sm:leading-none sm:mt-5
                      sm:mx-10 sm:text-3xl xs:text-3xl lg:text-5xl"
          >
            My Trusted Partners
          </motion.h1>
          <motion.p
            initial="hidden"
            whileInView="show"
            variants={textVariant(0.25)}
            className="font-medium text-brand-black sm:leading-2 
                  font-proxima-nova xs:leading-normal xs:text-sm lg:text-lg text-wrap 
                  text-center sm:text-[16px] xs:w-full sm:w-2/3"
          >
            I work with top-rated airlines, hotels, and experience providers to
            ensure your journey is smooth. No nasty surprises, I only love
            giving you trusted and amazing recommendations.
          </motion.p>
        </div>
        <div className="relative overflow-hidden w-full py-6 flex gap-0">
          {[...Array(2)].map((_, i) => (
            <motion.div
              key={i}
              className="flex w-max gap-10 ml-4 flex-shrink-0 py-2"
              animate={{
                x: ["0%", "-100%"],
              }}
              transition={{
                ease: "linear",
                duration: 20,
                repeat: Infinity,
              }}
            >
              {partners.map((partner) => (
                <Image
                  key={partner.desc}
                  src={partner.imageURL}
                  alt={partner.desc}
                  width={partner.width}
                  height={44}
                  className="mx-2"
                />
              ))}
            </motion.div>
          ))}
        </div>
      </main>
    </section>
  );
};

export default TrustedPartners;
