import { BasePage } from './BasePage';
import { fixture } from '../fixtures/Fixture';
import { WaitHelper } from '../waits/WaitHelper';
import { ScreenshotHelper } from '../utils/ScreenshotHelper';

export class FlightSearchPage extends BasePage {    
    // Making elements accessible for step definitions - converted to modern Playwright locators
    public static elements = {
        // Input Fields
        departureInput: () => fixture.page.getByPlaceholder('From Where'),
        arrivalInput: () => fixture.page.getByPlaceholder('To Where'),
        
        // Location dropdown suggestions
        locationSuggestionDropdown: () => fixture.page.locator('div.absolute.z-10.mt-1.w-full.bg-white'),
        locationSuggestionItem: () => fixture.page.locator('div.px-4.py-2'),
        
        // Date picker elements - enhanced selectors for the new date picker component
        datePicker: () => fixture.page.locator('input[type="text"][readonly]'),
        datePickerCalendar: () => fixture.page.locator('.rdrCalendarWrapper'), // Component specific
        datePickerMonthYearHeader: () => fixture.page.locator('.rdrMonthName'),
        datePickerMonthSelector: () => fixture.page.locator('.rdrMonthPicker select'),
        datePickerYearSelector: () => fixture.page.locator('.rdrYearPicker select'),
        datePickerPrevButton: () => fixture.page.locator('button.rdrPprevButton'), // Calendar component specific
        datePickerNextButton: () => fixture.page.locator('button.rdrNextButton'), // Calendar component specific
        datePickerDayButton: () => fixture.page.locator('button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled)'),
        datePickerConfirmButton: () => fixture.page.getByRole('button').filter({ hasText: /(Apply|Done|Confirm)/i }),

        // Buttons
        searchButton: () => fixture.page.getByRole('button', { name: 'Search' }),

        // Labels
        departureSectionLabel: () => fixture.page.getByText('Departure Location'),
        arrivalSectionLabel: () => fixture.page.getByText('Arrival Location'),

        // Containers
        departureSectionContainer: () => fixture.page.getByText('Departure Location').locator('..').locator('..'),
        arrivalSectionContainer: () => fixture.page.getByText('Arrival Location').locator('..').locator('..'),
        datePickerContainer: () => fixture.page.locator('div.flex.relative.w-4/5'),

        // Navigation
        backToChatButton: () => fixture.page.getByRole('button', { name: 'Back to Chat' }),
        // Filter Buttons - Updated with new selectors and fallbacks
        tripTypeButton: () => fixture.page.getByTestId('trip-type-selector').or(fixture.page.getByText('Select Trip')),
        passengersButton: () => fixture.page.getByTestId('passengers-selector').or(fixture.page.getByRole('button').filter({ has: fixture.page.locator('svg.lucide-users') })),
        classSelectionButton: () => fixture.page.getByTestId('class-selector').or(fixture.page.getByRole('button', { name: 'Select Class' })),
        filtersButton: () => fixture.page.getByTestId('filters-button').or(fixture.page.getByRole('button', { name: 'Filters' })),
        
        // Button Content
        passengerCount: () => fixture.page.getByRole('button').filter({ has: fixture.page.locator('svg.lucide-users') }).first(),
        tripTypeText: () => fixture.page.getByRole('button').filter({ has: fixture.page.locator('svg.lucide-arrow-right') }).first(),
        classTypeText: () => fixture.page.getByText('Select Class'),

        // Icons
        backIcon: () => fixture.page.locator('svg.lucide-arrow-left'),
        chevronDownIcon: () => fixture.page.locator('svg.lucide-chevron-down'),
        usersIcon: () => fixture.page.locator('svg.lucide-users'),

        // Flight Details Expansion
        flightDetailsExpandIcon: () => fixture.page.getByRole('button').filter({ has: fixture.page.locator('svg.lucide-chevron-down') }),
        expandedFlightDetails: () => fixture.page.getByTestId('expanded-flight-details'),

        // Flight Selection
        selectFlightButton: () => fixture.page.getByRole('button', { name: 'Select Flight' }),
        selectFlightButtonGradient: () => fixture.page.getByRole('button', { name: 'Select Flight' }).filter({ has: fixture.page.locator('.bg-\\[linear-gradient') }),

        // Login Elements
        usernameInput: () => fixture.page.getByRole('textbox', { name: 'email' }),
        passwordInput: () => fixture.page.getByLabel('Password').or(fixture.page.locator('input[name="password"]')),
        signinButton: () => fixture.page.getByRole('button', { name: 'Sign in' }),
        userProfile: () => fixture.page.getByTestId('user-profile'),

        // Trip Type Dialog
        tripTypeDialog: () => fixture.page.locator('div[role="dialog"][data-state="open"]'),
        tripTypeOptions: () => fixture.page.locator('div[role="dialog"][data-state="open"]').getByRole('radio').all(),
        oneWayOption: () => fixture.page.getByText('One Way'),
        roundTripOption: () => fixture.page.getByText('Round Trip'),
        multiTripOption: () => fixture.page.getByText('Multi-Trip'),
        
        // Class Type Dialog
        classTypeDialog: () => fixture.page.locator('div[role="dialog"][data-state="open"]'),
        // Class options
        economyOption: () => fixture.page.getByText('Economy With Restrictions'),
        economyWithoutRestrictionsOption: () => fixture.page.getByText('Economy Without Restrictions'),
        premiumEconomyOption: () => fixture.page.getByText('Economy Premium'),
        businessOption: () => fixture.page.getByText('Business'),
        firstClassOption: () => fixture.page.getByText('First'),
        // Alternative options as fallback
        economyOptionAlt: () => fixture.page.getByLabel('Economy With Restrictions'),
        economyWithoutRestrictionsOptionAlt: () => fixture.page.getByLabel('Economy Without Restrictions'),
        premiumEconomyOptionAlt: () => fixture.page.getByLabel('Economy Premium'),
        businessOptionAlt: () => fixture.page.getByLabel('Business'),
        firstClassOptionAlt: () => fixture.page.getByLabel('First'),
        // Checkbox selector to be used after finding the right label
        classCheckbox: () => fixture.page.getByRole('checkbox'),
        
        // Passenger Count Dialog
        passengerCountDialog: () => fixture.page.locator('div[role="dialog"][data-state="open"]'),
        
        // Plus buttons for different passenger types
        adultIncrementButton: () => fixture.page.getByText('Adults').locator('..').locator('..').getByRole('button', { name: '+' }),
        childIncrementButton: () => fixture.page.getByText('Children').locator('..').locator('..').getByRole('button', { name: '+' }),
        infantIncrementButton: () => fixture.page.getByText('Infants').locator('..').locator('..').getByRole('button', { name: '+' }),
        
        // Minus buttons for different passenger types
        adultDecrementButton: () => fixture.page.getByText('Adults').locator('..').locator('..').getByRole('button', { name: '−' }),
        childDecrementButton: () => fixture.page.getByText('Children').locator('..').locator('..').getByRole('button', { name: '−' }),
        infantDecrementButton: () => fixture.page.getByText('Infants').locator('..').locator('..').getByRole('button', { name: '−' }),
        
        // Count display spans
        adultsCountText: () => fixture.page.getByText('Adults').locator('..').locator('..').locator('span.w-6, span.text-\\[\\#080236\\]').first(),
        childCountText: () => fixture.page.getByText('Children').locator('..').locator('..').locator('span.w-6, span.text-\\[\\#080236\\]').first(),
        infantCountText: () => fixture.page.getByText('Infants').locator('..').locator('..').locator('span.w-6, span.text-\\[\\#080236\\]').first(),
        
        // Total travelers count text
        totalTravelersText: () => fixture.page.getByText('Travelers'),
        totalTravelersCountSpan: () => fixture.page.getByText('Travelers').locator('span'),
        
        // Generic buttons for fallbacks
        genericPlusButton: () => fixture.page.getByRole('button', { name: '+' }),
        genericMinusButton: () => fixture.page.getByRole('button', { name: '−' }),
        genericCountSpan: () => fixture.page.locator('span.w-6.xs\\:w-4.text-\\[\\#080236\\].text-center'),
        
        confirmButton: () => fixture.page.getByRole('button').filter({ hasText: /(Confirm|Done)/i }),
        
        // Search Results
        searchResultsContainer: () => fixture.page.locator('[class*="flight-search-results"]'),
        searchResultItem: () => fixture.page.locator('[class*="flight-result-item"]'),
        noResultsMessage: () => fixture.page.getByText('No flights found')
    };

    /**
     * Sets the departure location in the search form
     */
    public static async setDepartureLocation(location: string): Promise<void> {
        console.log(`Setting departure location: ${location}`);
        await this.elements.departureInput().fill(location);
        // Wait a moment for the dropdown to appear
        await fixture.page.waitForTimeout(1000);
    }    /**
     * Sets the arrival location in the search form
     */
    public static async setArrivalLocation(location: string): Promise<void> {
        console.log(`Setting arrival location: ${location}`);
        await this.elements.arrivalInput().fill(location);
        // Wait a moment for the dropdown to appear
        await fixture.page.waitForTimeout(1000);
    }/**
     * Sets date(s) in the date picker based on trip type
     * @param dateString - For One-Way/Multi-Trip: Date in YYYY-MM-DD format
     *                     For Round-Trip: Two dates in YYYY-MM-DD,YYYY-MM-DD format (departure,return)
     * @param tripType - Optional trip type ('oneway', 'roundtrip', or 'multitrip'). If not provided,
     *                   will try to determine from the current selected trip type or input format.
     */
    public static async setDepartureDate(dateString: string, tripType?: string): Promise<void> {
        try {
            console.log(`Setting date(s) to: ${dateString} with trip type: ${tripType || 'auto-detect'}`);
              // Take screenshot before starting
            await ScreenshotHelper.takeScreenshot('before-date-selection');
            
            // If no trip type provided, try to detect it
            if (!tripType) {
                try {
                    tripType = (await this.getTripType() || '').toLowerCase();
                    console.log(`Auto-detected trip type: ${tripType}`);
                } catch (e) {
                    console.log('Could not auto-detect trip type from UI, will use input format detection');
                }
                
                // If still no trip type, try to determine from input format
                if (!tripType) {
                    tripType = dateString.includes(',') ? 'roundtrip' : 'oneway';
                    console.log(`Determined trip type from input format: ${tripType}`);
                }
            }
            
            // Normalize tripType to lowercase
            tripType = tripType.toLowerCase();
            
            // Click the date picker to open it
            await this.elements.datePicker().click();
            await fixture.page.waitForTimeout(1000);
              // Take screenshot after opening date picker
            await ScreenshotHelper.takeScreenshot('date-picker-opened');
            
            // Process dates based on trip type
            if (tripType === 'roundtrip') {
                // For round trip, we expect two dates (departure and return)
                const dates = dateString.split(',');
                if (dates.length !== 2) {
                    throw new Error(`Round trip requires two dates in format YYYY-MM-DD,YYYY-MM-DD but got: ${dateString}`);
                }
                
                // Select departure date first
                await this.selectSingleDate(dates[0], 'Departure');
                await fixture.page.waitForTimeout(500);
                
                // Then select return date
                await this.selectSingleDate(dates[1], 'Return');
            } else {
                // For one-way or multi-trip, just select a single date
                await this.selectSingleDate(dateString, tripType === 'oneway' ? 'One Way' : 'Multi-Trip');
            }
            
            // Handle date picker confirmation (if there's a confirm button)
            try {
                const confirmButton = fixture.page.getByRole('button').filter({ hasText: /(Apply|Done|Confirm)/i });
                const hasConfirm = await confirmButton.isVisible();
                if (hasConfirm) {
                    await confirmButton.click();
                    console.log('Clicked date picker confirmation button');
                }
            } catch (confirmError) {
                console.log('No confirmation button found or error clicking it:', confirmError);
                // Click outside to close the date picker dialog if no confirm button
                await fixture.page.mouse.click(10, 10);
            }
              // Take screenshot after date selection
            await ScreenshotHelper.takeScreenshot('date-selection-complete');
            
            // Verify the date was set correctly by checking input value
            try {
                const currentValue = await this.elements.datePicker().inputValue();
                console.log(`Date picker value after selection: "${currentValue}"`);
                
                // For round-trip, verify we have something like "date - date"
                if (tripType === 'roundtrip' && !currentValue.includes('-')) {
                    console.warn('Round trip date selection may not have been successful. Current value:', currentValue);
                }
            } catch (error) {
                console.warn('Could not verify date picker value:', error);
            }
            
        } catch (error) {
            console.error(`Error setting date(s): ${error}`);
            await ScreenshotHelper.takeScreenshot('date-selection-error');
            throw error;
        }
    }
    
    /**
     * Helper method to select a single date in the date picker
     * @param dateString - Date in YYYY-MM-DD format
     * @param context - Context for logging (e.g., 'Departure', 'Return')
     */
    private static async selectSingleDate(dateString: string, context: string): Promise<void> {
        try {
            console.log(`Selecting ${context} date: ${dateString}`);
            
            // Parse the date
            const [year, month, day] = dateString.split('-').map(part => parseInt(part, 10));
            const monthName = new Date(year, month - 1, 1).toLocaleString('en-US', { month: 'long' });
            const dayOfWeek = new Date(year, month - 1, day).toLocaleString('en-US', { weekday: 'short' });
            
            console.log(`Parsed date: ${dayOfWeek} ${monthName} ${day}, ${year}`);
            
            // First try to ensure we're on the right month/year by using the selectors in the date picker header
            try {
                // Check if we need to navigate to a different month/year
                const visibleMonthYear = await this.elements.datePickerMonthYearHeader().textContent();
                console.log(`Current visible month/year: ${visibleMonthYear}`);
                
                if (visibleMonthYear && !visibleMonthYear.includes(`${monthName} ${year}`)) {
                    console.log(`Need to navigate to ${monthName} ${year}`);
                    
                    // Try to use month/year selectors
                    try {
                        // Check if there's a month dropdown
                        const monthSelector = this.elements.datePickerMonthSelector();
                        const hasMonthSelect = await monthSelector.isVisible();
                        if (hasMonthSelect) {
                            // Select month from dropdown
                            await monthSelector.selectOption({ label: monthName });
                            console.log(`Selected month ${monthName} from dropdown`);
                        }
                        
                        // Check if there's a year dropdown
                        const yearSelector = this.elements.datePickerYearSelector();
                        const hasYearSelect = await yearSelector.isVisible();
                        if (hasYearSelect) {
                            // Select year from dropdown
                            await yearSelector.selectOption({ value: String(year) });
                            console.log(`Selected year ${year} from dropdown`);
                        }
                        
                        await fixture.page.waitForTimeout(500);
                    } catch (navError) {
                        console.log(`Error using month/year selectors: ${navError}, will try navigation buttons`);
                        
                        // If selectors didn't work, use prev/next buttons
                        let currentMonthYear = await this.elements.datePickerMonthYearHeader().textContent();
                        let attempts = 0;
                        const maxAttempts = 24; // Limit to prevent infinite loops
                        
                        while (currentMonthYear && !currentMonthYear.includes(`${monthName} ${year}`) && attempts < maxAttempts) {
                            // Parse current month and year
                            const currentParts = currentMonthYear.trim().split(' ');
                            const currentMonth = currentParts[0];
                            const currentYear = parseInt(currentParts[1], 10);
                            
                            // Determine if we need to go forward or backward
                            const targetDate = new Date(year, month - 1, 1);
                            const currentDate = new Date(currentYear, new Date(`${currentMonth} 1, ${currentYear}`).getMonth(), 1);
                            
                            if (targetDate > currentDate) {
                                // Need to go forward
                                await this.elements.datePickerNextButton().click();
                                console.log('Clicked next month button');
                            } else {
                                // Need to go backward
                                await this.elements.datePickerPrevButton().click();
                                console.log('Clicked previous month button');
                            }
                            
                            await fixture.page.waitForTimeout(300);
                            currentMonthYear = await this.elements.datePickerMonthYearHeader().textContent();
                            attempts++;
                        }
                        
                        if (attempts >= maxAttempts) {
                            console.warn(`Could not navigate to ${monthName} ${year} after ${maxAttempts} attempts`);
                        }
                    }
                } else {
                    console.log(`Already on ${monthName} ${year}`);
                }
            } catch (monthYearError) {
                console.log(`Error navigating to month/year: ${monthYearError}`);
            }
            
            // Now try to select the day using multiple strategies
            let daySelected = false;
            
            // Strategy 1: Look for the specific day button using the rdrDay class and day number
            try {
                // Find all day buttons that match our day number
                const daySelector = fixture.page.locator(`button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled) .rdrDayNumber span:has-text("${day}")`);
                const dayCount = await daySelector.count();
                
                if (dayCount > 0) {
                    // Click the first matching day button
                    await daySelector.first().click();
                    console.log(`Selected day ${day} using day number strategy`);
                    daySelected = true;
                    // Take a screenshot after day selection
                    await ScreenshotHelper.takeScreenshot(`${context.toLowerCase()}-day-selected`);
                }
            } catch (dayError) {
                console.log(`Error selecting day using day number strategy: ${dayError}`);
            }
            
            // Strategy 2: Try to click by exact day number with specific parent class
            if (!daySelected) {
                try {
                    const exactDaySelector = fixture.page.locator(`.rdrDays button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled) .rdrDayNumber span:text-is("${day}")`);
                    await exactDaySelector.click();
                    console.log(`Selected day ${day} using exact text match strategy`);
                    daySelected = true;
                } catch (exactDayError) {
                    console.log(`Error selecting day using exact text match: ${exactDayError}`);
                }
            }
            
            // Strategy 3: Try to find the day using Playwright's more advanced text search
            if (!daySelected) {
                try {
                    const textSearchSelector = fixture.page.locator(`button.rdrDay:visible`).filter({ hasText: String(day) });
                    await textSearchSelector.click();
                    console.log(`Selected day ${day} using text search strategy`);
                    daySelected = true;
                } catch (textSearchError) {
                    console.log(`Error selecting day using text search: ${textSearchError}`);
                }
            }
            
            // Strategy 4: Last resort - try to find any clickable element with our day
            if (!daySelected) {
                try {
                    const genericButtonSelector = fixture.page.getByRole('button', { name: String(day) }).filter({ hasText: String(day) });
                    await genericButtonSelector.click();
                    console.log(`Selected day ${day} using generic button strategy`);
                    daySelected = true;
                } catch (genericError) {
                    console.log(`Error selecting day using generic button strategy: ${genericError}`);
                    
                    // If all else fails, we have to report failure
                    throw new Error(`Could not select day ${day} in ${monthName} ${year} using any strategy`);
                }
            }
            
            return;
        } catch (error) {
            console.error(`Error selecting ${context} date: ${error}`);
            await ScreenshotHelper.takeScreenshot(`${context.toLowerCase()}-date-selection-error`);
            throw error;
        }
    }

    /**
     * Clicks the search button to initiate flight search
     */
    public static async clickSearchButton(): Promise<void> {
        await this.elements.searchButton().click();
    }

    /**
     * Checks if the flight search form is visible
     */
    public static async isSearchFormVisible(): Promise<boolean> {
        const departureVisible = await this.elements.departureSectionContainer().isVisible();
        const arrivalVisible = await this.elements.arrivalSectionContainer().isVisible();
        return departureVisible && arrivalVisible;
    }

    /**
     * Gets the current value of the departure location input
     */
    public static async getDepartureLocation(): Promise<string> {
        return await this.elements.departureInput().inputValue();
    }

    /**
     * Gets the current value of the arrival location input
     */
    public static async getArrivalLocation(): Promise<string> {
        return await this.elements.arrivalInput().inputValue();
    }

    /**
     * Gets the selected date from the date picker
     */
    public static async getSelectedDate(): Promise<string> {
        return await this.elements.datePicker().inputValue();
    }

    /**
     * Sets both departure and return dates for round trip
     * @param departureDate - Departure date in YYYY-MM-DD format
     * @param returnDate - Return date in YYYY-MM-DD format
     */
    public static async setDepartureAndReturnDates(departureDate: string, returnDate: string): Promise<void> {
        await this.setDepartureDate(`${departureDate},${returnDate}`, 'roundtrip');
    }

    /**
     * Clicks the back to chat button
     */
    public static async clickBackToChat(): Promise<void> {
        await this.elements.backToChatButton().click();
    }

    /**
     * Opens the trip type selection dialog
     */
    public static async openTripTypeSelection(): Promise<void> {
        await this.elements.tripTypeButton().click();
    }

    /**
     * Opens the passengers selection dialog
     */
    public static async openPassengersSelection(): Promise<void> {
        await this.elements.passengersButton().click();
    }

    /**
     * Opens the class selection dialog     */    public static async openClassSelection(): Promise<void> {
        try {
            console.log('Opening class selection dialog...');
            // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-open-class-dialog');
            
            // Try multiple approaches to click the class selection button
            try {
                // First try the defined selector
                await this.elements.classSelectionButton().click();
                console.log('Clicked class selection button using defined selector');
            } catch (error) {
                console.log('Primary selector failed, trying alternative approaches');
                
                // Try clicking by text content
                try {
                    await fixture.page.click('button:has-text("Select Class")');
                    console.log('Clicked class selection button by text content');
                } catch (textError) {
                    // Try using a more specific selector based on the HTML structure (exactly as provided)
                    try {
                        await fixture.page.click('button[type="button"][aria-haspopup="dialog"] span:has-text("Select Class")');
                        console.log('Clicked class selection button span by specific attributes');
                    } catch (spanError) {
                        try {
                            await fixture.page.click('button[type="button"][aria-haspopup="dialog"]');
                            console.log('Clicked class selection button by specific attributes');
                        } catch (attrError) {
                            // Last resort: use JavaScript evaluation with the exact HTML structure you provided
                            const wasClicked = await fixture.page.evaluate(() => {
                                // Find the button with the exact structure from the HTML you provided
                                const buttonWithSpan = Array.from(document.querySelectorAll('button')).find(btn => {
                                    // Check if it has the right type and attributes
                                    if (btn.getAttribute('type') === 'button' && 
                                        btn.getAttribute('aria-haspopup') === 'dialog') {
                                        
                                        // Check if it has a span with "Select Class" text
                                        const span = btn.querySelector('span');
                                        return span && span.textContent === 'Select Class';
                                    }
                                    return false;
                                });
                                
                                if (buttonWithSpan) {
                                    buttonWithSpan.click();
                                    return true;
                                }
                                
                                // If still not found, look for any button containing "Select Class"
                                const anySelectClassButton = Array.from(document.querySelectorAll('button')).find(btn => 
                                    btn.textContent && btn.textContent.includes('Select Class'));
                                
                                if (anySelectClassButton) {
                                    anySelectClassButton.click();
                                    return true;
                                }
                                
                                return false;
                            });
                            
                            if (wasClicked) {
                                console.log('Successfully clicked class selection button using JavaScript evaluation');
                            } else {
                                throw new Error('Could not find and click the "Select Class" button using any method');
                            }
                        }
                    }
                }
             }
            
            // Wait for a moment for dialog to appear
            await fixture.page.waitForTimeout(1000);
            
            // Verify dialog opened
            try {
                const dialogVisible = await this.elements.classTypeDialog().isVisible();
                if (dialogVisible) {
                    console.log('✅ Class selection dialog opened successfully');
                    await ScreenshotHelper.takeScreenshot('class-dialog-visible');
                } else {
                    console.log('⚠️ Class dialog may not be visible yet');
                }
            } catch (visibilityError) {
                console.log('Could not verify dialog visibility:', visibilityError);
            }
            
        } catch (error) {
            console.error('Failed to open class selection dialog:', error);
            await ScreenshotHelper.takeScreenshot('class-selection-button-error');
            throw error;
        }
    }

    /**
     * Opens the filters dialog (mobile view)
     */
    public static async openFilters(): Promise<void> {
        await this.elements.filtersButton().click();
    }

    /**
     * Gets the current passenger count
     */
    public static async getPassengerCount(): Promise<string> {
        return await this.elements.passengerCount().textContent() || '';
    }

    /**
     * Gets the current trip type
     */
    public static async getTripType(): Promise<string> {
        return await this.elements.tripTypeText().textContent() || '';
    }

    /**
     * Gets the current class selection
     */
    public static async getClassType(): Promise<string> {
        return await this.elements.classTypeText().textContent() || '';
    }

    /**
     * Checks if filters button is visible (mobile view check)
     */
    public static async isFilterButtonVisible(): Promise<boolean> {
        return await this.elements.filtersButton().isVisible();
    }

    /**
     * Clicks the expand icon to show flight details
     */
    public static async expandFlightDetails(): Promise<void> {
        await this.elements.flightDetailsExpandIcon().click();
    }

    /**
     * Checks if flight details section is expanded
     */
    public static async isFlightDetailsExpanded(): Promise<boolean> {
        return await this.elements.expandedFlightDetails().isVisible();
    }

    /**
     * Clicks the select flight button in the expanded flight details
     */
    public static async selectFlight(): Promise<void> {
        await this.elements.selectFlightButton().click();
    }

    /**
     * Checks if select flight button is visible
     */
    public static async isSelectFlightButtonVisible(): Promise<boolean> {
        return await this.elements.selectFlightButton().isVisible();
    }

    /**
     * Gets all available select flight buttons on the page
     * Useful when multiple flights are displayed
     */
    public static async getAllSelectFlightButtons(): Promise<number> {
        return await this.elements.selectFlightButton().count();
    }

    // Login Methods
    public static async setUsername(username: string): Promise<void> {
        await this.elements.usernameInput().fill(username);
    }

    public static async setPassword(password: string): Promise<void> {
        await this.elements.passwordInput().fill(password);
    }

    public static async clickSigninButton(): Promise<void> {
        await this.elements.signinButton().click();
    }    // Trip Type Methods
    public static async selectTripType(type: string): Promise<void> {
        try {
            console.log(`Selecting trip type: ${type}`);
            
            // Wait for dialog to appear
            await this.elements.tripTypeDialog().waitFor({ timeout: 5000 });
            
            // Find the appropriate option based on the trip type
            if (type.toLowerCase() === 'oneway') {
                // Click the checkbox next to "One Way"
                await this.elements.oneWayOption().click();
            } else if (type.toLowerCase() === 'roundtrip') {
                // Click the checkbox next to "Round Trip"
                await this.elements.roundTripOption().click();
            } else if (type.toLowerCase() === 'multitrip') {
                // Click the checkbox next to "Multi-Trip"
                await this.elements.multiTripOption().click();
            } else {
                throw new Error(`Trip type '${type}' is not supported`);
            }
            
            // Wait for dialog to close
            await fixture.page.waitForTimeout(1000);
        } catch (error) {
            console.error(`Error selecting trip type: ${error}`);            // Take a screenshot for debugging
            await ScreenshotHelper.takeScreenshot('trip-type-error');
            throw error;
        }
    }    // Passenger Count Methods
    public static async setPassengerCount(count: string): Promise<void> {
        try {
            console.log(`Setting passenger count to: ${count}`);
            
            // Parse the count
            const targetCount = parseInt(count, 10);
            
            // Wait for dialog to appear
            await this.elements.passengerCountDialog().waitFor({ timeout: 5000 });
            
            // Wait a moment for the dialog to stabilize
            await fixture.page.waitForTimeout(500);
            
            // Get current count
            const adultsCountElement = this.elements.adultsCountText();
            const currentCountText = await adultsCountElement.textContent() || '0';
            console.log(`Current passenger count: ${currentCountText}`);
            const currentCount = parseInt(currentCountText, 10);
            
            // Increase or decrease as needed
            const diff = targetCount - currentCount;
            console.log(`Need to change by: ${diff}`);
            
            if (diff > 0) {
                for (let i = 0; i < diff; i++) {
                    // Click the + button in the Adults section
                    await this.elements.adultIncrementButton().click();
                    await fixture.page.waitForTimeout(200);
                }
            } else if (diff < 0) {
                for (let i = 0; i < Math.abs(diff); i++) {
                    // Click the - button in the Adults section
                    await this.elements.adultDecrementButton().click();
                    await fixture.page.waitForTimeout(200);
                }
            }
            
            // Wait a moment
            await fixture.page.waitForTimeout(500);
            
            // No need for a confirm button, it should auto-close or we can click outside
            // but if there is one:
            try {
                const confirmButton = this.elements.confirmButton();
                const isVisible = await confirmButton.isVisible();
                if (isVisible) {
                    await confirmButton.click();
                }
            } catch (e) {
                // If there's no confirm button, just click outside to close
                await this.elements.totalTravelersText().click({ position: { x: 10, y: 10 } });
            }
            
            // Wait for dialog to close
            await fixture.page.waitForTimeout(1000);
        } catch (error) {
            console.error(`Error setting passenger count: ${error}`);            // Take a screenshot for debugging
            await ScreenshotHelper.takeScreenshot('passenger-count-error');
            throw error;
        }
    }    // Class Type Methods
    public static async selectClassType(classType: string): Promise<void> {
        try {
            console.log(`Selecting class type: ${classType}`);
            
            // // First click the class selection button
            // await this.openClassSelection();
            // await fixture.page.waitForTimeout(1000);
            
            // // Wait for dialog to appear with increased timeout
            // try {
            //     await fixture.page.waitForSelector(this.elements.classTypeDialog, { timeout: 5000 });
            // } catch (e) {
            //     console.log('Class type dialog not detected, clicking class selection button again');
            //     await this.openClassSelection();
            //     await fixture.page.waitForSelector(this.elements.classTypeDialog, { timeout: 5000 });
            // }
            
            // Select the appropriate class type based on the provided classType
            let classOption;
            switch (classType.toLowerCase()) {
                case 'economy':
                case 'economy with restrictions':
                    classOption = this.elements.economyOption;
                    break;
                case 'premium economy':
                case 'economy premium':
                    classOption = this.elements.premiumEconomyOption;
                    break;
                case 'business':
                    classOption = this.elements.businessOption;
                    break;
                case 'first':
                    classOption = this.elements.firstClassOption;
                    break;
                default:
                    throw new Error(`Class type '${classType}' is not supported`);
            }
            
            console.log(`Using selector for class type: ${classOption}`);
              // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-class-selection');
            
            // Click the class option
            await fixture.page.click(classOption);
            
            // Wait for dialog to close
            await fixture.page.waitForTimeout(1000);
            
            console.log('Class selection completed successfully');
        } catch (error) {
            console.error(`Error selecting class type: ${error}`);            // Take a screenshot for debugging
            await ScreenshotHelper.takeScreenshot('class-selection-error');
            throw error;
        }    }    // Search Results Methods
    public static async hasSearchResults(): Promise<boolean> {
        try {
            console.log('Checking for flight search results with enhanced logic...');
            
            // First try using the specialized waitForFlightResults method
            const resultsFound = await this.waitForFlightResults(60000);
            if (resultsFound) {
                console.log('waitForFlightResults found flight results');
                return true;
            }
            
            // If the specialized method failed, try the original approach as fallback
            console.log('Specialized detection failed, trying original approach...');
            await this.elements.searchResultsContainer().waitFor({ timeout: 15000 });
            
            // Take a screenshot after search results appear
            await ScreenshotHelper.takeScreenshot('flight-search-results-found-fallback', true, true);
            
            // Count the results
            const resultsCount = await this.elements.searchResultItem().count();
            console.log(`Found ${resultsCount} flight results using fallback method`);
            
            // Also check for "no results" message
            const hasNoResultsMsg = await this.elements.noResultsMessage().isVisible();
            if (hasNoResultsMsg) {
                console.log('Found "No flights found" message');
                await ScreenshotHelper.takeScreenshot('no-flights-found-message', true, true);
                return false;
            }
            
            return resultsCount > 0;
        } catch (error) {
            console.error(`Error checking flight search results: ${error}`);
            await ScreenshotHelper.takeScreenshot('flight-search-results-error', true, true);
            
            // Try one last desperate check for any flight-related content
            try {
                console.log('Trying final fallback checks for any flight-related content');
                
                // Look for any text that might indicate flight results
                const flightTexts = [
                    'Flight', 'Departure', 'Arrival', 'Duration', 'Nonstop', 'Stop', 
                    'Economy', 'Business', 'First Class'
                ];
                
                for (const text of flightTexts) {
                    const textExists = await fixture.page.isVisible(`text="${text}"`, { timeout: 1000 })
                        .catch(() => false);
                    
                    if (textExists) {
                        console.log(`Found flight-related text: "${text}" - assuming results exist`);
                        await ScreenshotHelper.takeScreenshot('flight-text-found', true, true);
                        return true;
                    }
                }
            } catch (fallbackError) {
                console.log('Final fallback check failed:', fallbackError);
            }
            
            return false;
        }
    }
    /**
     * Sets the passenger counts from an object containing adult, child, and infant counts
     * Enhanced version that verifies total traveler count and handles each passenger type separately
     * @param passengerData - Object containing counts for adults, children, and infants
     */    public static async setPassengerCountFromObject(passengerData: { adult?: number, child?: number, infant?: number }): Promise<void> {
        try {
            // Ensure we have valid numbers, defaulting to 0 if undefined
            // Adults needs to be at least 1, so we'll enforce that minimum
            const adultCount = Math.max(1, passengerData.adult || 0);
            const childCount = passengerData.child || 0;
            const infantCount = passengerData.infant || 0;
            
            console.log(`Setting passenger counts: Adults=${adultCount}, Children=${childCount}, Infants=${infantCount}`);
            
            // Calculate total expected travelers
            const totalExpectedTravelers = adultCount + childCount + infantCount;
            console.log(`Total expected travelers: ${totalExpectedTravelers}`);
            
            // Make sure passengers dialog is open and ready for interaction
            let isDialogVisible = false;
            let attempts = 0;
            const maxAttempts = 3;
            
            // First, ensure we've clicked the passengers button to open the dialog
            try {
                console.log('Opening passenger selection dialog...');
                await this.openPassengersSelection();
                await fixture.page.waitForTimeout(1000);
            } catch (openError) {
                console.warn(`Initial passenger button click failed: ${openError}`);
                // We'll still continue with the dialog check below
            }
            
            // Now check if the dialog is visible and has the expected elements
            while (!isDialogVisible && attempts < maxAttempts) {
                try {
                    // Add more robust wait - wait for the dialog to be visible, not just present in DOM
                    await fixture.page.waitForSelector("div[role='dialog']", { 
                        state: 'visible',
                        timeout: 5000 
                    });
                      // Take a screenshot of what we're looking at
                    await ScreenshotHelper.takeScreenshot(`travelers-dialog-visible-check-${attempts}`);
                    
                    // Verify we see the travelers dialog by looking for specific traveler type texts
                    const hasAdults = await fixture.page.isVisible("text=Adults");
                    const hasChildren = await fixture.page.isVisible("text=Children");
                    const hasInfants = await fixture.page.isVisible("text=Infants");
                    
                    console.log(`Dialog visibility check: Adults=${hasAdults}, Children=${hasChildren}, Infants=${hasInfants}`);
                    
                    if (hasAdults && hasChildren) {
                        isDialogVisible = true;
                        console.log('✓ Travelers dialog is visible and contains expected elements');
                    } else {
                        throw new Error('Dialog element found but missing expected traveler type sections');
                    }
                } catch (error) {
                    attempts++;
                    console.log(`Travelers dialog not properly visible on attempt ${attempts}, retrying...`);
                      // Take screenshot to help with debugging
                    await ScreenshotHelper.takeScreenshot(`travelers-dialog-attempt-${attempts}`);
                    
                    // If not visible and we have attempts remaining, try clicking the button again
                    if (attempts < maxAttempts) {
                        try {
                            // Try clicking the button again
                            await this.openPassengersSelection();
                            
                            // Wait a bit longer between attempts
                            await fixture.page.waitForTimeout(2000);
                        } catch (clickError) {
                            console.warn(`Failed to click passenger button on retry: ${clickError}`);
                        }
                    }
                }
            }
            
            // Throw error if we couldn't get the dialog to appear
            if (!isDialogVisible) {
                throw new Error('Travelers dialog did not appear after multiple attempts');
            }            // First take a screenshot of the dialog
            await ScreenshotHelper.takeScreenshot('travelers-dialog-before-changes');
            
            // Processing order is important - start with most stable/reliable inputs
            console.log('Starting traveler count adjustments in sequence...');
            
            // 1. First Adults as they're most reliable and typically have special validation
            console.log('Step 1: Setting Adults count...');
            await this.adjustPassengerTypeCount('Adults', adultCount);
            await fixture.page.waitForTimeout(500); // Wait for UI update
            
            // 2. Then Children
            console.log('Step 2: Setting Children count...');
            await this.adjustPassengerTypeCount('Children', childCount);
            await fixture.page.waitForTimeout(500); // Wait for UI update
            
            // 3. Finally Infants
            console.log('Step 3: Setting Infants count...');
            await this.adjustPassengerTypeCount('Infants', infantCount);
            await fixture.page.waitForTimeout(500); // Wait for UI update
              // Take screenshot after all changes
            await ScreenshotHelper.takeScreenshot('travelers-dialog-after-changes');
            
            // Verify the total travelers count matches what we expect
            let totalVerified = false;
            
            console.log('Verifying total travelers count...');
            try {
                // Multiple strategies to find the total travelers count display
                const totalSelectors = [
                    "p.text-xs:has-text('Travelers')",
                    "span:has-text('Travelers')",
                    "text=/\\d+ Traveler[s]?/"
                ];
                
                for (const selector of totalSelectors) {
                    try {
                        if (await fixture.page.isVisible(selector, { timeout: 1000 })) {
                            const totalText = await fixture.page.textContent(selector);
                            console.log(`Found total travelers text: "${totalText}"`);
                            
                            // Extract number from text
                            const matches = totalText?.match(/(\d+)/);
                            if (matches && matches.length > 1) {
                                const actualTotal = parseInt(matches[1], 10);
                                console.log(`Total travelers shown in UI: ${actualTotal}`);
                                
                                if (actualTotal !== totalExpectedTravelers) {
                                    console.warn(`Warning: UI shows ${actualTotal} travelers but we expected ${totalExpectedTravelers}`);
                                } else {
                                    console.log('✓ Total travelers count matches expected value');
                                }
                                
                                totalVerified = true;
                                break;
                            }
                        }
                    } catch (selectorError) {
                        console.log(`Selector ${selector} failed: ${selectorError}`);
                        // Continue to next selector
                    }
                }
                
                if (!totalVerified) {
                    console.warn('Could not verify total travelers count from UI');
                }
            } catch (totalError) {
                console.warn(`Error checking total travelers: ${totalError}`);
            }
            
            console.log('Confirming traveler selection...');
            
            // Try multiple strategies to confirm and close the dialog
            const closeStrategies = [
                // Strategy 1: Look for a specific confirm button
                async () => {
                    try {
                        const confirmSelectors = [
                            "button:has-text('Confirm')",
                            "button:has-text('Done')",
                            "button:has-text('Apply')",
                            "button:has-text('Ok')"
                        ];
                        
                        for (const selector of confirmSelectors) {
                            if (await fixture.page.isVisible(selector)) {
                                console.log(`Found confirm button with selector: ${selector}, clicking it`);
                                await fixture.page.click(selector);
                                await fixture.page.waitForTimeout(1000);
                                return true;
                            }
                        }
                        return false;
                    } catch (error) {
                        console.log(`Confirm button strategy failed: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 2: Click outside the dialog
                async () => {
                    try {
                        console.log('Clicking outside dialog to close it');
                        await fixture.page.mouse.click(10, 10);
                        await fixture.page.waitForTimeout(1000);
                        
                        // Check if this worked
                        const dialogGone = !(await fixture.page.isVisible("div[role='dialog']"));
                        return dialogGone;
                    } catch (error) {
                        console.log(`Click outside strategy failed: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 3: Press Escape key
                async () => {
                    try {
                        console.log('Pressing Escape key to close dialog');
                        await fixture.page.keyboard.press('Escape');
                        await fixture.page.waitForTimeout(1000);
                        
                        // Check if this worked
                        const dialogGone = !(await fixture.page.isVisible("div[role='dialog']"));
                        return dialogGone;
                    } catch (error) {
                        console.log(`Escape key strategy failed: ${error}`);
                        return false;
                    }
                }
            ];
            
            // Try each close strategy until one works
            let dialogClosed = false;
            for (const strategy of closeStrategies) {
                dialogClosed = await strategy();
                if (dialogClosed) {
                    console.log('✓ Successfully closed travelers dialog');
                    break;
                }
            }
            
            // Final verification that dialog is closed
            try {
                const stillVisible = await fixture.page.isVisible("div[role='dialog']");
                if (stillVisible) {
                    console.warn('Warning: Travelers dialog may still be open after all close attempts');
                    await ScreenshotHelper.takeScreenshot('travelers-dialog-not-closed');
                } else {
                    console.log('✓ Confirmed travelers dialog is closed');
                }
            } catch (verifyError) {
                console.warn(`Error during final dialog visibility check: ${verifyError}`);
            }
              // One more screenshot showing the end result in the main UI
            await ScreenshotHelper.takeScreenshot('travelers-selection-completed');
              } catch (error) {
            console.error(`Error setting passenger counts: ${error}`);
              // Take a screenshot to help with debugging
            await ScreenshotHelper.takeScreenshot('travelers-selection-error');
            
            // Before giving up, try one more approach - completely reset and try again if this looks like a first attempt
            if (!error.message.includes('retry')) {
                console.log('Attempting one more approach with complete reset...');
                
                try {
                    // Try to close any open dialog
                    await fixture.page.keyboard.press('Escape');
                    await fixture.page.waitForTimeout(1000);
                    
                    // Wait a moment and then try opening the selection again
                    await fixture.page.waitForTimeout(2000);
                    await this.openPassengersSelection();
                    await fixture.page.waitForTimeout(2000);
                    
                    // Now try individual adjustments with enhanced error handling
                    console.log('Retry: Setting Adults count...');
                    if (passengerData.adult && passengerData.adult > 0) {
                        await this.adjustPassengerTypeCount('Adults', passengerData.adult);
                        await fixture.page.waitForTimeout(1000);
                    }
                    
                    console.log('Retry: Setting Children count...');
                    if (passengerData.child && passengerData.child > 0) {
                        await this.adjustPassengerTypeCount('Children', passengerData.child);
                        await fixture.page.waitForTimeout(1000);
                    }
                    
                    console.log('Retry: Setting Infants count...');
                    if (passengerData.infant && passengerData.infant > 0) {
                        await this.adjustPassengerTypeCount('Infants', passengerData.infant);
                        await fixture.page.waitForTimeout(1000);
                    }
                    
                    // Try to close the dialog
                    await fixture.page.keyboard.press('Escape');
                    
                    console.log('Recovery attempt completed');
                    await ScreenshotHelper.takeScreenshot('travelers-selection-recovery');
                    
                    // We successfully recovered, so don't throw the error
                    return;
                    
                } catch (recoveryError) {
                    console.error(`Recovery attempt also failed: ${recoveryError}`);
                    // Fall through to throwing the original error
                }
            }
            
            // If we got here, both original attempt and recovery failed
            throw new Error(`Failed to set traveler counts (Adults: ${passengerData.adult}, Children: ${passengerData.child}, Infants: ${passengerData.infant}): ${error.message}`);
        }
    }
    
    /**
     * Verifies that the total travelers count displayed in the UI matches the expected count
     * @param expectedTotal - The expected total number of travelers
     */
    private static async verifyTotalTravelersCount(expectedTotal: number): Promise<void> {
        try {
            console.log(`Verifying total travelers count is ${expectedTotal}`);
              // Take screenshot for verification
            await ScreenshotHelper.takeScreenshot('travelers-count-verification');
            
            // Try multiple strategies to verify the total count
            let verificationSuccessful = false;
            let actualTotal: number | null = null;
            
            // Strategy 1: Check the travelers button text for a number
            try {
                const buttonText = await this.elements.passengersButton().textContent();
                const matches = buttonText?.match(/(\d+)/);
                if (matches && matches.length > 1) {
                    actualTotal = parseInt(matches[1], 10);
                    verificationSuccessful = true;
                    console.log(`Found traveler count in button text: ${actualTotal}`);
                }
            } catch (buttonError) {
                console.warn(`Could not get total from button text: ${buttonError}`);
            }
            
            // Strategy 2: Look for specific total travelers span
            if (!verificationSuccessful) {
                try {
                    const spanText = await this.elements.totalTravelersCountSpan().textContent();
                    if (spanText) {
                        const number = parseInt(spanText.trim(), 10);
                        if (!isNaN(number)) {
                            actualTotal = number;
                            verificationSuccessful = true;
                            console.log(`Found traveler count in dedicated span: ${actualTotal}`);
                        }
                    }
                } catch (spanError) {
                    console.warn(`Could not get total from span: ${spanError}`);
                }
            }
            
            // Strategy 3: Parse any visible text containing "Traveler" or "Travelers"
            if (!verificationSuccessful) {
                try {
                    const visibleText = await fixture.page.textContent('text=/\\d+ Traveler[s]?/');
                    if (visibleText) {
                        const matches = visibleText.match(/(\d+)/);
                        if (matches && matches.length > 1) {
                            actualTotal = parseInt(matches[1], 10);
                            verificationSuccessful = true;
                            console.log(`Found traveler count in generic text: ${actualTotal}`);
                        }
                    }
                } catch (textError) {
                    console.warn(`Could not get total from generic text: ${textError}`);
                }
            }
            
            if (!verificationSuccessful) {
                console.warn(`Unable to verify total travelers count in UI, assuming it's correct`);
                return;            }
            
            // If verification successful but count doesn't match, log a warning            
            if (actualTotal !== null && actualTotal !== expectedTotal) {
                console.warn(`Expected ${expectedTotal} travelers but found ${actualTotal} in UI`);
                await ScreenshotHelper.takeScreenshot('travelers-count-mismatch');
            } else if (actualTotal === expectedTotal) {
                console.log(`Successfully verified ${expectedTotal} travelers in UI`);
            }
            
        } catch (error) {
            console.error(`Error verifying total travelers count: ${error}`);
            // Do not throw - this is a verification step that shouldn't fail the test
        }
    }/**
     * Helper method to adjust passenger count for a specific type (Adults, Children, Infants)
     * This updated version uses precise selectors based on the exact HTML structure provided
     * @param passengerType - The type of passenger (Adults, Children, Infants)
     * @param targetCount - The desired count
     */    private static async adjustPassengerTypeCount(passengerType: string, targetCount: number): Promise<void> {
        try {
            console.log(`Adjusting ${passengerType} count to ${targetCount}`);
            
            // Selector for the passenger section div based on type
            // Using more precise selectors with attribute combinators to increase reliability
            const sectionSelector = `div.flex.justify-between.items-center.py-2:has(p.font-semibold:text('${passengerType}'))`;
              // Screenshot before for debugging
            await ScreenshotHelper.takeScreenshot(`${passengerType.toLowerCase()}-before-adjustment`);
            
            // Find the current count in the span element
            let currentCount = 0;
            let countFound = false;
            
            // Multiple strategies to find the current count
            const strategies = [
                // Strategy 1: Exact CSS selector based on the component structure
                async () => {
                    try {
                        const countSpanSelector = `${sectionSelector} span.w-6.xs\\:w-4.text-\\[\\#080236\\].text-center`;
                        const currentCountText = await fixture.page.$eval(countSpanSelector, el => el.textContent || '0');
                        currentCount = parseInt(currentCountText.trim(), 10) || 0;
                        console.log(`Strategy 1: Current ${passengerType} count from span: ${currentCount}`);
                        return true;
                    } catch (error) {
                        console.log(`Strategy 1 failed for ${passengerType}: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 2: Use XPath to find the count span near the traveler type text
                async () => {
                    try {
                        // Find the span that follows the passenger type text
                        const xpathSelector = `//p[contains(text(),'${passengerType}')]/ancestor::div[contains(@class, 'flex')]/descendant::span[contains(@class, 'text-center')]`;
                        const element = await fixture.page.$(`xpath=${xpathSelector}`);
                        
                        if (element) {
                            const text = await element.textContent();
                            if (text && /^\d+$/.test(text.trim())) {
                                currentCount = parseInt(text.trim(), 10);
                                console.log(`Strategy 2: Current ${passengerType} count from XPath: ${currentCount}`);
                                return true;
                            }
                        }
                        return false;
                    } catch (error) {
                        console.log(`Strategy 2 failed for ${passengerType}: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 3: Generic text search for any span with just a number near the passenger type
                async () => {
                    try {
                        const genericSpanSelector = `div:has-text('${passengerType}') span`;
                        const spans = await fixture.page.$$(genericSpanSelector);
                        
                        for (const span of spans) {
                            const text = await span.textContent();
                            if (text && /^\d+$/.test(text.trim())) {
                                currentCount = parseInt(text.trim(), 10);
                                console.log(`Strategy 3: Found ${passengerType} count in generic span: ${currentCount}`);
                                return true;
                            }
                        }
                        return false;
                    } catch (error) {
                        console.log(`Strategy 3 failed for ${passengerType}: ${error}`);
                        return false;
                    }
                }
            ];
            
            // Try each strategy until one succeeds
            for (const strategy of strategies) {
                countFound = await strategy();
                if (countFound) break;
            }
            
            if (!countFound) {
                console.warn(`All count detection methods failed for ${passengerType}, assuming count is 0`);
                currentCount = 0;
            }
              // Calculate the needed adjustment
            const diff = targetCount - currentCount;
            console.log(`Need to change ${passengerType} by ${diff}`);
            
            if (diff === 0) {
                console.log(`${passengerType} count is already at target value ${targetCount}, no changes needed`);
                return;
            }
            
            // Determine whether to increase or decrease count
            const isIncrease = diff > 0;
            
            // Multiple button selector strategies with fallbacks
            const buttonSelectors = [
                // Strategy 1: Most specific selector targeting the button inside the traveler section
                isIncrease 
                    ? `${sectionSelector} button:has-text('+')`
                    : `${sectionSelector} button:has-text('−')`,
                    
                // Strategy 2: XPath selector for finding the button near the passenger type text
                `xpath=//p[contains(text(),'${passengerType}')]/ancestor::div[2]/descendant::button[contains(text(),'${isIncrease ? '+' : '−'}')]`,
                
                // Strategy 3: General button selector with + or - text
                isIncrease 
                    ? `button:has-text('+')`
                    : `button:has-text('−')`,
                    
                // Strategy 4: CSS class-based selector as final fallback
                isIncrease 
                    ? `button.gap-2.whitespace-nowrap.rounded-md.text-sm.font-medium:has-text('+')`
                    : `button.gap-2.whitespace-nowrap.rounded-md.text-sm.font-medium:has-text('−')`
            ];
            
            // Click the button the required number of times
            const clicksNeeded = Math.abs(diff);
            console.log(`Need to click button ${clicksNeeded} times for ${passengerType}`);
              // Take a screenshot of the area before clicking
            await ScreenshotHelper.takeScreenshot(`${passengerType}-before-clicks`);
            
            for (let i = 0; i < clicksNeeded; i++) {
                let clickSuccessful = false;
                
                // Try each selector strategy until one works
                for (let j = 0; j < buttonSelectors.length; j++) {
                    try {
                        const selector = buttonSelectors[j];
                        
                        // Wait for the button to be visible and clickable
                        await fixture.page.waitForSelector(selector, { timeout: 1000, state: 'visible' });
                        
                        // Click the button
                        await fixture.page.click(selector);
                        console.log(`Click ${i + 1} of ${clicksNeeded} for ${passengerType} succeeded with selector strategy ${j + 1}`);
                        
                        // Add a delay between clicks to allow the UI to update
                        await fixture.page.waitForTimeout(300);
                        
                        clickSuccessful = true;
                        break; // Break out of the selector loop if click succeeds
                    } catch (selectorError) {
                        console.log(`Selector strategy ${j + 1} failed for ${passengerType}: ${selectorError}`);
                        // Continue to next selector strategy
                    }
                }
                
                // If all selector strategies failed, take a screenshot and throw an error
                if (!clickSuccessful) {
                    await ScreenshotHelper.takeScreenshot(`${passengerType}-click-error-${i}`);
                    throw new Error(`Failed to adjust ${passengerType} count - all button selector strategies failed`);
                }
                
                // Additional verification: check if count is updated after each click
                try {
                    // Wait a bit for the count to update
                    await fixture.page.waitForTimeout(200);
                    
                    // Try to find the updated count
                    const countSpanSelector = `${sectionSelector} span.w-6.xs\\:w-4.text-\\[\\#080236\\].text-center`;
                    const updatedCountText = await fixture.page.$eval(countSpanSelector, el => el.textContent || '0');
                    const updatedCount = parseInt(updatedCountText.trim(), 10) || 0;
                    
                    // Log the progress for debugging
                    console.log(`After click ${i + 1}, ${passengerType} count is now: ${updatedCount}`);
                    
                    // If we've already reached our target, we can stop clicking
                    if ((isIncrease && updatedCount >= targetCount) || 
                        (!isIncrease && updatedCount <= targetCount)) {
                        console.log(`Target count ${targetCount} reached after ${i + 1} clicks, stopping early`);
                        break;
                    }
                } catch (verificationError) {
                    console.log(`Could not verify updated count after click ${i + 1}: ${verificationError}`);
                    // Continue anyway, might still be working
                }
            }
              // Verify the final count using multiple strategies
            let finalCountVerified = false;
            let finalCount = -1;
              // Take a screenshot after all clicks are complete
            await ScreenshotHelper.takeScreenshot(`${passengerType.toLowerCase()}-after-clicks`);
            
            // Wait a moment for the UI to stabilize
            await fixture.page.waitForTimeout(500);
            
            // Try multiple strategies to verify final count
            const verificationStrategies = [
                // Strategy 1: Use the same selector as before
                async () => {
                    try {
                        const countSpanSelector = `${sectionSelector} span.w-6.xs\\:w-4.text-\\[\\#080236\\].text-center`;
                        const finalCountText = await fixture.page.$eval(countSpanSelector, el => el.textContent || '0');
                        finalCount = parseInt(finalCountText.trim(), 10) || 0;
                        console.log(`Verification Strategy 1: Final ${passengerType} count: ${finalCount}`);
                        return true;
                    } catch (error) {
                        console.log(`Verification Strategy 1 failed: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 2: Use XPath to find the count span
                async () => {
                    try {
                        const xpathSelector = `//p[contains(text(),'${passengerType}')]/ancestor::div[contains(@class, 'flex')]/descendant::span[contains(@class, 'text-center')]`;
                        const element = await fixture.page.$(`xpath=${xpathSelector}`);
                        
                        if (element) {
                            const text = await element.textContent();
                            if (text && /^\d+$/.test(text.trim())) {
                                finalCount = parseInt(text.trim(), 10);
                                console.log(`Verification Strategy 2: Final ${passengerType} count: ${finalCount}`);
                                return true;
                            }
                        }
                        return false;
                    } catch (error) {
                        console.log(`Verification Strategy 2 failed: ${error}`);
                        return false;
                    }
                },
                
                // Strategy 3: Look for any span with just a number near the passenger type
                async () => {
                    try {
                        const spans = await fixture.page.$$(`div:has-text('${passengerType}') span`);
                        
                        for (const span of spans) {
                            const text = await span.textContent();
                            if (text && /^\d+$/.test(text.trim())) {
                                finalCount = parseInt(text.trim(), 10);
                                console.log(`Verification Strategy 3: Final ${passengerType} count: ${finalCount}`);
                                return true;
                            }
                        }
                        return false;
                    } catch (error) {
                        console.log(`Verification Strategy 3 failed: ${error}`);
                        return false;
                    }
                }
            ];
            
            // Try each verification strategy until one succeeds
            for (const strategy of verificationStrategies) {
                finalCountVerified = await strategy();
                if (finalCountVerified) break;
            }
            
            if (finalCountVerified) {
                console.log(`Final ${passengerType} count: ${finalCount} (target: ${targetCount})`);
                
                if (finalCount !== targetCount) {
                    console.warn(`Warning: ${passengerType} count after adjustment (${finalCount}) doesn't match target count (${targetCount})`);
                      // Take an extra screenshot if we didn't reach the target
                    await ScreenshotHelper.takeScreenshot(`${passengerType.toLowerCase()}-count-mismatch`);
                } else {
                    console.log(`✓ Successfully set ${passengerType} count to ${targetCount}`);
                }
            } else {
                console.warn(`Could not verify final ${passengerType} count after multiple attempts`);
            }
              // Always take a final screenshot
            await ScreenshotHelper.takeScreenshot(`${passengerType.toLowerCase()}-after-adjustment`);
            
        } catch (error) {
            console.error(`Error adjusting ${passengerType} count: ${error}`);
            await ScreenshotHelper.takeScreenshot(`${passengerType.toLowerCase()}-adjustment-error`);
            throw error;
        }
    }

    /**
     * Checks if the passengers button is visible
     */
    public static async isPassengersButtonVisible(): Promise<boolean> {
        return await this.elements.passengersButton().isVisible();
    }
    
    /**
     * Checks if the passengers dialog is visible
     */
    public static async isPassengerDialogVisible(): Promise<boolean> {
        return await this.elements.passengerCountDialog().isVisible();
    }

    /**
     * Checks if location suggestions dropdown is visible
     */
    public static async isLocationSuggestionsVisible(): Promise<boolean> {
        return await this.elements.locationSuggestionDropdown().isVisible();
    }

    /**
     * Selects a location from the dropdown based on airport code
     * @param airportCode The airport code to select (e.g., "STN")
     */
    public static async selectLocationByAirportCode(airportCode: string): Promise<void> {
        console.log(`Selecting location with airport code: ${airportCode}`);
        
        // Wait for suggestions to be visible
        await this.elements.locationSuggestionDropdown().waitFor({ timeout: 10000 });
        
        // Take a screenshot before selection
        await ScreenshotHelper.takeScreenshot(`before-selecting-airport-${airportCode}`, true, true);
        
        try {
            // Try to click the airport option using getByText
            await fixture.page.getByText(`(${airportCode})`, { exact: false }).click();
            console.log(`Successfully selected airport with code ${airportCode}`);
        } catch (error) {
            console.error(`Failed to select airport with code ${airportCode}: ${error}`);
            
            // Take a screenshot of the error state
            await ScreenshotHelper.takeScreenshot(`airport-selection-error-${airportCode}`, true, true);
            
            // Try an alternative approach
            console.log('Trying alternative selection approach...');
            try {
                // Get all suggestion items
                const items = await this.elements.locationSuggestionItem().all();
                let selected = false;
                
                for (const item of items) {
                    const text = await item.textContent();
                    if (text && text.includes(`(${airportCode})`)) {
                        await item.click();
                        console.log(`Selected airport by iterating through items: ${text}`);
                        selected = true;
                        break;
                    }
                }
                
                if (!selected) {
                    throw new Error(`Could not find airport with code ${airportCode} in suggestions`);
                }
            } catch (altError) {
                console.error(`Alternative selection approach also failed: ${altError}`);
                throw new Error(`Failed to select airport with code ${airportCode}: ${error.message}`);
            }
        }
    }

    /**
     * Checks for flight search results using multiple selectors
     * This is a more comprehensive check that tries multiple selectors 
     * when standard ones don't work
     */
    public static async checkForFlightSearchResults(): Promise<{found: boolean, selector: string}> {
        console.log('Running comprehensive flight search results check...');
        
        // Array of possible selectors to try
        const selectors: Array<Function | string> = [
            // Standard selectors first
            this.elements.searchResultsContainer,
            this.elements.searchResultItem,
            
            // Alternative selectors based on common flight result patterns
            '.flight-results',
            '.search-results',
            '.flight-card',
            '.flight-item',
            'div[class*="flight"]',
            'div[class*="result"]',
            'div[class*="search-result"]',
            
            // Very generic fallbacks
            'div:has(button:has-text("Select Flight"))',
            'div:has(svg.lucide-plane)'
        ];
        
        // Try each selector
        for (const selector of selectors) {
            try {
                console.log(`Checking for selector: ${typeof selector === 'function' ? 'function' : selector}`);
                
                // Handle function selectors and string selectors differently
                let isVisible = false;
                let selectorString = '';
                
                if (typeof selector === 'function') {
                    // If it's a function that returns a locator
                    const locator = selector();
                    isVisible = await locator.isVisible({ timeout: 1000 });
                    selectorString = 'function-locator';
                } else {
                    // If it's a string selector
                    const locator = fixture.page.locator(selector as string);
                    isVisible = await locator.isVisible({ timeout: 1000 });
                    selectorString = selector as string;
                }
                
                if (isVisible) {
                    console.log(`Found flight results with selector: ${selectorString}`);
                    // Take a screenshot of what we found
                    await ScreenshotHelper.takeScreenshot(`flight-results-found-${selectorString.replace(/[^a-zA-Z0-9]/g, '-')}`, true, true);
                    return { found: true, selector: selectorString };
                }
            } catch (error) {
                // Ignore errors and continue trying other selectors
                console.log(`Selector ${selector} not found: ${error.message}`);
            }
        }
        
        // Take a screenshot of the current state if nothing was found
        await ScreenshotHelper.takeScreenshot('no-flight-results-found', true, true);
        console.log('No flight results found with any selector');
        return { found: false, selector: '' };
    }

    /**
     * Advanced method to wait for flight results with multiple strategies and debug information
     * @param maxWaitTime - Maximum time to wait in milliseconds
     * @returns Boolean indicating if flight results were found
     */
    public static async waitForFlightResults(maxWaitTime: number = 80000): Promise<boolean> {
        console.log(`Starting advanced flight results detection (max wait: ${maxWaitTime}ms)`);
        
        // Take a baseline screenshot
        await ScreenshotHelper.takeScreenshot('flight-results-wait-start', true, true);
        
        // Track the start time
        const startTime = Date.now();
        let elapsedTime = 0;
        
        // Define all possible selectors for flight results with priority ordering
        const resultSelectors = [
            this.elements.searchResultsContainer,
            this.elements.searchResultItem,
            '.flight-results',
            '.search-results',
            '.flight-card',
            '.flight-item',
            'div[class*="flight"]',
            'div[class*="result"]',
            'div:has(button:has-text("Select Flight"))',
            'div:has(svg.lucide-plane)',
            'div[class*="airline-logo"]',
            'div:has-text("Departure") >> xpath=following-sibling::div',
            'div:has-text("Duration") >> xpath=following-sibling::div'
        ];
        
        // Define common loading indicators
        const loadingSelectors = [
            'div[class*="loading"]', 
            'div[class*="spinner"]',
            'svg[class*="spinner"]',
            'div:has-text("Searching for flights")',
            'div:has-text("Please wait")'
        ];
        
        // First check if loading indicators are visible and wait for them to disappear
        try {
            console.log('Checking for loading indicators...');
            for (const loadingSelector of loadingSelectors) {
                const isLoading = await fixture.page.isVisible(loadingSelector, { timeout: 1000 }).catch(() => false);
                if (isLoading) {
                    console.log(`Loading indicator found: ${loadingSelector}. Waiting for it to disappear...`);
                    await fixture.page.waitForSelector(loadingSelector, { 
                        state: 'hidden', 
                        timeout: Math.min(30000, maxWaitTime / 2) 
                    }).catch(() => console.log('Loading indicator did not disappear'));
                    break;
                }
            }
        } catch (error) {
            console.log('Error checking loading indicators:', error);
        }
        
        // Calculate remaining wait time
        elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, maxWaitTime - elapsedTime);
        console.log(`Loading check completed. Elapsed time: ${elapsedTime}ms. Remaining time: ${remainingTime}ms`);
        
        if (remainingTime <= 0) {
            console.log('Maximum wait time exceeded during loading check');
            return false;
        }
        
        // Take another screenshot after loading indicators disappear
        await ScreenshotHelper.takeScreenshot('flight-results-after-loading', true, true);
        
        // First try using our search result container element
        try {
            const isVisible = await this.elements.searchResultsContainer().isVisible({ timeout: 5000 });
            if (isVisible) {
                console.log("Flight results found using searchResultsContainer");
                await ScreenshotHelper.takeScreenshot('flight-results-found', true, true);
                
                // Count the results
                const count = await this.elements.searchResultItem().count();
                console.log(`Found ${count} flight result items using searchResultItem`);
                return true;
            }
        } catch (error) {
            console.log('Could not find searchResultsContainer, trying alternative selectors');
        }
        
        // Try alternative selectors
        for (const selector of resultSelectors) {
            if (typeof selector === 'string') {
                try {
                    const locator = fixture.page.locator(selector);
                    const isVisible = await locator.isVisible({ timeout: 2000 });
                    if (isVisible) {
                        console.log(`Flight results found using selector: ${selector}`);
                        await ScreenshotHelper.takeScreenshot('flight-results-found', true, true);
                        
                        // Count the results
                        const count = await locator.count();
                        console.log(`Found ${count} flight result items using ${selector}`);
                        return true;
                    }
                } catch (error) {
                    console.log(`Error checking selector ${selector}:`, error.message);
                }
            }
        }
        
        // Take a final screenshot if nothing was found
        console.log('No flight results found after exhausting all strategies');
        await ScreenshotHelper.takeScreenshot('flight-results-not-found', true, true);
        
        return false;
    }
}
