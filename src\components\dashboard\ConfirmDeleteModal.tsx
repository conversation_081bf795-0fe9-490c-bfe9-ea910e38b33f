import React from "react";

interface ConfirmDeleteModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  message?: string;
}

const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({ open, onConfirm, onCancel, message }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="bg-white rounded-2xl shadow-lg p-8 w-full max-w-md flex flex-col items-center">
        <svg width="48" height="48" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="text-brand-black mb-4">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 7h12M9 7V4h6v3m2 0v12a2 2 0 01-2 2H8a2 2 0 01-2-2V7h12z" />
        </svg>
        <div className="text-2xl font-bold text-brand-black mb-2">Confirm Delete</div>
        <div className="text-lg text-brand-black mb-8">{message || "Are Sure you want to delete?"}</div>
        <div className="flex gap-4">
          <button
            className=" text-brand-white bg-brand px-6 py-2 rounded-[8px] font-semibol"
            onClick={onConfirm}
          >
            Delete
          </button>
          <button
            className="text-brand-white bg-brand px-6 py-2 rounded-[8px] font-semibol"
            onClick={onCancel}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeleteModal; 