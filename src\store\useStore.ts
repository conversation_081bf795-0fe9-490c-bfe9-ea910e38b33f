import { useMemo } from 'react';
import { AppStore, makeStore } from './store';

let store: AppStore;

export function initializeStore(preloadedState?: Partial<ReturnType<AppStore['getState']>>) {
  let _store = store ?? makeStore();

  if (preloadedState) {
    _store = makeStore();
    _store.dispatch({ type: 'HYDRATE', payload: preloadedState });
    store = undefined!;
  }

  if (typeof window === 'undefined') return _store;
  if (!store) store = _store;

  return _store;
}

export function useStore(initialState: Partial<ReturnType<AppStore['getState']>>) {
  return useMemo(() => initializeStore(initialState), [initialState]);
}
