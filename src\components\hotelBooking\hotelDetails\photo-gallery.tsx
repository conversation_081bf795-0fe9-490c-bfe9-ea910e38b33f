"use client";

import { X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface HotelImage {
    title: string;
    path: string;
    order: number;
    url: string;
    thumbnail: string;
}

interface PhotoGalleryProps {
    images: HotelImage[];
    hotelName: string;
    onClose: () => void;
}

export default function PhotoGallery({
    images,
    hotelName,
    onClose,
}: PhotoGalleryProps) {
    const [selectedImage, setSelectedImage] = useState<number | null>(null);

    // Debug: Log the images to check count
    console.log("PhotoGallery received images:", images?.length || 0, images);

    return (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg max-w-5xl w-full h-[90vh] flex flex-col overflow-hidden">
                {/* Sticky header */}
                <div className="sticky top-0 bg-white p-4 border-b flex items-center justify-between z-10">
                    <div>
                        <div className="text-[30px] font-bold text-[#080236] m-0 p-0">
                            {hotelName}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                            <div className="flex items-center text-[18px]">
                                <img
                                    src={
                                        "https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                                    }
                                    width={16}
                                    height={16}
                                    alt="rating star"
                                    className="object-contain m-0"
                                />
                                <span className="ml-1 font-bold text-[#4B4BC3] ">
                                    4.5
                                </span>
                            </div>
                            <span
                                className="text-[#707FF5]  hover:underline cursor-pointer"
                            >
                                128 Reviews
                            </span>
                            <span className="text-[#707FF5] ">• Hotel Location</span>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="h-8 w-8 rounded-full flex items-center justify-center hover:bg-gray-100"
                    >
                        <X className="h-5 w-5" />
                    </button>
                </div>

                {/* Scrollable image grid with padding around each image */}
                <div className="p-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 overflow-y-auto flex-grow">
                    {images.map((image, index) => (
                        <div key={index} className="p-[5px]">
                            <div
                                className="aspect-square relative rounded-lg overflow-hidden cursor-pointer shadow-sm hover:shadow-md transition-shadow duration-300 w-full h-full"
                                onClick={() => setSelectedImage(index)}
                            >
                                <Image
                                    src={image.url || "/placeholder.svg"}
                                    alt={`Hotel view ${index + 1}`}
                                    fill
                                    className="object-cover hover:scale-105 transition-transform duration-300"
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {selectedImage !== null && (
                <div className="fixed inset-0 bg-black/90 z-[60] flex flex-col items-center justify-center">
                    {/* Sticky header for fullscreen view */}
                    <div className="w-full sticky top-0 bg-black/50 p-4 flex items-center justify-between z-10 backdrop-blur-sm">
                        <h2 className="font-bold text-lg text-white">{hotelName}</h2>
                        <button
                            onClick={() => setSelectedImage(null)}
                            className="h-10 w-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20"
                        >
                            <X className="h-6 w-6 text-white" />
                        </button>
                    </div>

                    <div className="relative h-[80vh] w-[80vw] flex-grow">
                        <Image
                            src={images[selectedImage].url || "/placeholder.svg"}
                            alt={`Hotel view ${selectedImage + 1}`}
                            fill
                            className="object-contain"
                        />
                    </div>

                    {/* Image counter */}
                    <div className="w-full p-4 bg-black/50 text-white text-center backdrop-blur-sm">
                        {selectedImage + 1} / {images.length}
                    </div>
                </div>
            )}
        </div>
    )
}