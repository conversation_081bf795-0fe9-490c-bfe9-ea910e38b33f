import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import router from "next/router";
import {
  ChevronDown,
  ChevronLeft,
  ChevronUp,
  Plane,
  Users,
} from "lucide-react";
import SummaryCard from "@/components/chat_old/SummaryCard";
import FareCarousel from "@/components/flightsummary/FareCardCarousel";
import ChatFooter from "@/components/footer/chatFooter";
import AuthContainer from "@/components/layout/AuthContainer";
import LoadingOverlay from "@/components/LoadingOverlay/LoadingOverlay";
import Navbar from "@/components/NavBar";
import { FareOptionCard, Price, PriceBreakdown } from "@/constants/models";
import { useTravelerDisplay } from "@/context/TravelerDisplayContext";
import { getAirlineName } from "@/lib/utils/airline";
import { calculateGroupedPassengerPrice } from "@/lib/utils/calculateGroupedPassengerPrice";
import { formatFlightTime } from "@/lib/utils/flightTime";
import {
  getFlightCodes,
  isAppUser,
  setSearchParam,
} from "@/lib/utils/flightUtils";
import { formatShortLocationDisplay } from "@/lib/utils/formatLocation";
import { formatFlightPrice } from "@/lib/utils/formatPrice";
import { AppState } from "@/store/store";
import {
  resetTripSummary,
  updateTripSummary,
} from "@/store/slices/tripSummary";
import { activePages, TripOptions } from "@/constants/flight";
import PriceSummaryAccordion from "@/components/flightsummary/PriceSummaryAccordion";
import tracker from "@/utils/posthogTracker";
import { useSearchParams } from "next/navigation";
import {
  clearChatThreadState,
  updateCurrentThreadInfo,
} from "@/store/slices/chatThread";

import ImportantInfoSection from "@/components/tripSummary/important-info-section";

import { useAuth } from "@/components/AuthProvider/auth-Provider";
import { createFlightSearchPayload } from "@/lib/chatUtils";
import { processFlightSelection } from "@/lib/utils/processFlightSelection";
import { useFlightContext } from "@/context/FlightContext";
import { useCustomSession } from "@/hooks/use-custom-session";
import FlightCard from "@/components/flight/FlightCard";
import DashboardLayout from "@/layout/DashboardLayout";
import { useIsCollapsed } from "@/hooks/sidebar/useIsCollapsed";
import PriceBreakdownDialog from "@/components/flightsummary/PriceBreakdown";

const getComprehensiveFlightPrice = (flight: any) => {
  if (!flight) return null;

  // Extract flight-level price and taxes
  const flightPrice = flight.price?.amount || 0;
  const flightCurrency = flight.price?.currency || "GBP";

  // Extract flight-level taxes (credit card, bank charges)
  let flightTaxes = 0;
  if (flight.price?.tax_items?.[0]?.TaxItem) {
    flightTaxes = flight.price.tax_items[0].TaxItem.reduce(
      (sum: number, tax: any) => {
        return sum + parseFloat(tax.Amount || "0");
      },
      0
    );
  }

  // Extract passenger-level price and taxes
  let passengerPrice = 0;
  let passengerTaxes = 0;

  if (flight.passenger_prices?.[0]?.PassengerPrice) {
    const pp = flight.passenger_prices[0].PassengerPrice;
    passengerPrice = parseFloat(pp.Amount || "0");

    if (pp.TaxItemList?.[0]?.TaxItem) {
      passengerTaxes = pp.TaxItemList[0].TaxItem.reduce(
        (sum: number, tax: any) => {
          return sum + parseFloat(tax.Amount || "0");
        },
        0
      );
    }
  }

  return {
    // Flight-level totals (includes credit card fees)
    flightTotal: flightPrice,
    flightTaxes: flightTaxes,
    flightBasePrice: flightPrice - flightTaxes,

    // Passenger-level totals (airport fees, etc.)
    passengerTotal: passengerPrice,
    passengerTaxes: passengerTaxes,
    passengerBasePrice: passengerPrice - passengerTaxes,

    // Combined totals
    totalTaxes: flightTaxes + passengerTaxes,
    currency: flightCurrency,

    // Breakdown by type
    creditCardFees: flightTaxes,
    airportFees: passengerTaxes,
  };
};

const flightsummary = () => {
  const isCollapsed = useIsCollapsed();

  const [isSignInClicked, setIsSignInClicked] = useState<boolean>();
  const [isOpen, setIsOpen] = useState<boolean>();
  const { setToken } = useAuth();

  const [isCheckoutFixed, setIsCheckoutFixed] = useState(false);
  const [loadingForAppUser, setLoadingForAppUser] = useState(
    isAppUser() ? true : false
  );
  const [flightProcessed, setFlightProcessed] = useState(false);
  const [checkoutScrollState, setCheckoutScrollState] = useState<
    "top" | "normal" | "bottom"
  >("normal");
  const footerRef = useRef<HTMLDivElement | null>(null);
  const checkoutRef = useRef<HTMLDivElement | null>(null);
  const checkoutContainerRef = useRef<HTMLDivElement | null>(null);
  const [priceBreakdown, setPriceBreakdown] = useState<PriceBreakdown | null>(
    null
  );
  const [currency, setCurrency] = useState("GBP");
  const searchParams = useSearchParams();
  const paramAccessToken = searchParams.get("accessToken");
  const [isNavigating, setIsNavigating] = useState(false);

  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
  const { chatResult, allFlights } = chatThreadDetails;
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken as string;
  const dispatch = useDispatch();
  const {
    updateFareOptions,
    updatePassengerList,
    updateLuggageOptions,
    updateSupplierInfo,
    updateServiceFee,
  } = useFlightContext();

  const handleContinue = async () => {
    setIsNavigating(true);
    await new Promise((res) => setTimeout(res, 500));
    dispatch(
      updateTripSummary({
        activePage: activePages.trip_summary,
        from: activePages.flight_summary,
      })
    );
    const appUser = isAppUser();
    router.push(`/tripsummary${appUser ? `?${searchParams.toString()}` : ""}`);
  };

  const { calculateOutboundTravelers, calculateInboundTravelers } =
    useTravelerDisplay();

  const dept =
    tripSummaryDetails?.selectedOutboundFlight &&
    formatFlightTime(
      tripSummaryDetails?.selectedOutboundFlight.departure,
      tripSummaryDetails?.selectedOutboundFlight.origin
    );
  const arr =
    tripSummaryDetails?.selectedOutboundFlight &&
    formatFlightTime(
      tripSummaryDetails?.selectedOutboundFlight.arrival,
      tripSummaryDetails?.selectedOutboundFlight.destination
    );

  const firstSegment =
    tripSummaryDetails?.selectedOutboundFlight?.segments?.[0];
  const dep1 =
    firstSegment &&
    formatFlightTime(firstSegment.depart_date, firstSegment.origin);

  const outboundPriceData = useMemo(() => {
    return getComprehensiveFlightPrice(
      tripSummaryDetails?.selectedOutboundFlight
    );
  }, [tripSummaryDetails?.selectedOutboundFlight]);

  const inboundPriceData = useMemo(() => {
    return getComprehensiveFlightPrice(
      tripSummaryDetails?.selectedInboundFlight
    );
  }, [tripSummaryDetails?.selectedInboundFlight]);

  const totalFlightPrice = useMemo(() => {
    const outbound = outboundPriceData?.flightTotal || 0;
    const inbound = inboundPriceData?.flightTotal || 0;
    return outbound + inbound;
  }, [outboundPriceData, inboundPriceData]);

  useEffect(() => {
    if (paramAccessToken) {
      dispatch(resetTripSummary());
      dispatch(clearChatThreadState());
      setToken(paramAccessToken);
      setLoadingForAppUser(true);

      setTimeout(() => {
        const localTripSummaryDetails =
          localStorage.getItem("tripSummaryDetails");
        const localChatData = localStorage.getItem("chatData");
        if (localTripSummaryDetails) {
          const parsedTripSummaryDetails =
            typeof JSON.parse(localTripSummaryDetails ?? "{}") === "string"
              ? JSON.parse(JSON.parse(localTripSummaryDetails ?? "{}"))
              : JSON.parse(localTripSummaryDetails ?? "{}");
          dispatch(updateTripSummary(parsedTripSummaryDetails));
        }

        if (localChatData) {
          const parsedChatData =
            typeof JSON.parse(localChatData ?? "{}") === "string"
              ? JSON.parse(JSON.parse(localChatData ?? "{}"))
              : JSON.parse(localChatData ?? "{}");
          dispatch(updateCurrentThreadInfo(parsedChatData));
        }
      }, 2500);
    }
  }, [dispatch, paramAccessToken]);

  useEffect(() => {
    if (isAppUser() && paramAccessToken && status === "authenticated") {
      if (
        (Object.keys(tripSummaryDetails?.selectedOutboundFlight).length > 0 ||
          Object.keys(tripSummaryDetails?.selectedInboundFlight).length > 0) &&
        !flightProcessed
      ) {
        setLoadingForAppUser(true);
        flightProceesing();
      }
    }
  }, [
    tripSummaryDetails?.selectedOutboundFlight,
    tripSummaryDetails?.selectedInboundFlight,
    chatResult,
    flightProcessed,
    status,
  ]);

  useEffect(() => {
    const flowId = tracker.getFlowId();
    tracker.trackEvent("Step 2 - Page Loaded", {
      page: "Step2",
      flowId,
    });
  }, []);

  useEffect(() => {
    let selectedInOutBoundInfo = {};
    if (
      tripSummaryDetails?.selectedOutboundFlight &&
      Object.keys(tripSummaryDetails?.selectedOutboundFlight).length > 0
    ) {
      const response = calculateOutboundTravelers(
        tripSummaryDetails?.selectedOutboundFlight
      );
      selectedInOutBoundInfo = {
        ...selectedInOutBoundInfo,
        outboundTravelers: response.expandedTravelers,
        outboundTotal: response.totalAmount,
        outboundCounts: response.groupCounts,
      };
    }
    if (
      tripSummaryDetails?.selectedInboundFlight &&
      Object.keys(tripSummaryDetails?.selectedInboundFlight).length > 0
    ) {
      const response = calculateInboundTravelers(
        tripSummaryDetails?.selectedInboundFlight
      );
      selectedInOutBoundInfo = {
        ...selectedInOutBoundInfo,
        inboundTravelers: response.expandedTravelers,
        inboundTotal: response.totalAmount,
        inboundCounts: response.groupCounts,
      };
    }
    dispatch(
      updateTripSummary({ ...tripSummaryDetails, ...selectedInOutBoundInfo })
    );
  }, [
    tripSummaryDetails.selectedInboundFlight,
    tripSummaryDetails.selectedOutboundFlight,
  ]);

  useEffect(() => {
    setCurrency(tripSummaryDetails?.selectedOutboundFareOption?.currency);

    let finalTotalPrice = 0;
    let finalPriceBreakdown = null;

    // PRIORITY 1: Use fare option prices if selected
    if (
      tripSummaryDetails?.selectedOutboundFareOption ||
      tripSummaryDetails?.selectedInboundFareOption
    ) {
      let outboundFarePrice = 0;
      let inboundFarePrice = 0;
      let outboundFareTaxes = 0;
      let inboundFareTaxes = 0;
      let outboundCreditCardFees = 0;
      let inboundCreditCardFees = 0;
      let outboundAirportFees = 0;
      let inboundAirportFees = 0;
      let outboundPassengerPortion = 0;
      let inboundPassengerPortion = 0;
      let totalTicketsCount = 0;
      let updatedTravelerData = {};

      // Get outbound fare data
      if (tripSummaryDetails?.selectedOutboundFareOption) {
        outboundFarePrice = parseFloat(
          tripSummaryDetails.selectedOutboundFareOption.amount
        );

        if (tripSummaryDetails.selectedOutboundFareOption.flights) {
          const fareFlightData = getComprehensiveFlightPrice(
            tripSummaryDetails.selectedOutboundFareOption.flights
          );
          outboundFareTaxes = fareFlightData?.totalTaxes || 0;
          outboundCreditCardFees = fareFlightData?.creditCardFees || 0;
          outboundAirportFees = fareFlightData?.airportFees || 0;
          outboundPassengerPortion = fareFlightData?.passengerTotal || 0;

          //Calculate outbound traveler data
          const outboundResponse = calculateOutboundTravelers(
            tripSummaryDetails.selectedOutboundFareOption.flights
          );
          updatedTravelerData = {
            ...updatedTravelerData,
            outboundTravelers: outboundResponse.expandedTravelers,
            outboundTotal: outboundResponse.totalAmount,
            outboundCounts: outboundResponse.groupCounts,
          };
        }
      } else {
        outboundFarePrice = outboundPriceData?.flightTotal || 0;
        outboundFareTaxes = outboundPriceData?.totalTaxes || 0;
        outboundCreditCardFees = outboundPriceData?.creditCardFees || 0;
        outboundAirportFees = outboundPriceData?.airportFees || 0;
        outboundPassengerPortion = outboundPriceData?.passengerTotal || 0;
      }

      // Get inbound fare data
      if (tripSummaryDetails?.selectedInboundFareOption) {
        inboundFarePrice = parseFloat(
          tripSummaryDetails.selectedInboundFareOption.amount
        );

        if (tripSummaryDetails.selectedInboundFareOption.flights) {
          const fareFlightData = getComprehensiveFlightPrice(
            tripSummaryDetails.selectedInboundFareOption.flights
          );
          inboundFareTaxes = fareFlightData?.totalTaxes || 0;
          inboundCreditCardFees = fareFlightData?.creditCardFees || 0;
          inboundAirportFees = fareFlightData?.airportFees || 0;
          inboundPassengerPortion = fareFlightData?.passengerTotal || 0;

          //Calculate inbound traveler data
          const inboundResponse = calculateInboundTravelers(
            tripSummaryDetails.selectedInboundFareOption.flights
          );
          updatedTravelerData = {
            ...updatedTravelerData,
            inboundTravelers: inboundResponse.expandedTravelers,
            inboundTotal: inboundResponse.totalAmount,
            inboundCounts: inboundResponse.groupCounts,
          };
        }
      } else {
        inboundFarePrice = inboundPriceData?.flightTotal || 0;
        inboundFareTaxes = inboundPriceData?.totalTaxes || 0;
        inboundCreditCardFees = inboundPriceData?.creditCardFees || 0;
        inboundAirportFees = inboundPriceData?.airportFees || 0;
        inboundPassengerPortion = inboundPriceData?.passengerTotal || 0;
      }

      // Calculate total tickets from existing data
      if (tripSummaryDetails?.passengersList?.length > 0) {
        const passengerBreakdown = calculateGroupedPassengerPrice(
          tripSummaryDetails.passengersList
        );
        totalTicketsCount = passengerBreakdown.totalTickets;
      } else {
        totalTicketsCount =
          (tripSummaryDetails?.flightSearch?.adults || 0) +
          (tripSummaryDetails?.flightSearch?.children || 0) +
          (tripSummaryDetails?.flightSearch?.infants || 0);
      }

      finalTotalPrice = (outboundFarePrice || 0) + (inboundFarePrice || 0);

      const finalTotalTaxes = outboundFareTaxes + inboundFareTaxes;
      const finalBasePrice = finalTotalPrice - finalTotalTaxes;
      const finalCreditCardFees =
        outboundCreditCardFees + inboundCreditCardFees;
      const finalAirportFees = outboundAirportFees + inboundAirportFees;
      const finalPassengerPortion =
        outboundPassengerPortion + inboundPassengerPortion;

      finalPriceBreakdown = {
        grouped: { adult: null, child: null, infant: null },
        total: finalTotalPrice,
        totalTaxAndCharges: finalTotalTaxes,
        baseTicketPrice: finalBasePrice,
        totalTickets: totalTicketsCount,
        creditCardFees: finalCreditCardFees,
        airportFees: finalAirportFees,
        passengerPortion: finalPassengerPortion,
      };

      console.log(
        "Using fare option prices with complete API-based breakdown:",
        finalPriceBreakdown
      );

      //Include traveler data in Redux dispatch
      dispatch(
        updateTripSummary({
          totalPrice: finalTotalPrice,
          priceBreakdown: finalPriceBreakdown,
          ...updatedTravelerData,
        })
      );
    }
    // PRIORITY 2: Use flight-level prices
    else if (totalFlightPrice > 0) {
      finalTotalPrice = totalFlightPrice;

      // Creating comprehensive breakdown
      const totalCreditCardFees =
        (outboundPriceData?.creditCardFees || 0) +
        (inboundPriceData?.creditCardFees || 0);
      const totalAirportFees =
        (outboundPriceData?.airportFees || 0) +
        (inboundPriceData?.airportFees || 0);
      const totalAllTaxes = totalCreditCardFees + totalAirportFees;

      //Try to enhance with passenger breakdown for more detailed tax info
      if (tripSummaryDetails?.passengersList?.length > 0) {
        const passengerBreakdown = calculateGroupedPassengerPrice(
          tripSummaryDetails.passengersList
        );

        finalPriceBreakdown = {
          grouped: passengerBreakdown.grouped,
          total: totalFlightPrice,
          totalTaxAndCharges: totalAllTaxes,
          baseTicketPrice: totalFlightPrice - totalAllTaxes,
          totalTickets: passengerBreakdown.totalTickets,
          creditCardFees: totalCreditCardFees,
          airportFees: totalAirportFees,
          passengerPortion:
            (outboundPriceData?.passengerTotal || 0) +
            (inboundPriceData?.passengerTotal || 0),
        };

        console.log("Using flight price with detailed tax breakdown");
      } else {
        const totalTicketsFromSearch =
          (tripSummaryDetails?.flightSearch?.adults || 0) +
          (tripSummaryDetails?.flightSearch?.children || 0) +
          (tripSummaryDetails?.flightSearch?.infants || 0);

        finalPriceBreakdown = {
          grouped: { adult: null, child: null, infant: null },
          total: totalFlightPrice,
          totalTaxAndCharges: totalAllTaxes,
          baseTicketPrice: totalFlightPrice - totalAllTaxes,
          totalTickets: totalTicketsFromSearch,
          creditCardFees: totalCreditCardFees,
          airportFees: totalAirportFees,
          passengerPortion:
            (outboundPriceData?.passengerTotal || 0) +
            (inboundPriceData?.passengerTotal || 0),
        };

        console.log("Using flight-level data only");
      }

      //This dispatch for non-fare option cases
      dispatch(
        updateTripSummary({
          totalPrice: finalTotalPrice,
          priceBreakdown: finalPriceBreakdown,
        })
      );
    }
    // PRIORITY 3: Fallback to passenger data only
    else if (tripSummaryDetails?.passengersList?.length > 0) {
      const breakdown = calculateGroupedPassengerPrice(
        tripSummaryDetails.passengersList
      );
      finalTotalPrice = breakdown.total;
      finalPriceBreakdown = breakdown;
      console.log("Fallback: passenger data only");

      //This dispatch for fallback case
      dispatch(
        updateTripSummary({
          totalPrice: finalTotalPrice,
          priceBreakdown: finalPriceBreakdown,
        })
      );
    }

    console.log("Final priceBreakdown being passed to accordion:", {
      total: finalPriceBreakdown?.total,
      baseTicketPrice: finalPriceBreakdown?.baseTicketPrice,
      totalTaxAndCharges: finalPriceBreakdown?.totalTaxAndCharges,
    });

    setPriceBreakdown(finalPriceBreakdown);
  }, [
    tripSummaryDetails?.selectedOutboundFlight,
    tripSummaryDetails?.selectedInboundFlight,
    tripSummaryDetails?.selectedOutboundFareOption,
    tripSummaryDetails?.selectedInboundFareOption,
    tripSummaryDetails?.passengersList,
    totalFlightPrice,
    outboundPriceData,
    inboundPriceData,
    currency,
    dispatch,
  ]);

  useEffect(() => {
    // Handle the scroll behavior for the checkout section
    const handleScroll = () => {
      if (!checkoutContainerRef.current || !checkoutRef.current) return;

      const containerRect =
        checkoutContainerRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // When the container is above the viewport - make sticky at bottom
      if (containerRect.top < 0 && containerRect.bottom > windowHeight) {
        setCheckoutScrollState("bottom");
      }
      // When the container is within the viewport - normal flow
      else if (containerRect.top >= 0 && containerRect.bottom <= windowHeight) {
        setCheckoutScrollState("normal");
      }
      // When the container is below the viewport - make sticky at top
      else if (containerRect.top > 0) {
        setCheckoutScrollState("top");
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsCheckoutFixed(!entry.isIntersecting);
      },
      { threshold: 0.1 }
    );
    if (footerRef.current) {
      observer.observe(footerRef.current);
    }
    return () => {
      if (footerRef.current) {
        observer.unobserve(footerRef.current);
      }
    };
  }, []);

  const styles = {
    button: {
      background: "#1E1E76",
      color: "#FFFFFF",
      borderRadius: "10px",
      fontSize: "20px",
      fontWeight: "600",
    },
  };

  const handleSignIn = () => {
    setIsSignInClicked(true);
  };

  const handleAuthClose = () => {
    setIsSignInClicked(false);
  };

  // Process selected Flight For Mobile App User
  const flightProceesing = async () => {
    const getFlightSearchPayload = createFlightSearchPayload(
      chatResult,
      tripSummaryDetails
    );
    if (!getFlightSearchPayload) return;

    try {
      setLoadingForAppUser(true);
      const response: any = await processFlightSelection({
        routing_id: allFlights?.routing_id,
        outward_id: tripSummaryDetails?.selectedOutboundFlight?.id,
        return_id: tripSummaryDetails?.selectedInboundFlight?.id || "",
        updateFareOptions,
        updatePassengerList,
        updateLuggageOptions,
        updateServiceFee,
        updateSupplierInfo,
        router,
        token: token,
      });

      // Update trip summary for Mobile App user
      dispatch(
        updateTripSummary({
          passengersList: response.passenger_price_segregation,
          luggageOptions: response.luggage_options,
          supplierInfo: response.supplier_info,
          activePage: activePages.flight_summary,
          from: activePages.flight_search,
          serviceFee: response.service_fee_percentage,
        })
      );
    } catch (error) {
      // dispatch(
      //   updateTripSummary({
      //     selectedInboundFlight: {},
      //     selectedOutboundFlight: {},
      //   })
      // );
    } finally {
      setLoadingForAppUser(false);
      setFlightProcessed(true);
    }
  };

  const handleOutboundFareOption = (card: FareOptionCard | null) => {
    //console.log('handleOutboundFareOption===>', card)
    if (card) {
      dispatch(
        updateTripSummary({
          selectedOutboundFareOption: card,
          selectedOutboundFlight: card.flights
        })
      );
      if (card?.flights) {
        const response = calculateOutboundTravelers(card.flights);
        dispatch(
          updateTripSummary({
            outboundTravelers: response.expandedTravelers,
            outboundTotal: response.totalAmount,
            outboundCounts: response.groupCounts,
          })
        );
      }
    }
  };

  const handleInboundFareOption = (card: FareOptionCard | null) => {
    if (card) {
      dispatch(updateTripSummary({ selectedInboundFareOption: card }));
      if (card?.flights) {
        const response = calculateInboundTravelers(card.flights);
        dispatch(
          updateTripSummary({
            inboundTravelers: response.expandedTravelers,
            inboundTotal: response.totalAmount,
            inboundCounts: response.groupCounts,
          })
        );
      }
    }
  };

  //more price added here
  const getOutboundPrice = (): Price | null => {
    if (
      tripSummaryDetails?.selectedOutboundFareOption &&
      Object.keys(tripSummaryDetails?.selectedOutboundFareOption).length > 0
    ) {
      return {
        amount: parseFloat(
          tripSummaryDetails?.selectedOutboundFareOption?.amount
        ),
        currency: tripSummaryDetails?.selectedOutboundFareOption?.currency,
      };
    } else if (tripSummaryDetails?.selectedOutboundFlight) {
      return {
        amount: tripSummaryDetails?.selectedOutboundFlight?.price?.amount,
        currency: tripSummaryDetails?.selectedOutboundFlight?.price?.currency,
      };
    }
    return null;
  };

  const getInboundPrice = () => {
    if (
      tripSummaryDetails?.selectedInboundFareOption &&
      Object.keys(tripSummaryDetails?.selectedInboundFareOption).length > 0
    ) {
      return {
        amount: parseFloat(
          tripSummaryDetails?.selectedInboundFareOption?.amount
        ),
        currency: tripSummaryDetails?.selectedInboundFareOption?.currency,
      };
    } else if (tripSummaryDetails?.selectedInboundFlight) {
      return {
        amount: tripSummaryDetails?.selectedInboundFlight?.price?.amount,
        currency: tripSummaryDetails?.selectedInboundFlight?.price?.currency,
      };
    }
    return null;
  };

  const handleBacktoSearch = () => {
    if (isAppUser()) {
      return setSearchParam("backToSearch", "true");
    }

    dispatch(
      updateTripSummary({
        activePage: activePages?.flight_search,
        from: activePages?.flight_summary,
      })
    );
    router.push(
      `/flights?departureValue=${tripSummaryDetails?.flightSearch?.departureLocation}&destinationValue=${tripSummaryDetails?.flightSearch?.departureLocation?.destinationLocation}`
    );
  };

  //flight error calculations
  useEffect(() => {
    if (
      (tripSummaryDetails?.selectedOutboundFareOption &&
        Object.keys(tripSummaryDetails?.selectedOutboundFareOption).length >
        0) ||
      (tripSummaryDetails?.selectedInboundFareOption &&
        Object.keys(tripSummaryDetails?.selectedInboundFareOption).length > 0)
    ) {
      dispatch(
        updateTripSummary({
          totalPrice:
            (getOutboundPrice()?.amount || 0) +
            (getInboundPrice()?.amount || 0),
        })
      );
    }
  }, [
    tripSummaryDetails?.selectedOutboundFareOption,
    tripSummaryDetails?.selectedInboundFareOption,
  ]);

  if (!priceBreakdown) return null;

  if (Object.keys(tripSummaryDetails?.flightSearchRespnse).length === 0) {
    // return null
  }

  const { total, totalTaxAndCharges, baseTicketPrice } = priceBreakdown;

  if (loadingForAppUser) {
    return <LoadingOverlay />;
  }

  const textInfo =
    tripSummaryDetails?.supplierInfo?.filter((ele: any) => ele.InfoType === "text") || [];
  const urlInfo =
    tripSummaryDetails?.supplierInfo?.filter((ele: any) => ele.InfoType === "url") || [];

  return (
    <DashboardLayout>
      {isNavigating ? <LoadingOverlay /> :
        <div className={`w-full h-screen`}>
          <div className="flex pb-[120px] w-full">
            <div className="font-proxima-nova md:w-4/6">
              {" "}
              {/* Added padding to prevent content from being hidden behind fixed checkout */}
              {/*only outbound */}
              {Object.keys(tripSummaryDetails.selectedOutboundFlight).length >
                0 &&
                Object.keys(tripSummaryDetails.selectedInboundFlight).length ===
                0 && (
                  <div className="flex flex-col">
                    <button
                      onClick={handleBacktoSearch}
                      className="text-brand-grey flex gap-1 items-center w-max text-xl font-semibold mb-4"
                    >
                      <ChevronLeft />
                      <span className="underline">Back to search</span>
                    </button>
                    <div className="w-full">
                      <FlightCard
                        showPrice={false}
                        flightType="OUT-BOUND"
                        flight={tripSummaryDetails.selectedOutboundFlight}
                        key={tripSummaryDetails.selectedOutboundFlight.id}
                        airport={
                          tripSummaryDetails?.flightSearchRespnse?.airport_data
                        }
                      />
                    </div>
                  </div>
                )}
              {Object.keys(tripSummaryDetails.selectedOutboundFlight).length >
                0 &&
                Object.keys(tripSummaryDetails.selectedInboundFlight).length >
                0 && (
                  <div className="flex flex-col">
                    <button
                      onClick={handleBacktoSearch}
                      className="text-brand-grey flex gap-1 items-center w-max text-xl font-semibold mb-4"
                    >
                      <ChevronLeft />
                      <span className="underline">Back to search</span>
                    </button>
                    <div className="w-full">
                      <FlightCard
                        showPrice={false}
                        flightType="OUT-BOUND"
                        flight={tripSummaryDetails.selectedOutboundFlight}
                        flight2={tripSummaryDetails.selectedInboundFlight}
                        key={tripSummaryDetails.selectedOutboundFlight.id}
                        airport={
                          tripSummaryDetails?.flightSearchRespnse?.airport_data
                        }
                      />
                    </div>
                  </div>
                )}
              <div className="py-6 flex flex-col">
                <div className="w-full">
                  <div className="flex gap-1 font-bold capitalize">
                    {tripSummaryDetails?.selectedOutboundFlight &&
                      Object.keys(
                        tripSummaryDetails?.sharedFlightResults
                          ?.supplier_feature_comparison ?? {}
                      ).length > 0 && (
                        <>
                          {tripSummaryDetails?.flightSearch && (
                            <>
                              <div className="text-brand-grey">
                                {"Outbound"} |
                              </div>

                              <div className="text-brand-black">
                                {`${formatShortLocationDisplay(
                                  tripSummaryDetails?.selectedOutboundFlight
                                    .origin,
                                  tripSummaryDetails?.sharedFlightResults
                                    ?.airport_data,
                                  true,
                                  false
                                )} `}
                                (
                                {
                                  tripSummaryDetails?.selectedOutboundFlight
                                    .origin
                                }
                                ){" "}
                                {formatShortLocationDisplay(
                                  tripSummaryDetails?.selectedOutboundFlight
                                    .destination,
                                  tripSummaryDetails?.sharedFlightResults
                                    ?.airport_data,
                                  true,
                                  false
                                )}{" "}
                                (
                                {
                                  tripSummaryDetails?.selectedOutboundFlight
                                    .destination
                                }
                                )
                              </div>
                              {/* <div className="text-brand-grey">
                                | {tripSummaryDetails?.selectedOutboundFlight?.travel_class}
                              </div> */}
                            </>
                          )}
                        </>
                      )}
                  </div>
                  <FareCarousel
                    flight={tripSummaryDetails?.selectedOutboundFlight}
                    onClick={(card: FareOptionCard | null) =>
                      handleOutboundFareOption(card)
                    }
                    selected={
                      tripSummaryDetails?.selectedOutboundFareOption?.title
                    }
                    searchFlightResults={
                      tripSummaryDetails?.sharedFlightResults?._outward
                    }
                  />
                </div>
                <div className="flex gap-1 font-bold capitalize">
                  {Object.keys(tripSummaryDetails?.selectedInboundFlight).length >
                    0 &&
                    Object.keys(
                      tripSummaryDetails?.sharedFlightResults
                        ?.supplier_feature_comparison
                    ).length > 0 && (
                      <>
                        {tripSummaryDetails?.flightSearch && (
                          <>
                            <div className="text-brand-grey">{"Inbound"} |</div>

                            <div className="text-brand-black">
                              {`${formatShortLocationDisplay(
                                tripSummaryDetails?.selectedOutboundFlight.origin,
                                tripSummaryDetails?.sharedFlightResults
                                  ?.airport_data,
                                true,
                                false
                              )} `}
                              ({tripSummaryDetails?.selectedOutboundFlight.origin}
                              ){" "}
                              {formatShortLocationDisplay(
                                tripSummaryDetails?.selectedOutboundFlight
                                  .destination,
                                tripSummaryDetails?.sharedFlightResults
                                  ?.airport_data,
                                true,
                                false
                              )}{" "}
                              (
                              {
                                tripSummaryDetails?.selectedOutboundFlight
                                  .destination
                              }
                              )
                            </div>
                            <div className="text-brand-grey">
                              | {tripSummaryDetails?.flightSearch?.travel_class}
                            </div>
                          </>
                        )}
                      </>
                    )}
                </div>
                <div className="">
                  <FareCarousel
                    flight={tripSummaryDetails?.selectedInboundFlight}
                    onClick={(card: FareOptionCard | null) =>
                      handleInboundFareOption(card)
                    }
                    selected={
                      tripSummaryDetails?.selectedInboundFareOption?.title
                    }
                    searchFlightResults={
                      tripSummaryDetails?.sharedFlightResults?._return
                    }
                  />
                </div>


                {((textInfo?.length ?? 0) > 0 || (urlInfo?.length ?? 0) > 0) && (
                  <ImportantInfoSection supplierInfo={tripSummaryDetails?.supplierInfo} />
                )}
              </div>
            </div>
            <div className="w-2/6 ml-6 hidden md:block">
              <div className="sticky top-20">
                <div className="w-full flex flex-col justify-center">
                  <div className="bg-brand-white border border-[#181882] shadow p-4 rounded-lg flex flex-col">

                    {/* Header */}
                    <div className="flex justify-between items-center">
                      <div className="text-brand-black text-xl font-semibold">
                        Total Price
                      </div>
                      <div className="text-brand-black opacity-90 font-semibold text-xl">
                        {formatFlightPrice({
                          amount: tripSummaryDetails?.totalPrice,
                          currency,
                        })}
                      </div>
                    </div>

                    {/* Accordion & Details */}
                    <div className="flex-1 overflow-y-auto mt-2">
                      {/* <PriceSummaryAccordion
                        baseTicketPrice={baseTicketPrice}
                        totalTaxAndCharges={totalTaxAndCharges}
                        total={total}
                        currency={currency}
                        formatFlightPrice={formatFlightPrice}
                      /> */}
                      {(Object.keys(tripSummaryDetails.selectedInboundFlight).length === 0) &&
                        <PriceBreakdownDialog outbound={tripSummaryDetails.selectedOutboundFlight} airport={
                          tripSummaryDetails?.flightSearchRespnse?.airport_data
                        } />}
                      {(Object.keys(tripSummaryDetails.selectedInboundFlight).length > 0) &&
                        <PriceBreakdownDialog outbound={tripSummaryDetails.selectedOutboundFlight} airport={
                          tripSummaryDetails?.flightSearchRespnse?.airport_data
                        } inbound={tripSummaryDetails.selectedInboundFlight} />}

                    </div>

                    {/* Button */}
                    <div className="mt-4">
                      <button
                        onClick={handleContinue}
                        style={styles.button}
                        className="px-4 py-2 w-full"
                      >
                        Continue to checkout
                      </button>
                    </div>

                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      }

      {/* for mobile */}
      <div
        ref={checkoutContainerRef}
        className={`fixed bottom-0 right-0 left-0 z-50 transition-all duration-300 md:hidden block`}
      >
        <div ref={checkoutRef} className="w-full flex justify-center">
          <div className="bg-slate-50 p-4  border border-brand rounded-lg w-[80rem]">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="text-brand-black text-xl font-semibold">
                  Total Price
                </div>
                <div className="text-brand-black opacity-90 font-semibold text-xl">
                  {formatFlightPrice({
                    amount: tripSummaryDetails?.totalPrice,
                    currency,
                  })}
                </div>
              </div>

              {/* <PriceSummaryAccordion
                baseTicketPrice={baseTicketPrice}
                totalTaxAndCharges={totalTaxAndCharges}
                total={total}
                currency={currency}
                formatFlightPrice={formatFlightPrice}
              /> */}
              <PriceBreakdownDialog outbound={tripSummaryDetails?.selectedOutboundFlight} airport={
                tripSummaryDetails?.flightSearchRespnse?.airport_data
              } />
            </div>

            <div className="flex w-full justify-end mt-4">
              <button
                onClick={handleContinue}
                style={styles.button}
                className="px-4 py-2"
              >
                Continue to checkout
              </button>
            </div>
          </div>
        </div>
      </div>

      {isSignInClicked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="rounded-lg p-4">
            <AuthContainer onCloseAuth={handleAuthClose} />
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default flightsummary;
