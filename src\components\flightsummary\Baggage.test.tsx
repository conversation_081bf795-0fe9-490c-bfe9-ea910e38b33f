import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Baggage from "./Baggage";

// Mock Redux
const mockDispatch = jest.fn();
const mockUseSelector = jest.fn();
jest.mock("react-redux", () => ({
  useSelector: (fn: any) => mockUseSelector(fn),
  useDispatch: () => mockDispatch,
}));

// Mock icons
jest.mock("lucide-react", () => ({
  ChevronLeft: () => <span data-testid="chevron-left" />,
  ChevronRight: () => <span data-testid="chevron-right" />,
}));

// Mock Radix Tabs
jest.mock("@radix-ui/react-tabs", () => {
  const TabsRoot = ({ children }: any) => <div>{children}</div>;
  const TabsList = ({ children }: any) => <div>{children}</div>;
  const TabsTrigger = ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  );
  return { Root: TabsRoot, List: TabsList, Trigger: TabsTrigger };
});

// Mock utils
jest.mock("@/lib/utils/formatPrice", () => ({
  formatFlightPrice: ({ amount, currency }: any) => `${currency} ${amount}`,
}));
jest.mock("@/lib/utils/formatAirport", () => ({
  formatAirportDisplayShort: (code: string) => code,
}));

// Mock updateTripSummary action
jest.mock("@/store/slices/tripSummary", () => ({
  updateTripSummary: (payload: any) => ({ type: "UPDATE_TRIP_SUMMARY", payload }),
}));

const mockOnClose = jest.fn();

const baseState = {
  outboundTravelers: [
    { travelerType: "adult", name: "A" },
    { travelerType: "infant", name: "B" },
  ],
  luggageOptions: {
    _outward: [
      { option_value: "bag15", allowed_weights: "15kg", price: "100", currency: "USD" },
      { option_value: "bag20", allowed_weights: "20kg", price: "150", currency: "USD" },
    ],
    _return: [
      { option_value: "bag15", allowed_weights: "15kg", price: "120", currency: "USD" },
    ],
  },
  selectedOutboundFlight: { origin: "JFK", destination: "LHR" },
  selectedInboundFlight: { origin: "LHR", destination: "JFK" },
  sharedFlightResults: { airport_data: {} },
  selectedLuggageInfo: {
    _outward: { 1: { option: "bag15", price: "100" } },
    _return: {},
    totalPrice: 100,
  },
};

describe("Baggage component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation((fn: any) => fn({ tripSummary: baseState }));
  });

  it("renders Outbound Baggage header and traveler tabs", () => {
    render(<Baggage onClose={mockOnClose} />);
    expect(screen.getByText(/Outbound Baggage/i)).toBeInTheDocument();
    expect(screen.getByText("Traveler 1")).toBeInTheDocument();
    expect(screen.getByText("Traveler 2")).toBeInTheDocument();
  });

  it("renders baggage options and prices", () => {
    render(<Baggage onClose={mockOnClose} />);
    expect(screen.getByText(/Additional 15kg/)).toBeInTheDocument();
    // There are two "USD 100" elements, so check both are present
    expect(screen.getAllByText(/USD 100/)).toHaveLength(2);
    expect(screen.getByText(/Additional 20kg/)).toBeInTheDocument();
    expect(screen.getByText(/USD 150/)).toBeInTheDocument();
  });

  it("checkbox interaction updates selection and dispatches updateTripSummary on Update", () => {
    render(<Baggage onClose={mockOnClose} />);
    // Select 20kg for traveler 1
    const checkboxes = screen.getAllByRole("checkbox");
    fireEvent.click(checkboxes[1]);
    // Click Update
    fireEvent.click(screen.getByText("Update"));
    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({
        type: "UPDATE_TRIP_SUMMARY",
        payload: expect.objectContaining({
          selectedLuggageInfo: expect.objectContaining({
            _outward: expect.objectContaining({
              1: expect.objectContaining({ option: "bag20", price: 150, currency: "USD" }),
            }),
            totalPrice: expect.any(Number),
          }),
        }),
      })
    );
    expect(mockOnClose).toHaveBeenCalled();
  });

  it("disables checkboxes for infants", () => {
    render(<Baggage onClose={mockOnClose} />);
    // Switch to traveler 2 (infant)
    fireEvent.click(screen.getByText("Traveler 2"));
    // All checkboxes should be disabled
    const checkboxes = screen.getAllByRole("checkbox");
    checkboxes.forEach((cb) => {
      expect(cb).toBeDisabled();
    });
    expect(screen.getByText(/Infants are not eligible/)).toBeInTheDocument();
  });

  it("navigates between Outbound and Inbound slides", () => {
    render(<Baggage onClose={mockOnClose} />);
    // Outbound by default
    expect(screen.getByText(/Outbound Baggage/)).toBeInTheDocument();
    // Click next (to Inbound)
    const nextBtn = screen.getAllByTestId("chevron-right")[0].parentElement;
    fireEvent.click(nextBtn!);
    expect(screen.getByText(/Inbound Baggage/)).toBeInTheDocument();
    // Click prev (back to Outbound)
    const prevBtn = screen.getAllByTestId("chevron-left")[0].parentElement;
    fireEvent.click(prevBtn!);
    expect(screen.getByText(/Outbound Baggage/)).toBeInTheDocument();
  });

  it("shows correct total price and baggage selection count", () => {
    render(<Baggage onClose={mockOnClose} />);
    expect(screen.getByText("1 of 1 Baggage Selected")).toBeInTheDocument();
    // Check that the total price is displayed
    expect(screen.getAllByText("USD 100").length).toBeGreaterThan(0);
  });
});