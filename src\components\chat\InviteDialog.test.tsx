import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { InviteDialog } from "./InviteDialog";

// InviteDialog.test.tsx

describe("InviteDialog", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders Share button", () => {
    render(<InviteDialog />);
    expect(screen.getByText("Share")).toBeInTheDocument();
  });

  it("opens dialog when Share button is clicked", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    expect(await screen.findByText("Invite people")).toBeInTheDocument();
    expect(screen.getByText("Create a group chat with friends or family and plan together")).toBeInTheDocument();
  });

  it("shows input, textarea, and buttons in dialog", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    expect(await screen.findByPlaceholderText("Email, comma separated...")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Add a note (optional)...")).toBeInTheDocument();
    expect(screen.getByText("Invite")).toBeInTheDocument();
    expect(screen.getByText("Copy link")).toBeInTheDocument();
    expect(screen.getByText("Anyone")).toBeInTheDocument();
  });

  it("updates note textarea and character count", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    const textarea = await screen.findByPlaceholderText("Add a note (optional)...");
    fireEvent.change(textarea, { target: { value: "Hello world" } });
    expect(textarea).toHaveValue("Hello world");
    expect(screen.getByText("11/300")).toBeInTheDocument();
  });

  it("closes dialog when CircleX button is clicked", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    const closeBtn = await screen.findByRole("button", { name: "" });
    fireEvent.click(closeBtn);
    await waitFor(() => {
      expect(screen.queryByText("Invite people")).not.toBeInTheDocument();
    });
  });

  it("Invite button is clickable", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    const inviteBtn = await screen.findByText("Invite");
    fireEvent.click(inviteBtn);
    // No crash, no-op
    expect(inviteBtn).toBeInTheDocument();
  });

  it("Copy link button is clickable", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    const copyBtn = await screen.findByText("Copy link");
    fireEvent.click(copyBtn);
    expect(copyBtn).toBeInTheDocument();
  });



  it("closes dialog on Escape key", async () => {
    render(<InviteDialog />);
    fireEvent.click(screen.getByText("Share"));
    fireEvent.keyDown(document, { key: "Escape", code: "Escape" });
  });
});