import React, { useState, useRef, useCallback } from "react";
import { Lo<PERSON>, Mic, Square } from "lucide-react";
import axios from "axios";
import { useIsMobile } from "@/hooks/use-mobile";
import { useCustomSession } from "@/hooks/use-custom-session";

interface VoiceRecordingButtonProps {
  onTranscriptionComplete: (text: string) => void;
  disabled?: boolean;
}

export const VoiceRecordingButton: React.FC<VoiceRecordingButtonProps> = ({
  onTranscriptionComplete,
  disabled = false,
}) => {

  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const startRecording = useCallback(async () => {
    try {
      console.log("🎤 Starting recording...");
      setError(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        console.log("🎤 Recording stopped, processing audio...");
        setIsProcessing(true);
        
        try {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
          
          console.log("🎤 Audio data:", {
            size: audioBlob.size,
            type: audioBlob.type,
            chunks: audioChunksRef.current.length
          });
          
          if (audioBlob.size === 0) {
            throw new Error("No audio data recorded");
          }
          
          // Create FormData for file upload
          const formData = new FormData();
          formData.append('file', audioBlob, 'recording.wav');
          
          console.log("🔄 Sending transcription request...");
          console.log("🔄 Request details:", {
            url: `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/voice/transcribe`,
            fileSize: audioBlob.size
          });
          
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/voice/transcribe`,
            formData,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data',
              },
            }
          );
          
          console.log("✅ Transcription response:", response.data);
          
          // Our backend wraps the payload as { detail: { status, message, data: { transcription } } }
          const transcriptionText =
            response.data?.detail?.data?.transcription ||
            response.data?.data?.transcription;

          if (transcriptionText) {
            console.log("📝 Transcription text:", transcriptionText);
            onTranscriptionComplete(transcriptionText);
          } else {
            throw new Error("No transcription text received");
          }
          
        } catch (error) {
          console.error("❌ Transcription failed:", error);
          setError(`Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
          setIsProcessing(false);
          // Clean up stream
          stream.getTracks().forEach(track => track.stop());
        }
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      console.log("🎤 Recording started");
      
    } catch (error) {
      console.error("❌ Failed to start recording:", error);
      setError(`Failed to start recording: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [onTranscriptionComplete]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      console.log("🎤 Stopping recording...");
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  }, [isRecording]);

  const handleClick = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);

  const isMobile = useIsMobile();

  return (
    <div className="flex flex-col items-center">
      <button
        type="button"
        onClick={handleClick}
        disabled={disabled || isProcessing}
        className={`
          ${isMobile ? "p-1.5" : "p-2"} rounded-lg transition-all duration-200
          ${isRecording 
            ? "bg-red-500 hover:bg-red-600 text-white shadow-md hover:shadow-lg" 
            : "bg-[#1E1E76] hover:bg-blue-700 text-white shadow-md hover:shadow-lg"
          }
          ${(disabled || isProcessing) && "opacity-50 cursor-not-allowed"}
        `}
        title={isRecording ? "Stop recording" : "Start recording"}
      >

        {
          isProcessing ? <Loader className="size-5  animate-spin" />
          : isRecording ? (
              <Square className={`${isMobile ? "h-4 w-4" : "h-5 w-5"}`} />
            ) : (
              <Mic className={`${isMobile ? "h-4 w-4" : "h-5 w-5"}`} />
          )
        }
      </button>
      
      {error && (
        <div className="mt-2 text-xs text-red-500 max-w-xs text-center">
          {error}
        </div>
      )}
    </div>
  );
}; 