import { generateUUID } from '@/lib/utils/uuid';
import posthog from 'posthog-js';


function getBrowserDetails() {
  const ua = navigator.userAgent;
  let name = 'Other', version = '';

  if (/Chrome/.test(ua) && !/Edge/.test(ua)) {
    name = 'Chrome';
    version = ua.match(/Chrome\/([\d.]+)/)?.[1] || '';
  } else if (/Safari/.test(ua) && !/Chrome/.test(ua)) {
    name = 'Safari';
    version = ua.match(/Version\/([\d.]+)/)?.[1] || '';
  } else if (/Firefox/.test(ua)) {
    name = 'Firefox';
    version = ua.match(/Firefox\/([\d.]+)/)?.[1] || '';
  } else if (/Edge/.test(ua)) {
    name = 'Edge';
    version = ua.match(/Edg\/([\d.]+)/)?.[1] || '';
  } else if (/MSIE|Trident/.test(ua)) {
    name = 'IE';
    version = ua.match(/(MSIE |rv:)([\d.]+)/)?.[2] || '';
  }

  return { name, version };
}

function getDeviceType() {
  const width = window.innerWidth;
  if (width <= 768) return 'Mobile';
  if (width <= 1024) return 'Tablet';
  return 'Desktop';
}

function getOS() {
  const platform = navigator.platform.toLowerCase();
  const ua = navigator.userAgent.toLowerCase();
  if (platform.includes('win')) return 'Windows';
  if (platform.includes('mac')) return 'macOS';
  if (/android/.test(ua)) return 'Android';
  if (/iphone|ipad|ipod/.test(ua)) return 'iOS';
  return 'Other';
}

function getLanguage() {
  return navigator.language || navigator.userLanguage;
}


async function getGeoLocation() {
  const cached = sessionStorage.getItem("geoLocation");
  if (cached) return JSON.parse(cached);

  try {
    const response = await fetch("https://ipapi.co/json/");
    const data = await response.json();
    const geo = {
      ip: data.ip,
      country: data.country_name,
      city: data.city,
    };
    sessionStorage.setItem("geoLocation", JSON.stringify(geo));
    return geo;
  } catch {
    const fallback = { ip: "Unknown", country: "Unknown", city: "Unknown" };
    sessionStorage.setItem("geoLocation", JSON.stringify(fallback));
    return fallback;
  }
}


// Ensure flow_id is set once per app load
let isFirstLoad = true;

function initializeFlowId() {
  if (isFirstLoad) {
    const newFlowId = generateUUID()
    sessionStorage.setItem('flow_id', newFlowId);
    isFirstLoad = false;
  }
}

function getFlowId() {
  return sessionStorage.getItem('flow_id');
}

// Main tracking function
const trackEvent = async (eventName, additionalProps = {}) => {
  if (process.env.NEXT_PUBLIC_ENABLE_POSTHOG !== 'true') return;

  if (typeof window !== 'undefined') {
    initializeFlowId();

    const browser = getBrowserDetails();
    const device = getDeviceType();
    const os = getOS();
    const language = getLanguage();
    const geo = await getGeoLocation();
    const flowId = getFlowId();
    const timestamp = new Date().toISOString();

    console.log("geo======", geo);

    posthog.capture(eventName, {
      browser: `${browser.name} ${browser.version}`,
      deviceType: device,
      operatingSystem: os,
      languagePreference: language,
      country: geo.country,
      city: geo.city,
      flow_id: flowId,
      timestamp, // ⏱️ Include ISO 8601 timestamp
      ...additionalProps,
    });
  }
};

export default {
  trackEvent,
  getFlowId,
};
