import useSWR from 'swr'

const authenticatedFetcher = (url: string, token: string) =>
  fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  }).then((res) => {
    if (!res.ok) throw new Error('Failed to fetch')
    return res.json()
  })

export function useAuthenticatedSWR<T = any>(url: string, token?: string) {
  const shouldFetch = !!token && !!url

  const { data, error, isLoading, mutate } = useSWR<T>(
    shouldFetch ? [url, token] : null,
    ([url, token]) => authenticatedFetcher(url, token!)
  )

  return { data, error, isLoading, mutate }
}
