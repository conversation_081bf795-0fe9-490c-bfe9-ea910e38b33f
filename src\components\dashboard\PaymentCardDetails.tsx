import React, { useState } from "react";

import SavedPaymentCards from "./SavedPaymentCards";
import PaymentCardForm from "./PaymentCardForm";
import { User, PaymentCard, UserDetailDelete } from "@/constants/user";

interface PaymentCardDetailsProps {
  user: User | null;
  updateUser: (data: User) => Promise<void>;
  updatedStatus: string | null;
  deleteApi: (data: UserDetailDelete) => Promise<void>;
  showAddress: () => void;
}

const PaymentCardDetails: React.FC<PaymentCardDetailsProps> = ({ user, updateUser, updatedStatus, deleteApi, showAddress }) => {
  const [showForm, setShowForm] = useState(false);
  const [editData, setEditData] = useState<PaymentCard | undefined>(undefined);
  const [loading, setLoading] = useState(false);

  const handleEdit = (editData: PaymentCard) => {
    setShowForm(true);
    setEditData(editData);
  };

  async function saveDataApi(cardData: PaymentCard) {
    if (user) {
      setLoading(true);

      const isExisting = user.paymentCards.some(c => c.card_id === cardData.card_id);
      const updatedCards = isExisting
        ? user.paymentCards.map(c => (c.card_id === cardData.card_id ? cardData : c))
        : [...user.paymentCards, cardData];

      const updatedUser: User = {
        ...user,
        paymentCards: updatedCards,
      };

      await updateUser(updatedUser); // wait for server update and refetch

      //Wait for updated user to be fetched from parent before hiding the form
      setTimeout(() => {
        setShowForm(false);
        setLoading(false);
      }, 300); // give parent a moment to update props
    }
  }

  return (
    <div className="w-full">
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-t-4 border-blue-500 border-solid"></div>
        </div>
      ) : showForm ? (
        <PaymentCardForm
          onCancel={() => setShowForm(false)}
          initialData={editData}
          addresses={user?.addresses}
          onSave={saveDataApi}
          showAdd={showAddress}
        />
      ) : (
        <SavedPaymentCards
          onAddNewCard={() => {
            setShowForm(true);
            setEditData(undefined);
          }}
          onEdit={handleEdit}
          user={user}
          deleteCard={deleteApi}
        />
      )}

      {updatedStatus === "success" && !loading ? (
        <p className="text-green-600 text-sm w-full p-2 text-center my-6">
          New Card Added Successfully
        </p>
      ) : null}
    </div>
  );
};

export default PaymentCardDetails;
