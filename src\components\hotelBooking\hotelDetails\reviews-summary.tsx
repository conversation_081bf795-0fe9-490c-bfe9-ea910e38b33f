"use client"

import { But<PERSON> } from "@/components/ui/button"

interface ReviewsSummaryProps {
    rating: number | undefined
    reviewCount: number
    onShowReviews: () => void
}

export default function ReviewsSummary({ rating, reviewCount, onShowReviews }: ReviewsSummaryProps) {
    return (
        <div className="col-span-12 md:col-span-5 bg-white p-4 rounded-lg border border-[#B4BBE8] flex flex-col">
            <h3 className="font-bold text-lg mb-4 text-[#080236]">{reviewCount} Verified Reviews</h3>
            <div className="grid grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                    <div className="flex flex-col items-center">
                        <div className="flex gap-4 text-lg font-bold text-[#4B4BC3]">
                            <img
                                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                                width={16}
                                height={16}
                                alt="rating star"
                                className="object-contain m-0"
                            />
                            <div>{rating}</div>
                        </div>
                        <div className="text-xs font-bold text-[#4B4BC3]">Exceptional</div>
                    </div>
                </div>
                <div className="text-center">
                    <div className="flex flex-col items-center">
                        <div className="text-lg font-medium text-[#080236]">9.3</div>
                        <div className="text-xs text-[#080236]">Exceptional</div>
                    </div>
                </div>
                <div className="text-center">
                    <div className="flex flex-col items-center">
                        <div className="text-lg font-medium text-[#080236]">9.3</div>
                        <div className="text-xs text-[#080236]">Staff</div>
                    </div>
                </div>
                <div className="text-center">
                    <div className="flex flex-col items-center">
                        <div className="text-lg font-medium text-[#080236]">9.2</div>
                        <div className="text-xs text-[#080236]">Location</div>
                    </div>
                </div>
            </div>
            <div className="mt-auto pt-2">
                <Button variant="link" className="text-[#4B4BC3] text-base p-0 h-auto underline" onClick={onShowReviews}>
                    Show {reviewCount} Verified Reviews
                </Button>
            </div>
        </div>
    )
}
