import React, { useState } from "react";
import { PencilLine } from "lucide-react";
import { Button } from "@/components/ui/button";
import SavedPassengerProfile from "./SavedPassengerProfile";
import PassengerProfileForm from "./PassengerProfileForm";
import EditPassengerProfileForm from "./EditPassengerProfileForm";
import { User } from "@/constants/user";

interface PassengerProfileDetailsProps {
  user: User | null;
}

const PassengerProfileDetails: React.FC<PassengerProfileDetailsProps> = ({ user }) => {
  // Mock data for saved passenger profiles
  const [savedPassengerProfiles, setSavedPassengerProfiles] = useState<any[]>([
    {
      id: 1,
      name: "Passenger Name 1",
      profile: {
        label: "Passenger Profile 1",
        userName: "User Name",
        userType: "User Type",
        dob: "User Date of Birth",
        country: "Country Name",
        gender: "Gender"
      }
    },
    {
      id: 2,
      name: "Passenger Name 2",
      profile: {
        label: "Passenger Profile 2",
        userName: "User Name",
        userType: "User Type",
        dob: "User Date of Birth",
        country: "Country Name",
        gender: "Gender"
      }
    },
    {
      id: 3,
      name: "Passenger Name 3",
      profile: {
        label: "Passenger Profile 3",
        userName: "User Name",
        userType: "User Type",
        dob: "User Date of Birth",
        country: "Country Name",
        gender: "Gender"
      }
    },
  ]);

  const [openStates, setOpenStates] = useState<boolean[]>(savedPassengerProfiles.map(() => false));
  const [editingProfile, setEditingProfile] = useState<any | null>(null);
  const [addingProfile, setAddingProfile] = useState<boolean>(false);

  const handleEditProfile = (profile: any) => setEditingProfile(profile);
  
  const handleSaveEdit = (updatedProfile: any) => {
    setSavedPassengerProfiles(profiles =>
      profiles.map(p => p.id === updatedProfile.id ? { ...p, ...updatedProfile } : p)
    );
    setEditingProfile(null);
  };

  const handleCancelEdit = () => setEditingProfile(null);

  return (
    <div className="p-6">
      {addingProfile || !savedPassengerProfiles || savedPassengerProfiles.length === 0 ? (
        <PassengerProfileForm user={user} 
          onUpdate={() => setAddingProfile(false)}
        />
      ) : editingProfile ? (
        <EditPassengerProfileForm
          profile={editingProfile}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
        />
      ) : (
        <>
          {/* Saved Passenger Profiles Section */}
          {savedPassengerProfiles && savedPassengerProfiles.length > 0 && (
            <div className="p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 md:gap-0 mb-10">
                <h2 className="text-xl md:text-3xl font-semibold text-[#4B4BC3]">Saved Passenger Profiles</h2>
                <Button className="text-sm md:text-lg text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={() => setAddingProfile(true)}>
                  <PencilLine /> Add New Passenger
                </Button>
              </div>
              <SavedPassengerProfile
                profiles={savedPassengerProfiles}
                openStates={openStates}
                onToggle={idx => setOpenStates(states => states.map((open, i) => i === idx ? !open : open))}
                onEdit={id => handleEditProfile(savedPassengerProfiles.find(p => p.id === id))}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default PassengerProfileDetails; 