"use client"

import type React from "react"
import { useRef, useState, useMemo, useEffect } from "react"
import { DateRange } from "react-date-range"
import { format } from "date-fns"
import { useSelector, useDispatch } from "react-redux"
import { Listbox, ListboxOption, ListboxOptions, ListboxButton } from "@headlessui/react"
import { CalendarIcon, ChevronDown } from "lucide-react"
import { debounce } from "lodash"
import { agentPostMethod } from "@/utils/api"
import { useFlightContext } from "@/context/FlightContext"
import type { AppState } from "@/store/store"
import { updateTripSummary } from "@/store/slices/tripSummary"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import { useCustomSession } from "@/hooks/use-custom-session"
import { AccordionContent, AccordionItem, AccordionTrigger, Accordion } from "@/components/ui/accordion"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface PassengerDetailsProps {
  index: number
  travelerType: "adult" | "child" | "infant"
  formerror: any
  disabled: boolean
}

type Country = {
  code: string
  name: string
}

type CountryState = {
  search: string
  loading: boolean
  countryList: Country[]
}

const PassengerDetails: React.FC<PassengerDetailsProps> = ({ travelerType, index, formerror, disabled = false }) => {
  const [country, setCountry] = useState<CountryState>({
    search: "",
    loading: false,
    countryList: [],
  })
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [showCalendar, setShowCalendar] = useState(false)
  const { data: session, status } = useCustomSession()
  const token = session?.accessToken
  const [autoFillOption, setAutoFillOption] = useState("")
  const { currentUserDetails } = useSelector((state: AppState) => state.userDetails)

  const [dateRange, setDateRange] = useState([
    {
      startDate: new Date(),
      endDate: new Date(),
      key: "selection",
    },
  ])
  const [dob, setDob] = useState<Date | null>(null)
  const calendarRef = useRef<HTMLDivElement>(null)
  const tripSummaryDetails = useSelector((state: AppState) => state.tripSummary)
  const dispatch = useDispatch()

  const form = tripSummaryDetails?.passengerDetails[index]

  const { updateGlobalPopup } = useFlightContext()

  // Helper function to check if there are any errors for this passenger
  const hasErrors = () => {
    if (!formerror[index]) return false
    return Object.values(formerror[index]).some((error) => error !== undefined && error !== null && error !== "")
  }

  // Helper function to check if all required fields are filled
  const hasAllRequiredFields = () => {
    if (!form) return false
    return form.title && form.firstName && form.lastName && form.dob && form.gender
  }

  // Helper function to check if accordion is open
  const [isAccordionOpen, setIsAccordionOpen] = useState(false)

  const handlePassengerDetail = (index: number, field: string, value: string) => {
    const updatePassenger = tripSummaryDetails?.passengerDetails.map((passenger: any, i: number) => {
      if (i === index) {
        if (formerror[index] && field in formerror[index]) {
          formerror[index][field] = undefined
        }
        return { ...passenger, [field]: value }
      }
      return passenger
    })
    dispatch(updateTripSummary({ passengerDetails: updatePassenger }))
  }

  const debouncedHandleCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod("flight/suggest-country", { query }, token ?? "")
            setCountry({
              search: query,
              loading: false,
              countryList: response?.detail?.data,
            })
          } catch (error) {
            updateGlobalPopup({
              isOpen: true,
              message: "Something Went Wrong .Please try again",
              type: "error",
            })
            console.log("Api error", error)
          } finally {
            console.log("Done")
          }
        }
      }, 500),
    [],
  )

  const handleCountry = (query: string) => {
    setCountry({ ...country, loading: true, search: query })
    debouncedHandleCountry(query)

    if (!query) {
      // Handle if user clear the selected country
      const updatedPassenger = tripSummaryDetails?.passengerDetails.map((passenger: any, i: number) => {
        if (i === index) {
          return { ...passenger, passportCountry: "", countryText: "" }
        }
        return passenger
      })
      dispatch(updateTripSummary({ passengerDetails: updatedPassenger }))
    }
  }

  const handleSuggestionClick = (data: Country) => {
    const updatedPassenger = tripSummaryDetails?.passengerDetails.map((passenger: any, i: number) => {
      if (i === index) {
        return {
          ...passenger,
          passportCountry: data.code,
          countryText: data.name,
        }
      }
      return passenger
    })
    dispatch(updateTripSummary({ passengerDetails: updatedPassenger }))
    setCountry({ search: data.name, loading: false, countryList: [] })
  }

  const handleSelect = (index: number) => (rangesByKey: any) => {
    const { selection } = rangesByKey
    const startDate = selection.startDate
    const endDate = selection.endDate

    if (startDate) {
      const formatted = format(startDate, "dd/MM/yyyy")
      setDob(startDate)
      setShowCalendar(false) // Close on selection
      const updatedPassenger = tripSummaryDetails?.passengerDetails.map((passenger: any, i: number) => {
        if (i === index) {
          if (formerror[index] && "dob" in formerror[index]) {
            formerror[index].dob = undefined
          }
          return { ...passenger, dob: formatted, selectedDob: startDate }
        }
        return passenger
      })
      dispatch(updateTripSummary({ passengerDetails: updatedPassenger }))
    }

    if (endDate) {
      setDob(endDate)
      setShowCalendar(false) // Close on selection
    }
  }

  useEffect(() => {
    if (tripSummaryDetails.passengerDetails[index]) {
      setDob(tripSummaryDetails.passengerDetails[index]?.selectedDob)
      setCountry({
        ...country,
        search: tripSummaryDetails.passengerDetails[index].countryText,
      })
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setCountry((prev) => ({ ...prev, countryList: [] })) // Hide suggestions
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setShowCalendar(false)
      }
    }

    if (showCalendar) {
      document.addEventListener("mousedown", handleClickOutside)
    } else {
      document.removeEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showCalendar])

  const handleAutoFill = (value: string) => {
    console.log("profile", value)

    try {
      const selectedPassenger = JSON.parse(value)

      if (selectedPassenger && selectedPassenger.profile) {
        const profile = selectedPassenger.profile

        // Create updated passenger object with auto-filled data
        const updatedPassenger = tripSummaryDetails?.passengerDetails.map((passenger: any, i: number) => {
          if (i === index) {
            // Clear any existing form errors for this passenger
            if (formerror[index]) {
              Object.keys(formerror[index]).forEach((key) => {
                formerror[index][key] = undefined
              })
            }

            // Parse and format DOB if available
            let formattedDob = ""
            let selectedDobDate = null
            if (profile.dateOfBirth) {
              try {
                const dobDate = new Date(profile.dateOfBirth)
                if (!isNaN(dobDate.getTime())) {
                  formattedDob = format(dobDate, "dd/MM/yyyy")
                  selectedDobDate = dobDate
                  setDob(dobDate)
                }
              } catch (error) {
                console.log("Error parsing DOB:", error)
              }
            }

            // Update country search state if passport country is available
            if (profile.passportCountry || profile.nationality) {
              const countryName = profile.passportCountryName || profile.nationalityName || ""
              setCountry((prev) => ({ ...prev, search: countryName }))
            }

            return {
              ...passenger,
              title: profile.title || "",
              firstName: profile.firstName || "",
              middleName: profile.middleName || "",
              lastName: profile.lastName || "",
              gender: profile.gender || "",
              dob: formattedDob,
              selectedDob: selectedDobDate,
              passportNumber: profile.passportNumber || "",
              passportCountry: profile.passportCountry || profile.nationality || "",
              countryText: profile.passportCountryName || profile.nationalityName || "",
              specialService: profile.specialService || "",
              // Add any other fields that might be available in the profile
              email: profile.email || "",
              phone: profile.phone || "",
              nationality: profile.nationality || "",
              passportExpiry: profile.passportExpiry || "",
            }
          }
          return passenger
        })

        // Update Redux state
        dispatch(updateTripSummary({ passengerDetails: updatedPassenger }))

        // Set the auto-fill option to show selected passenger name
        const fullName = [profile.title, profile.firstName, profile.middleName, profile.lastName]
          .filter(Boolean)
          .join(" ")

        setAutoFillOption(value)
      }
    } catch (error) {
      console.log("Error parsing selected passenger:", error)
    }
  }

  if (!form) return null

  return (
    <Accordion type="multiple" className="relative overflow-visible">
      <AccordionItem value={`Adult-${index}`} className="border rounded-lg !overflow-visible">
        <AccordionTrigger
          className="px-4 py-3 hover:no-underline overflow-visible"
          onClick={() => setIsAccordionOpen(!isAccordionOpen)}
        >
          <div className="flex items-center justify-between w-full">
            <span className="font-semibold text-[#1E1E76] text-left">
              {travelerType.charAt(0).toUpperCase() + travelerType.slice(1)} {index + 1}
            </span>
            {/* Error message - show only when accordion is closed, has errors, and not all fields are filled */}
            {!isAccordionOpen && hasErrors() && !hasAllRequiredFields() && (
              <span className="text-red-500 text-sm font-normal mr-2">Please complete required fields</span>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4 overflow-visible">
          <div key={index} className="rounded-lg flex flex-col gap-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <Select value={autoFillOption} onValueChange={handleAutoFill} disabled={disabled}>
                  <SelectTrigger className="w-full rounded-lg bg-brand-white border-2 border-[#EBEBEB] text-brand-grey focus:ring-0">
                    <SelectValue placeholder="Automatically fill in my details" />
                  </SelectTrigger>
                  <SelectContent className="bg-brand-white border-2 border-[#EBEBEB]">
                    {currentUserDetails?.passengers?.map((ele: any) => {
                      const fullName = [
                        ele.profile.title,
                        ele.profile.firstName,
                        ele.profile.middleName,
                        ele.profile.lastName,
                      ]
                        .filter(Boolean)
                        .join(" ")

                      return (
                        <SelectItem
                          value={JSON.stringify(ele)}
                          key={ele.profile.passenger_id}
                          className="hover:!bg-[#EBEBEB]  !text-brand-black"
                        >
                          {fullName}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>
              {/* Title */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto shadow-sm">
                  <Listbox
                    disabled={disabled}
                    value={form.title}
                    onChange={(value) => {
                      handlePassengerDetail(index, "title", value)
                    }}
                  >
                    <ListboxButton
                      id="title"
                      className="w-full flex justify-between text-brand-grey items-center focus:outline-none rounded-full px-4 xs:px-2 py-2 bg-brand-white text-left"
                    >
                      <div>{form.title ? form.title : "Select Title"}</div>
                      <ChevronDown />
                    </ListboxButton>
                    <ListboxOptions className="bg-brand-white border-2 border-[#EBEBEB] rounded-xl flex flex-col gap-2 cursor-pointer absolute mt-1 z-10 w-full max-h-60 overflow-auto focus:outline-none">
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="">
                        Select Title
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:[#EBEBEB]  text-brand-black" value="Mr">
                        Mr
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB] text-brand-black" value="Mrs">
                        Mrs
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="Miss">
                        Miss
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="Dr">
                        Dr
                      </ListboxOption>
                    </ListboxOptions>
                  </Listbox>
                </div>
                {formerror[index]?.title && <div className="text-red-500 text-sm mt-1">{formerror[index]?.title}</div>}
              </div>
              {/* First Name */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] text-brand-grey shadow-sm">
                  <input
                    disabled={disabled}
                    type="text"
                    id="firstName"
                    value={form.firstName}
                    className="w-full px-3 py-2 placeholder:text-brand-grey  focus:outline-none"
                    placeholder="First Name"
                    onChange={(e) => handlePassengerDetail(index, "firstName", e.target.value)}
                  />
                </div>
                {formerror[index]?.firstName && (
                  <div className="text-red-500 text-sm mt-1">{formerror[index]?.firstName}</div>
                )}
              </div>

              {/* Middle Name */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] shadow-sm">
                  <input
                    disabled={disabled}
                    type="text"
                    id="middleName"
                    value={form.middleName}
                    className="w-full px-3 py-2 placeholder:text-brand-grey text-brand-grey bg-brand-white focus:outline-none"
                    placeholder="Middle Name"
                    onChange={(e) => handlePassengerDetail(index, "middleName", e.target.value)}
                  />
                </div>
              </div>

              {/* Last Name */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] shadow-sm">
                  <input
                    disabled={disabled}
                    type="text"
                    id="lastName"
                    value={form.lastName}
                    className="peer w-full px-3 py-2 placeholder:text-brand-grey text-brand-grey bg-white focus:outline-none"
                    placeholder="Last Name"
                    onChange={(e) => handlePassengerDetail(index, "lastName", e.target.value)}
                  />
                </div>
                {formerror[index]?.lastName && (
                  <div className="text-red-500 text-sm mt-1">{formerror[index]?.lastName}</div>
                )}
              </div>

              {/* Date of Birth */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-[#EBEBEB] border-2 h-auto shadow-sm">
                  {/* Input field with icon */}
                  <div
                    onClick={() => !disabled && setShowCalendar(!showCalendar)}
                    className="peer w-full px-3 cursor-pointer py-2 placeholder:text-brand-grey text-brand-grey bg-whitefocus:outline-none"
                  >
                    {dob ? format(dob, "dd/MM/yyyy") : "Date of Birth"}
                    <CalendarIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brand-grey" />
                  </div>

                  {/* Calendar dropdown */}
                  {showCalendar && (
                    <div className="absolute top-full rounded-xl z-10 mt-2 shadow-md" ref={calendarRef}>
                      <DateRange
                        className="rounded-xl"
                        editableDateInputs={true}
                        onChange={handleSelect(index)}
                        moveRangeOnFirstSelection={false}
                        showDateDisplay={false}
                        months={1}
                        direction="horizontal"
                        maxDate={new Date()}
                        ranges={dateRange}
                        rangeColors={["#4B4BC3"]}
                      />
                    </div>
                  )}
                </div>
                {formerror[index]?.dob && <div className="text-red-500 text-sm mt-1">{formerror[index]?.dob}</div>}
              </div>

              {/* Gender */}
              <div className="space-y-1">
                <div className="relative font-proxima-nova w-full p-px rounded-lg border-2 border-[#EBEBEB] h-auto  shadow-sm">
                  <Listbox
                    disabled={disabled}
                    value={form.gender}
                    onChange={(value) => {
                      handlePassengerDetail(index, "gender", value)
                    }}
                  >
                    <ListboxButton
                      id="gender"
                      className="w-full flex justify-between text-brand-grey items-center focus:outline-none rounded-lg px-4 xs:px-2 py-2 bg-brand-white text-left"
                    >
                      <div>{form.gender ? form.gender : "Select Gender"}</div>
                      <ChevronDown />
                    </ListboxButton>
                    <ListboxOptions className="bg-brand-white border-2 border-[#EBEBEB]   rounded-xl flex flex-col gap-2 mt-1 cursor-pointer absolute z-10 w-full max-h-60 overflow-auto focus:outline-none">
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="">
                        Select Gender
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="Male">
                        Male
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="Female">
                        Female
                      </ListboxOption>
                      <ListboxOption className="bg-none px-2 hover:bg-[#EBEBEB]  text-brand-black" value="Other">
                        Other
                      </ListboxOption>
                    </ListboxOptions>
                  </Listbox>
                </div>
                {formerror[index]?.gender && (
                  <div className="text-red-500 text-sm mt-1">{formerror[index]?.gender}</div>
                )}
              </div>

              {/* Passport Number */}
              {/* <div className="space-y-1">
          <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
            <input
              type="text"
              id="passportNumber"
              value={form.passportNumber}
              className="w-full px-3 py-2 placeholder:text-[#A195F9] text-[#080236] bg-[#E6E3FF] border border-gray-300 rounded-full focus:outline-none"
              placeholder="Passport Number"
              onChange={(e) => handlePassengerDetail(index, "passportNumber", e.target.value)}
            />
          </div>
        </div> */}

              {/* Passport Country */}
              {/* <div className="space-y-1">
          <div ref={dropdownRef} className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
            <input
              type="text"
              id="passportNumber"
              value={country.search}
              className="w-full px-3 py-2 placeholder:text-[#A195F9] text-[#080236] text-[#A195F9] bg-[#E6E3FF] border border-gray-300 rounded-full focus:outline-none"
              placeholder="Passport Country of Issue"
              onChange={(e) => handleCountry(e.target.value)}
            />

            {country.search !== "" && (
              <div className="absolute z-10 mt-2 w-full bg-[#E6E3FF] text-black rounded-md shadow-lg max-h-60 overflow-auto">
                {country.loading ? (
                  <div className="px-4 py-2 text-[#A195F9]">
                    Loading suggestions...
                  </div>
                ) :
                  (
                    country.countryList.map(
                      (suggestion, index) => (
                        <div
                          key={`destination-${suggestion?.name}-${index}`}
                          className="px-4 py-2 hover:bg-[#4B4BC3] hover:text-[#B4BBE8] cursor-pointer text-[#A195F9]"
                          onClick={() =>
                            handleSuggestionClick(suggestion)
                          }
                        >
                          {suggestion.name}
                        </div>
                      )
                    )
                  )}
              </div>
            )}


          </div>
          {formerror[index]?.passportCountry &&
            <div className="text-red-500 text-sm mt-1">
              {formerror[index]?.passportCountry}
            </div>
          }
        </div> */}
            </div>
            {/* Todo : Enable Service option after live  */}

            {/* <div className="flex flex-col w-full">
        <div className="flex text-[#1E1E76] mb-2 font-semibold text-lg">
          Please Select Special Service Options
        </div>
        <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
          <Listbox value={form.specialService} onChange={(value) => {
            handlePassengerDetail(index, "specialService", value);
          }}>
            <ListboxButton
              id="Specific Services"
              className="w-full flex justify-between text-[#A195F9] items-center focus:outline-none rounded-full px-4 xs:px-2 py-2 bg-[#E6E3FF] text-left"
            >
              <div>{form.specialService ? form.specialService : "Select Special Services"}</div>
              <ChevronDown />
            </ListboxButton>
            <ListboxOptions className="bg-white rounded-xl flex flex-col gap-2 mt-1 cursor-pointer absolute z-10 w-full max-h-60 overflow-auto focus:outline-none">
              <ListboxOption className="bg-none px-2 hover:bg-[#4B4BC3] hover:text-[#B4BBE8] text-[#080236]" value="">Select Special Service Options</ListboxOption>
              <ListboxOption className="bg-none px-2 hover:bg-[#4B4BC3] hover:text-[#B4BBE8] text-[#080236]" value="wheelchair">Wheelchair</ListboxOption>
              <ListboxOption className="bg-none px-2 hover:bg-[#4B4BC3] hover:text-[#B4BBE8] text-[#080236]" value="infant">Traveling with Infant</ListboxOption>
              <ListboxOption className="bg-none px-2 hover:bg-[#4B4BC3] hover:text-[#B4BBE8] text-[#080236]" value="specialmeal">Special Meal Request</ListboxOption>
            </ListboxOptions>
          </Listbox>
        </div>
      </div> */}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}

export default PassengerDetails
