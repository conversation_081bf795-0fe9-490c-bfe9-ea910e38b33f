import { useAuth } from "@/components/AuthProvider/auth-Provider";
import { isAppUser } from "@/lib/utils/flightUtils";
import { Session } from "next-auth";
import { useSession } from "next-auth/react";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

/**
 * Returns a session-like object.
 * - For AppUser: pulls token/user from AuthProvider context.
 * - For web/NextAuth: returns the standard NextAuth session.
 */
export const useCustomSession = (): { 
  data: Session | null; 
  status: "authenticated" | "loading" | "unauthenticated" 
} => {
  const { token } = useAuth();
  const { data: session, status } = useSession();
  const params = useSearchParams();
  const userParam = params.get("user");
  const refreshToken = params.get("refreshToken");
  const accessTokenExpireOn = params.get("accessTokenExpiresOn");
  const refreshTokenExpireOn = params.get("refreshTokenExpiresOn");

  const user = useMemo(() => {
    return userParam ? JSON.parse(decodeURIComponent(userParam)) : null;
  }, [userParam]);

  if (isAppUser()) {
    if (token && accessTokenExpireOn && refreshToken && refreshTokenExpireOn) {
      // Return a session-like object for AppUser
      return {
        data: {
          accessToken: token,
          user,
          accessTokenExpireOn: accessTokenExpireOn,
          refreshToken: refreshToken,
          refreshTokenExpireOn: refreshTokenExpireOn,
          tokenType: "Bearer",
          error: null,
          expires: "",
        },
        status: "authenticated",
      };
    } else {
      return {
        data: {
          accessToken: "",
          user,
          accessTokenExpireOn: "",
          refreshToken: "",
          refreshTokenExpireOn: "",
          tokenType: "Bearer",
          error: null,
          expires: "",
        },
        status: "loading",
      };
    }
  }

  // Fallback to NextAuth session
  return { data: session, status };
};