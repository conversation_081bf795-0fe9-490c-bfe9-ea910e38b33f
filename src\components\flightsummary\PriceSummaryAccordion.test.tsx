import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import PriceSummaryAccordion from "./PriceSummaryAccordion";

const mockFormatFlightPrice = jest.fn(({ amount, currency }) => `${currency} ${amount}`);

const defaultProps = {
  baseTicketPrice: 100,
  totalTaxAndCharges: 20,
  total: 120,
  currency: "USD",
  formatFlightPrice: mockFormatFlightPrice,
};

describe("PriceSummaryAccordion", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders collapsed by default", () => {
    render(<PriceSummaryAccordion {...defaultProps} />);
    expect(screen.getByText("Show price summary")).toBeInTheDocument();
  });

  it("expands and shows content on header click", () => {
    render(<PriceSummaryAccordion {...defaultProps} />);
    const header = screen.getByText("Show price summary");
    fireEvent.click(header);
    expect(screen.getByText("Hide price summary")).toBeInTheDocument();
    expect(screen.getByText("Ticket Price")).toBeVisible();
    expect(screen.getByText("Taxes & fees")).toBeVisible();
    expect(screen.getByText("Total per ticket")).toBeVisible();
  });

  it("collapses content when header is clicked again", () => {
    render(<PriceSummaryAccordion {...defaultProps} />);
    const header = screen.getByText("Show price summary");
    fireEvent.click(header);
    expect(screen.getByText("Hide price summary")).toBeInTheDocument();
    fireEvent.click(screen.getByText("Hide price summary"));
    expect(screen.getByText("Show price summary")).toBeInTheDocument();
  });

  it("calls formatFlightPrice with correct values", () => {
    render(<PriceSummaryAccordion {...defaultProps} />);
    fireEvent.click(screen.getByText("Show price summary"));
    expect(mockFormatFlightPrice).toHaveBeenCalledWith({ amount: 100, currency: "USD" });
    expect(mockFormatFlightPrice).toHaveBeenCalledWith({ amount: 20, currency: "USD" });
    expect(mockFormatFlightPrice).toHaveBeenCalledWith({ amount: 120, currency: "USD" });
    expect(screen.getAllByText(/USD/).length).toBe(3);
  });


  it("handles zero and negative values gracefully", () => {
    render(
      <PriceSummaryAccordion
        {...defaultProps}
        baseTicketPrice={0}
        totalTaxAndCharges={-5}
        total={-5}
      />
    );
    fireEvent.click(screen.getByText("Show price summary"));
    expect(screen.getByText("USD 0")).toBeInTheDocument();
    expect(screen.getAllByText("USD -5")).toHaveLength(2);
  });
});