import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import OutboundFlight from "./outboundFlight";

// Mocks
jest.mock("@/context/FlightContext", () => ({
  useFlightContext: () => ({
    selectedOutboundFlight: {
      supplier_logo: "logo.png",
      airline: "Test Airline",
      airline_code: "TA",
      origin: "MAA",
      destination: "DEL",
      departure: "2024-06-01T10:00:00Z",
      arrival: "2024-06-01T12:00:00Z",
      departure_time_ampm: "10:00 AM",
      arrival_time_ampm: "12:00 PM",
      duration: "2h",
      segments: [
        {
          origin: "MAA",
          destination: "DEL",
          depart_date: "2024-06-01T10:00:00Z",
          arrive_date: "2024-06-01T12:00:00Z",
          duration: "2h"
        }
      ]
    },
    selectedInboundFlight: {},
    sharedFlightResults: {
      airport_data: {
        MAA: { airport_name: "Chennai Intl", city_name_original: "Chennai" },
        DEL: { airport_name: "Delhi Intl", city_name_original: "Delhi" }
      }
    },
    searchedFlightResults: {}
  })
}));

jest.mock("react-redux", () => ({
  useSelector: jest.fn(fn =>
    fn({
      tripSummary: {
        selectedOutboundFlight: {
          supplier_logo: "logo.png",
          airline: "Test Airline",
          airline_code: "TA",
          origin: "MAA",
          destination: "DEL",
          departure: "2024-06-01T10:00:00Z",
          arrival: "2024-06-01T12:00:00Z",
          departure_time_ampm: "10:00 AM",
          arrival_time_ampm: "12:00 PM",
          duration: "2h",
          segments: [
            {
              origin: "MAA",
              destination: "DEL",
              depart_date: "2024-06-01T10:00:00Z",
              arrive_date: "2024-06-01T12:00:00Z",
              duration: "2h"
            }
          ]
        },
        selectedInboundFlight: {},
        sharedFlightResults: {
          airport_data: {
            MAA: { airport_name: "Chennai Intl", city_name_original: "Chennai" },
            DEL: { airport_name: "Delhi Intl", city_name_original: "Delhi" }
          }
        }
      }
    })
  )
}));

jest.mock("@/lib/utils/flightTime", () => ({
  formatFlightTime: () => ({
    dayLabel: "Sat, 1 Jun",
    timeLabel: "10:00",
  }),
}));

jest.mock("@/lib/utils/layover", () => ({
  getFormattedLayoverTime: () => "1h 30m layover",
}));

jest.mock("@/lib/utils/flightDescription", () => ({
  getFlightDescription: () => "Test flight description",
}));

describe("OutboundFlight component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders collapsed desktop view by default", () => {
    render(<OutboundFlight />);
    expect(screen.getAllByAltText("Test Airline").length).toBeGreaterThan(0);
    expect(screen.getAllByText("Sat, 1 Jun")[0]).toBeInTheDocument();
    expect(screen.getByText("Departure")).toBeInTheDocument();
    expect(screen.getByText("10:00 AM - 12:00 PM")).toBeInTheDocument();
    expect(screen.getAllByText("2h").length).toBeGreaterThan(0);
    expect(screen.getByText("$ 235")).toBeInTheDocument();
    expect(screen.getByText("Trip Total")).toBeInTheDocument();
    expect(screen.getAllByText("*Free Cancellation within 24 hours of booking").length).toBeGreaterThan(0);
  });

 
});