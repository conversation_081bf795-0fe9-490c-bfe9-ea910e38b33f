"use client";

import { CardElement } from "@stripe/react-stripe-js";

interface CreditCardSectionProps {
    hasActiveCard: boolean;
    showCardForm: boolean;
    setShowCardForm: (show: boolean) => void;
    cardLast4: string;
    isCardExpired: boolean;
    cardTouched: boolean;
    setCardTouched: (touched: boolean) => void;
    cardError: string;
    setCardError: (error: string) => void;
    cardComplete: boolean;
    setCardComplete: (complete: boolean) => void;
    isCardEmpty: boolean;
    setIsCardEmpty: (empty: boolean) => void;
    message: string;
    success_message: string;
}

const CreditCardSection = ({
    hasActiveCard,
    showCardForm,
    setShowCardForm,
    cardLast4,
    isCardExpired,
    cardTouched,
    setCardTouched,
    cardError,
    setCardError,
    cardComplete,
    setCardComplete,
    isCardEmpty,
    setIsCardEmpty,
    message,
    success_message,
}: CreditCardSectionProps) => {
    const cardElementOptions = {
        hidePostalCode: true,
        style: {
            base: {
                fontSize: "16px",
                color: "#424770",
                "::placeholder": {
                    color: "#A195F9",
                },
            },
            invalid: {
                color: "#9e2146",
            },
        },
    };

    return (
        <div className="flex flex-col gap-5 mb-8">
            <div className="relative font-proxima-nova w-full p-px rounded-2xl  h-auto  shadow-sm">
                <div className="flex flex-col gap-3 justify-between w-full 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-3 xs:p-3 border-2 border-[#EBEBEB] shadow-md bg-brand-white relative">
                    <div className="flex flex-col w-full">
                        <div className="flex flex-col w-full gap-4">
                            <div className="text-lg text-brand-black flex flex-col sm:flex-row items-start sm:items-center justify-between font-proxima-nova w-full">
                                <div className="mb-2 sm:mb-0 text-2xl font-bold">
                                    Payment Information
                                </div>

                                <div className="flex flex-wrap gap-4 max-w-full overflow-hidden">
                                    <img
                                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/320px-Visa_Inc._logo.svg.png"
                                        alt="Visa"
                                        className="h-4 max-w-[50px] object-contain"
                                    />
                                    {/* <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b5/PayPal.svg/320px-PayPal.svg.png" alt="PayPal" className="h-4 max-w-[50px] object-contain" />
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/Google_Pay_Logo.svg/320px-Google_Pay_Logo.svg.png" alt="Google Pay" className="h-4 max-w-[50px] object-contain" />
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/Apple_Pay_logo.svg/320px-Apple_Pay_logo.svg.png" alt="Apple Pay" className="h-4 max-w-[50px] object-contain" /> */}
                                    <img
                                        src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/320px-Mastercard-logo.svg.png"
                                        alt="Mastercard"
                                        className="h-4 max-w-[50px] object-contain"
                                    />
                                    <img
                                        src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Amex.png"
                                        alt="Amex"
                                        className="h-4 max-w-[50px] object-contain"
                                    />
                                </div>
                            </div>

                            {hasActiveCard && !showCardForm ? (
                                <div className="flex flex-col gap-4">
                                    <div className="text-lg text-brand-black font-semibold">
                                        Payment will be charged with{" "}
                                        <span className="font-bold">
                                            **** **** **** {cardLast4}
                                        </span>
                                    </div>
                                    {isCardExpired && (
                                        <div className="text-red-500 text-base font-medium">
                                            This card has expired. Please change your card.
                                        </div>
                                    )}
                                    <button
                                        type="button"
                                        onClick={() => setShowCardForm(true)}
                                        className="px-4 py-2 w-fit rounded-lg text-lg bg-brand-white text-brand border-2 border-brand font-semibold"
                                    >
                                        Change Card
                                    </button>
                                </div>
                            ) : (
                                <span style={{ display: "none" }}></span>
                            )}

                            <form
                                className="w-full flex flex-col gap-4"
                                style={{ display: showCardForm ? "flex" : "none" }}
                            >
                                <div className="text-lg text-brand-black">
                                    Pay with your Credit / Debit cards via Stripe
                                </div>
                                <CardElement
                                    id="card-element"
                                    options={cardElementOptions}
                                    onChange={(event) => {
                                        setCardComplete(event.complete);
                                        setIsCardEmpty(event.empty);
                                        setCardError(event.error ? event.error.message : "");
                                        if (!cardTouched) setCardTouched(true);
                                    }}
                                    className={`w-full p-3 rounded-lg bg-brand-white  border border-[#EBEBEB]  ${cardTouched && (cardError || isCardEmpty)
                                            ? "border-red-500"
                                            : "border-[#707FF5]"
                                        }`}
                                />
                                {message && <p className="text-red-500 mt-2">{message}</p>}
                                {success_message && (
                                    <p className="text-brand-black mt-2">{success_message}</p>
                                )}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreditCardSection;
