import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AppState } from "@/store/store";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { StopsOptions } from "@/constants/flight";
import { updateTripSummary } from "@/store/slices/tripSummary";
import { AirportData } from "@/types/flights";
import { formatMinutestoTime } from "@/lib/utils/layover";

interface FlightFilterProps {
  mobile?: boolean;
  desktop?: boolean;
}

const FlightFilter: React.FC<FlightFilterProps> = ({
  mobile = false,
  desktop = false,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const dispatch = useDispatch();
  const handleFilterChange = (filter: string, value: string) => {
    let updatedFilters = { ...tripSummaryDetails?.selectedFilters };
    switch (filter) {
      case "airline":
        const airlineOptionValue = value.toLowerCase();
        updatedFilters = {
          ...updatedFilters,
          [filter]: tripSummaryDetails?.selectedFilters[filter].includes(
            airlineOptionValue
          )
            ? tripSummaryDetails?.selectedFilters[filter].filter(
                (item: string) => item !== airlineOptionValue
              )
            : [
                ...tripSummaryDetails?.selectedFilters[filter],
                airlineOptionValue,
              ],
          allAirlines: false,
        };
        break;
      case "priceRange":
      case "departureOutbound":
      case "departureInbound":
        updatedFilters = { ...updatedFilters, [filter]: value };
        break;
      default:
        updatedFilters = {
          ...tripSummaryDetails?.selectedFilters,
          [filter]: tripSummaryDetails?.selectedFilters[filter].includes(value)
            ? tripSummaryDetails?.selectedFilters[filter].filter(
                (item: string) => item !== value
              )
            : [...tripSummaryDetails?.selectedFilters[filter], value],
        };
        break;
    }
    dispatch(updateTripSummary({ selectedFilters: updatedFilters }));
  };

  const handleAllAirline = (value: boolean) => {
    dispatch(
      updateTripSummary({
        selectedFilters: {
          ...tripSummaryDetails?.selectedFilters,
          allAirlines: value,
          airline: [],
        },
      })
    );
  };

  const handleAllLayoverAirline = (value: boolean) => {
    dispatch(
      updateTripSummary({
        selectedFilters: {
          ...tripSummaryDetails?.selectedFilters,
          allLayoverAirports: value,
          layoverAirports: [],
        },
      })
    );
  };

  useEffect(() => {
    if (
      tripSummaryDetails?.flightSearchRespnse &&
      Object.keys(tripSummaryDetails?.flightSearchRespnse).length > 0
    ) {
      const airportData = tripSummaryDetails?.flightSearchRespnse
        ?.airport_data as AirportData;
      const airportCities: {
        [city: string]: { iata_code: string; airport_name: string }[];
      } = {};
      if (airportData && typeof airportData === "object") {
        Object.values(airportData).forEach(
          ({ iata_code, city_name_original, airport_name }) => {
            if (!airportCities[city_name_original]) {
              airportCities[city_name_original] = [];
            }
            airportCities[city_name_original].push({ iata_code, airport_name });
          }
        );
      }

      const filterOptions = {
        stopsOptions: StopsOptions || [],
        airlinesOptions:
          tripSummaryDetails?.flightSearchRespnse?._outward?.airlines || [],
        airportOptions: airportCities,
        layoverAirportsOptions:
          tripSummaryDetails?.flightSearchRespnse?._outward?.layover_airports ||
          [],
      };
      dispatch(
        updateTripSummary({
          ...tripSummaryDetails?.filterOptions,
          filterOptions,
        })
      );
      setIsLoading(false);
    }
  }, [tripSummaryDetails?.flightSearchRespnse]);

  if (isLoading && !Object.keys(tripSummaryDetails?.sharedFlightResults).length)
    return null;

  return (
    <>
      {desktop && (
        <>
          <div className="pl-4">
            <div className="flex w-full flex-col pr-5 right-5">
              <h2 className="text-lg xs:text-base text-center font-proxima-nova text-[#1E1E76] font-semibold mb-3 xs:mb-1">
                Filters
              </h2>

              {/* Stops Section */}
              {tripSummaryDetails?.filterOptions?.stopsOptions &&
                tripSummaryDetails?.filterOptions?.stopsOptions.length > 0 && (
                  <div className="mb-4 text-[#080236] font-proxima-nova">
                    <div className="flex w-full border"></div>
                    <div className="font-semibold text-lg xs:text-base mt-4">
                      Stops
                    </div>
                    <div className="">
                    {tripSummaryDetails?.filterOptions?.stopsOptions?.map(
                      (option: any) => (
                        <label
                          key={option}
                          className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={tripSummaryDetails?.selectedFilters.stops.includes(
                              option
                            )}
                            onChange={() => handleFilterChange("stops", option)}
                            className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                          />
                          {option}
                        </label>
                      )
                    )}
                    </div>
                  </div>
                )}

              {/* Airlines Section */}
              {tripSummaryDetails?.filterOptions?.airlinesOptions &&
                tripSummaryDetails?.filterOptions?.airlinesOptions.length >
                  0 && (
                  <div className="mb-4 font-proxima-nova text-[#080236]">
                    <div className="flex w-full border"></div>
                    <h3 className="font-semibold text-lg mt-4">Airlines</h3>
                    <div className="flex items-center justify-between gap-2 py-1">
                      <span>Select all Airlines</span>
                      <Switch
                        checked={
                          tripSummaryDetails?.selectedFilters?.allAirlines
                        }
                        onCheckedChange={handleAllAirline}
                      />
                    </div>
                    {tripSummaryDetails?.filterOptions?.airlinesOptions.map(
                      (option: any) => (
                        <label
                          key={option}
                          className="flex items-center gap-2 p-2 xs:text-sm xs:p-1 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={tripSummaryDetails?.selectedFilters?.airline.includes(
                              option.toLowerCase()
                            )}
                            onChange={() =>
                              handleFilterChange("airline", option)
                            }
                            className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border-[] rounded"
                          />
                          {option}
                        </label>
                      )
                    )}
                  </div>
                )}

              {/* Price Section */}
              <div className="flex w-full border border-[#B4BBE8]"></div>
              <div className="mb-8 mt-4 font-proxima-nova text-[#080236]">
                <h3 className="font-semibold text-lg">Price</h3>
                <p>
                  £{tripSummaryDetails?.selectedFilters?.priceRange[0]} - £
                  {tripSummaryDetails?.selectedFilters?.priceRange[1]}
                </p>
                <Slider
                  color="#1E1E76"
                  value={tripSummaryDetails?.selectedFilters?.priceRange}
                  onValueChange={(e: any) =>
                    handleFilterChange("priceRange", e)
                  }
                  min={0}
                  max={10000}
                  step={100}
                  className=" mt-4"
                />
              </div>

              {/* Airports Section */}
              {tripSummaryDetails?.filterOptions?.airportOptions &&
                Object.keys(tripSummaryDetails?.filterOptions?.airportOptions)
                  .length > 0 && (
                  <div className="mb-2 font-proxima-nova text-[#080236]">
                    <div className="flex w-full border"></div>
                    <h3 className="font-semibold xs:mb-2 mb-3">Airports</h3>
                    {Object.entries(
                      tripSummaryDetails?.filterOptions?.airportOptions
                    ).map(([city, airports]: any) => (
                      <div key={city} className="mb-2">
                        <h4 className="font-semibold text-sm text-[#080236] mb-1">
                          {city}
                        </h4>
                        {airports.map((airport: any, index: number) => (
                          <label
                            key={index}
                            className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={tripSummaryDetails?.selectedFilters?.airports.includes(
                                airport.iata_code
                              )}
                              onChange={() =>
                                handleFilterChange(
                                  "airports",
                                  airport.iata_code
                                )
                              }
                              className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                            />
                            <div className="flex flex-col">
                              <div className="text-wrap">{`${airport.airport_name} (${airport.iata_code})`}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    ))}
                  </div>
                )}

              {/* Departure times */}
              <div className="mb-8 font-proxima-nova text-[#080236]">
                <div className="flex w-full border"></div>
                <h3 className="font-semibold mt-4 mb-4 text-lg">
                  Departure times
                </h3>

                {/* Outbound */}
                <div className="mb-4">
                  <p className="font-medium mb-1 text-[#080236]">Outbound</p>
                  <p className="text-sm text-[#080236]">
                    {formatMinutestoTime(
                      tripSummaryDetails?.selectedFilters?.departureOutbound[0]
                    )}{" "}
                    -
                    {formatMinutestoTime(
                      tripSummaryDetails?.selectedFilters?.departureOutbound[1]
                    )}
                  </p>
                  <Slider
                    value={
                      tripSummaryDetails?.selectedFilters?.departureOutbound
                    }
                    onValueChange={(e: any) =>
                      handleFilterChange("departureOutbound", e)
                    }
                    min={0}
                    max={1439}
                    step={15}
                    className="mt-2"
                  />
                </div>

                {/* Return */}
                <div>
                  <p className="font-medium mb-1 text-[#080236]">Return</p>
                  <p className="text-sm text-[#080236]">
                    {formatMinutestoTime(
                      tripSummaryDetails?.selectedFilters?.departureInbound[0]
                    )}{" "}
                    -
                    {formatMinutestoTime(
                      tripSummaryDetails?.selectedFilters?.departureInbound[1]
                    )}
                  </p>
                  <Slider
                    value={
                      tripSummaryDetails?.selectedFilters?.departureInbound
                    }
                    onValueChange={(e: any) =>
                      handleFilterChange("departureInbound", e)
                    }
                    min={0}
                    max={1439}
                    step={15}
                    className="mt-2"
                  />
                </div>
              </div>

              {/* Layover Airports Section */}
              {tripSummaryDetails?.filterOptions?.layoverAirportsOptions &&
                tripSummaryDetails?.filterOptions?.layoverAirportsOptions
                  .length > 0 && (
                  <div className="mb-2 font-proxima-nova text-[#080236]">
                    <div className="flex w-full border"></div>
                    <h3 className="font-semibold">Layover Airports</h3>
                    <div className="flex items-center justify-between p-2">
                      <span>Select all layover airports</span>
                      <Switch
                        checked={
                          tripSummaryDetails?.selectedFilters
                            ?.allLayoverAirpirts
                        }
                        onCheckedChange={handleAllLayoverAirline}
                      />
                    </div>
                    {tripSummaryDetails?.filterOptions?.layoverAirportsOptions.map(
                      (option: any) => (
                        <label
                          key={option}
                          className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={tripSummaryDetails?.selectedFilters?.layoverAirports?.includes(
                              option
                            )}
                            onChange={() =>
                              handleFilterChange("layoverAirports", option)
                            }
                            className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                          />
                          {option}
                        </label>
                      )
                    )}
                  </div>
                )}
            </div>
          </div>
        </>
      )}
      {mobile && (
        <div className="flex flex-col h-screen bg-white overflow-y-auto">
          <div className="px-4 pb-[300px]">
            {/* Stops Section */}
            {tripSummaryDetails?.filterOptions?.stopsOptions &&
              tripSummaryDetails?.filterOptions?.stopsOptions.length > 0 && (
                <div className="mb-2 text-[#080236] font-proxima-nova">
                  <div className="flex w-full"></div>
                  <div className="font-semibold xs:text-base">Stops</div>
                  {tripSummaryDetails?.filterOptions?.stopsOptions.map(
                    (option: any) => (
                      <label
                        key={option}
                        className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={tripSummaryDetails?.selectedFilters.stops.includes(
                            option
                          )}
                          onChange={() => handleFilterChange("stops", option)}
                          className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                        />
                        {option}
                      </label>
                    )
                  )}
                </div>
              )}

            {/* Airlines Section */}
            {tripSummaryDetails?.filterOptions?.airlinesOptions &&
              tripSummaryDetails?.filterOptions?.airlinesOptions.length > 0 && (
                <div className="mb-2 font-proxima-nova text-[#080236]">
                  <div className="flex w-full border"></div>
                  <h3 className="font-semibold">Airlines</h3>
                  <div className="flex items-center justify-between p-2">
                    <span>Select all Airlines</span>
                    <Switch
                      checked={tripSummaryDetails?.selectedFilters?.allAirlines}
                      onCheckedChange={handleAllAirline}
                    />
                  </div>
                  {tripSummaryDetails?.filterOptions?.airlinesOptions.map(
                    (option: any) => (
                      <label
                        key={option}
                        className="flex items-center gap-2 p-2 xs:text-sm xs:p-1 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={tripSummaryDetails?.selectedFilters?.airline.includes(
                            option
                          )}
                          onChange={() => handleFilterChange("airline", option)}
                          className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                        />
                        {option}
                      </label>
                    )
                  )}
                </div>
              )}

            {/* Price Section */}
            <div className="mb-4 font-proxima-nova text-[#080236]">
              <div className="flex w-full border"></div>
              <h3 className="font-semibold">Price</h3>
              <p>
                £{tripSummaryDetails?.selectedFilters?.priceRange[0]} - £
                {tripSummaryDetails?.selectedFilters?.priceRange[1]}
              </p>
              <Slider
                color="#1E1E76"
                value={tripSummaryDetails?.selectedFilters?.priceRange}
                onValueChange={(e: any) => handleFilterChange("priceRange", e)}
                min={0}
                max={10000}
                step={100}
                className="mt-2"
              />
            </div>

            {/* Airports Section */}
            {tripSummaryDetails?.filterOptions?.airportOptions &&
              Object.keys(tripSummaryDetails?.filterOptions?.airportOptions)
                .length > 0 && (
                <div className="mb-2 font-proxima-nova text-[#080236]">
                  <div className="flex w-full border"></div>
                  <h3 className="font-semibold xs:mb-2 mb-3">Airports</h3>
                  {Object.entries(
                    tripSummaryDetails?.filterOptions?.airportOptions
                  ).map(([city, airports]: any) => (
                    <div key={city} className="mb-2">
                      <h4 className="font-semibold text-sm text-[#080236] mb-1">
                        {city}
                      </h4>
                      {airports.map((airport: any, index: number) => (
                        <label
                          key={index}
                          className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={tripSummaryDetails?.selectedFilters?.airports.includes(
                              airport.iata_code
                            )}
                            onChange={() =>
                              handleFilterChange("airports", airport.iata_code)
                            }
                            className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                          />
                          <div className="flex flex-col">
                            <div>{airport.airport_name}</div>
                            <div>{airport.iata_code}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                  ))}
                </div>
              )}

            {/* Departure times */}
            <div className="mb-8 font-proxima-nova text-[#080236]">
              <div className="flex w-full border"></div>
              <h3 className="font-semibold mt-4 mb-4 text-lg">
                Departure times
              </h3>

              {/* Outbound */}
              <div className="mb-4">
                <p className="font-medium mb-1 text-[#080236]">Outbound</p>
                <p className="text-sm text-[#080236]">
                  {formatMinutestoTime(
                    tripSummaryDetails?.selectedFilters?.departureOutbound[0]
                  )}{" "}
                  -
                  {formatMinutestoTime(
                    tripSummaryDetails?.selectedFilters?.departureOutbound[1]
                  )}
                </p>
                <Slider
                  value={tripSummaryDetails?.selectedFilters?.departureOutbound}
                  onValueChange={(e: any) =>
                    handleFilterChange("departureOutbound", e)
                  }
                  min={0}
                  max={1439}
                  step={15}
                  className="mt-2"
                />
              </div>

              {/* Return */}
              <div>
                <p className="font-medium mb-1 text-[#080236]">Return</p>
                <p className="text-sm text-[#080236]">
                  {formatMinutestoTime(
                    tripSummaryDetails?.selectedFilters?.departureInbound[0]
                  )}{" "}
                  -
                  {formatMinutestoTime(
                    tripSummaryDetails?.selectedFilters?.departureInbound[1]
                  )}
                </p>
                <Slider
                  value={tripSummaryDetails?.selectedFilters?.departureInbound}
                  onValueChange={(e: any) =>
                    handleFilterChange("departureInbound", e)
                  }
                  min={0}
                  max={1439}
                  step={15}
                  className="mt-2"
                />
              </div>
            </div>

            {/* Layover Airports Section */}
            {tripSummaryDetails?.filterOptions?.layoverAirportsOptions &&
              tripSummaryDetails?.filterOptions?.layoverAirportsOptions.length >
                0 && (
                <div className="mb-2 font-proxima-nova text-[#080236]">
                  <div className="flex w-full border"></div>
                  <h3 className="font-semibold">Layover Airports</h3>
                  <div className="flex items-center justify-between p-2">
                    <span>Select all layover airports</span>
                    <Switch
                      checked={
                        tripSummaryDetails?.selectedFilters?.allLayoverAirpirts
                      }
                      onCheckedChange={handleAllLayoverAirline}
                    />
                  </div>
                  {tripSummaryDetails?.filterOptions?.layoverAirportsOptions.map(
                    (option: any) => (
                      <label
                        key={option}
                        className="flex items-center xs:text-sm gap-2 p-2 xs:p-1 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={tripSummaryDetails?.selectedFilters?.layoverAirports.includes(
                            option
                          )}
                          onChange={() =>
                            handleFilterChange("layoverAirports", option)
                          }
                          className="w-4 xs:w-3 xs:h-3 h-4 accent-brand-black border rounded"
                        />
                        {option}
                      </label>
                    )
                  )}
                </div>
              )}
          </div>
        </div>
      )}
    </>
  );
};

export default FlightFilter;
