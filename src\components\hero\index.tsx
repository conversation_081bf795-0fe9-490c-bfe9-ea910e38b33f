"use client"

import { useEffect, useState } from 'react';

export const Hero = () => {
  const [isLandscape, setIsLandscape] = useState(false);
  
  useEffect(() => {
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };
    
    checkOrientation();
    
    window.addEventListener('resize', checkOrientation);
    
    return () => window.removeEventListener('resize', checkOrientation);
  }, []);

  return (
    <div className={`relative w-full overflow-hidden flex items-center justify-center
      ${isLandscape 
        ? 'h-[80vh] sm:h-[85vh] md:h-[88vh] lg:h-[78vh] xl:h-[72vh]' 
        : 'h-[24vh] sm:h-[37vh] md:h-[37vh] lg:h-[98vh] xl:h-[64vh]'
      }`}>
  
      {/* 🌟 Layer 1: Background Image */}
      <div className="absolute inset-0">
        <img 
          src="/images/BG.png" 
          alt="Background"
          className="w-full h-full object-cover"
        />
      </div>
    
      {/* 🌟 Layer 3: Centered Circular Video   2xl:w-[1100px] 2xl:h-[1100px] */}
      <div className="absolute flex items-center justify-center top-0">
        <div className="relative w-[300px] h-[300px] 
          xs:w-[400px] xs:h-[400px]
          sm:w-[478px] sm:h-[478px]
          md:w-[478px] md:h-[478px]
          lg:w-[800px] lg:h-[800px]
          xl:w-[920px] xl:h-[920px] 
        rounded-full overflow-hidden border-2 border-[#A195F9]">
          <video 
            autoPlay loop muted playsInline
            className="w-full h-full object-cover scale-110 opacity-50"
          >
            <source src="/images/videos/Logo.mp4" type="video/mp4" />
          </video>
        </div>
      </div>
  
      {/* 🌟 Layer 4: Foreground Images 2xl:w-[1200px] 2xl:h-[700px] /* Large Screens (2560px width) */ }
      <div className="absolute inset-0 bottom-0 w-full flex items-end justify-center z-999">
        <div className="relative">
          <img
            src="/images/Group.png"
            alt="Foreground Group 1"
            className="relative top-0 left-0 
                      w-[320px] h-[180px]  /* Mobile */
                      sm:w-[436px] sm:h-[264px]  /* Tablet */
                      lg:w-[840px] lg:h-[509px]  /* Desktop */
                      
                      object-contain"
          />
        </div>
      </div>

  
      {/* 🌟 Layer 5: Centered Text Content  2xl:w-[900px] 2xl:text-[72px] 2xl:leading-[85px]/ 2xl:w-[900px] 2xl:text-[24px] 2xl:leading-[28px]*/}
      <div className="relative z-10 text-center text-[#F2F3FA] px-8 sm:px-9 w-full">
        <h1 className="font-proxima-nova font-bold text-[28px] leading-[34.1px] w-[324px] mx-auto 
                      sm:w-[486px] sm:text-[42px] sm:leading-[51.16px] 
                      lg:w-[672px] lg:text-[58px] lg:leading-[70.64px] 
                      ">
          NxVoy - Your AI-Powered Travel Companion
        </h1>
    
        <p className="hidden md:block font-proxima-nova font-bold text-[18px] leading-[21.92px] 
                      w-[471px] mx-auto sm:w-[471px] 
                      lg:w-[671px] ">
          Say goodbye to travel stress! Shasa, NxVoy's AI Assistant,
        </p>
    
        <p className="hidden md:block font-proxima-nova font-bold text-[18px] leading-[21.92px] 
                      w-[471px] mx-auto sm:w-[471px] 
                      lg:w-[671px] ">
          makes trip planning effortless with real-time updates and seamless itineraries.
        </p>
      </div>
  
    </div>
  )
}
