import { persistor, store } from "@/store/store"; // Use the same persistor instance
import { actionTypes } from "./actionTypes";

export const clearReduxOnLogout = async () => {
  try {
    console.log("Before clearing - localStorage:", localStorage.getItem('persist:root'));
    
    // Use the same persistor instance
    persistor.pause();
    
    // Clear Redux state
    store.dispatch({ type: actionTypes.RESET_STORE });
    
    // Clear persisted storage
    await persistor.purge();
    await persistor.flush();
    
    // Manual backup clear (in case purge doesn't work)
    localStorage.removeItem('persist:root');
    
    console.log("After clearing - localStorage:", localStorage.getItem('persist:root'));
    
    // Resume persistence
    persistor.persist();
    
    console.log("Redux and persisted storage cleared on logout.");
  } catch (error) {
    console.error("Failed to clear Redux store on logout:", error);
  }
};