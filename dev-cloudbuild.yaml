steps:
   # 1️⃣ SonarQube Code Analysis
  - name: 'sonarsource/sonar-scanner-cli'
    args:
      - "-Dsonar.projectKey=nxvoy-travel-agent-fe"
      - "-Dsonar.host.url=http://************:9000"
      - "-Dsonar.login=$$SONAR_TOKEN"
    secretEnv: ["SONAR_TOKEN"]

  # 2.Deploy to Dev Server SSH into the Dev Server and Execute `deploy_container.sh`
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud compute ssh madhavan@dev-server --project=dev-nxvoytrips --zone=europe-west2-a --command='
        sudo /bin/bash /home/<USER>/deploy_container.sh'
timeout: 1800s  # Set timeout to 30 minutes (adjust if needed)
availableSecrets:
  secretManager:
    - versionName: projects/dev-nxvoytrips/secrets/sonar-token/versions/latest
      env: "SONAR_TOKEN"  
options:
  logging: CLOUD_LOGGING_ONLY 
  

