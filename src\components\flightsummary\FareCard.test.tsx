import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FareCard from "./FareCard";

// src/components/flightsummary/FareCard.test.tsx

// Mock useFlightContext
jest.mock("@/context/FlightContext", () => ({
  useFlightContext: () => ({
    sharedFlightResults: {
      airline_data: {
        "XY": { name: "Test Airline" },
      },
    },
  }),
}));

// Mock getAirlineName
jest.mock("@/lib/utils/airline", () => ({
  getAirlineName: jest.fn(() => "Test Airline"),
}));

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
  CheckCircle: (props: any) => <svg data-testid="CheckCircle" {...props} />,
  XCircle: (props: any) => <svg data-testid="XCircle" {...props} />,
}));

const baseProps: any = {
  title: "Super Saver",
  price: "$100",
  discount: "10% off",
  selected: false,
  buttonLabel: "Select",
  flight: {
    id: "flight1",
    supplier_logo: "logo.png",
    airline: "Test Airline",
    airline_code: "XY",
    origin: "JFK",
    destination: "LAX",
    departure: "2024-01-01T10:00:00Z",
    arrival: "2024-01-01T13:00:00Z",
    duration: "180",
    stops: 0,
    price: 100,
    currency: "USD",
    segments: [
      { travel_class: { class: "Economy" } }
    ],
    baggage: [],
    fare_basis: "",
    refundable: false,
    change_penalty: false,
    provider: "",
    cabin: "Economy",
    aircraft: "",
    meal: "",
    layovers: [],
    booking_class: "",
    fare_family: "",
    fare_type: "",
    validating_carrier: "",
    codeshare: false,
    extra: {},
    supplier: "",
    is_return: false,
    passenger_prices: [],
    outward_id: "",
    return_id: "",
    fare_rules: [],
    fare_restrictions: [],
    fare_conditions: [],
    validating_airline: "",
    marketing_airline: "",
    operating_airline: "",
    booking_code: "",
    seats_remaining: 9,
    total_price: 100,
    base_price: 90,
    taxes: 10,
    surcharges: 0,
    fees: 0,
    commission: 0,
    markup: 0,
    discount: 0,
    // Add required missing fields for Flight type
    departure_date: "2024-01-01",
    departure_time_24hr: "10:00",
    departure_time_ampm: "10:00 AM",
    departure_time: "10:00",
    arrival_date: "2024-01-01",
    arrival_time_24hr: "13:00",
    arrival_time_ampm: "01:00 PM",
    arrival_time: "13:00",
    stopover_airports: [],
    stopover_durations: [],
    stopover_cities: [],
    // Add any other required fields with default/mock values as needed
    departure_tzone: "America/New_York",
    arrival_tzone: "America/Los_Angeles",
    wait_time: "",
    wait_time_in_seconds: 0,
  },
  options: ["Free meal", "No checked bag"],
  onClick: jest.fn(),
};

describe("FareCard", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders airline logo, name, route, title, cabin class, options, price, discount, and button", () => {
    render(<FareCard {...baseProps} />);
    expect(screen.getByAltText("Test Airline")).toHaveAttribute("src", "logo.png");
    expect(screen.getByText("Test Airline")).toBeInTheDocument();
    expect(screen.getByText("JFK - LAX")).toBeInTheDocument();
    expect(screen.getByText("Super Saver")).toBeInTheDocument();
    expect(
      screen.getByText((content, node) =>
        node?.textContent === "Cabin : Economy"
      )
    ).toBeInTheDocument();
    expect(screen.getByText("Free meal")).toBeInTheDocument();
    expect(screen.getByText("No checked bag")).toBeInTheDocument();
    expect(screen.getByText("$100")).toBeInTheDocument();
    expect(screen.getByText("10% off")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Select" })).toBeInTheDocument();
  });

  it("renders CheckCircle for positive option and XCircle for negative option", () => {
    render(<FareCard {...baseProps} />);
    const checkIcons = screen.getAllByTestId("CheckCircle");
    const xIcons = screen.getAllByTestId("XCircle");
    expect(checkIcons.length).toBe(1);
    expect(xIcons.length).toBe(1);
  });

  it("shows 'Selected' and disables button when selected is true", () => {
    render(<FareCard {...baseProps} selected={true} />);
    const button = screen.getByRole("button", { name: "Selected" });
    expect(button).toBeDisabled();
  });

  it("calls onClick(null) when button is clicked and not selected", () => {
    const onClick = jest.fn();
    render(<FareCard {...baseProps} onClick={onClick} />);
    const button = screen.getByRole("button", { name: "Select" });
    fireEvent.click(button);
    expect(onClick).toHaveBeenCalledWith(null);
  });

  it("does not call onClick when selected is true and button is disabled", () => {
    const onClick = jest.fn();
    render(<FareCard {...baseProps} selected={true} onClick={onClick} />);
    const button = screen.getByRole("button", { name: "Selected" });
    fireEvent.click(button);
    expect(onClick).not.toHaveBeenCalled();
  });

  it("handles missing options gracefully", () => {
    render(<FareCard {...baseProps} options={undefined} />);
    // Should not throw and should render other elements
    expect(screen.getByText("Super Saver")).toBeInTheDocument();
  });

  it("handles missing flight gracefully", () => {
    render(<FareCard {...baseProps} flight={undefined} />);
    expect(screen.getByText("Super Saver")).toBeInTheDocument();
    // Airline logo and name should not throw
  });

  it("handles segment with travel_class as string", () => {
    const flight = {
      ...baseProps.flight,
      segments: [{ travel_class: "Business" }],
    };
    render(<FareCard {...baseProps} flight={flight} />);
    expect(
      screen.getByText((content, node) =>
        node?.textContent?.replace(/\s/g, "") === "Cabin:") // Remove all whitespace for matching
    ).toBeInTheDocument();
  });

  it("handles segment with travel_class as null", () => {
    const flight = {
      ...baseProps.flight,
      segments: [{ travel_class: null }],
    };
    render(<FareCard {...baseProps} flight={flight} />);
    expect(
      screen.getByText((content, node) =>
        node?.textContent?.replace(/\s/g, "") === "Cabin:"
      )
    ).toBeInTheDocument();
  });
});