import HotelDetailsContainer from '@/components/hotelBooking/hotelDetails/hotelDetailsContainer'
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react'
import { useCustomSession } from "@/hooks/use-custom-session"
import axios from "axios";
import { Button } from '@/components/ui/button';
import { useSelector, useDispatch } from 'react-redux';
import { AppState } from '@/store/store';
import { setSelectedHotel } from '@/store/slices/hotelDetails';
import { Hotel } from '@/lib/types';

import { HotelImage, HotelPhone, HotelFacility, RoomDetail, InterestPoint } from '@/lib/types';

interface HotelData {
    name: string;
    description: string;
    address: string;
    postalCode: string;
    city: string;
    email: string;
    phones: HotelPhone[];
    website: string | null;
    img_base_url: string;
    img_base_thumbnail_url: string;
    images: HotelImage[];
    room_images: Record<string, HotelImage>;
    facilities: HotelFacility[];
    interest_points: InterestPoint[];
    nearest_terminals: string[];
    room_details: Record<string, RoomDetail>;
    check_in?: string;
    check_out?: string;
}

interface ApiResponse {
    detail: {
        status: string;
        message: string;
        request_uuid: string;
        data: Record<string, HotelData>;
    };
}

const HotelDetails = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const { id } = router.query;
    const { data: session, status } = useCustomSession();
    const token = session?.accessToken;
    const [hotelData, setHotelData] = useState<HotelData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const selectedHotel = useSelector((state: AppState) => state.hotelDetails.selectedHotel);

    const enrichSelectedHotelWithContent = (contentData: HotelData) => {
        if (!selectedHotel) return;

        // Helper function to get amenities based on hotel facilities
        const getAmenities = (facilities: HotelFacility[], boardName: string) => {
            const amenities = [];

            // Breakfast
            if (boardName.includes('BREAKFAST') || boardName.includes('BB')) {
                amenities.push('Breakfast Included');
            }

            // WiFi
            if (facilities.some(f => f.label?.toLowerCase().includes('wi-fi') || f.label?.toLowerCase().includes('wifi'))) {
                amenities.push('Free WiFi');
            }

            // Parking
            if (facilities.some(f => f.label?.toLowerCase().includes('car park') || f.label?.toLowerCase().includes('parking'))) {
                amenities.push('Free Parking');
            }

            // Room Service
            if (facilities.some(f => f.label?.toLowerCase().includes('room service'))) {
                amenities.push('Room Service');
            }

            // Air Conditioning
            if (facilities.some(f => f.label?.toLowerCase().includes('air conditioning'))) {
                amenities.push('Air Conditioning');
            }

            // Gym/Fitness
            if (facilities.some(f => f.label?.toLowerCase().includes('gym') || f.label?.toLowerCase().includes('fitness'))) {
                amenities.push('Fitness Center');
            }

            return amenities;
        };

        // Enhanced rooms with content data
        const enhancedRooms = selectedHotel.rooms.map(room => {
            const roomDetail = contentData.room_details[room.room_code];
            const roomImage = contentData.room_images[room.room_code];
            
            // Get room image URL
            const roomImageUrl = roomImage 
                ? `${contentData.img_base_url}${roomImage.path}`
                : selectedHotel.imageSrc;

            // Get hotel images for carousel
            const hotelImages = contentData.images?.slice(0, 8).map(img => 
                `${contentData.img_base_url}${img.path}`
            ) || [];

            // Combine room image with hotel images
            const allImages = roomImage 
                ? [roomImageUrl, ...hotelImages] 
                : hotelImages.length > 0 
                    ? hotelImages 
                    : [selectedHotel.imageSrc];

            // Get room facilities
            const roomFacilities = roomDetail?.facilities?.map(f => f.label) || [];
            const hotelRoomFacilities = contentData.facilities
                ?.filter(f => f.group === 'Room facilities (Standard room)')
                ?.map(f => f.label) || [];

            // Enhanced rate options with amenities
            const enhancedRateOptions = room.rate_options.map(rateOption => ({
                ...rateOption,
                amenities: getAmenities(contentData.facilities, rateOption.board_name),
                room_facilities: [...roomFacilities, ...hotelRoomFacilities].slice(0, 20) // Limit facilities
            }));

            return {
                ...room,
                room_images: allImages,
                room_description: roomDetail?.description || room.room_name,
                room_size: roomDetail ? {
                    min_pax: roomDetail.minPax,
                    max_pax: roomDetail.maxPax,
                    max_adults: roomDetail.maxAdults,
                    max_children: roomDetail.maxChildren,
                    min_adults: roomDetail.minAdults
                } : undefined,
                room_facilities: [...roomFacilities, ...hotelRoomFacilities],
                rate_options: enhancedRateOptions
            };
        });

        // Enhanced hotel with content data
        const enhancedHotel: Hotel = {
            ...selectedHotel,
            rooms: enhancedRooms,
            hotel_content: {
                full_description: contentData.description,
                address_full: `${contentData.address}, ${contentData.city} ${contentData.postalCode}`,
                email: contentData.email,
                phones: contentData.phones,
                website: contentData.website,
                check_in: contentData.check_in,
                check_out: contentData.check_out,
                all_images: contentData.images?.map(img => ({
                    ...img,
                    url: `${contentData.img_base_url}${img.path}`,
                    thumbnail: `${contentData.img_base_thumbnail_url}${img.path}`
                })) || [],
                facilities_grouped: contentData.facilities.reduce((acc, facility) => {
                    if (!acc[facility.group]) {
                        acc[facility.group] = [];
                    }
                    acc[facility.group].push(facility);
                    return acc;
                }, {} as Record<string, HotelFacility[]>),
                interest_points: contentData.interest_points,
                nearest_terminals: contentData.nearest_terminals
            }
        };

        // Update Redux state with enhanced data
        dispatch(setSelectedHotel(enhancedHotel));
    };

    const fetchHotelDetails = async (hotelId: string) => {
        if (!token && status === "loading" || status === "unauthenticated") {
            setError("Authentication token not available");
            return;
        }

        if(!selectedHotel) {
            setError("Selected Hotel data lost. Please go back and try again");
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const requestData = {
                hotel_codes: [parseInt(hotelId)],
                request_uuid: "client_1234"
            };

            const response = await axios.post<ApiResponse>(
                `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/hotel/hotel-content`,
                requestData,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    }
                }
            );

            console.log('Hotel details response:', response.data);

            if (response.status === 200 && response.data?.detail?.status === 'success') {
                const hotelDataFromApi = response.data.detail.data;
                const hotelKey = Object.keys(hotelDataFromApi)[0];

                if (hotelKey && hotelDataFromApi[hotelKey]) {
                    const contentData = hotelDataFromApi[hotelKey];
                    setHotelData(contentData);
                    
                    // Enrich Redux state with content data
                    enrichSelectedHotelWithContent(contentData);
                } else {
                    setError("Hotel data not found");
                }
            } else {
                throw new Error("Failed to fetch hotel details");
            }
        } catch (error: any) {
            console.error('Error fetching hotel details:', error);
            setError(
                error.response?.data?.message ||
                error.message ||
                "Failed to fetch hotel details. Please try again."
            );
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (id && typeof id === 'string') {
            fetchHotelDetails(id);
        }
    }, [id, token]);

    // Loading state
    if (loading) {
        return (
            <main className="font-proxima-nova max-w-7xl mx-auto p-4">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#080236] mx-auto mb-4"></div>
                        <p className="text-gray-600">Loading hotel details...</p>
                    </div>
                </div>
            </main>
        );
    }

    // Error state
    if (error) {
        return (
            <main className="font-proxima-nova max-w-7xl mx-auto p-4">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <p className="text-red-600 mb-4">{error}</p>
                        <div className="space-x-4">
                            <Button
                                variant="outline"
                                onClick={() => router.back()}
                            >
                                Go Back
                            </Button>
                            <Button
                                onClick={() => {
                                    if (id && typeof id === 'string') {
                                        fetchHotelDetails(id);
                                    }
                                }}
                            >
                                Retry
                            </Button>
                        </div>
                    </div>
                </div>
            </main>
        );
    }

    // No hotel data
    if (!hotelData) {
        return (
            <main className="font-proxima-nova max-w-7xl mx-auto p-4">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <p className="text-gray-600 mb-4">Hotel not found</p>
                        <Button
                            variant="link"
                            onClick={() => router.back()}
                        >
                            Go Back
                        </Button>
                    </div>
                </div>
            </main>
        );
    }

    return (
        <main className="font-proxima-nova max-w-7xl mx-auto p-4">
            <HotelDetailsContainer hotelcontent={hotelData} selectedHotel={selectedHotel} />
        </main>
    );
};

export default HotelDetails;