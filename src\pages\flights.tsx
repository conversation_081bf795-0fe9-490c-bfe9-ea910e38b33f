"use client";

import type React from "react";
import { useState, useEffect, useMemo, useRef } from "react";
import { useRouter } from "next/router";
import { useSearchParams } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { parseISO } from "date-fns";
import AuthContainer from "@/components/layout/AuthContainer";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { DateRange } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import {
  PlaneLanding,
  PlaneTakeoff,
  ArrowRight,
  Users,
  ChevronDown,
  MapPinIcon,
  CalendarDaysIcon,
  ArrowLeftIcon,
  UserPlus2Icon,
  UserPlus,
  Check,
  RotateCcw,
  Link,
} from "lucide-react";
import type { Flight, Suggestions } from "@/constants/models";
import { useFlightContext } from "@/context/FlightContext";
import { formatShortLocationDisplay } from "@/lib/utils/formatLocation"; //not added
import { agentPostMethod } from "@/utils/api";
import { generateUUID } from "@/lib/utils/uuid";
import { useLoadingTransition } from "@/hooks/useLoadingTransition";
import LoadingOverlay from "@/components/LoadingOverlay/LoadingOverlay";
import { processFlightSelection } from "@/lib/utils/processFlightSelection";
import {
  parseDurationToMinutes,
  parse24HourTimeToMinutes,
} from "@/lib/utils/layover";
import { validateFlightSearchForm } from "@/lib/utils/formValidation";
import {
  FlightClasses,
  TravellerOptions,
  TripOptions,
  StopsOptions,
  activePages,
} from "@/constants/flight";
import type { AirportData, FlightsProps, InputType } from "@/types/flights";
import type { AppState } from "@/store/store";
import {
  updateTripSummary,
  resetTripSummary,
} from "@/store/slices/tripSummary";
import FlightFilter from "@/components/flightFilter";
import { useCustomSession } from "@/hooks/use-custom-session";
import tracker from "@/utils/posthogTracker";
import { ErrorModal } from "@/components/chat/errorModal/error-modal";
import FlightCard from "@/components/flight/FlightCard";
import DashboardLayout from "@/layout/DashboardLayout";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"

export const TripOptionsIcons = [
  { label: "One Way", icon: ArrowRight },
  { label: "Round Trip", icon: RotateCcw },
  { label: "Multi-Trip", icon: Link },
];

const FlightSearchPage: React.FC<FlightsProps> = () => {
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const router = useRouter();
  const [isSignInClicked, setIsSignInClicked] = useState<boolean>(false);
  const [selectedOutbound, setSelectedOutbound] = useState<number | null>(null);
  const [airlinesOptions, setAirlinesOptions] = useState<string[]>([]);
  const [airportsOptions, setAirportsOptions] = useState<{
    [city: string]: { iata_code: string; airport_name: string }[];
  }>({});
  const [layoverAirportsOptions, setLayoverAirportsOptions] = useState<
    string[]
  >([]);
  const [isLoading, setIsLoading] = useState({
    departure: false,
    destination: false,
  });
  const [suggestions, setSuggestions] = useState({
    departure: [] as Suggestions[],
    destination: [] as Suggestions[],
  });
  const [selectedTrip, setSelectedTrip] = useState<string[]>(["One Way"]);
  const [showCalendar, setShowCalendar] = useState(false);
  const [hasSelectedEndDate, setHasSelectedEndDate] = useState(false);
  const [isFlightLoading, setIsFlightLoading] = useState(true);
  const {
    sharedFlightResults,
    searchedFlightResults,
    selectOutboundFlight,
    selectInboundFlight,
    updateSearchedFlightResults,
    recommendedFlight,
    updateFareOptions,
    updatePassengerList,
    updateLuggageOptions,
    updateSearchFlights,
    updateGlobalPopup,
    updateSupplierInfo,
    updateServiceFee,
  } = useFlightContext();

  const searchParams = useSearchParams();

  const [dateRange, setDateRange] = useState([
    {
      startDate: recommendedFlight?.departure_date
        ? parseISO(recommendedFlight.departure_date)
        : new Date(),
      endDate: recommendedFlight?.departure_date
        ? parseISO(recommendedFlight.departure_date)
        : new Date(),
      key: "selection",
    },
  ]);

  useEffect(() => {
    const flowId = tracker.getFlowId();
    tracker.trackEvent("Step 1 - Page Loaded", {
      page: "Step1",
      flowId,
    });
  }, []);

  const [travelers, setTravelers] = useState({
    adults: 1,
    children: 0,
    infants: 0,
  });
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
  const [selectedStops, setSelectedStops] = useState<string[]>([]);
  const [selectedAirlines, setSelectedAirlines] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<number[]>([0, 100000]);
  const [timeRange, setTimeRange] = useState<number[]>([0, 24]);
  const [outboundTimeRange, setOutboundTimeRange] = useState<number[]>([
    0, 1439,
  ]);
  const [returnTimeRange, setReturnTimeRange] = useState<number[]>([0, 1439]);
  const [selectedAirports, setSelectedAirports] = useState<string[]>([]);
  const [duration, setDuration] = useState<number[]>([420, 2820]); // default: 7h to 47h
  const [selectedLayoverAirports, setSelectedLayoverAirports] = useState<
    string[]
  >([]);
  const [selectAllAirlines, setSelectAllAirlines] = useState(false);
  const [selectAllLayovers, setSelectAllLayovers] = useState(false);

  const [departureLocation, setDepartureLocation] = useState("");
  const [destinationLocation, setDestinationLocation] = useState("");
  const [departureValue, setDepatureValue] = useState("");
  const [destinationValue, setDestinationValue] = useState("");
  const [sameAirportsOnly, setSameAirportsOnly] = useState(false);
  const [activeInput, setActiveInput] = useState<InputType | null>(null);
  const [calendarMonths, setCalendarMonths] = useState(2);
  const { isFetching, runWithLoading } = useLoadingTransition();
  const [searchFormError, setSearchFormError] = useState({
    destination: "",
    origin: "",
    departure_date: "",
  });
  const calendarRef = useRef<HTMLDivElement | null>(null);
  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const chatThread = useSelector((state: AppState) => state.chatThread);
  const { _outward, _return } = tripSummaryDetails?.sharedFlightResults || {};
  const dispatch = useDispatch();

  //Error Handling in ChatScreen
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState(
    "https://storage.googleapis.com/nxvoytrips-img/HomeImgs/ErrorModal/shash-I.png"
  );
  const [currentMessage, setCurrentMessage] = useState(
    "Oops! Something went wrong on my end. Try again in a bit — I’m on it!"
  );

  const styles = {
    button: {
      background:
        "linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)",
      color: "white",
      borderRadius: "100px",
    },
  };

  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);
    return debouncedValue;
  };

  const debouncedDeparture = useDebounce(departureLocation, 300);
  const debouncedDestination = useDebounce(destinationLocation, 300);

  const handleInputChange = (value: string, type: InputType) => {
    setActiveInput(type);
    if (type === "departure") {
      setDepartureLocation(value);
      setDepatureValue(value);
    } else if (type === "destination") {
      setDestinationLocation(value);
      setDestinationValue(value);
    }

    setSearchFormError({
      ...searchFormError,
      [type === "departure" ? "origin" : type]: "",
    });
  };

  const handleSuggestionClick = (suggestion: Suggestions, type: InputType) => {
    const cityCode = suggestion.iata_code || "";
    const cityName = suggestion.city_name_original || "";
    const airportName = suggestion.airport_name || "";
    const countryName = suggestion.country_name || "";
    const displayName = `${cityName}, ${countryName} (${cityCode})`;
    if (type === "departure") {
      setDepartureLocation(displayName);
      setDepatureValue(cityCode);
    } else {
      setDestinationLocation(displayName);
      setDestinationValue(cityCode);
    }
    setSuggestions((prev) => ({ ...prev, [type]: [] }));
    setActiveInput(null);
  };

  const handleSelectOutbound = async (flight: Flight) => {
    if (!selectedTrip.includes("Round Trip")) {
      runWithLoading(async () => {
        selectOutboundFlight(flight);
        dispatch(
          updateTripSummary({
            ...tripSummaryDetails,
            selectedOutboundFlight: flight,
          })
        );
        try {
          const response: any = await processFlightSelection({
            routing_id:
              tripSummaryDetails?.sharedFlightResults?.routing_id ||
              searchedFlightResults?.routing_id,
            outward_id: flight.cabin_classes?.[0]?.outward_id,
            return_id: "",
            updateFareOptions,
            updatePassengerList,
            updateLuggageOptions,
            updateSupplierInfo,
            updateServiceFee,
            router,
            token,
          });
          if (response.status === "failed") {
            setIsErrorModalOpen(true);
            setCurrentMessage(response.message);
            return;
          }
          dispatch(
            updateTripSummary({
              ...tripSummaryDetails,
              passengersList: response.passenger_price_segregation,
              selectedOutboundFlight: flight,
              luggageOptions: response.luggage_options,
              supplierInfo: response.supplier_info,
              activePage: activePages.flight_summary,
              from: activePages.flight_search,
              serviceFee: response.service_fee_percentage,
            })
          );
          await router.push("/flightsummary");
        } catch (error) {
          dispatch(
            updateTripSummary({
              selectedInboundFlight: {},
              selectedOutboundFlight: {},
            })
          );
          updateGlobalPopup({
            isOpen: true,
            message: "Flight processing failed. Please try again",
            type: "error",
          });
        }
      });
    } else {
      dispatch(updateTripSummary({ selectedOutboundFlight: flight }));
      selectOutboundFlight(flight);
    }
  };

  const handleSelectInbound = (flight: Flight) => {
    runWithLoading(async () => {
      selectInboundFlight(flight);
      try {
        const response: any = await processFlightSelection({
          routing_id:
            tripSummaryDetails?.sharedFlightResults?.routing_id ||
            searchedFlightResults?.routing_id,
          outward_id: tripSummaryDetails?.selectedOutboundFlight?.id,
          return_id: flight?.id,
          updateFareOptions,
          updatePassengerList,
          updateLuggageOptions,
          updateServiceFee,
          updateSupplierInfo,
          router,
          token,
        });

        console.log("helloStatusfailed", response);
        if (response.status === "failed") {
          setIsErrorModalOpen(true);
          setCurrentMessage(response.message);
          return;
        }
        dispatch(
          updateTripSummary({
            passengersList: response.passenger_price_segregation,
            selectedInboundFlight: flight,
            luggageOptions: response.luggage_options,
            activePage: activePages.flight_summary,
            from: activePages.flight_search,
            supplierInfo: response.supplier_info,
            serviceFee: response.service_fee_percentage,
          })
        );

        // Use setTimeout to ensure state updates are completed before navigation
        setTimeout(() => {
          router.push("/flightsummary");
        }, 100);
      } catch (error) {
        console.log("error", error);
        dispatch(
          updateTripSummary({
            selectedInboundFlight: {},
            selectedOutboundFlight: {},
          })
        );
        updateGlobalPopup({
          isOpen: true,
          message: "Flight processing failed. Please try again",
          type: "error",
        });
      }
    });
  };

  const handleAuthClose = () => {
    setIsSignInClicked(false);
  };

  const handleSignIn = () => {
    setIsSignInClicked(true);
  };

  const getTotalTravelers = () =>
    travelers.adults + travelers.children + travelers.infants;

  const updateCount = (type: keyof typeof travelers, value: number) => {
    setTravelers((prev) => ({
      ...prev,
      [type]: Math.max(0, prev[type] + value),
    }));
  };

  const handleSelect = (rangesByKey: any) => {
    const { selection } = rangesByKey;

    if (selectedTrip.includes("Round Trip")) {
      setDateRange([
        {
          startDate: selection?.startDate || new Date(),
          endDate: selection?.endDate || new Date(),
          key: "selection",
        },
      ]);
      if (selection?.endDate) setHasSelectedEndDate(true);
    } else {
      // One Way or no trip selected → only startDate allowed
      setDateRange([
        {
          startDate: selection.startDate || new Date(),
          endDate: selection.startDate || new Date(),
          key: "selection",
        },
      ]);
      setHasSelectedEndDate(true);
      setShowCalendar(false); // close calendar on one selection
    }
  };

  const toggleTrip = (trip: string) => {
    setSelectedTrip([trip]);
  };

  const toggleClass = (classType: string) => {
    setSelectedClasses([classType]);
  };

  const toggleSelection = (option: string, state: string[], setState: any) => {
    setState((prev: string[]) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };

  const extractAirportCode = (locationString: string): string => {
    if (!locationString) return "";

    // Return if 3 letters
    if (locationString.length === 3 && /^[A-Z]{3}$/.test(locationString)) {
      return locationString;
    }

    // Extract code from "City, Country (CODE)" format
    const match = locationString.match(/\(([A-Z]{3})\)$/);
    return match ? match[1] : locationString;
  };

  const handleSearch = async () => {
    dispatch(resetTripSummary());
    //Extract codes for API payload only
    const originCode = extractAirportCode(departureValue);
    const destinationCode = extractAirportCode(destinationValue);

    const startDate = dateRange[0]?.startDate ?? new Date();
    const endDate = dateRange[0]?.endDate ?? new Date();

    const payload = {
      origin: recommendedFlight?.origin || originCode,
      destination: recommendedFlight?.destination || destinationCode,
      departure_date: format(startDate, "yyyy-MM-dd"),
      return_date: selectedTrip?.includes("Round Trip")
        ? format(endDate, "yyyy-MM-dd")
        : "",
      adults: travelers?.adults,
      children: travelers?.children,
      infants: travelers?.infants,
      max_changes: selectedStops?.includes("Direct") ? 0 : 1,
      direct_only: selectedStops?.includes("Direct"),
      request_uuid: generateUUID(),
      travel_class: selectedClasses[0] || FlightClasses[0],
    };

    const params = new URLSearchParams(searchParams);
    params.set("departureValue", departureValue);
    params.set("destinationValue", destinationValue);
    router.replace(`?${params.toString()}`);

    const validateSearchForm = validateFlightSearchForm(payload);
    if (Object.keys(validateSearchForm).length) {
      setSearchFormError({
        destination: validateSearchForm.destination || "",
        origin: validateSearchForm.origin || "",
        departure_date: validateSearchForm.departure_date || "",
      });
      return;
    }

    setSearchFormError({ destination: "", origin: "", departure_date: "" });
    runWithLoading(async () => {
      try {
        const searchPayload = {
          ...payload,
          travel_class: selectedClasses[0] || FlightClasses[0],
          total_count: payload.adults + payload.children + payload.infants,
          trip_type: selectedTrip?.includes("Round Trip")
            ? "Round Trip"
            : "One Way",
        };
        updateSearchFlights(searchPayload);

        const response = await agentPostMethod(
          "flight/search",
          payload,
          token ?? ""
        );
        if (response?.detail?.status === "success") {
          updateSearchedFlightResults(response?.detail?.data);
          const flightdata = response?.detail?.data;

          // Set airlines options
          setAirlinesOptions(flightdata?._outward?.airlines || []);

          // Set airports options (source + destination)
          const airportData = flightdata?.airport_data as AirportData;

          const airportCities: {
            [city: string]: { iata_code: string; airport_name: string }[];
          } = {};
          Object.values(airportData).forEach(
            ({ iata_code, city_name_original, airport_name }) => {
              if (!airportCities[city_name_original]) {
                airportCities[city_name_original] = [];
              }
              airportCities[city_name_original].push({
                iata_code,
                airport_name,
              });
            }
          );
          setAirportsOptions(airportCities);
          selectOutboundFlight(null);
          selectInboundFlight(null);
          // Set layover airports options
          setLayoverAirportsOptions(
            flightdata?._outward?.layover_airports || []
          );
          dispatch(
            updateTripSummary({
              flightSearch: {
                ...searchPayload,
                departureLocation,
                departureValue: originCode,
                destinationLocation,
                destinationValue: destinationCode,
                dateRange,
              },
              flightSearchRespnse: flightdata,
              sharedFlightResults: flightdata,
              selectedInboundFlight: {},
              selectedOutboundFlight: {},
            })
          );
          if (!selectedClasses.length)
            setSelectedClasses([payload.travel_class]);
        } else {
          updateGlobalPopup({
            isOpen: true,
            message: "Flight search failed. Please try again.",
            type: "error",
          });
        }
      } catch (error) {
        console.error("Search API Error:", error);
        updateGlobalPopup({
          isOpen: true,
          message: "Flight search failed. Please try again.",
          type: "error",
        });
      }
    });
  };

  const applyFilters = (
    flights: Flight[],
    direction: "outbound" | "return"
  ) => {
    return flights.filter((flight, i) => {
      //Filter by Stops
      if (tripSummaryDetails?.selectedFilters?.stops.length) {
        const flightStopCount = flight.segments?.length || 0;
        const stopLabel =
          StopsOptions[
          flightStopCount > 0 ? flightStopCount - 1 : flightStopCount
          ];
        if (!tripSummaryDetails?.selectedFilters?.stops.includes(stopLabel)) {
          return false;
        }
      }

      // ✅ Filter by airline
      if (
        tripSummaryDetails?.selectedFilters?.airline.length &&
        !tripSummaryDetails?.selectedFilters?.airline.includes(
          flight.airline.toLowerCase()
        )
      ) {
        return false;
      }

      // ✅ Filter by price
      const price = flight.price?.amount || 0;
      if (
        price < tripSummaryDetails?.selectedFilters?.priceRange[0] ||
        price > tripSummaryDetails?.selectedFilters?.priceRange[1]
      ) {
        return false;
      }

      // ✅ Filter by departure time (assumes flight.departure_time in HH:mm format)
      // const hour = parseInt(flight.departure_time?.split(":")[1] || "0");
      // if (hour < timeRange[0] || hour > timeRange[1]) {
      //   console.log("Filtered by time", hour);
      //   return false;
      // }

      // ✅ Filter by origin or destination airport
      if (
        tripSummaryDetails?.selectedFilters?.airports.length &&
        !tripSummaryDetails?.selectedFilters?.airports.includes(
          flight.origin
        ) &&
        !tripSummaryDetails?.selectedFilters?.airports.includes(
          flight.destination
        )
      ) {
        return false;
      }

      // Filter by Departure Times
      const depTimes = parse24HourTimeToMinutes(flight.departure_time_24hr);
      const filterKey =
        direction === "outbound"
          ? "departureOutbound"
          : direction === "return"
            ? "departureInbound"
            : null;

      if (
        filterKey &&
        (depTimes < tripSummaryDetails?.selectedFilters?.[filterKey]?.[0] ||
          depTimes > tripSummaryDetails?.selectedFilters?.[filterKey]?.[1])
      ) {
        return false;
      }

      // ✅ Filter by duration
      const durationMinutes = parseDurationToMinutes(flight.duration) || 0;
      // if (durationMinutes < duration[0] || durationMinutes > duration[1]) {
      //   console.log("Filtered by duration", durationMinutes);
      //   return false;
      // }

      const layovers = flight.segments
        .slice(0, -1)
        .map((segment) => segment.destination);

      // ✅ Filter by layover
      if (
        tripSummaryDetails?.selectedFilters?.layoverAirports.length &&
        !layovers.some((airport) =>
          tripSummaryDetails?.selectedFilters?.layoverAirports.includes(airport)
        )
      ) {
        return false;
      }
      return true;
    });
  };

  const filteredFlights = useMemo(() => {
    const baseOutwardFlights = _outward?.flights || [];
    const baseReturnFlights = _return?.flights || [];
    const filteredOutbound = applyFilters(baseOutwardFlights, "outbound");
    const filteredReturn = applyFilters(baseReturnFlights, "return");
    console.log("Caaling", filteredOutbound, filteredReturn, _outward, _return);
    return {
      outbound: filteredOutbound,
      return: filteredReturn,
    };
  }, [
    tripSummaryDetails?.flightSearchRespnse,
    tripSummaryDetails?.selectedFilters,
  ]);

  const resetFilters = () => {
    setSelectedStops([]);
    setSelectedAirlines([]);
    setPriceRange([0, 10000]);
    // setTimeRange([0, 24]);
    setOutboundTimeRange([0, 1440]); // full day in minutes
    setReturnTimeRange([0, 1440]);
    setSelectedAirports([]);
    setDuration([0, 2820]);
    setSelectedLayoverAirports([]);
    setSelectAllAirlines(false);
    setSelectAllLayovers(false);
  };

  const handleBacktoChat = () => {
    dispatch(
      updateTripSummary({
        activePage: activePages.chat,
        from: activePages.flight_search,
      })
    );
    router.push("/chat/" + chatThread?.currentChatPageThreadId);
  };

  async function getFullLocation(locationCode: any) {
    try {
      console.log("getFullLocation======");
      const response = await agentPostMethod(
        "flight/suggest",
        { query: locationCode },
        token ?? ""
      );
      const locations = response?.detail?.data;

      if (!Array.isArray(locations)) return locationCode;

      const match = locations.find(
        (loc) => loc.iata_code === locationCode.toUpperCase()
      );

      if (!match) return "";

      return `${match.city_name_original}, ${match.country_name} (${match.iata_code})`;
    } catch (error) {
      console.error("Error fetching location:", error);
      return "";
    }
  }

  const prefillTheForm = (
    destinationLocation?: string,
    departureLocation?: string
  ) => {
    console.log(
      "prefillTheForm tripSummaryDetails=======",
      tripSummaryDetails?.flightSearch,
      Object.keys(tripSummaryDetails?.flightSearch).length,
      destinationLocation,
      departureLocation
    );

    if (
      tripSummaryDetails?.flightSearch &&
      Object.keys(tripSummaryDetails?.flightSearch).length
    ) {
      setSelectedTrip([tripSummaryDetails?.flightSearch?.trip_type]);
      setTravelers({
        adults: tripSummaryDetails?.flightSearch?.adults || 0,
        children: tripSummaryDetails?.flightSearch?.children || 0,
        infants: tripSummaryDetails?.flightSearch?.infants || 0,
      });
      setDateRange(tripSummaryDetails?.flightSearch?.dateRange);
      setSelectedClasses([tripSummaryDetails?.flightSearch?.travel_class]);
      console.log(
        "set location from stored data =======",
        departureLocation?.length,
        destinationLocation
      );

      //get full location from api
      if (
        destinationLocation &&
        destinationLocation?.length > 3 &&
        departureLocation &&
        departureLocation?.length > 3
      ) {
        console.log(
          "set location from stored data =======",
          departureLocation,
          destinationLocation
        );
        setDepartureLocation(departureLocation);
        setDestinationLocation(destinationLocation);
      } else {
        console.log("get location from api==========");
        getFullLocation(departureLocation)
          .then(setDepartureLocation)
          .catch(console.error);

        getFullLocation(destinationLocation)
          .then(setDestinationLocation)
          .catch(console.error);
      }

      setDepatureValue(tripSummaryDetails?.flightSearch?.departureLocation);
      setDestinationValue(
        tripSummaryDetails?.flightSearch?.destinationLocation
      );
      dispatch(
        updateTripSummary({
          activePage: activePages.flight_search,
          from: "",
          selectedInboundFlight: {},
          selectedOutboundFlight: {},
        })
      );
    }
  };

  useEffect(() => {
    const airportData = tripSummaryDetails?.sharedFlightResults?.airport_data;
    if (recommendedFlight) {
      setDepartureLocation(
        formatShortLocationDisplay(recommendedFlight.origin, airportData)
      );
      setDestinationLocation(
        formatShortLocationDisplay(recommendedFlight.destination, airportData)
      );
    }
  }, [recommendedFlight]);

  useEffect(() => {
    const updateCalendarMonths = () => {
      if (window.innerWidth < 1280) {
        setCalendarMonths(1); // mobile view
      } else {
        setCalendarMonths(2); // tablet and above
      }
    };

    if (
      tripSummaryDetails?.activePage === activePages?.flight_search &&
      tripSummaryDetails?.from === activePages.flight_summary
    ) {
      prefillTheForm();
    }

    function handleClickOutside(event: MouseEvent) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setShowCalendar(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);

    updateCalendarMonths(); // Run initially
    window.addEventListener("resize", updateCalendarMonths); // Recalculate on resize

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", updateCalendarMonths);
    };
  }, []);

  useEffect(() => {
    if (!router.isReady) return;

    if (status !== "loading") {
      const departureValue = router.query.departureValue as string;
      const destinationValue = router.query.destinationValue as string;

      const hasValidQueryParams =
        departureValue?.trim() && destinationValue?.trim();
      console.log("hasValidQueryParams======", hasValidQueryParams);
      if (hasValidQueryParams) {
        setIsFlightLoading(false);
        const { destinationLocation, departureLocation } =
          tripSummaryDetails?.flightSearch;
        // console.log("tripSummaryDetails?======", tripSummaryDetails);
        prefillTheForm(destinationLocation, departureLocation);
      } else {
        if (tripSummaryDetails?.from !== activePages.flight_summary) {
          dispatch(
            updateTripSummary({
              activePage: activePages.chat,
              chat_thread_id: "",
              from: "",
              flightSearch: {},
              flightSearchRespnse: {},
              sharedFlightResults: {},
              selectedInboundFlight: {},
              selectedOutboundFlight: {},
            })
          );
        }
        console.log("hasValidQueryParams else======", hasValidQueryParams);

        setIsFlightLoading(false);
        // Optionally: router.push('/chat');
      }
    }
    /* Removed deps because of it was impacting on redirect to flight summary  */
  }, [status]);

  useEffect(() => {
    const fetchSuggestions = async (query: string, type: InputType) => {
      if (query?.length < 1) {
        setSuggestions((prev) => ({ ...prev, [type]: [] }));
        return;
      }
      setIsLoading((prev) => ({ ...prev, [type]: true }));
      try {
        const response = await agentPostMethod(
          "flight/suggest",
          { query },
          token ?? ""
        );
        setSuggestions((prev) => ({ ...prev, [type]: response.detail?.data }));
      } catch (error) {
        setSuggestions((prev) => ({ ...prev, [type]: [] }));
        updateGlobalPopup({
          isOpen: true,
          message: "Something went wrong. Please try again",
          type: "error",
        });
      } finally {
        setIsLoading((prev) => ({ ...prev, [type]: false }));
      }
    };
    if (activeInput === "departure") {
      fetchSuggestions(debouncedDeparture, "departure");
    } else if (activeInput === "destination") {
      fetchSuggestions(debouncedDestination, "destination");
    }
  }, [debouncedDeparture, debouncedDestination, activeInput]);

  return (
    <DashboardLayout>
      {isFetching || isFlightLoading ? (
        <LoadingOverlay loadingText="Just a moment! Sasha’s lining up the best fares for your journey." />
      ) : (
        <>
          <div className={`w-full h-screen`}>
            <div className="flex flex-col">
              <div className="flex pb-[200px]">
                <div className="md:mr-4 w-full">
                  <div className="flex flex-col gap-5 pb-10 mx-auto">
                    <div className="flex md:flex-row flex-col justify-between">
                      <div className="flex items-center bg-white rounded-full h-10 p-1 w-fit md:w-auto border border-[#00000014]">
                        {TripOptionsIcons.map((trip, index) => {
                          const IconComponent = trip.icon;
                          const isSelected = selectedTrip.includes(trip.label);
                          return (
                            <button
                              key={index}
                              onClick={() => toggleTrip(trip.label)}
                              className={`
                                flex items-center md:gap-2 px-2 md:px-6 md:py-2 py-1.5 rounded-full md:font-medium text-sm
                                ${isSelected
                                  ? "bg-brand text-white shadow-sm"
                                  : "text-brand-black hover:text-brand-black hover:border-2 hover:border-brand"
                                }
                              `}
                            >
                              <IconComponent className="md:w-4 md:h-4 h-3 w-3 mr-1" />
                              <span>{trip.label}</span>
                            </button>
                          );
                        })}
                      </div>

                      <div className="w-64">
                        {/* Class Type Dropdown */}
                        <Popover>
                          <PopoverTrigger asChild className="mt-4 md:mt-0 h-10">
                            <button className="bg-white border border-gray-200 hover:border-gray-300 text-brand-black font-medium flex items-center justify-between gap-1 rounded-full px-4 md:py-3 py-2 md:min-w-[100px] transition-colors w-full">
                              <span className="md:font-medium text-sm">
                                {selectedClasses.length
                                  ? selectedClasses.join(", ")
                                  : "Economy"}
                              </span>
                              <ChevronDown className="w-5 h-5 text-brand-black" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent className="rounded-2xl shadow-lg bg-white border border-gray-200 p-4 font-medium">
                            <div className="space-y-1">
                              {FlightClasses.map((classType, index) => (
                                <div
                                  key={classType}
                                  className={`
                                flex items-center justify-between p-2 rounded-xl cursor-pointer transition-colors
                                ${selectedClasses.includes(classType)
                                      ? "bg-gray-100 text-brand-black"
                                      : "text-brand-black hover:bg-gray-50"
                                    }
                              `}
                                  onClick={() => toggleClass(classType)}
                                >
                                  <span className="font-medium">{classType}</span>
                                  {selectedClasses.includes(classType) && (
                                    <Check className="w-5 h-5 text-[#4B4BC3]" />
                                  )}
                                </div>
                              ))}
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    <div className="relative w-full h-auto font-proxima-nova">
                      <div className="p-0.5 bg-gradient-to-r from-[#1E1E76] via-[#3838DC] via-[#3131B6] via-[#3838DC] to-[#181882] rounded-2xl">
                        <div className="grid md:grid-cols-2 gap-4 bg-white rounded-t-2xl p-4">
                          <div className="relative">
                            <div className="bg-white border flex gap-2 items-center rounded-xl px-2 xl:py-2 xl:py-2 md:py-1 sm:py-0.5 xs:py-1">
                              <PlaneTakeoff color="#999999" />
                              <input
                                className="w-full text-base xs:text-sm focus:outline-none"
                                value={departureLocation}
                                onChange={(e) =>
                                  handleInputChange(e.target.value, "departure")
                                }
                                placeholder="From Where"
                                onFocus={() => setActiveInput("departure")}
                              />
                            </div>
                            {searchFormError.origin && (
                              <p className="text-red-500 text-xs mt-1">
                                {searchFormError.origin}
                              </p>
                            )}
                            {activeInput === "departure" &&
                              ((suggestions &&
                                suggestions.departure &&
                                suggestions.departure.length > 0) ||
                                isLoading.departure) && (
                                <div className="absolute w-full z-10 mt-1 bg-white text-black rounded-md shadow-lg max-h-60 overflow-auto">
                                  {isLoading.departure ? (
                                    <div className="px-4 py-2 text-gray-500">
                                      Loading suggestions...
                                    </div>
                                  ) : (
                                    suggestions.departure.map(
                                      (suggestion, index) => (
                                        <div
                                          key={`departure-${suggestion.city_name_original}-${index}`}
                                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                          onClick={() =>
                                            handleSuggestionClick(
                                              suggestion,
                                              "departure"
                                            )
                                          }
                                        >
                                          {suggestion.city_name_original},{" "}
                                          {suggestion.country_name} (
                                          {suggestion.iata_code})
                                        </div>
                                      )
                                    )
                                  )}
                                </div>
                              )}
                          </div>
                          <div className="relative">
                            <div className="bg-white flex gap-2 px-2 2xl:py-2 xl:py-2 md:py-1 sm:py-0.5 xs:py-1 border rounded-xl relative">
                              <PlaneLanding color="#999999" />
                              <input
                                className="w-full text-base xs:text-sm focus:outline-none"
                                value={destinationLocation}
                                onChange={(e) =>
                                  handleInputChange(e.target.value, "destination")
                                }
                                placeholder="To Where"
                                onFocus={() => setActiveInput("destination")}
                              />
                            </div>
                            {searchFormError.destination && (
                              <p className="text-red-500 text-xs mt-1">
                                {searchFormError.destination}
                              </p>
                            )}
                            {activeInput === "destination" &&
                              ((suggestions &&
                                suggestions.destination &&
                                suggestions.destination.length > 0) ||
                                isLoading.destination) && (
                                <div className="absolute z-10 mt-1 w-full bg-white text-black rounded-md shadow-lg max-h-60 overflow-auto">
                                  {isLoading.destination ? (
                                    <div className="px-4 py-2 text-gray-500">
                                      Loading suggestions...
                                    </div>
                                  ) : (
                                    suggestions.destination.map(
                                      (suggestion, index) => (
                                        <div
                                          key={`destination-${suggestion.city_name_original}-${index}`}
                                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                          onClick={() =>
                                            handleSuggestionClick(
                                              suggestion,
                                              "destination"
                                            )
                                          }
                                        >
                                          {suggestion.city_name_original},{" "}
                                          {suggestion.country_name} (
                                          {suggestion.iata_code})
                                        </div>
                                      )
                                    )
                                  )}
                                </div>
                              )}
                          </div>
                          <div className="relative bg-white flex justify-start gap-2 2xl:p-2 xl:p-2 md:py-1 sm:p-0.5 xs:py-1 xs:px-4 border rounded-xl">
                            {/* <Calendar/> */}
                            <CalendarDaysIcon
                              className="xs:w-6 h-6"
                              color="#999999"
                            />
                            <input
                              type="text"
                              className="w-full text-base xs:text-sm focus:outline-none cursor-pointer"
                              value={
                                selectedTrip.includes("Round Trip")
                                  ? `${format(dateRange[0].startDate, "EEE MMM dd")} - ${format(
                                    dateRange[0].endDate,
                                    "EEE MMM dd"
                                  )}`
                                  : `${format(dateRange[0].startDate, "EEE MMM dd")}`
                              }
                              onClick={() => {
                                setShowCalendar(!showCalendar);
                                setHasSelectedEndDate(false); // Reset on new selection
                              }}
                              readOnly
                            />
                            {searchFormError.departure_date && (
                              <p className="text-red-500 text-xs mt-1">
                                {searchFormError.departure_date}
                              </p>
                            )}
                            {/* Date Range Picker */}
                            {showCalendar && (
                              <div
                                className="absolute top-full rounded-xl z-10 mt-2 border border-gray-200"
                                ref={calendarRef}
                              >
                                <DateRange
                                  className="rounded-xl font-proxima-nova text-xl"
                                  editableDateInputs={true}
                                  onChange={handleSelect}
                                  moveRangeOnFirstSelection={false}
                                  showDateDisplay={false}
                                  months={calendarMonths}
                                  direction="horizontal"
                                  retainEndDateOnFirstSelection={
                                    !selectedTrip.includes("Round Trip")
                                  }
                                  minDate={new Date()}
                                  ranges={dateRange}
                                  rangeColors={["#4B4BC3"]}
                                  onRangeFocusChange={(
                                    focusedRange: number[]
                                  ) => {
                                    // For One Way or None: close after 1st pick
                                    if (
                                      !selectedTrip.includes("Round Trip") ||
                                      (hasSelectedEndDate &&
                                        focusedRange[0] === 0)
                                    ) {
                                      setShowCalendar(false);
                                    }
                                  }}
                                />
                              </div>
                            )}
                          </div>
                          <div>
                            <Popover>
                              <PopoverTrigger asChild>
                                <button className="bg-white border w-full font-proxima-nova flex items-center gap-2 p-2 rounded-xl">
                                  <UserPlus
                                    color="#999999"
                                    className="w-4 xs:w-3 xs:h-3 h-4"
                                  />
                                  <span>
                                    {getTotalTravelers() +
                                      " " +
                                      (getTotalTravelers() > 1
                                        ? "Travelers"
                                        : "Traveler")}
                                  </span>
                                </button>
                              </PopoverTrigger>

                              {/* ✅ Popover Content */}
                              <PopoverContent className="w-80 xs:w-72 p-4 xs:p-2 bg-white font-proxima-nova rounded-xl shadow-lg">
                                <h3 className="flex items-center text-[#1E1E76] justify-center gap-2 text-lg xs:text-sm font-semibold">
                                  <UserPlus2Icon
                                    fill="#1E1E76"
                                    color="#1E1E76"
                                    className="w-5 xs:w-4 xs:h-4 h-5"
                                  />
                                  Travelers
                                </h3>
                                <p className="text-xs text-center justify-center text-[#A195F9]">
                                  {getTotalTravelers()} Travelers
                                </p>
                                <hr className="my-2" />

                                {/* ✅ Counter for each traveler type */}
                                {TravellerOptions.map(({ label, desc, key }) => (
                                  <div
                                    key={key}
                                    className="flex justify-between items-center py-2"
                                  >
                                    <div>
                                      <p className="font-semibold text-[#080236]">
                                        {label}
                                      </p>
                                      <p className="text-xs text-[#A195F9]">
                                        {desc}
                                      </p>
                                    </div>
                                    <div
                                      style={styles.button}
                                      className="border p-[0.75px] rounded-full"
                                    >
                                      <div className="flex bg-white rounded-full items-center gap-2">
                                        <Button
                                          variant={null}
                                          className="w-8 xs:w-6 xs:h-6 h-8 text-[#080236] p-0 flex items-center justify-center"
                                          onClick={() =>
                                            updateCount(
                                              key as keyof typeof travelers,
                                              -1
                                            )
                                          }
                                          disabled={
                                            travelers[
                                            key as keyof typeof travelers
                                            ] === 0
                                          }
                                        >
                                          −
                                        </Button>
                                        <span className="w-6 xs:w-4 text-[#080236] text-center">
                                          {
                                            travelers[
                                            key as keyof typeof travelers
                                            ]
                                          }
                                        </span>
                                        <Button
                                          variant={null}
                                          className="w-8 xs:w-6 xs:h-6 h-8 text-[#080236] p-0 flex items-center justify-center"
                                          onClick={() =>
                                            updateCount(
                                              key as keyof typeof travelers,
                                              1
                                            )
                                          }
                                        >
                                          +
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                ))}

                                {/* ✅ Update Button
                                    <Button className="w-full mt-4 bg-blue-500 text-white rounded-lg" onClick={() => document.body.click()}>
                                    Update
                                </Button> */}
                              </PopoverContent>
                            </Popover>
                          </div>
                        </div>
                        <div className="">
                          <div className="bg-[#F2F2FF] rounded-b-2xl p-4">
                            <div className="flex gap-4 justify-end">
                              <button
                                onClick={() => router.push("/chat")}
                                className="text-sm md:text-base text-brand py-2 px-4 rounded-[8px] border-2 border-brand"
                              >
                                Back to Chat
                              </button>
                              <button
                                onClick={handleSearch}
                                className="text-sm md:text-base text-white bg-brand py-2 px-4 rounded-[8px]"
                              >
                                Search Flights
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {tripSummaryDetails?.selectedOutboundFlight &&
                      Object.keys(tripSummaryDetails?.selectedOutboundFlight)
                        .length === 0 && (
                        <>
                          <div className="flex justify-between items-center mb-4">

                            <div className="md:text-xl font-bold text-[#1E1E76] text-center">
                              Best Outbound Flights
                            </div>

                            <div>
                              <div className="lg:hidden xs:visible sm:visible">
                                <div className="lg:hidden xs:visible sm:visible">
                                  <Sheet>
                                    <SheetTrigger asChild>
                                      <button className="bg-white border border-gray-200 hover:border-gray-300 text-brand-black font-medium flex items-center justify-between gap-1 rounded-full px-4 md:py-3 py-1 md:min-w-[100px] transition-colors w-full">
                                        Filters
                                        <ChevronDown className="w-4 xs:w-3 xs:h-3 h-4" />
                                      </button>
                                    </SheetTrigger>
                                    <SheetContent side="bottom" className="h-[80vh] px-0 mx-0">
                                      <SheetHeader>
                                        <SheetTitle className="text-lg xs:text-base text-center font-proxima-nova text-[#1E1E76] font-semibold mb-3 xs:mb-1">Filters</SheetTitle>
                                      </SheetHeader>
                                      <div className="mt-4 border border-t pt-2">
                                        <FlightFilter mobile={true} />
                                      </div>
                                    </SheetContent>
                                  </Sheet>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            {filteredFlights.outbound.length > 0 ? (
                              filteredFlights.outbound.map((flight, i) => (
                                //<Card key={`filtered-${i}`} flight={flight} onSelect={() => handleSelectOutbound(flight)} value={i} />
                                <FlightCard
                                  flightType="OUT-BOUND"
                                  key={`filtered-${i}`}
                                  onCardSelect={() =>
                                    handleSelectOutbound(flight)
                                  }
                                  showDetails={i === 0}
                                  flight={flight}
                                  airport={
                                    tripSummaryDetails?.flightSearchRespnse
                                      ?.airport_data
                                  }
                                  class_type = {tripSummaryDetails?.flightSearch?.travel_class}
                                />
                              ))
                            ) : (
                              <div className="text-center w-full text-xl">
                                No Results to Display
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    {tripSummaryDetails?.selectedOutboundFlight !== null &&
                      Object.keys(tripSummaryDetails?.selectedOutboundFlight)
                        .length > 0 && (
                        <>
                          <button
                            className="text-sm text-[#707FF5] underline ml-2 hover:text-blue-800 transition"
                            onClick={() => selectOutboundFlight(null)}
                          >
                            ← Back to Outbound Flights
                          </button>
                          <div className="text-xl font-bold text-[#1E1E76] text-center mb-4">
                            Best Inbound Flights
                          </div>
                          <div className="space-y-4">
                            {filteredFlights.return.length > 0 ? (
                              filteredFlights.return.map((flight, i) => (
                                // <Card key={`filtered-${i}`} flight={flight} onSelect={() => handleSelectInbound(flight)} value={i} />
                                <FlightCard
                                  flightType="INBOUND"
                                  key={`filtered-${i}`}
                                  onCardSelect={() => handleSelectInbound(flight)}
                                  showDetails={i === 0}
                                  flight={flight}
                                  airport={
                                    tripSummaryDetails?.flightSearchRespnse
                                      ?.airport_data
                                  }
                                />
                              ))
                            ) : (
                              <div className="text-center w-full text-xl">
                                No Results to Display
                              </div>
                            )}
                          </div>
                        </>
                      )}
                  </div>
                </div>
                <div className="top-4 self-start hidden md:block w-[35%]">
                  <FlightFilter desktop={true} />
                </div>
              </div>
            </div>
          </div>
          {isSignInClicked && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
              <div className="rounded-lg p-4">
                <AuthContainer onCloseAuth={handleAuthClose} />
              </div>
            </div>
          )}
          <ErrorModal
            isOpen={isErrorModalOpen}
            onClose={() => {
              setIsErrorModalOpen(false);
            }}
            errorMessage={currentMessage}
            errorImage={currentImage}
          />
        </>
      )}
    </DashboardLayout>
  );
};

export default FlightSearchPage;
