import { loadStripe } from "@stripe/stripe-js"
import { Elements } from "@stripe/react-stripe-js"
import TripSummaryContent from "../components/tripSummary/trip-summary-content"

interface Passenger {
  title: string
  firstName: string
  middleName?: string
  lastName: string
  dob: string
  gender: "Male" | "Female" | "Other"
  // passportNumber: string;
  // passportCountry: string;
  specialService?: string
}

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? "")

const TripSummary = () => {
  return (
    <div className="h-screen">
      <Elements stripe={stripePromise}>
        <TripSummaryContent />
      </Elements>
    </div>
  )
}

export default TripSummary
