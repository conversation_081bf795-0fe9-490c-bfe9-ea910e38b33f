
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Flight, Airport } from "@/constants/models";

interface FlightResponseData {
    airport_data: { [iataCode: string]: Airport };
    routing_id: string;
    _outward: { flights: Flight[] };
    _return?: { flights: Flight[] };
}

interface FlightResponseState {
    // API response data
    airport_data: { [iataCode: string]: Airport };
    routing_id: string;

    // Extracted flight arrays for easy access
    outbound_flights: Flight[];
    return_flights: Flight[];

    // Loading and error states
    loading: boolean;
    error: string | null;

    // Search metadata
    search_timestamp: string | null;
    total_results: number;

    // Original data for filter reset
    original_outbound_flights: Flight[];
    original_return_flights: Flight[];
}

const initialState: FlightResponseState = {
    airport_data: {},
    routing_id: "",
    outbound_flights: [],
    return_flights: [],
    loading: false,
    error: null,
    search_timestamp: null,
    total_results: 0,
    original_outbound_flights: [],
    original_return_flights: []
};

const flightResponseSlice = createSlice({
    name: 'flightResponse',
    initialState,
    reducers: {
        // Set loading state
        setLoading: (state, action: PayloadAction<boolean>) => {
            state.loading = action.payload;
            if (action.payload) {
                state.error = null;
            }
        },

        // Set error state
        setError: (state, action: PayloadAction<string>) => {
            state.error = action.payload;
            state.loading = false;
        },

        // Set complete flight response data from API
        setFlightResponseData: (state, action: PayloadAction<FlightResponseData>) => {
            console.log("action:", action);

            state.airport_data = action.payload.airport_data || {};
            state.routing_id = action.payload.routing_id;

            // Extract flights from nested structure
            state.outbound_flights = action.payload._outward?.flights || [];
            state.return_flights = action.payload._return?.flights || [];

            // Store original data for filter reset
            state.original_outbound_flights = action.payload._outward?.flights || [];
            state.original_return_flights = action.payload._return?.flights || [];

            // Set metadata
            state.search_timestamp = new Date().toISOString();
            state.total_results = state.outbound_flights.length + state.return_flights.length;
            state.loading = false;
            state.error = null;
        },

        // Update individual flight arrays (for filtering/sorting)
        updateFlightArrays: (state, action: PayloadAction<{
            outbound_flights?: Flight[],
            return_flights?: Flight[]
        }>) => {
            if (action.payload.outbound_flights !== undefined) {
                state.outbound_flights = action.payload.outbound_flights;
            }
            if (action.payload.return_flights !== undefined) {
                state.return_flights = action.payload.return_flights;
            }
        },

        // Update airport data (merge with existing)
        updateAirportData: (state, action: PayloadAction<{ [iataCode: string]: Airport }>) => {
            state.airport_data = { ...state.airport_data, ...action.payload };
        },

        // Update routing information
        updateRoutingId: (state, action: PayloadAction<string>) => {
            state.routing_id = action.payload;
        },

        // Add flights to existing arrays (for pagination/load more)
        addFlights: (state, action: PayloadAction<{
            outbound_flights?: Flight[],
            return_flights?: Flight[]
        }>) => {
            if (action.payload.outbound_flights) {
                state.outbound_flights.push(...action.payload.outbound_flights);
                state.original_outbound_flights.push(...action.payload.outbound_flights);
            }
            if (action.payload.return_flights) {
                state.return_flights.push(...action.payload.return_flights);
                state.original_return_flights.push(...action.payload.return_flights);
            }

            // Update total count
            state.total_results = state.outbound_flights.length + state.return_flights.length;
        },

        // Sort flights by criteria
        sortFlights: (state, action: PayloadAction<{
            type: 'outbound' | 'return',
            sortBy: 'price' | 'duration' | 'departure_time' | 'arrival_time',
            order: 'asc' | 'desc'
        }>) => {
            const { type, sortBy, order } = action.payload;
            const flights = type === 'outbound' ? state.outbound_flights : state.return_flights;

            const sortedFlights = [...flights].sort((a, b) => {
                let comparison = 0;

                switch (sortBy) {
                    case 'price':
                        comparison = (a.price?.amount || 0) - (b.price?.amount || 0);
                        break;
                    case 'duration':
                        // Convert duration to minutes for comparison
                        const getDurationMinutes = (duration: string) => {
                            const segments = duration.match(/(\d+)h/);
                            const minutes = duration.match(/(\d+)m/);
                            return (segments ? parseInt(segments[1]) * 60 : 0) + (minutes ? parseInt(minutes[1]) : 0);
                        };
                        comparison = getDurationMinutes(a.duration || '') - getDurationMinutes(b.duration || '');
                        break;
                    case 'departure_time':
                        comparison = (a.departure_time_24hr || '').localeCompare(b.departure_time_24hr || '');
                        break;
                    case 'arrival_time':
                        comparison = (a.arrival_time_24hr || '').localeCompare(b.arrival_time_24hr || '');
                        break;
                    default:
                        return 0;
                }

                return order === 'desc' ? -comparison : comparison;
            });

            // Update the appropriate flight array
            if (type === 'outbound') {
                state.outbound_flights = sortedFlights;
            } else {
                state.return_flights = sortedFlights;
            }
        },

        // Filter flights by criteria
        filterFlights: (state, action: PayloadAction<{
            type: 'outbound' | 'return',
            filters: {
                maxPrice?: number;
                airlines?: string[];
                maxStops?: number;
                departureTimeRange?: { start: string; end: string };
                minDuration?: number;
                maxDuration?: number;
            }
        }>) => {
            const { type, filters } = action.payload;
            const originalFlights = type === 'outbound' ?
                state.original_outbound_flights :
                state.original_return_flights;

            const filteredFlights = originalFlights.filter(flight => {
                // Price filter
                if (filters.maxPrice && flight.price?.amount > filters.maxPrice) {
                    return false;
                }

                // Airlines filter
                if (filters.airlines && filters.airlines.length > 0 &&
                    !filters.airlines.includes(flight.airline_code)) {
                    return false;
                }

                // Stops filter (segments - 1 = stops)
                if (filters.maxStops !== undefined &&
                    (flight.segments.length - 1) > filters.maxStops) {
                    return false;
                }

                // Departure time filter
                if (filters.departureTimeRange &&
                    (flight.departure_time_24hr < filters.departureTimeRange.start ||
                        flight.departure_time_24hr > filters.departureTimeRange.end)) {
                    return false;
                }

                // Duration filters (convert duration string to minutes)
                if (filters.minDuration || filters.maxDuration) {
                    const getDurationMinutes = (duration: string) => {
                        const segments = duration.match(/(\d+)h/);
                        const minutes = duration.match(/(\d+)m/);
                        return (segments ? parseInt(segments[1]) * 60 : 0) + (minutes ? parseInt(minutes[1]) : 0);
                    };

                    const durationMinutes = getDurationMinutes(flight.duration || '');

                    if (filters.minDuration && durationMinutes < filters.minDuration) {
                        return false;
                    }
                    if (filters.maxDuration && durationMinutes > filters.maxDuration) {
                        return false;
                    }
                }

                return true;
            });

            // Update the appropriate flight array
            if (type === 'outbound') {
                state.outbound_flights = filteredFlights;
            } else {
                state.return_flights = filteredFlights;
            }
        },

        // Reset filters (restore original flights)
        resetFilters: (state, action: PayloadAction<{ type?: 'outbound' | 'return' | 'both' }>) => {
            const resetType = action.payload.type || 'both';

            if (resetType === 'outbound' || resetType === 'both') {
                state.outbound_flights = [...state.original_outbound_flights];
            }

            if (resetType === 'return' || resetType === 'both') {
                state.return_flights = [...state.original_return_flights];
            }
        },

        // Update partial response data
        updateFlightResponse: (state, action: PayloadAction<Partial<FlightResponseData>>) => {
            if (action.payload.airport_data) {
                state.airport_data = { ...state.airport_data, ...action.payload.airport_data };
            }
            if (action.payload.routing_id) {
                state.routing_id = action.payload.routing_id;
            }
            if (action.payload._outward?.flights) {
                state.outbound_flights = action.payload._outward.flights;
                state.original_outbound_flights = action.payload._outward.flights;
            }
            if (action.payload._return?.flights) {
                state.return_flights = action.payload._return.flights;
                state.original_return_flights = action.payload._return.flights;
            }
        },

        // Clear all flight data
        clearFlightResponse: () => {
            return initialState;
        },

        // Reset to initial state
        resetFlightResponse: () => {
            return initialState;
        }
    },
});

// Export actions
export const {
    setLoading,
    setError,
    setFlightResponseData,
    updateFlightArrays,
    updateAirportData,
    updateRoutingId,
    addFlights,
    sortFlights,
    filterFlights,
    resetFilters,
    updateFlightResponse,
    clearFlightResponse,
    resetFlightResponse
} = flightResponseSlice.actions;

// Export reducer
export default flightResponseSlice.reducer;

// Selectors for easy access to state
export const selectFlightResponseState = (state: { flightResponse: FlightResponseState }) => state.flightResponse;
export const selectFlightResponseData = (state: { flightResponse: FlightResponseState }): FlightResponseData => ({
    airport_data: state.flightResponse.airport_data,
    routing_id: state.flightResponse.routing_id,
    _outward: { flights: state.flightResponse.outbound_flights },
    _return: { flights: state.flightResponse.return_flights }
});
export const selectAirportData = (state: { flightResponse: FlightResponseState }) => state.flightResponse.airport_data;
export const selectRoutingId = (state: { flightResponse: FlightResponseState }) => state.flightResponse.routing_id;
export const selectOutboundFlights = (state: { flightResponse: FlightResponseState }) => state.flightResponse.outbound_flights;
export const selectInboundFlights = (state: { flightResponse: FlightResponseState }) => state.flightResponse.return_flights;
export const selectReturnFlights = (state: { flightResponse: FlightResponseState }) => state.flightResponse.return_flights;
export const selectOriginalOutboundFlights = (state: { flightResponse: FlightResponseState }) => state.flightResponse.original_outbound_flights;
export const selectOriginalReturnFlights = (state: { flightResponse: FlightResponseState }) => state.flightResponse.original_return_flights;
export const selectAllFlights = (state: { flightResponse: FlightResponseState }) => ({
    outbound: state.flightResponse.outbound_flights,
    return: state.flightResponse.return_flights
});
export const selectFlightCount = (state: { flightResponse: FlightResponseState }) => ({
    outbound: state.flightResponse.outbound_flights.length,
    return: state.flightResponse.return_flights.length,
    total: state.flightResponse.total_results
});
export const selectLoadingState = (state: { flightResponse: FlightResponseState }) => state.flightResponse.loading;
export const selectErrorState = (state: { flightResponse: FlightResponseState }) => state.flightResponse.error;
export const selectSearchTimestamp = (state: { flightResponse: FlightResponseState }) => state.flightResponse.search_timestamp;
export const selectAirportByCode = (iataCode: string) => (state: { flightResponse: FlightResponseState }) =>
    state.flightResponse.airport_data[iataCode];

// Derived selectors
export const selectHasFlightData = (state: { flightResponse: FlightResponseState }) =>
    state.flightResponse.outbound_flights.length > 0 || state.flightResponse.return_flights.length > 0;

export const selectIsRoundTrip = (state: { flightResponse: FlightResponseState }) =>
    state.flightResponse.return_flights.length > 0;

export const selectFlightsByAirline = (state: { flightResponse: FlightResponseState }) => {
    const airlines: { [code: string]: { outbound: Flight[], return: Flight[] } } = {};

    state.flightResponse.outbound_flights.forEach(flight => {
        if (!airlines[flight.airline_code]) {
            airlines[flight.airline_code] = { outbound: [], return: [] };
        }
        airlines[flight.airline_code].outbound.push(flight);
    });

    state.flightResponse.return_flights.forEach(flight => {
        if (!airlines[flight.airline_code]) {
            airlines[flight.airline_code] = { outbound: [], return: [] };
        }
        airlines[flight.airline_code].return.push(flight);
    });

    return airlines;
};

export const selectPriceRange = (state: { flightResponse: FlightResponseState }) => {
    const allFlights = [...state.flightResponse.outbound_flights, ...state.flightResponse.return_flights];
    const prices = allFlights.map(flight => flight.price?.amount || 0).filter(price => price > 0);

    return {
        min: prices.length > 0 ? Math.min(...prices) : 0,
        max: prices.length > 0 ? Math.max(...prices) : 0,
        average: prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0
    };
};