import React from "react";
import { SquareArrowLeft } from "lucide-react";
import DashboardNavbar from "./DashboardNavbar";
import Link from "next/link";

interface DashboardLayoutProps {
    children: React.ReactNode;
    sectionChangeHandler?: (section: string) => void;
}

export default function DashboardLayout({ children, sectionChangeHandler }: DashboardLayoutProps) {
    return (
        <div className="w-full font-proxima-nova min-h-screen overflow-y-auto bg-[#F2F3FA] pb-10">
            {/* Header */}
            <div className="w-full mx-auto flex flex-col justify-center">
                <div className="w-[85%] mx-auto">
                    <DashboardNavbar sectionChangeHandler={sectionChangeHandler} />
                </div>
            </div>
            {/* Top Section */}
            <div className="w-[95%] mx-auto my-8">
                <div className="flex justify-between items-center mx-8">
                    <Link href="/">
                        <button className="hidden bg-[#E6E3FF] px-4 py-2 rounded-full text-md shadow-sm md:flex flex-row space-x-4">
                            <SquareArrowLeft />
                            <span>Back</span>
                        </button>
                    </Link>
                </div>
            </div>
            <div className="flex flex-col">
                <div className="w-[95%] mx-auto overflow-hidden">
                    {/*sidebar and content*/}
                    {children}
                </div>
            </div>
        </div>
    )
}