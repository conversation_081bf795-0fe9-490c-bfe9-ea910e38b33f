import { useEffect, useState } from "react";
import axios from "axios";
import { useSearchParams } from "next/navigation";
import { format } from "date-fns";
import Image from "next/image";
import {
  SquarePen,
  ChevronUp,
  ChevronDown,
  Ellipsis,
  Pencil,
  Trash2,
  <PERSON><PERSON>ine,
  Loader,
} from "lucide-react";
import { useRouter } from "next/router";
import { useCustomSession } from "@/hooks/use-custom-session"
import { useSelector, useDispatch } from "react-redux";
import { toast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";

import { useChatContext } from "@/context/ChatContext";
import { AppState } from "@/store/store";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { NEW_THREAD_DEFAULT_NAME } from "@/constants/chat";
import { getInitials } from "@/screens/dashboard/DashboardNavbar";

type Thread = {
  thread_id: string;
  thread_name: string;
  updated_at: string;
  created_at: string;
  message_count: number;
};

type GroupedThreads = Record<string, Thread[]>;

export const ChatSidebar = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const currentUser = useSelector((state: AppState) => state.loggedInUser);
  const { currentUserDetails } = useSelector(
    (state: AppState) => state.userDetails
  );

  const [chatOpen, setChatOpen] = useState(true);
  const [threads, setThreads] = useState<GroupedThreads>({}); // Using local state like your original
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [threadToDelete, setThreadToDelete] = useState<Thread | null>(null);
  const [threadToEdit, setThreadToEdit] = useState<Thread | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const [threadsLoading, setThreadsLoading] = useState(true);

  const searchParams = useSearchParams();

  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
  const { currentChatPageThreadId } = chatThreadDetails;

  const {
    setNewChatThreadId,
    refreshChatHistory,
    setRefreshChatHistory,
    setCurrentThreadTitle,
    setLoginModal,
  } = useChatContext();

  // Load chat history on authentication
  useEffect(() => {
    if (status === "authenticated" && session?.user?.email) {
      console.log("Loading chat history on auth...");
      loadChatHistory();
    }
  }, [status, session]);

  // Refresh chat history when flag is set
  useEffect(() => {
    if (refreshChatHistory) {
      console.log("Refreshing chat history...");
      loadChatHistory();
      setRefreshChatHistory(false);
    }
  }, [refreshChatHistory]);

  const loadChatHistory = async () => {
    try {
      console.log("Loading chat history...");
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/list`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      console.log("Chat history response:", response.data);

      if (response.status !== 200) {
        throw new Error("Failed to fetch user chat history");
      }

      const data: Thread[] = response.data.detail.data.data;
      console.log("Chat history data:", data);

      const grouped: GroupedThreads = data.reduce(
        (acc: GroupedThreads, thread: Thread) => {
          const month = format(new Date(thread.created_at), "MMMM");
          if (!acc[month]) acc[month] = [];
          acc[month].push(thread);
          return acc;
        },
        {}
      );

      console.log("Grouped chat history:", grouped);
      setThreads(grouped); // Using local state
      setThreadsLoading(false);
    } catch (error) {
      console.error("Chat history error:", error);
    }
  };

  const handleNewChat = async () => {
    if (isCreating) return;
    setIsCreating(true);

    try {

      if (!token) {
        setLoginModal(true);
        return;
      }

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/thread/generate?_new=true`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to generate thread");
      }

      console.log("new thread Id created=======", response);
      const newThreadId = response.data.detail.data.thread_id;

      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: newThreadId,
          currentThreadName: NEW_THREAD_DEFAULT_NAME,
          history: false,
          newThread: true,
          showMoreFlightsOption: false,
          chatResult: {},
          allFlights: {},
          tripType: "",
        })
      );

      setNewChatThreadId(newThreadId);
      setCurrentThreadTitle("New Chat");

      // Navigate to the new chat thread
      router.push(`/chat/${newThreadId}`);
    } catch (err: any) {
      console.error("Failed to start new chat:", err);
      if (axios.isAxiosError(err) && err.response?.status === 429) {
        toast({
          title: "Too many requests",
          description:
            "You're creating threads too quickly. Please wait a second and try again.",
        });
      } else {
        toast({
          title: "Unable to start a new chat",
          description: "Something went wrong. Please retry in a moment.",
          variant: "destructive",
        });
      }
    } finally {
      setIsCreating(false);
    }
  };

  const handleChatThread = (item: Thread) => {
    // Navigate to the chat thread using the new route structure
    router.push(`/chat/${item.thread_id}`);

    dispatch(
      updateCurrentThreadInfo({
        currentChatPageThreadId: item.thread_id,
        currentThreadName: item.thread_name,
        history: true,
        newThread: false,
        allFlights: {},
        tripType: "",
        chatResult: {},
      })
    );

    setCurrentThreadTitle(item.thread_name);
  };

  // Regular functions for thread actions (no hooks!)
  const handleEditThread = async (threadId: string, newName: string) => {
    if (!newName) return;

    try {
      await axios.put(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread-name`,
        { thread_id: threadId, thread_name: newName },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      // Refresh chat history to reflect changes
      await loadChatHistory();

      // Update current thread name if it's the one being edited
      const currentThreadId = router.query.chatThreadId as string;
      if (currentThreadId === threadId) {
        dispatch(
          updateCurrentThreadInfo({
            currentThreadName: newName,
          })
        );
        setCurrentThreadTitle(newName);
      }

      toast({
        title: "Thread renamed successfully",
        className:
          "bg-[#1E1E76] text-white top-10 left-1/2 -translate-x-1/2 absolute z-[9999]",
        duration: 2000,
      });
    } catch (error) {
      console.error("Rename error", error);
      toast({
        title: "Failed to rename thread",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteThread = async (threadId: string) => {
    try {
      await axios.delete(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/chat-history/thread`,
        {
          data: { thread_id: threadId },
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      // Reload chat history to reflect changes
      await loadChatHistory();
      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: "",
          currentThreadName: NEW_THREAD_DEFAULT_NAME,
          history: false,
          newThread: true,
          showMoreFlightsOption: false,
          chatResult: {},
          allFlights: {},
          tripType: "",
        })
      );

      // If the deleted thread is currently active, redirect to home
      const currentThreadId = router.query.chatThreadId as string;
      if (currentThreadId === threadId) {
        router.push("/chat");
      }

      toast({
        title: "Thread deleted successfully",
        className:
          "bg-[#1E1E76] text-white top-10 left-1/2 -translate-x-1/2 absolute z-[9999]",
        duration: 2000,
      });
    } catch (error) {
      console.error("Delete error", error);
      toast({
        title: "Failed to delete thread",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-80 md:block hidden bg-[url('https://storage.googleapis.com/nxvoytrips-img/ChatPage/chat-sidebar-bg.png')] bg-cover bg-center text-white flex flex-col justify-between ml-4 rounded-2xl my-2 relative">
      <div className="p-4">
        <div
          className="mb-6 justify-center flex py-2 cursor-pointer"
          onClick={() => router.push("/")}
        >
          <Image
            alt="logo"
            src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/main-logo-white.svg"
            height={120}
            width={120}
          />
        </div>

        <Button
          variant="secondary"
          className="rounded-xl text-[#F2F3FA] text-md flex justify-between font-medium w-full mb-4 bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)]"
          onClick={handleNewChat}
          disabled={isCreating}
        >
          New Chat
          {
            isCreating 
            ? <Loader className="animate-spin" />
            : <SquarePen />
          }
        </Button>

        {currentUser.id && (
          <div className="py-4 flex-grow">
            <Collapsible open={chatOpen} onOpenChange={setChatOpen}>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-[#F2F3FA]">Chat History</h3>
                <CollapsibleTrigger asChild className="hover:cursor-pointer">
                  <div>
                    {chatOpen ? (
                      <ChevronUp color="#F2F3FA" className="h-4 w-4" />
                    ) : (
                      <ChevronDown color="#F2F3FA" className="h-4 w-4" />
                    )}
                  </div>
                </CollapsibleTrigger>
              </div>

              <CollapsibleContent>
                {!threadsLoading ? (
                  <div className="space-y-4 max-h-[300px] pr-2 ">
                    {Object.keys(threads).length === 0 ? (
                      <div className="text-sm text-[#F2F3FA] opacity-60">
                        No chat history yet
                      </div>
                    ) : (
                      Object.entries(threads).map(([month, items]) => (
                        <div key={month}>
                          <div className="text-sm text-[#F2F3FA] mb-1 font-bold">
                            {month}
                          </div>
                          <div className="border border-[#B4BBE8] rounded-xl mt-2 w-[290px]">
                            {items.map((item, i) => (
                              <div
                                key={`${month}-${i}`}
                                className="p-2 text-sm cursor-pointer flex justify-between"
                              >
                                <div
                                  className="text-[#F2F3FA] flex-1 truncate pr-2"
                                  onClick={() => handleChatThread(item)}
                                  title={item.thread_name}
                                >
                                  {item.thread_name}
                                </div>
                                <ActionDropdown
                                  onEdit={() => {
                                    setThreadToEdit(item);
                                    setEditDialogOpen(true);
                                  }}
                                  onDelete={() => {
                                    setThreadToDelete(item);
                                    setAlertDialogOpen(true);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                ) : (
                  <div className="mt-6">
                    <Skeleton className="h-[6px] w-[250px] rounded-full" />
                    <Skeleton className="h-[6px] w-[200px] rounded-full mt-2" />
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}
      </div>

      <div className="p-4 flex flex-col gap-2 absolute bottom-0 left-0 right-0">
        {currentUser.id && (
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarImage src={currentUserDetails?.profile_picture || ""} />
              <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                {getInitials(currentUserDetails)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{`${currentUser?.firstName} ${currentUser?.lastName}`}</p>
              <p className="text-xs text-white/70">
                {currentUser?.email ?? ""}
              </p>
            </div>
          </div>
        )}
      </div>

      <DeleteConfirmDialog
        thread={threadToDelete}
        open={alertDialogOpen}
        onCancel={() => {
          setAlertDialogOpen(false);
          setThreadToDelete(null);
        }}
        onConfirm={async () => {
          if (threadToDelete) {
            await handleDeleteThread(threadToDelete.thread_id);
            setAlertDialogOpen(false);
            setThreadToDelete(null);
          }
        }}
      />

      <EditThreadDialog
        open={editDialogOpen}
        onCancel={() => {
          setEditDialogOpen(false);
          setThreadToEdit(null);
        }}
        defaultName={threadToEdit?.thread_name || ""}
        onConfirm={async (newName) => {
          if (threadToEdit) {
            await handleEditThread(threadToEdit.thread_id, newName);
          }
          setEditDialogOpen(false);
          setThreadToEdit(null);
        }}
      />
    </div>
  );
};

function ActionDropdown({
  onEdit,
  onDelete,
}: {
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="cursor-pointer">
          <Ellipsis color="#F2F3FA" size={20} />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[160px]">
        <DropdownMenuItem onClick={onEdit}>
          <Pencil className="mr-2 h-4 w-4" /> Rename Trip
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onDelete}>
          <Trash2 className="mr-2 h-4 w-4" /> Delete Chat
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function DeleteConfirmDialog({
  thread,
  open,
  onCancel,
  onConfirm,
}: {
  thread: any;
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}) {
  return (
    <AlertDialog open={open} onOpenChange={onCancel}>
      <AlertDialogContent className="flex flex-col items-center">
        <AlertDialogHeader className="flex flex-col items-center">
          <div>
            <Trash2 />
          </div>
          <AlertDialogTitle>Confirm Delete Trip</AlertDialogTitle>
          <AlertDialogDescription className="font-medium text-[#080236]">
            Are sure you want to delete {thread && thread.thread_name} ?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction
            className="bg-white text-black border border-[#1E1E76] mr-6 rounded-2xl hover:bg-white"
            onClick={onConfirm}
          >
            Delete
          </AlertDialogAction>
          <AlertDialogCancel className="hover:bg-[#4B4BC3] hover:text-white bg-[#4B4BC3] text-white rounded-2xl">
            Cancel
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

type EditThreadDialogProps = {
  open: boolean;
  onCancel: () => void;
  onConfirm: (newName: string) => void;
  defaultName?: string;
};

function EditThreadDialog({
  open,
  onCancel,
  onConfirm,
  defaultName = "",
}: EditThreadDialogProps) {
  const [newName, setNewName] = useState(defaultName);

  useEffect(() => {
    if (open) setNewName(defaultName);
  }, [open, defaultName]);

  return (
    <AlertDialog open={open} onOpenChange={onCancel}>
      <AlertDialogContent className="flex flex-col items-center">
        <AlertDialogHeader className="flex flex-col items-center">
          <div>
            <PenLine />
          </div>
          <AlertDialogTitle>Edit trip name</AlertDialogTitle>
        </AlertDialogHeader>

        <Input
          value={newName}
          onChange={(e) => setNewName(e.target.value)}
          placeholder="Enter new thread name"
          className="mt-2"
        />

        <AlertDialogFooter>
          <AlertDialogCancel className="bg-white text-black border border-[#1E1E76] mr-6 rounded-2xl hover:bg-white">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            className="hover:bg-[#4B4BC3] hover:text-white bg-[#4B4BC3] text-white rounded-2xl"
            onClick={() => {
              if (newName.trim()) onConfirm(newName.trim());
            }}
          >
            Save
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
