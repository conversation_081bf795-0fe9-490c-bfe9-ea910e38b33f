{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"page": {"type": "integer"}, "per_page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}, "data": {"type": "array", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "avatar": {"type": "string"}}, "required": ["id", "email", "first_name", "last_name", "avatar"], "additionalProperties": false}, "support": {"type": "object", "properties": {"url": {"type": "string"}, "text": {"type": "string"}}, "required": ["url", "text"], "additionalProperties": false}}, "required": ["page", "per_page", "total", "total_pages", "data", "support"], "additionalProperties": false}