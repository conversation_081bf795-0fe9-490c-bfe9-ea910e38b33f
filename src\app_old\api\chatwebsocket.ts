import { NextApiRequest, NextApiResponse } from 'next';
import { Server as HTTPServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';

let wsServer: WebSocketServer | undefined;

export default function handler(req: NextApiRequest, res: NextApiResponse) {
    // Typecasting req.socket to include the HTTPServer
    const server = req.socket as any as { server: HTTPServer };

    if (!wsServer) {
        wsServer = new WebSocketServer({ noServer: true });

        wsServer.on('connection', (socket: WebSocket, request) => {
            console.log('✅ WebSocket connection established');

            // Parse query parameters from the URL
            const url = new URL(request.url || "", `http://${request.headers.host}`);
            const query = url.searchParams.get('query') || "No query provided";

            // Send initial response to client
            socket.send(JSON.stringify({ ai_response: `Received query: ${query}` }));

            socket.on('message', (message: string) => {
                try {
                    const data = JSON.parse(message);
                    console.log('Received from client:', data);

                    // Process received message and respond
                    const ai_response = `Echo from server: ${data.query}`;
                    socket.send(JSON.stringify({ ai_response }));
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            });

            socket.on('close', () => {
                console.log('❌ WebSocket connection closed');
            });
        });

        // Listen for WebSocket upgrade requests
        server.server.on('upgrade', (request, socket, head) => {
            wsServer?.handleUpgrade(request, socket, head, (client) => {
                wsServer?.emit('connection', client, request);
            });
        });
    }

    res.status(200).end();
}