import Image from "next/image";
import { Card } from "@/components/ui/card";
import { StarRating } from "./star-rating";
import Link from "next/link";
import { Hotel } from "@/lib/types";
import { setSelectedHotel } from "@/store/slices/hotelDetails";
import {useDispatch} from "react-redux";
interface HotelCardProps {
    hotelData: Hotel
}

export function HotelCard({
    hotelData
}: HotelCardProps) {
    const dispatch = useDispatch();

    function setHotelData() {
        dispatch(setSelectedHotel(hotelData))
    }

    return (
        <div className="relative">
            {/* Tag positioned on the border */}
            <div
                className={`absolute -top-3 left-6 bg-[#1E1E76] text-white text-[12px] font-medium px-2 py-1 rounded-full z-10 shadow-sm`}
            >
                {'Best Value'}
            </div>

            <Card className="relative overflow-hidden border-2 border-blue-200 rounded-2xl mt-2">
                {/* Mobile Layout */}
                <div className="sm:hidden p-4">
                    {/* Top row: Image and content side by side */}
                    <div className="flex gap-4 mb-4">
                        {/* Hotel image */}
                        <div className="relative w-[160px] h-[160px] flex-shrink-0">
                            <Image
                                src={hotelData.imageSrc || "/placeholder.svg"}
                                alt={hotelData.name}
                                width={160}
                                height={160}
                                className="object-cover rounded-sm"
                                style={{ width: "160px", height: "160px" }}
                            />
                        </div>

                        {/* Content aligned with image */}
                        <div className="flex-1 min-w-0">
                            {/* Rating at top */}
                            <div className="mb-2">
                                <StarRating rating={hotelData.rating} />
                            </div>

                            <div className="min-w-0">
                                <h2 className="text-xl font-bold text-[#1E1E76] break-words">
                                    {hotelData.name}
                                </h2>
                                <p className="text-[#707FF5] text-sm break-words">
                                    <span>{hotelData.address}</span>
                                    <span className="mx-2">·</span>
                                    <span>{''}</span>
                                </p>
                                <p className="text-[#B4BBE8] text-xs mt-0.5 break-words">
                                    Free cancellation / pay at property
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Description - full width below */}
                    <div className="w-full">
                        <p className="text-sm text-[#080236]">{hotelData.description}</p>
                        <Link href={`/hotel-details?id=${hotelData.code}`}>
                            <button onClick={setHotelData} className="mt-4 py-2 px-4 text-white rounded-full bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]">
                                View more
                            </button>
                        </Link>
                    </div>
                </div>

                {/* Desktop Layout - Original */}
                <div className="hidden sm:flex p-4 gap-4">
                    {/* Hotel image - fixed size on all devices */}
                    <div className="relative w-[160px] h-[160px] flex-shrink-0">
                        <Image
                            src={hotelData.imageSrc || "/placeholder.svg"}
                            alt={hotelData.name}
                            width={160}
                            height={160}
                            className="object-cover rounded-sm"
                            style={{ width: "160px", height: "160px" }}
                        />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                            <div className="min-w-0 pr-2">
                                <h2 className="text-xl font-bold text-[#1E1E76] break-words">
                                    {hotelData.name}
                                </h2>
                                <p className="text-[#707FF5] text-sm break-words">
                                    <span>{hotelData.address}</span>
                                    <span className="mx-2">·</span>
                                    <span>{''}</span>
                                </p>
                                <p className="text-[#B4BBE8] text-xs mt-0.5 break-words">
                                    Free cancellation / pay at property
                                </p>
                            </div>

                            {/* Star rating with fixed width */}
                            <div className="flex-shrink-0 mt-1 sm:mt-0">
                                <StarRating rating={hotelData.rating} />
                            </div>
                        </div>

                        <p className="text-sm mt-2 text-[#080236]">{hotelData.description}</p>
                        <Link href={`/hotel-details?id=${hotelData.code}`}>
                            <button onClick={setHotelData} className="mt-4 py-2 px-4 text-white rounded-full bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]">
                                View more
                            </button>
                        </Link>
                    </div>
                </div>
            </Card>
        </div>
    );
}