"use client"

import { MapPin, Phone, Mail, Globe } from "lucide-react"
import Image from "next/image"

interface HotelContactInfoProps {
    address: string
    phone: string
    website: string
    email: string
    mapImage: string
    onShowMap: () => void
}

export default function HotelContactInfo({
    address,
    phone,
    website,
    email,
    mapImage,
    onShowMap,
}: HotelContactInfoProps) {
    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-1">
                <div className="flex items-start gap-3 mb-4">
                    <MapPin className="h-5 w-5 text-[#080236] mt-1" />
                    <div>
                        <h3 className="text-lg font-bold text-[#080236]">Address</h3>
                        <p className="text-sm text-[#080236]">{address}</p>
                    </div>
                </div>

                <div className="flex items-start gap-3 mb-4">
                    <Phone className="h-5 w-5 text-[#080236] mt-1" />
                    <div>
                        <h3 className="text-lg font-bold text-[#080236]">Phone</h3>
                        <p className="text-sm text-[#080236]">{phone}</p>
                    </div>
                </div>
            </div>

            <div className="md:col-span-1">
                <div className="flex items-start gap-3 mb-4">
                    <Globe className="h-5 w-5 text-[#080236] mt-1" />
                    <div>
                        <h3 className="text-lg font-bold text-[#080236]">Website</h3>
                        <p className="text-sm text-[#080236] hover:underline">{website}</p>
                    </div>
                </div>

                <div className="flex items-start gap-3 mb-4">
                    <Mail className="h-5 w-5 text-[#080236] mt-1" />
                    <div>
                        <h3 className="text-lg font-bold text-[#080236]">Email</h3>
                        <p className="text-sm text-[#080236]">{email}</p>
                    </div>
                </div>
            </div>

            <div className="md:col-span-1">
                <div className="relative h-[120px] rounded-lg overflow-hidden border border-[#B4BBE8]">
                    <Image src={mapImage || "/placeholder.svg"} alt="Hotel location map" fill className="object-cover" />
                    <div
                        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                        onClick={onShowMap}
                    >
                        <div className="bg-black rounded-full h-8 w-8 flex items-center justify-center">
                            <MapPin className="h-5 w-5 text-white" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
