"use client"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface HotelHeaderProps {
    name: string
    rating?: number
    reviewCount: number
    location: string
    price?: number 
    onReviewsClick: () => void
    currency?: string
}

export default function HotelHeader({ name, rating, reviewCount, location, price, onReviewsClick, currency }: HotelHeaderProps) {
    return (
        <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="w-full md:w-auto">
                <div className="text-[30px] font-bold text-[#080236] m-0 p-0">{name}</div>
                <div className="flex items-center gap-2 text-sm">
                    <div className="flex items-center text-[18px]">
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                            width={16}
                            height={16}
                            alt="rating star"
                            className="object-contain m-0"
                        />
                        <span className="ml-1 font-bold text-[#4B4BC3]">{rating}</span>
                    </div>
                    <span className="text-[#707FF5] hover:underline cursor-pointer" onClick={onReviewsClick}>
                        {reviewCount} Reviews
                    </span>
                    <span className="text-[#707FF5]">• {location}</span>
                </div>
            </div>
            <div className="flex flex-col items-center md:items-end w-full md:w-auto mt-4 md:mt-0">
                <div className="text-[30px] font-bold text-[#4B4BC3]">{`${currency} ${price}`}</div>
                <span className="text-[16px] text-[#B4BBE8]">price per night</span>
                <Link href="/hotel-room-selection">
                    <Button className="py-2 px-10 md:px-6 mt-6 text-white rounded-full min-w-[200px] md:min-w-0 bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]">
                        Select Your Room
                    </Button>
                </Link>
            </div>
        </div>
    )
}
