import React, { useState, useRef, useEffect } from "react";
import { X } from "lucide-react";
import OtpInput from "../../components/auth/OtpInput";
import Button from "../../components/ui/LoginButton";

interface OtpVerificationScreenProps {
  onVerify: (otp: string[]) => void;
  handleResend: () => void;
  loadingEmailOtp: boolean;
  loadingEmailOtpVerify: boolean;
  otpErrorMessage: string | null;
  type?: "email" | "phone";
  close?: () => void;
}

const OtpVerificationScreen: React.FC<OtpVerificationScreenProps> = ({
  onVerify,
  handleResend,
  loadingEmailOtp,
  loadingEmailOtpVerify,
  otpErrorMessage,
  type = "email",
  close
}) => {
  console.log("OtpVerificationScreen");
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const [otpError, setOtpError] = useState<string | null>(null);
  // Create an array of refs, one for each input
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));


  useEffect(() => {
    if (otpErrorMessage) {
      setOtpError(otpErrorMessage);
    }
    if (otpErrorMessage === null) {
      setOtpError(null);
    }
  }, [otpErrorMessage]);

  // Handle OTP input change
  const handleChange = (index: number, value: string) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    // Take only the last character if multiple are pasted
    newOtp[index] = value.slice(-1);
    setOtp(newOtp);
    setOtpError(null);
    // Auto-focus next input if a digit was entered
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle key down for backspace navigation
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      // Focus previous input if current is empty and backspace is pressed
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle paste for the entire OTP
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain").trim();

    // Check if pasted content is a number with expected length
    if (!/^\d+$/.test(pastedData)) return;

    const digits = pastedData.slice(0, 6).split("");
    const newOtp = [...otp];

    // Fill in as many inputs as we have digits
    digits.forEach((digit, index) => {
      if (index < 6) {
        newOtp[index] = digit;
      }
    });

    setOtp(newOtp);

    // Focus the next empty input or the last one
    const nextEmptyIndex = newOtp.findIndex((val) => !val);
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus();
    } else {
      inputRefs.current[5]?.focus();
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that all OTP fields are filled
    if (otp.every((digit) => digit)) {
      setOtpError(null);
      onVerify(otp);
    } else {
      setOtpError("Please enter the full verification code to continue.");
    }
  };

  // Handle OTP completion callback
  const handleOtpComplete = (otpValue: string) => {
    // Optional: auto-submit when all digits are filled
    // onVerify();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center bg-gray-50">
      <div className="p-8 flex flex-col items-center text-center max-w-2xl mx-auto bg-white rounded-lg relative">
        {close && (
          <button
            onClick={close}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 transition-colors"
            aria-label="Close"
          >
            <img src="https://storage.googleapis.com/nxvoytrips-img/popup/close-button.svg" alt="" className="w-4 h-4 m-4 cursor-pointer" />
          </button>
        )}

        <h1
          className="md:text-4xl sm:text-3xl xs:text-2xl font-bold mb-4 text-brand-black"
          style={styles.header}
        >
          OTP Verification
        </h1>

        <div className="w-full mb-8">
          <p className="text-brand-black text-xl xs:text-base">
            Enter the verification code we just sent to your email/phone number.
          </p>
        </div>

        <h2 className="md:text-2xl sm:text-xl xs:text-lg font-bold mb-4 text-brand-black">
          Verification Code
        </h2>

        <form onSubmit={handleSubmit} className="w-full">
          <div className="mb-8">
            <OtpInput
              otp={otp}
              setOtp={setOtp}
              inputCount={6}
              onComplete={handleOtpComplete}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              inputRefs={inputRefs}
            />
            {otpError && <p className="text-red-500 text-sm mt-2">{otpError}</p>}
          </div>
          <div className="flex justify-center mb-6">
            <Button type="submit" className="max-w-sm">
              {loadingEmailOtpVerify ? "Verifying..." : type === "email" ? "Change email" : "Change phone"}
            </Button>
          </div>
          <div className="flex justify-center mb-6" >
            <div className="max-w-sm cursor-pointer" onClick={() => {
              handleResend()
            }}>
              {loadingEmailOtp ? "Resending..." : "Resend code"}
            </div>
          </div>


        </form>
      </div>
    </div>
  );
};

const styles = {
  header: {
    fontFamily: "Proxima Nova, sans-serif",
    fontWeight: 700,
    letterSpacing: "0%",
    WebkitBackgroundClip: "text",
    backgroundClip: "text",
  },
};

export default OtpVerificationScreen;
