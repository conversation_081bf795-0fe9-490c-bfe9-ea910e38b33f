import type React from "react"
import DashboardLayout from "@/layout/DashboardLayout"
import { useUserData } from "@/hooks/useUserData"
import { useUserOperations } from "@/hooks/useUserOperations"
import { useSuccessMessage } from "@/hooks/useSuccessMessage"
import { useDashboardNavigation } from "@/hooks/useDashboardNavigation"
import LoadingState from "@/components/dashboard/LoadingState"
import UserProfileSidebar from "@/components/dashboard/UserProfileSidebar"
import UserProfileMobileTabs from "@/components/dashboard/UserProfileMobileTabs"
import UserProfileContent from "@/components/dashboard/UserProfileContent"

const DashboardUserProfile: React.FC = () => {
    // Custom hooks for data and operations
    const { user, loading, error, fetchUserDetails } = useUserData()
    const { updateUserDetails, updateUserDetailsVoid, deleteApi } = useUserOperations(fetchUserDetails)
    const { updateSuccessMsg, setUpdateSuccessMsg } = useSuccessMessage()
    const { activeSection, setActiveSection } = useDashboardNavigation()

    // Single update function that handles success messages
    const handleUpdateUser = async (data: any): Promise<void> => {
        try {
            const status = await updateUserDetails(data) // This returns the status
            setUpdateSuccessMsg(status)
        } catch (error) {
            throw error
        }
    }

    return (
        <DashboardLayout sectionChangeHandler={setActiveSection}>
            <div className="md:flex-row flex-col flex gap-2">
                {/* Sidebar */}
                <UserProfileSidebar
                    activeSection={activeSection}
                    setActiveSection={setActiveSection}
                    user={user}
                    updateUser={handleUpdateUser} // Use the unified function
                />

                {/* Mobile Tabs */}
                <UserProfileMobileTabs setActiveSection={setActiveSection} />

                {/* Main Content */}
                {loading ? (
                    <LoadingState error={error} />
                ) : (
                    <UserProfileContent
                        activeSection={activeSection}
                        user={user}
                        updateUser={handleUpdateUser} // Use the unified function
                        deleteApi={deleteApi}
                        updateSuccessMsg={updateSuccessMsg}
                    />
                )}
            </div>
        </DashboardLayout>
    )
}

export default DashboardUserProfile
