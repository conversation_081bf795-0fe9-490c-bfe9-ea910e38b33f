"use client"
import { useEffect, useRef } from 'react'
import gsap from 'gsap'

export const WaveAnimation = () => {
  const wave1Ref = useRef<SVGPathElement>(null)
  const wave2Ref = useRef<SVGPathElement>(null)

  useEffect(() => {
    const tl = gsap.timeline({ repeat: -1 })
    
    tl.to([wave1Ref.current, wave2Ref.current], {
      duration: 2,
      y: -20,
      ease: "sine.inOut",
      stagger: 0.2,
      yoyo: true,
      repeat: -1
    })
  }, [])

  return (
    <div className="fixed inset-0 bg-blue-600">
      <svg className="absolute inset-0 w-full h-full" preserveAspectRatio="none">
        <path
          ref={wave1Ref}
          fill="rgba(255,255,255,0.1)"
          d="M0,100 C300,0 600,100 900,50 C1200,0 1500,100 1800,50 L1800,400 L0,400 Z"
        />
        <path
          ref={wave2Ref}
          fill="rgba(255,255,255,0.15)"
          d="M0,50 C300,100 600,50 900,100 C1200,50 1500,100 1800,50 L1800,400 L0,400 Z"
        />
      </svg>
    </div>
  )
}
