// filepath: d:\flight_booking\nxvoy-travel-agent-fe\test\src\test\steps\ui\PassportCountryFix.ts
import { fixture } from '../../fixtures/Fixture';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';

/**
 * Comprehensive helper function to handle the passport country selection.
 * This implementation combines multiple approaches to ensure reliable selection
 * of passport country in the dropdown.
 * 
 * @param countryValue - The country value to be selected (can be name or ISO code)
 * @returns Promise<void>
 */
export async function fillPassportCountry(countryValue: string): Promise<void> {
    if (!countryValue) {
        console.log('No passport country of issue provided, skipping');
        return;
    }

    console.log(`Setting passport country of issue to: ${countryValue}`);
    
    try {
        // Country code mapping for common 2-letter ISO codes
        const countryCodeMapping: Record<string, string> = {
            'IN': 'India',
            'US': 'United States',
            'UK': 'United Kingdom',
            'GB': 'United Kingdom',
            'ID': 'Indonesia',
            'CN': 'China',
            'AU': 'Australia',
            'CA': 'Canada',
            'FR': 'France',
            'DE': 'Germany',
            'JP': 'Japan',
            'SG': 'Singapore',
            'MY': 'Malaysia',
            'ES': 'Spain',
            'IT': 'Italy',
            'BR': 'Brazil',
            'RU': 'Russia',
            'MX': 'Mexico',
            'ZA': 'South Africa',
            'TH': 'Thailand',
            'AE': 'United Arab Emirates',
            'SA': 'Saudi Arabia'
        };
        
        // Use the full country name if available in mapping, otherwise use the original value
        const countryToUse = countryCodeMapping[countryValue.toUpperCase()] || countryValue;
        console.log(`Using country value: ${countryToUse} (original input: ${countryValue})`);
        
        // The passport country field is a searchable dropdown with specific selectors
        const passportCountrySelectors = [
            'input[placeholder="Select Country"]',
            'input[placeholder="Passport Country of Issue"]',
            'input#passportNumber[placeholder="Passport Country of Issue"]',
            'input.w-full.px-3.py-2.placeholder\\:text-\\[\\#A195F9\\]',
            'div[ref="dropdownRef"] input',
            'input[placeholder*="Country"]',
            'input[id*="country" i]',
            'input[aria-label*="country" i]'
        ];
        
        // Track if we found and interacted with the field
        let countryFieldFound = false;
        
        // Try each selector until we find a visible input field - use Promise.race for ACTUAL faster detection
        // This is more efficient than Promise.all as it stops once any promise resolves to true
        const findVisibleSelector = async (): Promise<string | null> => {
            for (const selector of passportCountrySelectors) {
                try {
                    const isVisible = await fixture.page.locator(selector).first().isVisible({ timeout: 400 });
                    if (isVisible) {
                        return selector;
                    }
                } catch {
                    // Continue to next selector if this one fails
                }
            }
            return null;
        };
        
        // Only wait for the first visible field
        const visibleSelector = await findVisibleSelector();
        
        if (visibleSelector) {
            try {
                const passportCountryInput = fixture.page.locator(visibleSelector).first();
                // Found the field, clear any existing value first - no need to wait after fill
                await passportCountryInput.fill('');
                
                // Type the country name to trigger suggestions - using even faster typing for quicker execution
                await passportCountryInput.type(countryToUse, { delay: 10 });
                console.log(`Filled passport country search field using selector: ${visibleSelector}`);
                countryFieldFound = true;
                
                // Only take screenshot in debug mode to save time
                if (process.env.DEBUG_SCREENSHOTS === 'true') {
                    await ScreenshotHelper.takeScreenshot('passport-country-typing', true);
                }
                
                // Wait minimal time for suggestions to load - just enough for UI to respond
                await fixture.page.waitForTimeout(50);
                
                // More targeted selectors for faster matching
                const dropdownSelectors = [
                    'div.absolute.z-10.mt-2.w-full.bg-\\[\\#E6E3FF\\].text-black',
                    'div[class*="dropdown"]:visible',
                    'ul[role="listbox"]',
                    'div.absolute.z-10'
                ];
                
                // Try to find suggestion dropdown
                let dropdownFound = false;
                for (const dropdownSelector of dropdownSelectors) {
                    try {
                        const dropdown = fixture.page.locator(dropdownSelector);
                        if (await dropdown.isVisible({ timeout: 500 }).catch(() => false)) { // Reduced timeout
                            dropdownFound = true;
                            console.log(`Found dropdown with selector: ${dropdownSelector}`);
                            
                            // Only take screenshot in debug mode to save time
                            if (process.env.DEBUG_SCREENSHOTS === 'true') {
                                await ScreenshotHelper.takeScreenshot('passport-country-dropdown', true);
                            }
                            
                            // Define selectors for dropdown items
                            const itemSelectors = [
                                `div.px-4.py-2:has-text("${countryToUse}")`,
                                `div.hover\\:bg-\\[\\#4B4BC3\\]:has-text("${countryToUse}")`,
                                `div.px-4.py-2`,
                                `div[role="option"]`,
                                `div[class*="item"]`,
                                `li`
                            ];
                            
                            // Handle special case for country codes that might map to multiple countries
                            if (countryValue === 'ID') {
                                // Add both Indonesia and India as possibilities
                                itemSelectors.unshift(`div.px-4.py-2:has-text("India")`);
                                itemSelectors.unshift(`div.px-4.py-2:has-text("Indonesia")`);
                            }
                            
                            // Try to find and click on a suggestion
                            let suggestionClicked = false;
                            for (const itemSelector of itemSelectors) {
                                try {
                                    const items = await dropdown.locator(itemSelector).all();
                                    console.log(`Found ${items.length} items with selector: ${itemSelector}`);
                                    
                                    if (items.length > 0) {
                                        // Try to find an exact match first
                                        let exactMatch = null;
                                        let partialMatch = null;
                                        
                                        // Log each option for debugging
                                        for (let i = 0; i < Math.min(items.length, 5); i++) { // Limit to first 5 items for maximum speed
                                            const text = await items[i].textContent();
                                            console.log(`Option ${i}: ${text}`);
                                            
                                            if (text) {
                                                const normalizedText = text.toLowerCase().trim();
                                                const normalizedCountry = countryToUse.toLowerCase().trim();
                                                
                                                if (normalizedText === normalizedCountry) {
                                                    exactMatch = items[i];
                                                    break;
                                                } else if (normalizedText.includes(normalizedCountry) || 
                                                          normalizedCountry.includes(normalizedText)) {
                                                    partialMatch = items[i];
                                                    // Don't break here in case we find an exact match later
                                                }
                                            }
                                        }
                                        
                                        // Click in order of preference: exact match, partial match, or first item
                                        // Use Promise.race to select the first available option without waiting
                                        if (exactMatch) {
                                            // Don't wait for click to complete - use fire and forget approach
                                            exactMatch.click({ force: true, timeout: 300 }).catch(() => {}); // Ignore errors
                                            console.log(`Started click on exact match for: ${countryToUse}`);
                                            suggestionClicked = true;
                                        } else if (partialMatch) {
                                            partialMatch.click({ force: true, timeout: 300 }).catch(() => {}); // Ignore errors
                                            console.log(`Started click on partial match for: ${countryToUse}`);
                                            suggestionClicked = true;
                                        } else if (items.length > 0) {
                                            items[0].click({ force: true, timeout: 300 }).catch(() => {}); // Ignore errors
                                            console.log(`Started click on first item for: ${countryToUse}`);
                                            suggestionClicked = true;
                                        }
                                        
                                        if (suggestionClicked) {
                                            // Signal completion and proceed immediately
                                            break;
                                        }
                                    }
                                } catch (e) {
                                    console.log(`Error with item selector ${itemSelector}: ${e}`);
                                }
                            }
                            
                            // If no suggestion was clicked, try keyboard navigation
                            if (!suggestionClicked) {
                                console.log('No suggestion clicked, trying keyboard navigation');
                                await passportCountryInput.press('Enter');
                                await fixture.page.keyboard.press('Tab');
                                // No wait needed between keyboard presses
                            }
                            
                            break;
                        }
                    } catch (e) {
                        console.log(`Error with dropdown selector ${dropdownSelector}: ${e}`);
                    }
                }
                
                // Handle case where no dropdown appeared
                if (!dropdownFound) {
                    console.log('No suggestions dropdown found, trying alternative approaches');
                    
                    // Press Enter to accept the typed value
                    await passportCountryInput.press('Enter');
                    
                    // Press Tab to move focus away (no delay needed)
                    await fixture.page.keyboard.press('Tab');
                    console.log('Pressed Enter and Tab to submit country');
                    
                    // Click on a stable element outside the dropdown to commit the value if needed
                    try {
                        await fixture.page.click('label:has-text("Passport Country of Issue")', { timeout: 100 });
                        console.log('Clicked on label to commit the value');
                    } catch (e) {
                        // No need to log this error, just continue
                    }
                }
                
                // Verify the final value but don't wait for it to complete
                // Use a non-blocking approach to get the final value
                passportCountryInput.inputValue().then(finalValue => {
                    console.log(`Final passport country input value: "${finalValue}"`);
                }).catch(() => {
                    // Silently continue if we can't get the value
                });
                
                // Only take screenshot in debug mode to save time
                if (process.env.DEBUG_SCREENSHOTS === 'true') {
                    await ScreenshotHelper.takeScreenshot('passport-country-final', true);
                }
                
            } catch (e) {
                console.log(`Error with country selector ${visibleSelector}: ${e}`);
            }
        }
        
        // If we couldn't find or interact with the field through standard selectors,
        // try JavaScript approach as last resort
        if (!countryFieldFound) {
            console.warn('Could not find passport country field with any selector, trying JavaScript approach');
            
            await fixture.page.evaluate(
                (countryVal) => {
                    // Try to find any visible input for country using multiple criteria
                    const inputs = Array.from(document.querySelectorAll('input')).filter(
                        input => input.placeholder?.includes('Country') || 
                                input.placeholder?.includes('Passport') ||
                                input.id === 'passportNumber' ||
                                input.parentElement?.textContent?.includes('Country') ||
                                (input.classList.contains('w-full') && input.classList.contains('px-3'))
                    );
                    
                    if (inputs.length > 0) {
                        // Set value and dispatch events
                        inputs[0].value = countryVal;
                        inputs[0].dispatchEvent(new Event('input', { bubbles: true }));
                        inputs[0].dispatchEvent(new Event('change', { bubbles: true }));
                        
                        // Try to trigger suggestions and selection
                        setTimeout(() => {
                            const suggestions = document.querySelectorAll('div.px-4.py-2, div.hover\\:bg-\\[\\#4B4BC3\\]');
                            if (suggestions.length > 0) {
                                (suggestions[0] as HTMLElement).click();
                            }
                        }, 250); // Reduced timeout
                    }
                },
                countryToUse
            );
            
            console.log('Used JavaScript to set country value directly');
            await fixture.page.waitForTimeout(250); // Minimal wait time needed for JS execution
            
            // Also trigger Enter key in JavaScript
            await fixture.page.evaluate(() => {
                const event = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    bubbles: true
                });
                document.activeElement?.dispatchEvent(event);
                
                // Add an additional Tab event to move focus and commit the value immediately
                setTimeout(() => {
                    document.activeElement?.dispatchEvent(new KeyboardEvent('keydown', {
                        key: 'Tab',
                        code: 'Tab',
                        keyCode: 9,
                        bubbles: true
                    }));
                }, 10); // Very minimal timeout
            });
            
            console.log('Dispatched Enter and Tab key events via JavaScript');
            // No additional wait needed after JS event
            
            // Take screenshot after JavaScript approach
            if (process.env.DEBUG_SCREENSHOTS === 'true') {
                await ScreenshotHelper.takeScreenshot('passport-country-js-fallback', true);
            }
            
            // Signal success - we've made our best effort to fill the passport country 
            countryFieldFound = true;
        }
    } catch (error) {
        console.error(`Error filling passport country: ${error}`);
        await ScreenshotHelper.takeScreenshot('passport-country-error', true, true);
        throw error;
    }
    
    // Signal success and proceed immediately to the next step
    console.log('Passport country selection completed, proceeding to next step');
    // Force the event loop to proceed to the next step with minimal delay
    return Promise.resolve();
}
