import React, { useState } from "react";
import { Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import InputField from "@/components/input/InputField";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/input/Select";

interface AddressFormProps {
  onCancel: () => void;
  onSave: (data: any) => void;
  initialData?: any;
}

const AddressForm: React.FC<AddressFormProps> = ({ onCancel, onSave, initialData }) => {
  const [form, setForm] = useState({
    memorableName: initialData?.memorableName || "",
    userName: initialData?.userName || "",
    street: initialData?.street || "",
    city: initialData?.city || "",
    state: initialData?.state || "",
    country: initialData?.country || "",
    postalCode: initialData?.postalCode || "",
  });
  const [errors, setErrors] = useState<any>({});

  const validate = () => {
    const newErrors: any = {};
    if (!form.memorableName) newErrors.memorableName = "Memorable Name is required";
    if (!form.userName) newErrors.userName = "User Name is required";
    if (!form.street) newErrors.street = "Street address is required";
    if (!form.city) newErrors.city = "City is required";
    if (!form.state) newErrors.state = "State / Province is required";
    if (!form.country) newErrors.country = "Country is required";
    if (!form.postalCode) newErrors.postalCode = "Post code is required";
    else if (!/^[A-Za-z0-9\-,]+$/.test(form.postalCode)) newErrors.postalCode = "Post code can only contain letters (A-Z, a-z), Numbers (0-9), hyphens (-), and commas (,)";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleCountryChange = (value: string) => {
    setForm({ ...form, country: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSave(form);
    }
  };

  return (
    <div className="p-6 space-y-10">
      <form className="w-full bg-transparent" onSubmit={handleSubmit}>
        <div className="flex justify-between gap-4">
          <h2 className="text-3xl font-bold text-[#4B4BC3] mb-8">Add New Address</h2>
          <Button className="text-sm text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={handleSubmit}>
            <Save /> Update Changes
          </Button>
        </div>
        <div className="flex flex-col gap-1">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
            <div>
              <InputField
                label="Memorable Name"
                placeholder="User Memorable Name"
                name="memorableName"
                value={form.memorableName}
                onChange={handleChange}
              />
              {errors.memorableName && <div className="text-red-500 text-sm mt-1">{errors.memorableName}</div>}
            </div>
            <div>
              <InputField
                label="User Name"
                placeholder="User Name"
                name="userName"
                value={form.userName}
                onChange={handleChange}
              />
              {errors.userName && <div className="text-red-500 text-sm mt-1">{errors.userName}</div>}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
            <div>
              <InputField
                label="Street Address"
                placeholder="User Street Address"
                name="street"
                value={form.street}
                onChange={handleChange}
              />
              {errors.street && <div className="text-red-500 text-sm mt-1">{errors.street}</div>}
            </div>
            <div>
              <InputField
                label="City"
                placeholder="User City"
                name="city"
                value={form.city}
                onChange={handleChange}
              />
              {errors.city && <div className="text-red-500 text-sm mt-1">{errors.city}</div>}
            </div>
            <div>
              <InputField
                label="State / Province"
                placeholder="User State / Province"
                name="state"
                value={form.state}
                onChange={handleChange}
              />
              {errors.state && <div className="text-red-500 text-sm mt-1">{errors.state}</div>}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10">
            <div>
              <label className="font-semibold text-[#1E1E76] block">Country</label>
              <Select value={form.country} onValueChange={handleCountryChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Country Name" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="US">United States</SelectItem>
                  <SelectItem value="GB">United Kingdom</SelectItem>
                  <SelectItem value="IN">India</SelectItem>
                  {/* Add more countries as needed */}
                </SelectContent>
              </Select>
              {errors.country && <div className="text-red-500 text-sm mt-1">{errors.country}</div>}
            </div>
            <div>
              <InputField
                label="Postal Code"
                placeholder="User Postal Code"
                name="postalCode"
                value={form.postalCode}
                onChange={handleChange}
              />
              {errors.postalCode && <div className="text-red-500 text-sm mt-1">{errors.postalCode}</div>}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddressForm;
