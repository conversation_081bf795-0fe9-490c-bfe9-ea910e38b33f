// Sample hotel data
export interface Hotel {
  id: string;
  name: string;
  location: string;
  distance: string;
  description: string;
  tag: {
    text: string;
    color: string;
  };
  dimensions?: string;
  imageSrc: string;
  rating: number;
}
export const hotels: Hotel[] = [
  {
    id: "hotel-1",
    name: "ITC Grand Chola",
    location: "Guindy, Chennai",
    distance: "7 km from airport",
    description:
      "ITC Grand Chola, a Luxury Collection Hotel in Chennai, is one of the largest hotels in India, offering a blend of traditional South Indian architecture and modern luxury. The hotel features over 600 rooms and suites, ...",
    tag: {
      text: "Full Refund",
      color: "bg-[#1E1E76]",
    },
    imageSrc: "",
    rating: 5,
  },
  // {
  //   id: "hotel-2",
  //   name: "The Leela Palace",
  //   location: "MRC Nagar, Chennai",
  //   distance: "7 km from airport",
  //   description:
  //     "ITC Grand Chola, a Luxury Collection Hotel in Chennai, is one of the largest hotels in India, offering a blend of traditional South Indian architecture and modern luxury. The hotel features over 600 rooms and suites, ...",
  //   tag: {
  //     text: "More Flex",
  //     color: "bg-[#4DC34B]",
  //   },
  //   imageSrc: "",
  //   rating: 5,
  // },
  {
    id: "hotel-3",
    name: "The Park",
    location: "No 601, Near Us Embassy, Anna Salai",
    distance: "12 km from airport",
    description:
      "ITC Grand Chola, a Luxury Collection Hotel in Chennai, is one of the largest hotels in India, offering a blend of traditional South Indian architecture and modern luxury. The hotel features over 600 rooms and suites, ...",
    tag: {
      text: "Best value",
      color: "bg-[#4B4BC3]",
    },
    imageSrc: "",
    rating: 5,
  },
];

export const filterSections = [
  {
    id: "previous",
    title: "Your previous filters",
    options: [
      {
        id: "5-stars",
        label: "5 stars",
        checked: true,
      },

      {
        id: "hotels",
        label: "Hotels",
        checked: true,
      },
    ],
    showCount: 5,
  },

  {
    id: "popular",
    title: "Popular filters",
    options: [
      {
        id: "5-stars-pop",
        label: "5 stars",
        count: 24,
      },

      {
        id: "amenity",
        label: "Amenity",
        count: 42,
      },

      {
        id: "superb",
        label: "Superb 9+",
        count: 18,
      },
    ],
    showCount: 5,
  },

  {
    id: "facilities",
    title: "Facilities",
    expandable: true,
    options: [
      {
        id: "parking",
        label: "Parking",
        count: 120,
      },

      {
        id: "restaurant",
        label: "Restaurant",
        count: 85,
      },

      {
        id: "pets",
        label: "Pets allowed",
        count: 32,
      },

      {
        id: "pool",
        label: "Swimming pool",
        count: 45,
      },

      {
        id: "fitness",
        label: "Fitness center",
        count: 38,
      },

      {
        id: "spa",
        label: "Spa",
        count: 22,
      },

      {
        id: "wifi",
        label: "Free WiFi",
        count: 150,
      },

      {
        id: "breakfast",
        label: "Breakfast included",
        count: 67,
      },

      {
        id: "bar",
        label: "Bar",
        count: 55,
      },

      {
        id: "airport-shuttle",
        label: "Airport shuttle",
        count: 28,
      },

      {
        id: "room-service",
        label: "Room service",
        count: 42,
      },

      {
        id: "business-center",
        label: "Business center",
        count: 19,
      },

      {
        id: "childcare",
        label: "Childcare",
        count: 12,
      },

      {
        id: "hot-tub",
        label: "Hot tub",
        count: 25,
      },

      {
        id: "laundry",
        label: "Laundry service",
        count: 36,
      },

      {
        id: "concierge",
        label: "Concierge service",
        count: 29,
      },

      {
        id: "beach-access",
        label: "Beach access",
        count: 18,
      },

      {
        id: "electric-vehicle",
        label: "Electric vehicle charging",
        count: 15,
      },

      {
        id: "air-conditioning",
        label: "Air conditioning",
        count: 95,
      },

      {
        id: "terrace",
        label: "Terrace",
        count: 40,
      },

      {
        id: "garden",
        label: "Garden",
        count: 27,
      },

      {
        id: "non-smoking",
        label: "Non-smoking rooms",
        count: 110,
      },

      {
        id: "family-rooms",
        label: "Family rooms",
        count: 48,
      },

      {
        id: "allergy-free",
        label: "Allergy-free rooms",
        count: 22,
      },

      {
        id: "sauna",
        label: "Sauna",
        count: 19,
      },
    ],
    showCount: 3,
  },

  {
    id: "property-type",
    title: "Property type",
    expandable: true,
    options: [
      {
        id: "hotels",
        label: "Hotels",
        count: 103,
      },

      {
        id: "apartments",
        label: "Apartments",
        count: 67,
      },
    ],
    showCount: 2,
  },

  {
    id: "property-rating",
    title: "Property rating",
    options: [
      {
        id: "5-star",
        label: "5 star",
        count: 24,
      },

      {
        id: "4-star",
        label: "4 star",
        count: 45,
      },

      {
        id: "3-star",
        label: "3 star",
        count: 34,
      },
    ],
    showCount: 3,
  },

  {
    id: "room-facilities",
    title: "Room facilities",
    expandable: true,
    options: [
      {
        id: "toilet-paper",
        label: "Toilet paper",
        count: 150,
      },
    ],
    showCount: 3,
  },

  {
    id: "review-score",
    title: "Review score",
    options: [
      {
        id: "superb-9",
        label: "Superb 9+",
        count: 18,
      },

      {
        id: "very-good-8",
        label: "Very good 8+",
        count: 42,
      },

      {
        id: "good-7",
        label: "Good 7+",
        count: 78,
      },
    ],
    showCount: 3,
  },

  {
    id: "neighborhood",
    title: "Neighborhood",
    expandable: true,
    options: [
      {
        id: "central-north",
        label: "Central North",
        count: 24,
      },

      {
        id: "church-circle",
        label: "Church Circle",
        count: 15,
      },

      {
        id: "parkgate",
        label: "Parkgate",
        count: 8,
      },

      {
        id: "downtown",
        label: "Downtown",
        count: 12,
      },

      {
        id: "riverside",
        label: "Riverside",
        count: 9,
      },

      {
        id: "harbor-view",
        label: "Harbor View",
        count: 7,
      },

      {
        id: "old-town",
        label: "Old Town",
        count: 14,
      },

      {
        id: "business-district",
        label: "Business District",
        count: 18,
      },

      {
        id: "cultural-quarter",
        label: "Cultural Quarter",
        count: 11,
      },
    ],
    showCount: 3,
  },

  {
    id: "distance",
    title: "Distance from centre of Novo Delo",
    expandable: true,
    options: [
      {
        id: "less-than-1km",
        label: "Less than 1 km",
        count: 32,
      },

      {
        id: "less-than-3km",
        label: "Less than 3 km",
        count: 67,
      },
    ],
    showCount: 2,
  },

  {
    id: "fun-things",
    title: "Fun things to do",
    expandable: true,
    options: [
      {
        id: "bicycle-rental",
        label: "Bicycle rental",
        count: 12,
      },

      {
        id: "walking-tours",
        label: "Walking tours",
        count: 8,
      },
    ],
    showCount: 2,
  },

  {
    id: "landmarks",
    title: "Landmarks",
    expandable: true,
    options: [
      {
        id: "bus-port",
        label: "Bus Port",
        count: 15,
      },
    ],
    showCount: 1,
  },

  {
    id: "entire-places",
    title: "Entire places",
    options: [
      {
        id: "entire-homes",
        label: "Entire homes & apartments",
        count: 45,
      },
    ],
    showCount: 1,
  },

  {
    id: "certifications",
    title: "Certifications",
    options: [
      {
        id: "sustainability",
        label: "Sustainability certification",
        count: 12,
      },
    ],
    showCount: 1,
  },

  {
    id: "brands",
    title: "Brands",
    expandable: true,
    options: [
      {
        id: "eco-rooms",
        label: "ECO Rooms",
        count: 8,
      },

      {
        id: "fasthotels",
        label: "FastHotels",
        count: 15,
      },

      {
        id: "design-hotels",
        label: "Design Hotels",
        count: 6,
      },

      {
        id: "luxury-stays",
        label: "Luxury Stays",
        count: 9,
      },

      {
        id: "comfort-inn",
        label: "Comfort Inn",
        count: 12,
      },

      {
        id: "plaza-hotels",
        label: "Plaza Hotels",
        count: 7,
      },

      {
        id: "boutique-collection",
        label: "Boutique Collection",
        count: 5,
      },

      {
        id: "grand-resorts",
        label: "Grand Resorts",
        count: 4,
      },

      {
        id: "city-suites",
        label: "City Suites",
        count: 10,
      },
    ],
    showCount: 3,
  },

  {
    id: "property-accessibility",
    title: "Property accessibility",
    expandable: true,
    options: [
      {
        id: "toilet-with-grab-rails",
        label: "Toilet with grab rails",
        count: 24,
      },

      {
        id: "higher-level-toilet",
        label: "Higher level toilet",
        count: 18,
      },
    ],
    showCount: 2,
  },

  {
    id: "room-accessibility",
    title: "Room accessibility",
    expandable: true,
    options: [
      {
        id: "entire-unit-located",
        label: "Entire unit located on ground floor",
        count: 15,
      },

      {
        id: "upper-floors-accessible",
        label: "Upper floors accessible by elevator",
        count: 42,
      },
    ],
    showCount: 2,
  },
];
