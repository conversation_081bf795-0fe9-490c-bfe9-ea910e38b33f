"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface ImageCarouselProps {
    images: string[]
    height?: number
}

export default function ImageCarousel({ images, height = 300 }: ImageCarouselProps) {
    const [currentIndex, setCurrentIndex] = useState(0)

    const goToPrevious = () => {
        const isFirstSlide = currentIndex === 0
        const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1
        setCurrentIndex(newIndex)
    }

    const goToNext = () => {
        const isLastSlide = currentIndex === images.length - 1
        const newIndex = isLastSlide ? 0 : currentIndex + 1
        setCurrentIndex(newIndex)
    }

    const goToSlide = (slideIndex: number) => {
        setCurrentIndex(slideIndex)
    }

    return (
        <div className="relative group" style={{ height: `${height}px` }}>
            <div
                className="w-full h-full bg-center bg-cover duration-500"
                style={{ backgroundImage: `url(${images[currentIndex]})` }}
            />

            {/* Left Arrow */}
            <div className="absolute top-1/2 -translate-y-1/2 left-2 text-white cursor-pointer opacity-70 hover:opacity-100 z-10">
                <button onClick={goToPrevious} className="bg-black/50 p-1 rounded-full" aria-label="Previous image">
                    <ChevronLeft className="h-5 w-5" />
                </button>
            </div>

            {/* Right Arrow */}
            <div className="absolute top-1/2 -translate-y-1/2 right-2 text-white cursor-pointer opacity-70 hover:opacity-100 z-10">
                <button onClick={goToNext} className="bg-black/50 p-1 rounded-full" aria-label="Next image">
                    <ChevronRight className="h-5 w-5" />
                </button>
            </div>

            {/* Pagination Indicator */}
            <div className="absolute bottom-2 left-1/2 -translate-x-1/2 bg-[#4B4BC3]/70 text-white text-xs px-3 py-1 rounded-full">
                {currentIndex + 1} / {images.length}
            </div>
        </div>
    )
}
