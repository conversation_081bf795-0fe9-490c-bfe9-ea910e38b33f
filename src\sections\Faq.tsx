import React from "react";
import { faqs } from "@/constants";
import Accordion from "@/components/accordion/Accordion";
import AccordionItem from "@/components/accordion/AccordionItem";

const Faqs = () => {
  return (
    <section
      id="faq"
      className="xs:h-max flex bg-brand-white flex-col w-full md:p-10"
    >
      <div className="flex flex-col w-[90%] mx-auto items-center justify-start pb-[60px]">
        <h1
          className="font-bold text-medium text-brand-black 
              w-full text-2xl text-center text-[52px] sm:text-4xl xs:text-3xl leading-tight font-proxima-nova"
        >
          Have Questions?
          <br />
          Ask Shasa.
        </h1>
        <Accordion className="sm:max-w-2xl sm:mt-7">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={faq.id} trigger={faq.question}>
              {faq.answer}
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default Faqs;
