---
description: 
globs: 
alwaysApply: false
---
# Playwright Automation Rule for UI Feature Testing

## Overview

This rule describes the structure and best practices for implementing Playwright-based UI automation in this project. It references the following files for structure and conventions:
- [BasePage.ts](mdc:test/src/test/pages/BasePage.ts)
- [LoginPage.ts](mdc:test/src/test/pages/LoginPage.ts)
- [LoginUiTest.feature](mdc:test/src/resources/features/ui/LoginUiTest.feature)
- [test.json](mdc:test/src/resources/data/config/env/test.json)
- [LoginUiSteps.ts](mdc:test/src/test/steps/ui/LoginUiSteps.ts)

## Structure

### 1. Feature Files (Gherkin)
- All UI test scenarios must be written in Gherkin syntax (`.feature` files).
- Each feature file should focus on a single functional area (e.g., Login, Registration).
- Use clear, descriptive scenario names and steps.
- Reference data using keys from the environment config ([test.json](mdc:test/src/resources/data/config/env/test.json)) for maintainability.

### 2. Page Object Model
- All page interactions must be encapsulated in Page Object classes.
- The [BasePage.ts](mdc:test/src/test/pages/BasePage.ts) provides reusable, generic methods for navigation, element interaction, and assertions.
- Feature-specific pages (e.g., [LoginPage.ts](mdc:test/src/test/pages/LoginPage.ts)) should extend `BasePage` and expose only feature-relevant actions.

### 3. Step Definition Files
- Each feature should have a corresponding step definition file (e.g., [LoginUiSteps.ts](mdc:test/src/test/steps/ui/LoginUiSteps.ts)).
- Step definitions must map Gherkin steps to page object methods, using a data provider (such as `Properties.getProperty`) to resolve dynamic values from config files.
- Use clear, descriptive step patterns and keep step logic minimal—delegate all UI actions and assertions to page objects or utility helpers.
- Example:
  - `Given('The user types the {string} username on the login page', ...)` calls `LoginPage.typeUsername` with data from the config.
- Use assertion helpers and locale/message helpers for validation steps.

### 4. Data Management
- Test data (e.g., URLs, credentials) must be stored in [test.json](mdc:test/src/resources/data/config/env/test.json).
- Steps in feature files should reference data using dot notation (e.g., `"nxvoy.users.standard"`).
- Implement a data provider utility to resolve these keys at runtime for step definitions.

### 5. Reusability
- Identify and extract common actions (e.g., login, navigation, form filling) into reusable methods in `BasePage` or utility modules.
- Avoid code duplication in both step definitions and page objects.

### 6. Test Execution & Refactoring
- After implementing scenarios for a feature, run the automation suite and verify all steps pass.
- If new scenarios introduce redundancy or complexity, refactor page objects and utilities to improve maintainability.
- Review and refactor after each feature to ensure code quality and functional coverage.

## Implementation Steps

1. **Write Feature File**: Define scenarios in Gherkin, referencing data keys.
2. **Implement/Update Page Objects**: Add or update methods in `BasePage` and feature-specific pages.
3. **Create/Update Step Definitions**: Map Gherkin steps to page object methods in a dedicated step definition file, using the data provider for dynamic values.
4. **Run Tests**: Execute the suite, validate results, and fix failures.
5. **Refactor**: After each feature, refactor for reusability and maintainability.
6. **Repeat**: Continue feature by feature, ensuring full functional coverage.

## Example References

- [BasePage.ts](mdc:test/src/test/pages/BasePage.ts): Generic actions (navigation, click, fill, etc.)
- [LoginPage.ts](mdc:test/src/test/pages/LoginPage.ts): Login-specific actions, extending `BasePage`
- [LoginUiTest.feature](mdc:test/src/resources/features/ui/LoginUiTest.feature): Gherkin scenarios for login
- [test.json](mdc:test/src/resources/data/config/env/test.json): Centralized test data
- [LoginUiSteps.ts](mdc:test/src/test/steps/ui/LoginUiSteps.ts): Step definitions for login feature

---

**Note:** Always ensure new features/scenarios are covered by both Gherkin and automation, and that the codebase is refactored for clarity and reusability as coverage grows.
