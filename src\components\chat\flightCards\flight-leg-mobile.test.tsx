import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FlightLegMobile from "./flight-leg-mobile";
import { useSelector } from "react-redux";

// Mock Redux useSelector
jest.mock("react-redux", () => ({
    useSelector: jest.fn(),
}));

// Mock child components
jest.mock("./flight-segment-details", () => ({
    __esModule: true,
    default: (props: any) => (
        <div data-testid="FlightSegmentDetails">{JSON.stringify(props)}</div>
    ),
}));
jest.mock("./flight-pricing", () => ({
    __esModule: true,
    default: (props: any) => (
        <div data-testid="FlightPricing">{JSON.stringify(props)}</div>
    ),
}));

const mockSetIsOpen = jest.fn();
const mockOnSelectFlight = jest.fn();
const mockGetAirportDisplayName = jest.fn((code) => `Airport ${code}`);

const mockFlight = {
    id: "1",
    supplier_logo: "logo.png",
    departure_date: "2024-06-01",
    departure_time_ampm: "10:00 AM",
    arrival_date: "2024-06-01",
    arrival_time_ampm: "2:00 PM",
    duration: "4h",
    origin: "JFK",
    destination: "LAX",
    segments: [
        { id: "seg1" },
        { id: "seg2" },
    ],
    price: { amount: 123 },
};

const mockAirportOptions = {};

beforeEach(() => {
    ((useSelector as unknown) as jest.Mock).mockImplementation((fn) =>
        fn({ chatThread: { tripType: "oneway" } })
    );
    jest.clearAllMocks();
});

describe("FlightLegMobile", () => {
    it("renders collapsed view with correct info", () => {
        render(
            <FlightLegMobile
                label="Outbound"
                flightData={mockFlight}
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );

        expect(screen.getByText("Outbound")).toBeInTheDocument();

        expect(screen.getByText("4h")).toBeInTheDocument();
        const dateElements = screen.getAllByText("2024-06-01");
        expect(dateElements.length).toBeGreaterThanOrEqual(2);
        const matchingElements = screen.getAllByText((content, node) =>
            Boolean(node && node.textContent && node.textContent.replace(/\s+/g, " ").includes("2:00 PM | Airport LAX"))
        );
        expect(matchingElements.length).toBeGreaterThan(0);
        expect(screen.getByAltText("airline logo")).toHaveAttribute("src", "logo.png");
        expect(screen.getByTestId("FlightPricing")).toBeInTheDocument();
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("JFK", mockAirportOptions);
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("LAX", mockAirportOptions);
    });

    it("shows correct stops text for connecting flights", () => {
        render(
            <FlightLegMobile
                label="Outbound"
                flightData={mockFlight}
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByText("1 Stop")).toBeInTheDocument();
    });

    it("shows 'Connect' for direct flights", () => {
        const directFlight = { ...mockFlight, segments: [{ id: "seg1" }] };
        render(
            <FlightLegMobile
                label="Direct"
                flightData={directFlight}
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByText("Connect")).toBeInTheDocument();
    });

   

    it("renders expanded view with segment details and pricing", () => {
        render(
            <FlightLegMobile
                label="Outbound"
                flightData={mockFlight}
                isOpen={true}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        expect(screen.getByTestId("FlightSegmentDetails")).toBeInTheDocument();
        expect(screen.getByTestId("FlightPricing")).toBeInTheDocument();
    });

    it("passes correct props to FlightPricing in collapsed view", () => {
        render(
            <FlightLegMobile
                label="Outbound"
                flightData={mockFlight}
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        const pricing = screen.getByTestId("FlightPricing");
        expect(pricing.textContent).toContain('"amount":{"amount":123}');
        expect(pricing.textContent).toContain('"isMobile":true');
    });

    it("renders airline logo with correct src and alt", () => {
        render(
            <FlightLegMobile
                label="Outbound"
                flightData={mockFlight}
                isOpen={false}
                setIsOpen={mockSetIsOpen}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                onSelectFlight={mockOnSelectFlight}
                isSelected={false}
            />
        );
        const img = screen.getByAltText("airline logo") as HTMLImageElement;
        expect(img.src).toContain("logo.png");
    });
});