// src/components/Shared/Header.tsx
type HeaderProps = {
  title: string;
};

const Header = ({ title }: HeaderProps) => (
  <header className="relative bg-[#0e0b2b] py-10 px-6 text-center">
    {/* Left-aligned Logo */}
    <div className="absolute top-6 left-6">
      <a href="/">
        <img
          src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png"
          alt="NxVoy Logo"
          className="w-52 xs:w-40 h-10 object-contain"
        />
      </a>
    </div>

    {/* Centered Title */}
    <h1 className="text-white text-3xl md:text-5xl font-bold py-10">{title}</h1>
  </header>
);

export default Header;
