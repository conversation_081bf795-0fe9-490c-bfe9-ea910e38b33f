const renderTextInfo = (ele: any) => {
    return (
        <div>
            <div className="font-bold text-xl">{ele.DisplayName}</div>
            <div className="font-medium text-base">{ele.Info}</div>
        </div>
    );
};

const renderURLInfo = (ele: any) => {
    return (
        <div className="color-[#707FF5] underline">
            <a href={ele.Info} target="_blank">
                {ele.DisplayName}
            </a>
        </div>
    );
};

const ImportantInfoSection = (props: any) => {
    const { supplierInfo } = props;

    // Separate text and URL entries
    const textInfo =
        supplierInfo?.filter((ele: any) => ele.InfoType === "text") || [];
    const urlInfo =
        supplierInfo?.filter((ele: any) => ele.InfoType === "url") || [];

    return (
        <div className="flex flex-col gap-4">
            <div className="text-brand-black font-semibold text-2xl">
                Important information
            </div>
            <div className="relative font-proxima-nova w-full p-px rounded-2xl h-auto bg-brand-border shadow-sm">
                <div className="flex flex-col justify-between w-full 2xl:p-4 xl:p-4 lg:p-2 md:p-3 sm:p-3 xs:p-3 border rounded-2xl shadow-md bg-brand-white relative gap-4">
                    {/* Render all text entries in a column */}
                    <div className="flex flex-col gap-2 text-sm text-brand-black">
                        {textInfo.map((ele: any, ind: number) => (
                            <div key={`text-${ind}`}>{renderTextInfo(ele)}</div>
                        ))}
                    </div>

                    {/* Render all URL entries in one row */}
                    {urlInfo.length > 0 && (
                        <div className="flex flex-wrap gap-4 text-sm text-brand-grey">
                            {urlInfo.map((ele: any, ind: number) => (
                                <div key={`url-${ind}`}>{renderURLInfo(ele)}</div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ImportantInfoSection;
