// Flight segment details
export interface FlightSegment {
  origin: string;
  destination: string;
  depart_date: string;
  arrive_date: string;
  duration: string;
  duration_minutes: number;
  operator_code: string;
  flight_code: string;
  flight_number: string;
  //travel_class: TravelClass; //new response no travel class here
  supplier: string;
  supplier_logo: string;
  operator_logo: string;
  departure_date: string;
  departure_time_24hr: string;
  departure_time_ampm: string;
  departure_time: string;
  departure_tzone: string | null;
  arrival_date: string;
  arrival_time_24hr: string;
  arrival_time_ampm: string;
  arrival_time: string;
  arrival_tzone: string | null;
  wait_time?: string | null;
  operator: string;
}

export interface TravelClass {
  class: string;
  supplier_class: string;
  supplier_base_code: string;
  supplier_rbd_code: string;
}

export interface CabinClass {
  id: string;
  price: FlightPrice;
  passenger_prices: PassengerPriceWrapper[];
  outward_id: string;
  return_id: string;
  travel_class: TravelClass;
}

// Flight details
export interface Flight {
  id: string;
  airline: string;
  airline_code: string;
  origin: string;
  destination: string;
  departure: string;
  arrival: string;
  duration: string;
  segments: FlightSegment[];
  supplier: string;
  is_return: boolean;
  price: FlightPrice;
  passenger_prices: PassengerPriceWrapper[];
  outward_id: string;
  departure_date: string;
  departure_time_24hr: string;
  departure_time_ampm: string;
  departure_time: string;
  departure_tzone: string | null;
  arrival_date: string;
  arrival_time_24hr: string;
  arrival_time_ampm: string;
  arrival_time: string;
  arrival_tzone: string | null;
  supplier_logo: string;
  wait_time: string | null;
  wait_time_in_seconds: number | null;
  recommended?: boolean;
  feature?: string;
  cabin_classes: CabinClass[];
}

// Onward flights structure
export interface OnwardFlights {
  flights: Flight[];
  src_airports: string[];
  dst_airports: string[];
  layover_airports: string[];
  airlines: string[];
}

type FareTypeDetails = {
  Cancellation: boolean;
  LargeCabinBag: boolean;
  NameChange: boolean;
  HoldBag: boolean;
  SmallCabinBag: boolean;
  FlightChange: boolean;
  Seat: boolean;
  Product: boolean;
  SpeedyBoarding: boolean;
};

type AirlineFares = {
  [airlineCode: string]: {
    [fareType: string]: FareTypeDetails;
  };
};

// Overall flight results structure
export interface FlightResults {
  routing_id: string;
  onward_flights: OnwardFlights;
  _outward: OnwardFlights;
  _return: OnwardFlights;
  return_flights: OnwardFlights | null;
  airport_data: any | null;
  airline_data: any | null;
  supplier_feature_comparison: AirlineFares | null;
}

export type FormattedFlight = {
  id: string;
  airline: string;
  airline_code: string;
  origin: string;
  destination: string;
  departure: string;
  arrival: string;
  duration: string;
  segments: any[];
  supplier: string;
  is_return: boolean;
  price: {
    amount: number;
    currency: string;
  };
  outward_id: string;
  departure_date: string;
  departure_time_24hr: string;
  departure_time_ampm: string;
  departure_time: string;
  departure_tzone: null;
  arrival_date: string;
  arrival_time_24hr: string;
  arrival_time_ampm: string;
  arrival_time: string;
  arrival_tzone: null;
  supplier_logo: string;
  wait_time: null;
  wait_time_in_seconds: null;
  recommended: boolean;
  feature: string | null;
};

export interface TaxItemWrapper {
  TaxItem: TaxItem[];
}

export interface FlightPrice {
  amount: number;
  currency: string;
  price_includes_tax: boolean;
  tax_items?: TaxItemWrapper[];
  original_currency?: string;
  original_amount?: number;
}

export interface Airport {
  iata_code: string;
  airport_name: string;
  city_name_original: string;
  country_name: string;
  country_code: string;
}

export interface FlightPriceDetail {
  amount: string;
  currency: string;
  includesTax: string;
}

export interface FlightPrice {
  outward: FlightPriceDetail;
  return: FlightPriceDetail;
}

export interface FareOption {
  name: string;
  options: string[];
  Price: {
    Amount: string;
    Currency: string;
    PriceIncludesTax: string;
  };
}

export interface FareOptionCard {
  title: string;
  amount: string;
  currency: string;
  flights: Flight | null;
}

export interface FareCardProps {
  title: string;
  price: string;
  discount?: string;
  selected?: boolean;
  buttonLabel?: string;
  flight: Flight | null;
  options?: string[];
  onClick?: (card: FareOptionCard | null) => void;
}

export interface TaxItem {
  Name: string;
  Amount: string;
  Currency: string;
}

export interface PassengerPrice {
  Amount: string;
  Currency: string;
  PriceIncludesTax: string;
  TaxItemList: [
    {
      TaxItem: TaxItem[];
    },
  ];
  Age: string;
}

export interface PassengerPriceWrapper {
  PassengerPrice: PassengerPrice;
}

export interface PassengerPriceWrapperVariant {
  PassengerPrice: PassengerPrice[];
}
export type GroupKey = "adult" | "child" | "infant";

export interface GroupedFare {
  count: number;
  unitPrice: number;
  total: number;
}

export interface PriceBreakdown {
  grouped: Record<GroupKey, GroupedFare | null>;
  total: number;
  totalTaxAndCharges: number;
  baseTicketPrice: number;
  totalTickets: number;
  creditCardFees?: number;
  airportFees?: number;
  passengerPortion?: number;
}

export interface TravelerDisplayInfo {
  travelerType: GroupKey;
  amount: number;
  currency: string;
  taxes: number;
  ticketPrice: number;
}

export interface GroupedPassengerData {
  examplePerGroup: Partial<Record<GroupKey, TravelerDisplayInfo>>;
  expandedTravelers: TravelerDisplayInfo[];
  totalAmount: number;
  groupCounts: Record<GroupKey, number>;
}

export interface Suggestions {
  iata_code: string;
  country_name?: string;
  country_code?: string;
  city_name?: string;
  city_name_original?: string;
  airport_name?: string;
  alternative_names?: [];
}

export interface PassengerFormData {
  title: string;
  firstName: string;
  middleName: string;
  lastName: string;
  dob: string;
  gender: string;
  passportNumber: string;
  passportCountry: string;
  specialService: string;
  countryText?: string;
  selectedDob?: string;
}

export interface PaymentFormData {
  title: string;
  name: string;
  street: string;
  city: string;
  state: string;
  postcode: string;
  country_code: string;
  name_on_card: string;
  cc_number: string;
  cc_security_code: string;
  cc_expiry_date: string;
  cc_start_date: string;
  cc_type: string;
  email: string;
  phone_int_code: string;
  phone_area_code: string;
  phone_number: string;
  InternationalCode: string;
  AreaCode: string;
  Number: string;
  extension: string;
  contact_details_postcode?: string;
  contact_details_phone_number?: string;
  // contact_name?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  other?: string;
}

export type Price = {
  amount: number;
  currency: string;
};

export type FlightSearchForm = {
  destination: string;
  origin: string;
  departure_date: string;
  return_date: string | null;
  adults: number;
  children: number;
  infants: number;
  total_count?: number;
  trip_type?: string;
  travel_class: string;
  direct_only?: boolean;
  max_changes?: number;
};

export type SelectedFlightOptions = {
  outbound: Flight | null;
  inbound: Flight | null;
};

export interface LuggageOption {
  label: string;
  option_value: string;
  bags: string;
  allowed_weights: string;
  price: string;
  currency: string;
}

export interface LuggageOptionsResponse {
  _outward: LuggageOption[];
  _return: LuggageOption[];
  is_per_passenger: boolean;
  option_names: {
    generic: string;
    outward: string;
    return: string;
  };
}

export type LuggageSelection = {
  outbound: string[];
  inbound: string[];
  inboundPrice: number;
  outboundPrice: number;
};

export interface BaggageProps {
  onClose?: () => void;
}

export interface ErrorDialogProps {
  message: string;
  isOpen: boolean;
  onClose: () => void;
}

export interface ErrorProps {
  message: string;
  isOpen: boolean;
  type: string;
}