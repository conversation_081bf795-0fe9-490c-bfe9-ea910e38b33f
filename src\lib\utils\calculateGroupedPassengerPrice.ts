import {
    PriceBreakdown,
    GroupKey,
    GroupedFare,
    PassengerPriceWrapperVariant
} from "@/constants/models";

export const calculateGroupedPassengerPrice = (
    passengerList: PassengerPriceWrapperVariant[]
): PriceBreakdown => {
    console.log("calculateGroupedPassengerPrice INPUT:", passengerList);
    
    if (!Array.isArray(passengerList) || passengerList.length === 0) {
        console.warn("Invalid or empty passenger list");
        return {
            grouped: { adult: null, child: null, infant: null },
            total: 0,
            totalTaxAndCharges: 0,
            baseTicketPrice: 0,
            totalTickets: 0
        };
    }

    const groupedMap: Map<GroupKey, GroupedFare> = new Map();
    let total = 0;
    let totalTaxAndCharges = 0;

    for (const wrapper of passengerList) {
        const passengers = wrapper.PassengerPrice 
            ? Array.isArray(wrapper.PassengerPrice)
                ? wrapper.PassengerPrice
                : [wrapper.PassengerPrice]
            : [];

        for (const passengerPrice of passengers) {
            const age = parseInt(passengerPrice.Age, 10);
            const amount = parseFloat(passengerPrice.Amount);

            if (isNaN(age) || isNaN(amount) || amount < 0) {
                console.warn('Invalid passenger data:', { 
                    age: passengerPrice.Age, 
                    amount: passengerPrice.Amount 
                });
                continue;
            }

            total += amount;

            // Calculate passenger-level taxes (airport fees, etc.)
            const taxItems = passengerPrice.TaxItemList?.[0]?.TaxItem || [];
            if (Array.isArray(taxItems)) {
                for (const tax of taxItems) {
                    const taxAmount = parseFloat(tax.Amount);
                    if (!isNaN(taxAmount) && taxAmount > 0) {
                        totalTaxAndCharges += taxAmount;
                    }
                }
            }

            let group: GroupKey;
            if (age >= 12) group = 'adult';
            else if (age >= 2) group = 'child';
            else group = 'infant';

            const existing = groupedMap.get(group);

            if (existing) {
                existing.count += 1;
                existing.total += amount;
                existing.unitPrice = existing.total / existing.count;
            } else {
                groupedMap.set(group, {
                    count: 1,
                    unitPrice: amount,
                    total: amount
                });
            }
        }
    }

    const grouped: Record<GroupKey, GroupedFare | null> = {
        adult: groupedMap.get('adult') || null,
        child: groupedMap.get('child') || null,
        infant: groupedMap.get('infant') || null
    };

    // Base price = passenger amount - passenger taxes
    const baseTicketPrice = Math.max(0, total - totalTaxAndCharges);

    const totalTickets =
        (grouped.adult?.count || 0) +
        (grouped.child?.count || 0) +
        (grouped.infant?.count || 0);

    const result = {
        grouped,
        total,
        totalTaxAndCharges,
        baseTicketPrice,
        totalTickets
    };

    console.log("calculateGroupedPassengerPrice OUTPUT:", result);
    return result;
};
