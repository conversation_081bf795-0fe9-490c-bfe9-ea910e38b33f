'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

import { Flight, GroupKey, TravelerDisplayInfo } from "@/constants/models";
import { groupPassengerPrices } from '@/lib/utils/processPassengerPrice';

interface TravelerDisplayContextType {
    outboundTravelers: TravelerDisplayInfo[];
    inboundTravelers: TravelerDisplayInfo[];
    outboundTotal: number;
    inboundTotal: number;
    outboundCounts: Record<GroupKey, number>;
    inboundCounts: Record<GroupKey, number>;
    calculateOutboundTravelers: (flight: Flight) => any;
    calculateInboundTravelers: (flight: Flight) => any;
}

const TravelerDisplayContext = createContext<TravelerDisplayContextType | undefined>(undefined);

export const TravelerDisplayProvider = ({ children }: { children: ReactNode }) => {
    const [outboundTravelers, setOutboundTravelers] = useState<TravelerDisplayInfo[]>([]);
    const [inboundTravelers, setInboundTravelers] = useState<TravelerDisplayInfo[]>([]);
    const [outboundTotal, setOutboundTotal] = useState<number>(0);
    const [inboundTotal, setInboundTotal] = useState<number>(0);
    const [outboundCounts, setOutboundCounts] = useState<Record<GroupKey, number>>({ adult: 0, child: 0, infant: 0 });
    const [inboundCounts, setInboundCounts] = useState<Record<GroupKey, number>>({ adult: 0, child: 0, infant: 0 });
    
    const calculateOutboundTravelers = (flight: Flight) => {
      if (!flight.passenger_prices?.length) {
            setOutboundTravelers([]);
            setOutboundTotal(0);
            setOutboundCounts({ adult: 0, child: 0, infant: 0 });
            return groupPassengerPrices([]);
      }
        const result = groupPassengerPrices(flight.passenger_prices);
        setOutboundTravelers(result.expandedTravelers);
        setOutboundTotal(result.totalAmount);
        setOutboundCounts(result.groupCounts);
        return result
    };

    const calculateInboundTravelers = (flight: Flight) => {
      if (!flight.passenger_prices?.length) {
            setInboundTravelers([]);
            setInboundTotal(0);
            setInboundCounts({ adult: 0, child: 0, infant: 0 });
            return groupPassengerPrices([]);;
      }
        const result = groupPassengerPrices(flight.passenger_prices);
        setInboundTravelers(result.expandedTravelers);
        setInboundTotal(result.totalAmount);
        setInboundCounts(result.groupCounts);
        return result
    };

    return (
        <TravelerDisplayContext.Provider
          value={{
            outboundTravelers,
            inboundTravelers,
            outboundTotal,
            inboundTotal,
            outboundCounts,
            inboundCounts,
            calculateOutboundTravelers,
            calculateInboundTravelers,
          }}
        >
          {children}
        </TravelerDisplayContext.Provider>
      );
}

export const useTravelerDisplay = () => {
    const context = useContext(TravelerDisplayContext);
    if (!context) {
      throw new Error('useTravelerDisplay must be used within a TravelerDisplayProvider');
    }
    return context;
};