/**
 * Helper functions for running tests in CI environments
 */
export class CIHelper {
    /**
     * Determine if code is running in a CI environment
     * This checks for common CI environment variables
     */
    static isRunningInCI(): boolean {
        return process.env.CI === 'true' ||
            process.env.GITHUB_ACTIONS === 'true' ||
            process.env.GITLAB_CI === 'true' ||
            process.env.JENKINS_URL !== undefined ||
            process.env.TRAVIS === 'true' ||
            process.env.CIRCLE_CI === 'true';
    }

    /**
     * Get browser launch options suitable for the current environment
     * In CI environments, always use headless mode
     */
    static getBrowserLaunchOptions(): {headless: boolean} {
        const headless = this.isRunningInCI() ? true : false;
        
        if (headless) {
            console.log('🤖 CI environment detected: using headless browser mode');
        } else {
            console.log('🖥️ Local environment detected: using headed browser mode');
        }
        
        return { headless };
    }
}
