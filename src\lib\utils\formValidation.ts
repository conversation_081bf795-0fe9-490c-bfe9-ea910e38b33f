interface Passenger {
  title: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  dob: string;
  gender: "Male" | "Female" | "Other";
  passportNumber: string;
  passportCountry: string;
  specialService?: string;
}

export type FlightSearchForm = {
  destination: string;
  origin: string;
  departure_date: string;
};

type FlightSearchErrors = {
  destination?: string;
  origin?: string;
  departure_date?: string;
};

type PaymentForm = {
  cc_number: string;
  cc_expiry_date: string;
  cc_security_code: string;
  name: string;
  street: string;
  city: string;
  state: string;
  postcode: string;
  country_code: string;
  title: string;
  phone_int_code: string;
  contact_details_phone_number: string;
  email: string;
  card_exp_month?: number;
  card_exp_year?: number;
  // contact_name?: string;
};

type ValidationErrors = {
  [K in keyof PaymentForm]?: string;
};

// Flight search form validation
export const validateFlightSearchForm = (
  formData: FlightSearchForm
): FlightSearchErrors => {
  const error: FlightSearchErrors = {};

  if (!formData.destination) {
    error.destination = "Please Enter Arrival Location";
  }

  if (!formData.origin) {
    error.origin = "Please Enter Depature Location";
  }

  if (!formData.departure_date) {
    error.departure_date = "Please Enter Date";
  }

  return error;
};

export const findAge = (dateofbirth: string, returnDate?: string): number => {
  const [day, month, year] = dateofbirth.split("/").map(Number);
  const dob = new Date(year, month - 1, day);

  // Use returnDate if available, else use current date
  const referenceDate = returnDate ? new Date(returnDate) : new Date();

  let age = referenceDate.getFullYear() - dob.getFullYear();
  const monthDiff = referenceDate.getMonth() - dob.getMonth();
  const dayDiff = referenceDate.getDate() - dob.getDate();

  if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
    age--;
  }

  return age;
};

// Passenger validation function
export const validatePassenger = (
  passenger: Passenger,
  travelerType: string
): Partial<Record<keyof Passenger, string>> => {
  const errors: Partial<Record<keyof Passenger, string>> = {};

  console.log("travelerType", travelerType);

  const nameRegex = /^[a-zA-Z\s'-]+$/;
  const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
  const passportRegex = /^[A-Z0-9]{5,20}$/i;
  const countryRegex = /^[A-Z]{2}$/;

  // Title
  if (!passenger.title || passenger.title.trim() === "") {
    errors.title = "Title is required.";
  }

  // First name
  if (!passenger.firstName || passenger.firstName.trim().length < 2) {
    errors.firstName = "First name must be at least 2 characters.";
  } else if (!nameRegex.test(passenger.firstName)) {
    errors.firstName = "First name contains invalid characters.";
  }

  // Middle name
  if (passenger.middleName && !nameRegex.test(passenger.middleName)) {
    errors.middleName = "Middle name contains invalid characters.";
  }

  // Last name
  if (!passenger.lastName || passenger.lastName.trim().length < 2) {
    errors.lastName = "Last name must be at least 2 characters.";
  } else if (!nameRegex.test(passenger.lastName)) {
    errors.lastName = "Last name contains invalid characters.";
  }

  // Date of birth
  if (!passenger.dob) {
    errors.dob = "Date of birth is required.";
  } else {
    if (dateRegex.test(passenger.dob)) {
      const age = findAge(passenger.dob);

      switch (travelerType.toLowerCase()) {
        case "infant":
          if (age >= 2 || isNaN(age))
            errors.dob = "Infants must be under 2 years old.";
          break;
        case "child":
          if (age < 2 || age > 12 || isNaN(age))
            errors.dob = "Children must be between 2 and 12 years old.";
          break;
        case "adult":
          if (age < 13 || isNaN(age))
            errors.dob = "Adults must be 13 years or older.";
          break;
        default:
          errors.dob = "Invalid traveler type.";
      }
    }
  }

  // Gender
  const validGenders: Passenger["gender"][] = ["Male", "Female", "Other"];
  if (!passenger.gender || !validGenders.includes(passenger.gender)) {
    errors.gender = "Gender must be Male, Female, or Other.";
  }

  // Passport country
  // if (!passenger.passportCountry || !countryRegex.test(passenger.passportCountry)) {
  //   errors.passportCountry = 'Passport country must be a 2-letter ISO code.';
  // }

  // Special service - optional

  return errors;
};

export const validatePaymentForm = (
  data: Partial<PaymentForm>
): ValidationErrors => {
  const errors: ValidationErrors = {};
  const requiredFields: (keyof PaymentForm)[] = [
    // "cc_number",
    // "card_exp_month",
    // "card_exp_year",
    // "cc_security_code",
    "name",
    "street",
    "city",
    "state",
    "postcode",
    "country_code",
    "title",
    // "contact_name",
    "phone_int_code",
    "contact_details_phone_number",
    "email",
  ];

  const isEmpty = (val: string | number | undefined) =>
    val === undefined || (typeof val === "string" && !val.trim());

  // Regex patterns
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const ccNumberRegex = /^\d{16}$/;
  const ccSecurityCodeRegex = /^\d{3,4}$/;
  const postcodeRegex = /^[A-Za-z0-9 ]{5,10}$/;

  // Min -5, Max- 10, string, space, number

  const phoneRegex = /^\d{6,15}$/;

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100; // two-digit format
  const currentMonth = currentDate.getMonth() + 1;

  for (const field of requiredFields) {
    const value = data[field];

    if (value === undefined || value === null) {
      errors[field] = `This field is required`;
      continue;
    }

    if (isEmpty(value)) {
      errors[field] = `This field cannot be empty`;
      continue;
    }

    // Field-specific validations
    if (field === "cc_number" && !ccNumberRegex.test(String(value))) {
      errors.cc_number = "Credit card number must be exactly 16 digits";
    }

    if (field === "card_exp_month") {
      const expMonth = parseInt(String(value), 10);
      if (typeof expMonth !== "number" || expMonth < 1 || expMonth > 12) {
        errors.card_exp_month = "Invalid expiry month";
      }
    }

    if (field === "card_exp_year") {
      const cardExpiredYear =
        currentDate.getFullYear().toString().slice(0, 2) + String(value);
      console.log("cardExpiredYear", cardExpiredYear);
      const expYear = parseInt(cardExpiredYear.slice(2, 4), 10);

      const isTooOld =
        expYear < currentYear ||
        (expYear === currentYear && expYear < currentMonth);
      const isTooFar = expYear > currentYear + 10;

      if (isTooOld || isTooFar) {
        errors.card_exp_year = "Invalid expiry year";
      }

      if (
        expYear === currentYear &&
        data.card_exp_month !== undefined &&
        data.card_exp_month < currentMonth
      ) {
        errors.card_exp_month = "Invalid expiry month";
      }
    }

    if (
      field === "cc_security_code" &&
      !ccSecurityCodeRegex.test(String(value))
    ) {
      errors.cc_security_code = "Invalid security code";
    }

    if (
      field === "name" &&
      typeof value === "string" &&
      value.trim().length < 2
    ) {
      errors.name = "Name must be at least 2 characters long";
    }

    if (field === "postcode" && !postcodeRegex.test(String(value))) {
      errors[field] = "Invalid postcode";
    }

    if (
      field === "contact_details_phone_number" &&
      !phoneRegex.test(String(value))
    ) {
      errors.contact_details_phone_number = "Invalid phone number";
    }

    if (field === "email" && !emailRegex.test(String(value))) {
      errors.email = "Invalid email address";
    }
  }
  return errors;
};
