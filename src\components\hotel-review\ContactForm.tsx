import { Input } from "@/components/ui/input";

const ContactForm = () => {
  return (
    <div className="rounded-xl relative font-proxima-nova w-full p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
      <div className="bg-gray-100 rounded-xl px-6 pb-6 pt-3">
        <h3 className="text-[24px] font-semibold mb-4">Contact</h3>

        <div className="space-y-4">
          <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
              <Input
                placeholder="Email Address*"
                type="email"
                className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-full"
              />
            </div>

          <div className="flex gap-4">
            <div className="relative font-proxima-nova w-[250px] p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
              <Input
                placeholder="Country Code*"
                className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-full text-[#080236] placeholder-[#707FF5]"
              />
            </div>
            <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
              <Input
                placeholder="Mobile Phone Number*"
                className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-full text-[#080236] placeholder-[#707FF5]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactForm;
