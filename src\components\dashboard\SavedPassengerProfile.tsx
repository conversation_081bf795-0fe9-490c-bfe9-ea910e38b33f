import React, { useState, useEffect } from "react";
import { User, ChevronDown, PencilLine } from "lucide-react";
import ConfirmDeleteModal from "./ConfirmDeleteModal";

interface SavedPassengerProfileProps {
  profiles: any[];
  openStates: boolean[];
  onToggle: (idx: number) => void;
  onEdit: (id: any) => void;
  onDelete?: (id: string) => void;
}

const gradientBox =
  "relative font-proxima-nova w-full p-px rounded-sm h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm";
const innerBox =
  "bg-[#F2F3FA] rounded-sm w-full h-full p-2 md:p-4 flex flex-col justify-between";
const closedBox =
  "bg-[#F2F3FA] rounded-sm px-2 md:px-4 py-2 flex items-center gap-4 shadow-md cursor-pointer justify-between";

const SavedPassengerProfile: React.FC<SavedPassengerProfileProps> = ({
  profiles,
  openStates,
  onToggle,
  onDelete,
  onEdit,
}) => {
  const [deleteIdx, setDeleteIdx] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [localOpenStates, setLocalOpenStates] = useState<boolean[]>([]);

  // Initialize local state from props
  useEffect(() => {
    setLocalOpenStates(openStates);
  }, [openStates]);

  const handleDeleteClick = (idx: number) => {
    setDeleteIdx(idx);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteIdx !== null && onDelete) {
      onDelete(profiles[deleteIdx].id);
    }
    setModalOpen(false);
    setDeleteIdx(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteIdx(null);
  };

  const handleToggle = (idx: number, event?: React.MouseEvent) => {
    // Stop event propagation if provided
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    // Call the parent's onToggle function
    onToggle(idx);
    
    // Log for debugging
    console.log("Toggled profile", idx);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are you sure you want to delete this passenger profile?"
      />
      {profiles.map((profile, idx) => (
        <div key={profile.id} className="flex flex-col">
          {/* Opened Card */}
          {openStates[idx] ? (
            <div className={gradientBox + " mb-4"} style={{ minHeight: 280 }}>
              <div className={innerBox}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User />
                    <span className="font-semibold text-base md:text-lg text-[#1E1E76] ml-4">{profile.name}</span>
                  </div>
                  <button 
                    className="text-[#1E1E76] text-xl md:text-2xl" 
                    onClick={(e) => handleToggle(idx, e)}
                    aria-label="Collapse passenger profile"
                  >
                    <ChevronDown
                      className={`text-gray-500 transform rotate-180`}
                    />
                  </button>
                </div>
                <div className="mt-4 md:mt-6">
                  <div className="text-[#1E1E76] mb-1">{profile.profile.userName}</div>
                  <div className="text-[#1E1E76] mb-1">Type: {profile.profile.userType}</div>
                  <div className="text-[#1E1E76] mb-1">Gender: {profile.profile.gender}</div>
                  <div className="text-[#1E1E76] mb-1">DOB: {profile.profile.dob}</div>
                  <div className="text-[#1E1E76] mb-1">Nationality: {profile.profile.country}</div>
                </div>
                <div className="flex flex-col md:flex-row gap-4 mt-6 md:mt-8">
                  <button 
                    className="border border-[#4B4BC3] text-[#1E1E76] rounded-full px-6 py-2 w-full md:w-auto" 
                    onClick={() => handleDeleteClick(idx)}
                  >
                    Delete Profile
                  </button>
                  <button 
                    className="border border-[#4B4BC3] bg-[#F2F3FA] rounded-full px-6 py-2 w-full md:w-auto flex items-center justify-center gap-2" 
                    onClick={() => onEdit && onEdit(profile)}
                  >
                    <PencilLine />
                    Edit Profile
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Closed Card with gradient border and solid background
            <div className={gradientBox + " mb-4"}>
              <div 
                className={closedBox} 
                onClick={(e) => handleToggle(idx, e)}
                role="button"
                aria-expanded={false}
                aria-label="Expand passenger profile"
              >
                <div className="flex md:flex-row items-center">
                  <User />
                  <span className="font-semibold text-base md:text-lg text-[#1E1E76] ml-4">{profile.name}</span>
                </div>
                <ChevronDown className="text-gray-500" />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SavedPassengerProfile;