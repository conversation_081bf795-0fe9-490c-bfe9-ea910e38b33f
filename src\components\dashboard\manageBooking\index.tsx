import React, { useState, useEffect } from "react";
import { User, ChevronDown, PencilLine, UserIcon } from "lucide-react";
import { User as UserType, UserDetailDelete } from "@/constants/user";
import { Button } from "@/components/ui/button";
import Trips from "./trips";

interface PassengerProfileDetailsProps {
  user: UserType | null;
  updateUser: (data: UserType) => Promise<void>;
  updatedStatus: string | null;
  deleteApi: (data: UserDetailDelete) => Promise<void>;
}

const ManageBooking: React.FC<PassengerProfileDetailsProps> = ({
  user,
  updateUser,
  updatedStatus,
  deleteApi,
}) => {
  return <Trips />;
};

export default ManageBooking;
