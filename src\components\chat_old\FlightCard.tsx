import React, { useState } from 'react';
import { Plane, PlaneTakeoff, PlaneLanding, ChevronDown } from 'lucide-react';
import { Flight } from '@/constants/models';

interface FlightCardProps {
  flight: Flight;
  isRecommended?: boolean;
}

const FlightCard: React.FC<FlightCardProps> = ({ flight, isRecommended = false }) => {
  const [isOpen, setIsOpen] = useState(false);

  // Format date for display
  const formatDateTime = (dateTimeStr: string) => {
    const date = new Date(dateTimeStr);
    return {
      time: flight.departure_time_ampm || date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
      day: date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
    };
  };

  const departureDateTime = formatDateTime(flight.departure);
  const arrivalDateTime = formatDateTime(flight.arrival);

  // Extract flight number from first segment if available
  const flightNumber = flight.segments && flight.segments.length > 0
    ? flight.segments[0].flight_number
    : '';

  // Check for direct flight (no layovers)
  const isDirect = flight.segments && flight.segments.length === 1;

  return (
    <div className="relative p-0.5 rounded-2xl h-auto bg-gradient-to-r from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-md mb-4">
      <div className="flex flex-col items-center justify-between w-full p-5 border rounded-2xl shadow-md bg-[#F8F9FF] relative">
        {/* Badges */}
        {isRecommended && (
          <div className="absolute top-1 left-4 flex gap-2">
            <span className="bg-blue-700 text-white text-base px-4 py-0 rounded-full">
              Best
            </span>
            {isRecommended && (
              <span className="bg-green-500 text-white text-base px-4 py-0 rounded-full">
                Lowest
              </span>
            )}
          </div>
        )}

        <div className="flex w-full justify-end">
          {/* Dropdown Icon */}
          {/* <ChevronDown 
            onClick={() => setIsOpen((prev) => !prev)} 
            className="text-[#161B49] w-5 h-5 cursor-pointer" 
          /> */}
        </div>

        <img
          src={flight.supplier_logo || "/images/airlines/default-airline.png"}
          alt={flight.airline || "Airline"}
          onError={(e) => {
            e.currentTarget.src = "/images/airlines/default-airline.png";
          }}
          className="w-20 h-20"
        />
        {/* Airline Logo and Flight Info */}
        <div className="flex flex-row gap-4 w-[100%] mx-auto justify-center">
          <div className="flex flex-col gap-2">
            {!isOpen ? (
              <div className="flex flex-row gap-4 w-full">
                <div>
                  <h2 className="font-bold text-lg text-[#161B49]">
                    {departureDateTime.day}
                  </h2>
                  <p className="text-sm text-[#555]">
                    {flight.departure_time_ampm} | <span className="text-blue-600">{flight.origin}</span>
                  </p>
                </div>
                {/* Flight Duration & Connection */}
                <div className="text-center">
                  <p className="text-sm text-[#6A5ACD] font-medium">
                    {flight.duration}
                  </p>
                  <div className="h-1 w-40 bg-gradient-to-r from-pink-400 to-indigo-600 mt-1"></div>
                  <p className="text-green-500 text-sm font-medium">
                    {isDirect ? "Direct" : `${flight.segments.length - 1} Stop${flight.segments.length - 1 > 1 ? 's' : ''}`}
                  </p>
                </div>
                {/* Destination */}
                <div className="flex items-center gap-3">
                  <Plane fill="#1E1E76" className="text-[#161B49] w-5 h-5 rotate-45" />
                  <div>
                    <h2 className="font-bold text-lg text-[#161B49]">
                      {arrivalDateTime.day}
                    </h2>
                    <p className="text-sm text-[#555]">
                      {flight.arrival_time_ampm} | <span className="text-blue-600">{flight.destination}</span>
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex text-[#FF3B3F] text-xl font-bold">
                {flight.airline_code} {flightNumber}
              </div>
            )}
            <p className="text-xs text-gray-400">
              *Free Cancelation within 24 hours of booking
            </p>
          </div>
          {isOpen && (
            <div className="flex items-center">
              <button className="flex px-4 py-2 text-base rounded-full text-white bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)]">
                Select Flight
              </button>
            </div>
          )}
        </div>

        {/* Expanded Flight Details */}
        {isOpen && flight.segments && flight.segments.length > 0 && (
          <div className="py-3 w-full flex flex-col gap-2 justify-center items-center">
            {flight.segments.map((segment, index) => (
              <React.Fragment key={index}>
                <div className="flex flex-row gap-2 w-full h-auto p-2">
                  <div className="flex flex-col justify-between py-2">
                    <div className="flex flex-col">
                      <p className="text-lg font-bold text-[#161B49]">{segment.departure_time_ampm}</p>
                      <p className="text-sm text-blue-500">Departure - {segment.departure_date}</p>
                    </div>
                    <div className="flex flex-col">
                      <p className="text-lg font-bold text-[#161B49]">{segment.arrival_time_ampm}</p>
                      <p className="text-sm text-blue-500">Arrival - {segment.arrival_date}</p>
                    </div>
                  </div>
                  <div className="flex flex-col items-center justify-center p-4">
                    <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

                  </div>
                  <div className="flex flex-col gap-1 justify-between py-3">
                    <div>
                      <p className="font-bold text-[#161B49]">{segment.origin}</p>
                      <p className="text-sm text-gray-500">
                        {segment.operator_code} · {segment.travel_class} · {segment.flight_number}
                      </p>
                      <p className="text-sm text-gray-600 mt-1">{segment.duration}</p>
                    </div>
                    <div>
                      <p className="text-gray-700 font-semibold">{segment.destination}</p>
                    </div>
                  </div>
                </div>

                {/* Show layover information if there's another segment */}
                {index < flight.segments.length - 1 && (
                  <div className="flex items-center gap-3 bg-blue-50 p-2 rounded-lg border border-blue-200 w-max">
                    <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m0-4h.01M21 12c0-4.418-3.582-8-8-8s-8 3.582-8 8 3.582 8 8 8 8-3.582 8-8z"></path>
                    </svg>
                    <p className="text-sm text-blue-600 font-medium">
                      {flight.segments[index + 1].wait_time || "Layover"} at {segment.destination}
                    </p>
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        )}

        {/* Price */}
        <div className="w-full flex flex-col justify-end text-right">
          <p className="text-[#4B4BC3] text-xl font-bold">
            {flight.price.currency} {flight.price.amount.toFixed(2)}
          </p>
          <p className="text-gray-400 text-sm">Trip Total</p>
        </div>
      </div>
    </div>
  );
};

// Flight Results Display component
const FlightResultsDisplay: React.FC<{ flightResults: any }> = ({ flightResults }) => {
  // Check if we have valid flight results
  if (!flightResults || !flightResults.onward_flights || !flightResults.onward_flights.flights) {
    return null;
  }

  const flights = flightResults.onward_flights.flights as Flight[];

  // Log the flights for debugging

  if (flights.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-10 text-gray-500">
        <p>No flights found matching your criteria.</p>
      </div>
    );
  }

  // Find the recommended flight (could be explicitly marked or just the first one)
  const recommendedFlight = flights.find((flight: Flight) => flight.recommended) || flights[0];

  return (
    <div className="flex flex-col gap-5 p-4">
      <div className="flex text-xl w-full justify-center font-bold">
        Best Departing Flights
      </div>

      {/* Recommended Flight */}
      {recommendedFlight && (
        <FlightCard
          flight={recommendedFlight}
          isRecommended={true}
        />
      )}

      {/* Additional Options */}
      {flights
        .filter((flight: Flight) => flight.id !== recommendedFlight?.id)
        .map((flight: Flight, index: number) => (
          <FlightCard key={flight.id || index} flight={flight} />
        ))}
    </div>
  );
};

export { FlightCard, FlightResultsDisplay };