/**
 * RandomPassengerDataGenerator.ts
 * Utility to generate random passenger data for flight booking tests
 * Provides consistent yet random data for testing purposes
 */

/**
 * Interface defining the structure of passenger information
 */
export interface PassengerInfo {
    tittle: string;
    first_Name: string;
    last_Name: string;
    date_of_birth: string;
    gender: string;
}

/**
 * Generate a random passenger data object
 * @returns PassengerInfo object with random data
 */
export function generateRandomPassenger(): PassengerInfo {
    return {
        tittle: getRandomTitle(),
        first_Name: getRandomFirstName(),
        last_Name: getRandomLastName(),
        date_of_birth: generateRandomBirthDate(),
        gender: getRandomGender()
    };
}

/**
 * Available titles for passenger selection
 */
const titles = ["Mr", "Mrs", "Ms", "Dr"];

/**
 * Get a random title from the available options
 * @returns Title string (always "Mr" for consistency)
 */
function getRandomTitle(): string {
    // Always return "Mr" for consistent behavior in test runs
    // Original random selection code is commented out for reference
    // const randomIndex = Math.floor(Math.random() * titles.length);
    // return titles[randomIndex];
    return "Mr";
}

/**
 * Sample first names to use for random generation
 */
const firstNames = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
    "<PERSON>", "Andrew", "Donna", "Paul", "Michelle", "Joshua", 
    "Dorothy", "Kenneth"
];

/**
 * Get a random first name
 * @returns Random first name string
 */
function getRandomFirstName(): string {
    const randomIndex = Math.floor(Math.random() * firstNames.length);
    return firstNames[randomIndex];
}

/**
 * Sample last names to use for random generation
 */
const lastNames = [
    "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", 
    "Miller", "Wilson", "Moore", "Taylor", "Anderson", 
    "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", 
    "Garcia", "Martinez", "Robinson", "Clark", "Rodriguez", 
    "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", 
    "Hernandez", "King", "Wright", "Lopez", "Hill", "Scott", 
    "Green", "Adams", "Baker", "Gonzalez", "Nelson", "Carter"
];

/**
 * Get a random last name
 * @returns Random last name string
 */
function getRandomLastName(): string {
    const randomIndex = Math.floor(Math.random() * lastNames.length);
    return lastNames[randomIndex];
}

/**
 * Available gender options
 */
const genders = ["Male", "Female"];

/**
 * Get a random gender
 * @returns Random gender string
 */
function getRandomGender(): string {
    const randomIndex = Math.floor(Math.random() * genders.length);
    return genders[randomIndex];
}

/**
 * Generate a random birth date
 * Ensures the passenger is between 18 and 70 years old
 * @returns String date in YYYY-MM-DD format
 */
function generateRandomBirthDate(): string {
    const currentDate = new Date();
    const minAge = 18;
    const maxAge = 70;
    
    // Calculate the minimum and maximum birth years
    const minYear = currentDate.getFullYear() - maxAge;
    const maxYear = currentDate.getFullYear() - minAge;
    
    // Generate a random year between min and max
    const year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;
    
    // Generate a random month (0-11)
    const month = Math.floor(Math.random() * 12);
    
    // Get the number of days in the selected month
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    // Generate a random day (1 to days in month)
    const day = Math.floor(Math.random() * daysInMonth) + 1;
    
    // Create the date string in YYYY-MM-DD format
    const birthDate = new Date(year, month, day);
    
    const formattedYear = birthDate.getFullYear();
    const formattedMonth = String(birthDate.getMonth() + 1).padStart(2, '0');
    const formattedDay = String(birthDate.getDate()).padStart(2, '0');
    
    return `${formattedYear}-${formattedMonth}-${formattedDay}`;
}

/**
 * Log the generated passenger data for debugging purposes
 * @param passengerInfo The generated passenger information
 */
export function logPassengerData(passengerInfo: PassengerInfo): void {
    console.log('Generated Passenger Data:');
    console.log(`- Title: ${passengerInfo.tittle}`);
    console.log(`- Name: ${passengerInfo.first_Name} ${passengerInfo.last_Name}`);
    console.log(`- Date of Birth: ${passengerInfo.date_of_birth}`);
    console.log(`- Gender: ${passengerInfo.gender}`);
}
