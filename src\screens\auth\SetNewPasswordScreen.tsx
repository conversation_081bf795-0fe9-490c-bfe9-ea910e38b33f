import React, { useState } from "react";
import OtpInput from "../../components/auth/OtpInput";
import FormInput from "../../components/auth/FormInput";
import Button from "../../components/ui/LoginButton";
import { authPostMethod } from "@/utils/auth";
import { EyeIcon, EyeOffIcon } from "lucide-react";

interface SetNewPasswordScreenProps {
  onPasswordReset: () => void;
  identifier: string;
}

const SetNewPasswordScreen: React.FC<SetNewPasswordScreenProps> = ({
  onPasswordReset,
  identifier,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showPasswordTooltip, setShowPasswordTooltip] =
    useState<boolean>(false);
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });
  const [errors, setErrors] = useState({
    otp: "",
    password: "",
    confirmPassword: "",
  });
  const isPhone = /^\+?\d{10,15}$/.test(identifier);
  // Handle OTP completion
  const handleOtpComplete = (otpValue: string) => {
    // Clear any existing OTP errors
    if (errors.otp) {
      setErrors({ ...errors, otp: "" });
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors = {
      otp: "",
      password: "",
      confirmPassword: "",
    };
    let isValid = true;

    // Check OTP
    if (!otp.every((digit) => digit)) {
      newErrors.otp = "Please enter the full verification code to continue.";
      isValid = false;
    }

    // Check password
    if (!password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    // Check password match
    if (password !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const validatePassword = (value: string) => {
    const updatedValidation = {
      length: value.length >= 8,
      lowercase: /[a-z]/.test(value),
      uppercase: /[A-Z]/.test(value),
      number: /[0-9]/.test(value),
      specialChar: /[!@#$%^&*]/.test(value),
    };

    setPasswordValidation(updatedValidation);
    setShowPasswordTooltip(value.length > 0);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setGeneralError(null);
    const payload = {
      otp: otp.join(""),
      new_password: password,
      confirm_password: confirmPassword,
      ...(isPhone ? { phone: identifier } : { email: identifier }),
    };
    try {
      const response = await authPostMethod(
        "auth/verify-otp-reset-password",
        payload
      );
      if (!response.success) {
        setGeneralError(
          response.data?.detail.message || "Failed to reset password"
        );
        return;
      }
      console.log("Password reset successful");
      onPasswordReset();
    } catch (error) {
      console.error("❌ Reset password error:", error);
      setGeneralError("Something went wrong while resetting your password");
    }
  };

  return (
    <div className="p-8 flex flex-col items-center text-center max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="w-full">
        <div className="mb-6">
          <label className="block text-brand-black mb-1 text-sm md:text-lg text-left font-bold">
            New Password
          </label>
          <div className="relative">
            <FormInput
              type={(!showPassword && "password") || "text"}
              name="password"
              placeholder="Enter new password"
              value={password}
              onChange={(e) => {
                const value = e.target.value;
                setPassword(value);
                validatePassword(value);
              }}
              onFocus={() => setShowPasswordTooltip(false)}
              onBlur={() => setShowPasswordTooltip(false)}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-2">
              {(!showPassword && (
                <EyeIcon
                  onClick={() => setShowPassword(!showPassword)}
                  style={{ color: "#999999" }}
                />
              )) || (
                  <EyeOffIcon
                    onClick={() => setShowPassword(!showPassword)}
                    style={{ color: "#999999" }}
                  />
                )}
            </div>
          </div>
          {errors.password && (
            <p className="text-red-500 text-sm mt-1 text-left">
              {errors.password}
            </p>
          )}
          {showPasswordTooltip && (
            <div className="absolute z-10 mt-1 w-full max-w-xs bg-white border border-brand-border rounded-lg shadow-lg p-3">
              <p className="font-medium text-brand-black mb-1">
                Password Requirements:
              </p>
              <ul className="space-y-1">
                <li
                  className={`flex items-center ${passwordValidation.length ? "text-green-500" : "text-red-500"}`}
                >
                  {passwordValidation.length ? (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                  Must be at least 8 characters
                </li>
                <li
                  className={`flex items-center ${passwordValidation.lowercase ? "text-green-500" : "text-neutral-dark"}`}
                >
                  {passwordValidation.lowercase ? (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                  At least 1 lowercase letter
                </li>
                <li
                  className={`flex items-center ${passwordValidation.uppercase ? "text-green-500" : "text-neutral-dark"}`}
                >
                  {passwordValidation.uppercase ? (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                  At least 1 uppercase letter
                </li>
                <li
                  className={`flex items-center ${passwordValidation.specialChar ? "text-green-500" : "text-neutral-dark"}`}
                >
                  {passwordValidation.specialChar ? (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                  At least 1 special char (!@#$%^&*)
                </li>
                <li
                  className={`flex items-center ${passwordValidation.number ? "text-green-500" : "text-neutral-dark"}`}
                >
                  {passwordValidation.number ? (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                  At least 1 number
                </li>
              </ul>
            </div>
          )}
        </div>

        <div className="mb-8">
          <label className="block text-brand-black mb-1 text-sm md:text-lg text-left font-bold">
            Confirm Password
          </label>
          <FormInput
            type="password"
            name="confirmPassword"
            placeholder="Confirm new password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
          />
          {errors.confirmPassword && (
            <p className="text-red-500 text-sm mt-1 text-left">
              {errors.confirmPassword}
            </p>
          )}
        </div>

        <h2 className="text-2xl font-bold mb-2 text-brand-black">
          Verification Code
        </h2>
        <p className="text-brand-black text-sm">
          Enter the verification code we just sent to your{" "}
          {isPhone ? "phone" : "email"}.
        </p>
        <div className="mb-8 mt-4">
          <OtpInput
            otp={otp}
            setOtp={setOtp}
            inputCount={6}
            onComplete={handleOtpComplete}
          />
          {errors.otp && (
            <p className="text-red-500 text-sm mt-2">{errors.otp}</p>
          )}
        </div>
        {generalError && (
          <p className="text-red-600 text-sm mt-2 mb-4 text-center">
            {generalError}
          </p>
        )}
        <Button type="submit">Update Password</Button>
      </form>
    </div>
  );
};

export default SetNewPasswordScreen;
