"use client"

import Image from "next/image"
import { Play } from "lucide-react"

interface HotelImage {
    title: string;
    path: string;
    order: number;
    url: string;
    thumbnail: string;
}

interface PhotoGalleryGridProps {
    images: HotelImage[]
    onShowAllPhotos: () => void
}

export default function PhotoGalleryGrid({ images, onShowAllPhotos }: PhotoGalleryGridProps) {
    console.log(images)
    // Helper function to get image URL safely
    const getImageUrl = (index: number): string => {
        //console.log(images[index]?.url);
        return images[index]?.url;
    };

    return (
        <div className="mt-6 h-[300px] sm:h-[400px]">
            {/* Mobile view (3 photos) */}
            <div className="grid grid-cols-3 gap-2 h-full md:hidden">
                {/* Main large image on the left (full height) */}
                <div className="col-span-2 relative rounded-lg overflow-hidden h-full">
                    <Image 
                        src={getImageUrl(0)} 
                        alt={images[0]?.title || "Hotel main view"} 
                        fill 
                        className="object-cover" 
                    />
                </div>

                {/* Right side with 2 images (top and bottom) */}
                <div className="col-span-1 grid grid-rows-2 gap-2 h-full">
                    {/* Top image */}
                    <div className="relative rounded-lg overflow-hidden">
                        <Image 
                            src={getImageUrl(1)} 
                            alt={images[1]?.title || "Hotel view 1"} 
                            fill 
                            className="object-cover" 
                        />
                    </div>

                    {/* Bottom image with "Show all photos" overlay */}
                    <div className="relative rounded-lg overflow-hidden">
                        <Image 
                            src={getImageUrl(2)} 
                            alt={images[2]?.title || "Hotel view 2"} 
                            fill 
                            className="object-cover" 
                        />
                        <button
                            onClick={onShowAllPhotos}
                            className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white hover:bg-black/70 transition-colors"
                        >
                            <div className="h-8 w-8 rounded-full border-2 border-white flex items-center justify-center">
                                <Play className="h-4 w-4 text-white" />
                            </div>
                            <span className="mt-1 text-xs font-medium">Show all</span>
                        </button>
                    </div>
                </div>
            </div>

            {/* Desktop view (original grid) */}
            <div className="hidden md:grid grid-cols-12 gap-2 h-full">
                {/* Main large image on the left */}
                <div className="col-span-6 row-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(0)} 
                        alt={images[0]?.title || "Hotel main view"} 
                        fill 
                        className="object-cover" 
                    />
                </div>

                {/* Top row of 3 images */}
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(1)} 
                        alt={images[1]?.title || "Hotel view 1"} 
                        fill 
                        className="object-cover" 
                    />
                </div>
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(2)} 
                        alt={images[2]?.title || "Hotel view 2"} 
                        fill 
                        className="object-cover" 
                    />
                </div>
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(3)} 
                        alt={images[3]?.title || "Hotel view 3"} 
                        fill 
                        className="object-cover" 
                    />
                </div>

                {/* Bottom row of 2 images */}
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(4)} 
                        alt={images[4]?.title || "Hotel view 4"} 
                        fill 
                        className="object-cover" 
                    />
                </div>
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(5)} 
                        alt={images[5]?.title || "Hotel view 5"} 
                        fill 
                        className="object-cover" 
                    />
                </div>
                <div className="col-span-2 relative rounded-lg overflow-hidden">
                    <Image 
                        src={getImageUrl(6)} 
                        alt={images[6]?.title || "Hotel map view"} 
                        fill 
                        className="object-cover" 
                    />
                    <button
                        onClick={onShowAllPhotos}
                        className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white hover:bg-black/70 transition-colors"
                    >
                        <div className="h-10 w-10 rounded-full border-2 border-white flex items-center justify-center">
                            <Play className="h-5 w-5 text-white" />
                        </div>
                        <span className="mt-2 font-medium">Show all photos</span>
                    </button>
                </div>
            </div>
        </div>
    )
}