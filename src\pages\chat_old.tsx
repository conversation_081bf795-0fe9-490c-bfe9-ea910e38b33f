import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import Navbar from "@/components/NavBar";
import { motion } from "framer-motion";
import AuthContainer from "@/components/layout/AuthContainer";
import animation from "../../public/Hero section banner.json";
import { textVariant, fadeIn } from "@/utils/motion";
import dynamic from "next/dynamic";
import Markdown from "react-markdown";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
const Lottie = dynamic(() => import("react-lottie"), { ssr: false });
import { DateRange } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { agentPostMethod, getWebSocketBaseURL } from "@/utils/api";
import { FlightCard, FlightResultsDisplay } from "@/components/chat_old/FlightCard";

import {
  Send,
  RefreshCw,
  Plus,
  User,
  Mic,
  X,
  Trash2,
  Plane,
  PlaneLanding,
  PlaneTakeoff,
  ArrowLeftToLine,
  ArrowRight,
  Users,
  ChevronDown,
  SlidersHorizontal,
} from "lucide-react";
import { Flight, FlightResults, FormattedFlight } from "@/constants/models";
import { useFlightContext } from "@/context/FlightContext";
interface Message {
  sender: "human" | "receiver" | "ai";
  text?: string;
  audio?: string;
  file?: string;
  fileType?: "image" | "document" | "audio";
}

const flightClasses = ["Economy", "Premium Economy", "Business", "First Class"];
const stopsOptions = ["One Way", "Round Trip", "Multi-City"];
const airlinesOptions = [
  "IndiGo",
  "Air India",
  "British Airways",
  "Emirates Airlines",
  "Etihad Airways",
  "Gulf Air Company",
  "Lufthansa",
  "LOT Polish Airlines",
  "SWISS",
  "Qatar Airways",
  "Turkish Airlines",
  "Srilankan Airlines",
  "Virgin Atlantic",
  "Oman Air",
  "Singapore Airlines",
];
const airportsOptions = [
  "Chennai Airport - Meenambakkam",
  "Heathrow Airport",
  "Gatwick Airport",
  "Stansted Airport",
];
const layoverAirportsOptions = [
  "IndiGo",
  "Air India",
  "British Airways",
  "Emirates Airlines",
  "Etihad Airways",
  "Gulf Air Company",
  "Lufthansa",
];

const THREAD_COOKIE_NAME = "nxvoy_thread_id";

const chat = () => {
  const router = useRouter();
  const { traveler } = router.query;
  const chatBoxRef = useRef<HTMLDivElement | null>(null);
  const [width, setWidth] = useState(350);
  const [height, setHeight] = useState(300);
  const [message, setMessage] = useState("");
  const [showMoreFlights, setShowMoreFlights] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [socketStatus, setSocketStatus] = useState<
    "connecting" | "connected" | "disconnected" | "error"
  >("disconnected");
  const [isLoading, setIsLoading] = useState(false);
  const [agentMessage, setAgentMessage] = useState<string>("Loading");
  const [streamingMessage, setStreamingMessage] = useState<string>("");
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [messageAdded, setMessageAdded] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);
  const { recommendedFlight, selectRecommendedFlight } = useFlightContext();

  const [flightResults, setFlightResults] = useState<FlightResults | null>(
    null,
  );
  
  const { updateSharedFlightResults  } = useFlightContext();

  useEffect(() => {
    if (flightResults) {
      updateSharedFlightResults(flightResults);
    }
  }, [flightResults]);

  // useEffect(() => {
  //   const token = localStorage.getItem("accessToken"); // or whatever your key is
  //   if (!token) {
  //     router.push("/home"); // redirect to login page
  //   }
  // }, []);

  // Generate a new thread ID and save it to a cookie
  const generateThreadId = async () => {
    try {
      const response = await agentPostMethod("thread/generate", {});
      if (
        response?.detail?.status === "success" &&
        response?.detail?.data?.thread_id
      ) {
        const newThreadId = response.detail.data.thread_id;

        // Save the thread ID in a cookie
        const expiryDate = new Date(response.detail.data.expires_at);
        document.cookie = `${THREAD_COOKIE_NAME}=${newThreadId}; expires=${expiryDate.toUTCString()}; path=/;`;

        setThreadId(newThreadId);
        return newThreadId;
      } else {
        throw new Error("Failed to generate thread ID");
      }
    } catch (error) {
      console.error("Error generating thread ID:", error);
      return null;
    }
  };

  // Reconnect WebSocket with current thread ID or generate a new one
  const checkThreadAndReconnect = () => {
    // Close existing WebSocket if it exists
    if (ws) {
      ws.close();
      setWs(null);
    }

    // If we have a thread ID, try reconnecting with it
    // The WebSocket connection will validate the thread ID
    if (threadId) {
      setSocketStatus("connecting");
      return;
    }

    // No thread ID, generate a new one
    generateThreadId();
  };

  const [isSignInClicked, setIsSignInClicked] = useState<boolean>(false);
  const [flightDetailSection, setFlightDetailSection] =
    useState<boolean>(false);

  const handleSignIn = () => {
    setIsSignInClicked(true);
  };

  const handleAuthClose = () => {
    setIsSignInClicked(false);
  };

  const handleFlightDetailsSection = () => {
    setFlightDetailSection((prev) => !prev);
  };

  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animation,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  const styles = {
    button: {
      background:
        "linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)",
      color: "white",
      borderRadius: "100px",
    },
  };

  useEffect(() => {
    const recommendedFlight = () => {
      const flights = flightResults?.onward_flights.flights as Flight[];
      if(flights && flights?.length > 1) {
        return flights.find(x => x.recommended);
      } else {
        return flights?.[0];
      }
    }

    const flight = recommendedFlight();
    selectRecommendedFlight(flight);
  }, [flightResults])

  useEffect(() => {
    const updateWidth = () => {
      if (window.innerWidth >= 1535) {
        setWidth(700);
        setHeight(700);
      } else if (window.innerWidth >= 1280) {
        setWidth(580);
        setHeight(650);
      } else if (window.innerWidth >= 1024) {
        setWidth(550);
        setHeight(500);
      } else if (window.innerWidth >= 768) {
        setWidth(600);
        setHeight(525);
      } else if (window.innerWidth >= 640) {
        setWidth(550);
        setHeight(400);
      } else setWidth(425);
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  // On component mount, check for existing thread ID and establish WebSocket
  useEffect(() => {
    const startChatConnection = async () => {
      // Check for existing thread ID in cookie
      const cookies = document.cookie.split(";").reduce(
        (acc, cookie) => {
          const [name, value] = cookie.trim().split("=");
          acc[name] = value;
          return acc;
        },
        {} as Record<string, string>,
      );

      const existingThreadId = cookies[THREAD_COOKIE_NAME];

      if (existingThreadId) {
        // If thread ID exists, try to connect to WebSocket
        setThreadId(existingThreadId);
        // WebSocket connection will happen in the other useEffect
      } else {
        // No thread ID, generate a new one
        await generateThreadId();
      }
    };

    startChatConnection();

    if(localStorage.getItem("accessToken") === null) {
      handleSignIn();
    };
  }, []);

  // Establish WebSocket connection once we have a thread ID
  useEffect(() => {
    if (!threadId) return;

    const connectWebSocket = () => {
      setSocketStatus("connecting");
      const wsBaseUrl = getWebSocketBaseURL();

      if (!wsBaseUrl) {
        console.error("WebSocket URL could not be constructed");
        setSocketStatus("error");
        return;
      }

      const wsUrl = `${wsBaseUrl}/api/v1/thread/ws/${threadId}`;
      console.log("Connecting to WebSocket URL:", wsUrl);
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log(
          "WebSocket connection established with thread ID:",
          threadId,
        );
        setSocketStatus("connected");
      };

      socket.onmessage = (event) => {
        try {
          const response = JSON.parse(event.data);
          console.log("Received from WebSocket:", response);

          // Handle thread expiration/invalidation
          if (response.type === "error" && response.code === "THREAD_EXPIRED") {
            console.log("Thread expired or invalid");

            // Remove the invalid thread ID from cookie
            document.cookie = `${THREAD_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;

            // Close WebSocket and clear thread ID state
            socket.close();
            setThreadId(null);
            setSocketStatus("disconnected");

            // Generate a new thread ID
            generateThreadId();
            return;
          }

          if (response.type === "processing") {
            setAgentMessage(response.message);
          } else if (response.type === "agent_event") {
            setAgentMessage(response.message);
          } else if (response.type === "token_stream_start") {
            // Handle token stream start
            setIsStreaming(true);
            setStreamingMessage("");
            setMessageAdded(false);
            setIsLoading(false); // Hide the loading indicator when streaming starts
          } else if (response.type === "token_stream") {
            // Handle token stream messages
            setIsStreaming(true);
            setStreamingMessage(prev => prev + response.token);
          } else if (response.type === "token_stream_end") {
            // Stop displaying the streaming message
            setIsStreaming(false);
            setStreamingMessage("");
            // Don't add anything to chat history yet - wait for agent_response
          } else if (response.type === "chat_history") {
            setChatMessages((prev) => {
              // Transform messages from chat history format to app format
              const transformedMessages = response.messages.map((msg: any) => ({
                sender: msg.role,
                text: msg.content,
              }));
              return [...prev, ...transformedMessages];
            });
          } else if (response.type === "agent_response") {
            setIsLoading(false);
            
            // Always add the complete message from the agent response
            // Check if we have a structured response
            if (response.structured_response) {
              // Determine the type of structured response
              const responseType = response.structured_response.response_type;

              // Handle "conversation" type
              if (responseType === "conversation") {
                // Extract the conversation message and add it to chat
                const conversationMessage = response.content;
                if (conversationMessage) {
                  setChatMessages((prev) => {
                    return [
                      ...prev,
                      { sender: "ai", text: conversationMessage },
                    ];
                  });
                }
              }

              // Handle "flightResults" type
              else if (responseType === "flightResults") {
                // Add the conversation message to chat
                const conversationMessage = response.content;
                if (conversationMessage) {
                  setChatMessages((prev) => {
                    return [
                      ...prev,
                      { sender: "ai", text: conversationMessage },
                    ];
                  });
                }

                // Store flight data if available
                if (response.structured_response.detail?.data) {
                  // Process the flight data to match the expected format
                  const processedData = processFlightData(
                    response.structured_response.detail.data,
                  );

                  // Update flight state with properly formatted data
                  setFlightResults(processedData);

                  // Show flight results panel
                  setShowMoreFlights(true);
                  console.log("Flight results updated:", processedData);
                }
              }
            }
            // If no structured response, try to use the messages array
            else if (response.messages && Array.isArray(response.messages)) {
              const aiMessages = response.messages.filter(
                (msg: { role: string }) => msg.role === "ai",
              );

              if (aiMessages.length > 0) {
                const latestAiMessage = aiMessages[aiMessages.length - 1];

                if (latestAiMessage && latestAiMessage.content) {
                  setChatMessages((prev) => {
                    return [
                      ...prev,
                      { sender: "ai", text: latestAiMessage.content },
                    ];
                  });
                }
              }
            }
            // If all else fails, use the content field directly
            else if (response.content) {
              setChatMessages((prev) => {
                return [...prev, { sender: "ai", text: response.content }];
              });
            }
            
            // Reset streaming state
            setStreamingMessage("");
            setIsStreaming(false);
            setMessageAdded(false);
          }
        } catch (error) {
          console.error("Error handling WebSocket message:", error);
        }
      };

      socket.onclose = (event) => {
        console.log("WebSocket connection closed:", event.code, event.reason);
        setSocketStatus("disconnected");
      };

      socket.onerror = (error) => {
        console.error("WebSocket error:", error);
        setSocketStatus("error");

        // If connection fails entirely, it might be due to invalid thread ID
        document.cookie = `${THREAD_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        setThreadId(null);

        if (retryCount < 1) {
          setRetryCount((prevCount) => prevCount + 1);
          setTimeout(() => generateThreadId(), 1000);
        } else {
          // Show error message to user after second attempt fails
          setChatMessages((prev) => [
            ...prev,
            {
              sender: "ai",
              text: "Can't establish a connection. Please try later.",
            },
          ]);
          setRetryCount(0); // Reset for future attempts
        }
      };

      setWs(socket);

      return () => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.close();
        }
      };
    };

    connectWebSocket();
  }, [threadId]);

  useEffect(() => {
    if (chatBoxRef.current) {
      chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight;
    }
  }, [chatMessages, streamingMessage]);

  const sendMessage = () => {
    if(localStorage.getItem("accessToken") === null) {
      setMessage('')
      handleSignIn();
      return;
    };

    if (!message.trim() || !ws || socketStatus !== "connected") {
      if (socketStatus !== "connected") {
        console.log("Chat connection not established. Reconnecting...");
        checkThreadAndReconnect();
      }
      return;
    }

    // Add user message to chat
    setChatMessages((prev) => [...prev, { sender: "human", text: message }]);

    // Reset any existing streaming message
    setStreamingMessage("");
    setIsStreaming(false);
    setMessageAdded(false);
    
    // Set loading state to true
    setIsLoading(true);

    // Send message to server
    const messagePayload = {
      query: message,
      thread_id: threadId,
      message_type: "text",
    };

    ws.send(JSON.stringify(messagePayload));
    setMessage("");
  };

  // Process flight data from the WebSocket response to match the expected format
  const processFlightData = (data: any) => {
    // Check if we have the data in the expected format
    if (data.onward_flights?.flights) {
      return data;
    }

    // If data structure is like the response.structured_response.detail.data structure
    if (data.onward_flights) {
      // Return the data in the expected format
      return {
        routing_id: data.routing_id,
        onward_flights: data.onward_flights,
        return_flights: data.return_flights || null,
        airport_data: data.airport_data || null,
        airline_data: data.airline_data || null,
      };
    }

    // Otherwise, try to adapt from a different response structure
    try {
      // Extract flights from the data
      const flights = data.flights?.outbound || [];

      // If we have flights, format them to match the expected structure
      if (flights && flights.length > 0) {
        // Define a type for the formatted flight

        const formattedFlights = flights.map((flight: any) => {
          // Format each flight
          const formattedFlight: FormattedFlight = {
            id: flight.id,
            airline: flight.airline,
            airline_code: flight.flight_details?.[0]?.operator_code || "",
            origin: flight.flight_details?.[0]?.departure?.airport || "",
            destination: flight.flight_details?.[0]?.arrival?.airport || "",
            departure: flight.flight_details?.[0]?.departure?.datetime || "",
            arrival: flight.flight_details?.[0]?.arrival?.datetime || "",
            duration: flight.duration || "",
            segments:
              flight.flight_details?.map((detail: any) => ({
                origin: detail.departure?.airport || "",
                destination: detail.arrival?.airport || "",
                depart_date: detail.departure?.datetime || "",
                arrive_date: detail.arrival?.datetime || "",
                duration: detail.duration || "",
                duration_minutes: parseInt(detail.duration, 10) || 0,
                operator_code: detail.operator_code || "",
                flight_code: detail.flight_number || "",
                flight_number: detail.flight_number || "",
                travel_class: detail.cabin_class || "",
                supplier: flight.supplier || "",
                supplier_logo: flight.supplier_logo || "",
                operator_logo: detail.operator_logo || "",
                departure_date: new Date(detail.departure?.datetime || "")
                  .toISOString()
                  .split("T")[0],
                departure_time_24hr: new Date(detail.departure?.datetime || "")
                  .toTimeString()
                  .substring(0, 5),
                departure_time_ampm: new Date(
                  detail.departure?.datetime || "",
                ).toLocaleTimeString("en-US", {
                  hour: "numeric",
                  minute: "numeric",
                  hour12: true,
                }),
                departure_time: detail.departure?.datetime || "",
                departure_tzone: null,
                arrival_date: new Date(detail.arrival?.datetime || "")
                  .toISOString()
                  .split("T")[0],
                arrival_time_24hr: new Date(detail.arrival?.datetime || "")
                  .toTimeString()
                  .substring(0, 5),
                arrival_time_ampm: new Date(
                  detail.arrival?.datetime || "",
                ).toLocaleTimeString("en-US", {
                  hour: "numeric",
                  minute: "numeric",
                  hour12: true,
                }),
                arrival_time: detail.arrival?.datetime || "",
                arrival_tzone: null,
                wait_time: null,
              })) || [],
            supplier: flight.supplier || "",
            is_return: false,
            price: flight.price || { amount: 0, currency: "USD" },
            outward_id: "",
            departure_date: new Date(
              flight.flight_details?.[0]?.departure?.datetime || "",
            )
              .toISOString()
              .split("T")[0],
            departure_time_24hr: new Date(
              flight.flight_details?.[0]?.departure?.datetime || "",
            )
              .toTimeString()
              .substring(0, 5),
            departure_time_ampm: new Date(
              flight.flight_details?.[0]?.departure?.datetime || "",
            ).toLocaleTimeString("en-US", {
              hour: "numeric",
              minute: "numeric",
              hour12: true,
            }),
            departure_time:
              flight.flight_details?.[0]?.departure?.datetime || "",
            departure_tzone: null,
            arrival_date: new Date(
              flight.flight_details?.[0]?.arrival?.datetime || "",
            )
              .toISOString()
              .split("T")[0],
            arrival_time_24hr: new Date(
              flight.flight_details?.[0]?.arrival?.datetime || "",
            )
              .toTimeString()
              .substring(0, 5),
            arrival_time_ampm: new Date(
              flight.flight_details?.[0]?.arrival?.datetime || "",
            ).toLocaleTimeString("en-US", {
              hour: "numeric",
              minute: "numeric",
              hour12: true,
            }),
            arrival_time: flight.flight_details?.[0]?.arrival?.datetime || "",
            arrival_tzone: null,
            supplier_logo: flight.supplier_logo || "",
            wait_time: null,
            wait_time_in_seconds: null,
            recommended: flight.id === flights[0].id, // Mark the first flight as recommended
            feature: flight.id === flights[0].id ? "cheapest" : null,
          };
          return formattedFlight;
        });

        // Extract unique airports from flights
        const srcAirports = [
          ...new Set(
            flights
              .map((f: any) => f.flight_details?.[0]?.departure?.airport || "")
              .filter(Boolean),
          ),
        ];
        const dstAirports = [
          ...new Set(
            flights
              .map((f: any) => f.flight_details?.[0]?.arrival?.airport || "")
              .filter(Boolean),
          ),
        ];

        // Return processed data
        return {
          onward_flights: {
            flights: formattedFlights,
            src_airports: srcAirports,
            dst_airports: dstAirports,
            layover_airports: [],
            airlines: [
              ...new Set(
                formattedFlights
                  .map((f: FormattedFlight) => f.airline_code)
                  .filter(Boolean),
              ),
            ],
          },
          return_flights: null,
          airport_data: null,
          airline_data: null,
        };
      }
    } catch (error) {
      console.error("Error processing flight data:", error);
    }

    return data;
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/wav",
        });
        const audioURL = URL.createObjectURL(audioBlob);
        setAudioBlob(audioBlob);
        setAudioURL(audioURL);
        // setChatMessages([...chatMessages, { sender: "user", audio: audioURL }]);
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error("Error accessing microphone:", error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const deleteAudio = (index: number) => {
    setChatMessages(chatMessages.filter((_, i) => i !== index));
  };

  const sendAudioMessage = () => {
    if (audioBlob && audioURL) {
      const audioURL = URL.createObjectURL(audioBlob);
      setChatMessages([...chatMessages, { sender: "human", audio: audioURL }]);
      setAudioBlob(null);
      setAudioURL(null);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileType = file.type.startsWith("image/")
      ? "image"
      : file.type.startsWith("audio/")
        ? "audio"
        : "document";

    if (fileType === "image" || fileType === "audio") {
      const fileURL = URL.createObjectURL(file);
      setChatMessages([
        ...chatMessages,
        { sender: "human", file: fileURL, fileType },
      ]);
    } else {
      setChatMessages([
        ...chatMessages,
        { sender: "human", file: file.name, fileType },
      ]);
    }
  };

  return (
    <div
      className={`relative w-full h-screen xl:pb-10 lg:pb-5 overflow-hidden bg-[#F2F3FA] flex flex-col gap-2 mx-auto`}
    >
      <div className="z-50 w-full mx-auto flex flex-col justify-center">
        <div className="w-[85%] mx-auto">
          <Navbar handleSignIn={handleSignIn} />
        </div>
      </div>
      <div className="flex flex-row w-full h-full">
        <div
          className={`xs:flex sm:flex md:flex lg:flex flex-col ${showMoreFlights ? `${flightDetailSection ? "xl:hidden lg:hidden md:hidden sm:hidden xs:hidden" : "xl:w-1/2 lg:w-1/2 md:hidden sm:hidden xs:hidden"}` : "xl:w-1/2 lg:w-1/2 md:w-full sm:w-full xs:w-full"} gap-1 max-h-screen justify-between px-5 pt-2 xl:pb-10 lg:pb-5 md:pb-10 font-proxima-nova`}
        >
          <div className="flex flex-col w-full justify-between mx-auto">
            <div className="flex text-xl text-[#1E1E76] font-semibold">
              Where to today?
            </div>
            <div
              ref={chatBoxRef}
              className="flex flex-col gap-1 2xl:h-[550px] xl:h-[480px] lg:h-[480px] md:h-[500px] sm:h-[500px] xs:h-[500px] space-y-2 py-2 overflow-y-auto"
            >
              {chatMessages.map((msg, index) => (
                <div
                  key={index}
                  className={`flex gap-2 items-start ${
                    msg.sender === "human"
                      ? "justify-end items-center"
                      : "justify-end items-start"
                  }`}
                >
                  {msg.sender === "ai" && (
                    <div className="rounded-full p-3 w-auto flex items-center justify-center">
                      <img
                        className="w-10 h-10 object-contain"
                        src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA.png"
                        alt=""
                      />
                    </div>
                  )}
                  <div
                    className={`p-3 rounded-full ${
                      msg.sender === "human"
                        ? "bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)] text-white w-auto"
                        : " text-black w-full"
                    }`}
                  >
                    {msg.text && <Markdown>{msg.text}</Markdown>}

                    {msg.audio && (
                      <div className="flex items-center gap-2">
                        <audio controls src={msg.audio}></audio>
                        <button
                          onClick={() => deleteAudio(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    )}
                    {msg.file && msg.fileType === "image" && (
                      <img
                        src={msg.file}
                        alt="Uploaded"
                        className="w-40 h-auto rounded-lg mt-2"
                      />
                    )}
                    {msg.file && msg.fileType === "document" && (
                      <a
                        href={msg.file}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 underline"
                      >
                        {msg.file}
                      </a>
                    )}
                  </div>
                  {msg.sender === "human" && (
                    <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center ml-2">
                      <User color="#4B4BC3" size={18} className="text-white" />
                    </div>
                  )}
                </div>
              ))}

              {/* Display streaming message */}
              {isStreaming && streamingMessage && (
                <div className="flex gap-2 items-start justify-end items-start">
                  <div className="rounded-full p-3 w-auto flex items-center justify-center">
                    <img
                      className="w-10 h-10 object-contain"
                      src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA.png"
                      alt=""
                    />
                  </div>
                  <div className="p-3 rounded-full text-black w-full">
                    <Markdown>{streamingMessage}</Markdown>
                  </div>
                </div>
              )}

              {/* Loading indicator */}
              {isLoading && !isStreaming && (
                <div className="flex justify-center items-center py-2">
                  <div className="bg-[#EBE9FF] text-[#1E1E76] px-4 py-2 rounded-full flex items-center gap-2 shadow-sm">
                    <RefreshCw
                      size={16}
                      className="animate-spin text-[#4B4BC3]"
                    />
                    <span className="text-sm font-medium">{agentMessage}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-2 items-center border-2 border-[#B4BBE8] rounded-xl px-3 py-3">
            {/* <label
              htmlFor="file-upload"
              className="text-white hover:text-blue-600 p-2 bg-[linear-gradient(to_right,#4B4BC3,#707FF5,#A195F9)] rounded-full"
            >
              <Plus size={20} />
            </label>
            <input
              id="file-upload"
              type="file"
              className="hidden"
              onChange={handleFileUpload}
            /> */}
            <input
              type="text"
              placeholder="Ask me anything..."
              className="flex-1 outline-none px-2 text-gray-700 bg-[#F2F3FA]"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            {/* <button
              onClick={isRecording ? stopRecording : startRecording}
              className={`${
                isRecording ? "text-red-600" : "text-blue-600"
              } hover:text-blue-800`}
            >
              {isRecording ? <X size={18} /> : <Mic size={18} />}
            </button>
            {isRecording && (
              <div className="mt-2 text-red-600">Recording...</div>
            )}
            {audioBlob && (
              <button
                onClick={sendAudioMessage}
                className="mt-2 bg-blue-500 text-white px-4 py-2 rounded-lg"
              >
                Send Audio
              </button>
            )} */}
            {/* <button className="text-gray-500 hover:text-blue-600">
              <RefreshCw size={18} />
            </button> */}
            <button
              onClick={sendMessage}
              className="text-blue-600 hover:text-blue-800"
            >
              <Send size={18} />
            </button>
          </div>
        </div>
        {!showMoreFlights ? (
          <div className="lg:flex relative xl:w-1/2 lg:w-1/2 md:hidden sm:hidden xs:hidden overflow-y-auto">
            <div className="absolute w-full inset-0">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png"
                alt="Background"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="w-full bottom-0">
              <div className="flex flex-col relative bottom-0 w-[100%] h-full mx-auto">
                <div className="flex flex-col z-20 w-[85%] mx-auto xl:gap-0 lg:gap-4 text-[#F2F3FA] justify-center ">
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.25 }}
                    variants={textVariant(0.5)}
                    className="font-proxima-nova font-bold 2xl:text-[56px] xl:text-[52px] lg:text-[48px]"
                  >
                    Welcome to NxVoy!
                  </motion.div>
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.25 }}
                    variants={textVariant(1)}
                    className="font-proxima-nova font-bold 2xl:text-[48px] xl:text-[48px] lg:text-[48px] bg-[linear-gradient(to_right,rgba(242,161,242,1),rgba(161,149,249,0.6))] text-transparent bg-clip-text"
                  >
                    Meet SHASA, Your Travel Companion!
                  </motion.div>
                </div>
                <div className="flex relative w-full justify-center right-0 bottom-0">
                  <div className="flex relative justify-end m-0 2xl:right-5 xl:right-0 lg:right-0 bottom-0 z-10">
                    <Lottie
                      speed={0.5}
                      options={defaultOptions}
                      width={width}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div
            className={`xs:flex sm:flex ${flightDetailSection ? "w-full lg:w-full md:w-full bg-white" : "xl:w-1/2 lg:w-1/2 md:w-full sm:w-full xs:w-full"} max-h-screen gap-2 justify-between px-5 xs:px-0 lg:px-0 pt-2 xl:pb-6 lg:pb-2 md:pb-5 xs:pb-10 sm:pb-5 font-proxima-nova`}
          >
            <div
              className={`flex ${flightDetailSection ? "lg:w-[60%] md:w-full" : "lg:w-[95%] md:w-full sm:w-full xs:w-full"} 2xl:p-4 xl:p-2 lg:p-2 md:p-5 sm:p-5 xs:p-2 mx-auto bg-white rounded-md overflow-y-auto`}
            >
              <div className="w-full flex flex-col xl:gap-5 lg:gap-4 md:gap-4 sm:gap-5 xs:gap-4 h-full">               
                {recommendedFlight && (
                  <>
                  <div className="flex flex-col gap-5 mt-5">
                    <p className="font-bold font-proxima-nova text-2xl text-lucky-blue">Best Flights For You</p>
                    <FlightCard 
                      flight={recommendedFlight} 
                      isRecommended={true} 
                    />
                  </div>
                  </>
                )}
                
                {/* Flight results */}
                {flightResults && (
                  <div className="flex justify-end w-full">
                    <button
                      onClick={() => router.push("/flights")}
                      className="flex px-4 py-2 text-base"
                      style={styles.button}
                    >
                      Show More Flights
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
      {isSignInClicked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="rounded-lg p-4">
            <AuthContainer onCloseAuth={handleAuthClose} />
          </div>
        </div>
      )}
    </div>
  );
};

export default chat;
