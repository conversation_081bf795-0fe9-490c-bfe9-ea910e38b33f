import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import { ChatReactionBar } from "./ChatReactionBar";

// src/components/chat/ChatReactionBar.test.tsx

// Mock lucide-react icons to simple spans for test stability
jest.mock("lucide-react", () => ({
  ThumbsUp: (props: any) => <span data-testid="icon-thumbs-up" {...props}>👍</span>,
  ThumbsDown: (props: any) => <span data-testid="icon-thumbs-down" {...props}>👎</span>,
  Copy: (props: any) => <span data-testid="icon-copy" {...props}>📋</span>,
}));

describe("<ChatReactionBar />", () => {
  it("renders all reaction buttons", () => {
    render(<ChatReactionBar onSelect={jest.fn()} messageId="test-id" />);
    // There should be 3 buttons
    expect(screen.getAllByRole("button")).toHaveLength(3);
    // Icons present
    expect(screen.getByTestId("icon-thumbs-up")).toBeInTheDocument();
    expect(screen.getByTestId("icon-thumbs-down")).toBeInTheDocument();
    expect(screen.getByTestId("icon-copy")).toBeInTheDocument();
  });

  it("calls onSelect when a reaction is clicked", () => {
    const onSelect = jest.fn();
    render(<ChatReactionBar onSelect={onSelect} messageId="test-id" />);
    const buttons = screen.getAllByRole("button");
    fireEvent.click(buttons[0]);
    expect(onSelect).toHaveBeenCalledTimes(1);
  });

  it("has aria-labels on all buttons", () => {
    render(<ChatReactionBar onSelect={jest.fn()} messageId="test-id"  />);
    const buttons = screen.getAllByRole("button");
    buttons.forEach(btn => {
      expect(btn).toHaveAttribute("aria-label");
    });
  });

  it("does not crash if onSelect is a jest.fn", () => {
    expect(() => {
      render(<ChatReactionBar onSelect={jest.fn()} messageId="test-id"  />);
    }).not.toThrow();
  });

  it("changes hoveredIndex on mouse enter/leave (visual only)", () => {
    render(<ChatReactionBar onSelect={jest.fn()} messageId="test-id"  />);
    const buttons = screen.getAllByRole("button");
    fireEvent.mouseEnter(buttons[1]);
    fireEvent.mouseLeave(buttons[1]);
    // No assertion needed, just ensure no crash
    expect(true).toBe(true);
  });
});