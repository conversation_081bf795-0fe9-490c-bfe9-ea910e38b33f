import * as React from "react"
import type { NextPage } from 'next';
import { ArrowLeft, BedDouble, ChevronDown, Users, CalendarIcon, MapPin, Minus, Plus, X, Loader2 } from 'lucide-react';
import { addDays, format } from "date-fns"
import { DateRange } from "react-date-range"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { cn } from "@/lib/utils"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { debounce } from "lodash";
import axios from "axios";
import { useAuth } from "@/components/AuthProvider/auth-Provider";

interface LocationData {
  country: string;
  state: string;
  city: string;
}

interface LocationState {
  search: string;
  loading: boolean;
  locationList: LocationData[];
}

interface SearchParams {
  adults: number;
  check_in: string;
  check_out: string;
  children: number;
  hotel_codes: any[];
  items_per_page: number;
  location: string;
  page: number;
  rooms: number;
  sort: string;
  sort_order: string;
}

interface HotelSearchBarProps {
  onSearchResults?: (data: any, searchParams?: SearchParams) => void;
  onSearchStart?: () => void;
  onSearchError?: (error: string) => void;
}

interface TravelersData {
  adults: number;
  children: number;
  rooms: number;
  pets: number;
}

const HotelSearchBar: React.FC<HotelSearchBarProps> = ({
  onSearchResults,
  onSearchStart,
  onSearchError
}) => {
  // Date range state using react-date-range format
  const [dateRange, setDateRange] = React.useState([
    {
      startDate: new Date(),
      endDate: new Date(),
      key: "selection",
    },
  ]);

  const [location, setLocation] = React.useState<string>("Chennai");
  const [locationState, setLocationState] = React.useState<LocationState>({ 
    search: "", 
    loading: false, 
    locationList: [] 
  });
  const [locationOpen, setLocationOpen] = React.useState<boolean>(false);
  const [travelers, setTravelers] = React.useState<TravelersData>({
    adults: 2,
    children: 1,
    rooms: 1,
    pets: 1
  });
  
  const [travelersOpen, setTravelersOpen] = React.useState<boolean>(false);
  const [showCalendar, setShowCalendar] = React.useState<boolean>(false);
  const [calendarMonths, setCalendarMonths] = React.useState<number>(2);
  const [isSearching, setIsSearching] = React.useState<boolean>(false);
  const [dateError, setDateError] = React.useState<string>("");
  
  const { token } = useAuth();
  const calendarRef = React.useRef<HTMLDivElement | null>(null);

  const handleLocationChange = (value: string) => {
    setLocation(value);
  };

  const debouncedHandleLocation = React.useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await axios.get(
              `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/locations/search?query=${query}`,
              {
                headers: {
                  'Accept': 'application/json',
                  'Authorization': `Bearer ${token}`,
                }
              }
            );
            console.log(response);
            setLocationState({ 
              search: query, 
              loading: false, 
              locationList: response?.data?.detail?.data || [] 
            });
          } catch (error) {
            console.log("Api error", error);
            setLocationState({ 
              search: query, 
              loading: false, 
              locationList: [] 
            });
          }
        }
      }, 500),
    [token]
  );

  const handleLocationSearch = (query: string) => {
    setLocationState({ ...locationState, loading: true, search: query });
    debouncedHandleLocation(query);
  };
  const handleSelect = (rangesByKey: any) => {
    const { selection } = rangesByKey;
    setDateRange([
      {
        startDate: selection?.startDate || new Date(),
        endDate: selection?.endDate || selection?.startDate || new Date(),
        key: "selection",
      },
    ]);
    
    // Close calendar when both dates are selected
    if (selection?.startDate && selection?.endDate && selection.startDate !== selection.endDate) {
      setTimeout(() => setShowCalendar(false), 100);
    }
  };

  const updateTravelers = (key: keyof TravelersData, increment: boolean) => {
    setTravelers(prev => ({
      ...prev,
      [key]: increment ? prev[key] + 1 : Math.max(0, prev[key] - 1)
    }));
  };

  const getTravelersText = () => {
    const parts = [];
    if (travelers.adults > 0) parts.push(`${travelers.adults} Adult${travelers.adults > 1 ? 's' : ''}`);
    if (travelers.children > 0) parts.push(`${travelers.children} Children`);
    if (travelers.rooms > 0) parts.push(`${travelers.rooms} Room${travelers.rooms > 1 ? 's' : ''}`);
    return parts.join(' | ') || '0 Travelers';
  };

  const getNightsText = () => {
    const startDate = dateRange[0]?.startDate;
    const endDate = dateRange[0]?.endDate;
    if (startDate && endDate) {
      const nights = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return ` (${nights} Night${nights !== 1 ? 's' : ''})`;
    }
    return '';
  };

  const handleSearch = async () => {
    const startDate = dateRange[0]?.startDate ?? new Date();
    const endDate = dateRange[0]?.endDate ?? new Date();

    // Validate dates before search
    if (startDate >= endDate) {
      setDateError("Check-out date must be after check-in date");
      return;
    }

    // Clear date error if validation passes
    setDateError("");
    
    setIsSearching(true);
    onSearchStart?.();

    const searchData: SearchParams = {
      "adults": travelers.adults,
      "check_in": format(startDate, "yyyy-MM-dd"),
      "check_out": format(endDate, "yyyy-MM-dd"),
      "children": travelers.children,
      "hotel_codes": [],
      "items_per_page": 20,
      "location": location,
      "page": 1,
      "rooms": travelers.rooms,
      "sort": "price",
      "sort_order": "asc"
    };

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/hotel/search`,
        searchData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
          }
        }
      );

      console.log('Hotel search response:', response.data);

      if (response.status === 200) {
        // Pass both data and search parameters to parent
        onSearchResults?.(response.data, searchData);
      } else {
        throw new Error("Failed to fetch hotels");
      }
    } catch (error: any) {
      console.error('Error searching hotels:', error.response?.data || error.message);
      onSearchError?.(error.response?.data?.message || "Failed to search hotels. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  // Handle responsive calendar months
  React.useEffect(() => {
    const updateCalendarMonths = () => {
      if (window.innerWidth < 768) {
        setCalendarMonths(1); // mobile view
      } else {
        setCalendarMonths(2); // tablet and above
      }
    };

    function handleClickOutside(event: MouseEvent) {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setShowCalendar(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    updateCalendarMonths(); // Run initially
    window.addEventListener("resize", updateCalendarMonths); // Recalculate on resize

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", updateCalendarMonths);
    };
  }, []);

  return (
    <div className="p-4 sm:p-6 md:p-8 lg:p-10">
      <div className='grid grid-cols-1 lg:grid-cols-6 gap-4 sm:gap-6 lg:gap-8'>
        {/* Where to Field */}
        <div className="grid items-center gap-1.5 lg:col-span-3">
          <Label htmlFor="whereTo" className="text-cornflower text-sm sm:text-base">Where to?</Label>
          <div className="relative">
            <Popover open={locationOpen} onOpenChange={setLocationOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full text-base sm:text-lg text-[#F2F3FA] bg-[#080236] border-[#B4BBE8] rounded-full justify-start font-normal hover:bg-[#B4BBE8]/10 hover:text-[#F2F3FA] hover:border-[#F2F3FA] transition-all duration-200 flex items-center gap-2 pl-10"
                  style={{fontSize: "16px", height: "34px"}}
                >
                  {location || "Chennai"}
                </Button>
              </PopoverTrigger>
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#F2F3FA] h-4 w-4 z-10" />
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
                <Command className="bg-[#F2F3FA] rounded-lg shadow-none">
                  <CommandInput
                    placeholder="Search city..."
                    onValueChange={handleLocationSearch}
                  />
                  {locationState.loading ? (
                    <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </div>
                  ) : (
                    <>
                      <CommandEmpty>No location found.</CommandEmpty>
                      <CommandGroup>
                        {locationState.locationList.map((item: LocationData, index: number) => (
                          <CommandItem
                            key={`${item.city}-${item.state}-${item.country}-${index}`}
                            value={`${item.city}, ${item.state}, ${item.country}`}
                            onSelect={() => {
                              setLocation(`${item.city}, ${item.state}, ${item.country}`);
                              setLocationOpen(false);
                            }}
                            className="data-[selected=true]:text-white data-[selected=true]:bg-[#4B4BC3] aria-selected:bg-[#4B4BC3] aria-selected:text-white data-[highlighted=true]:bg-[#4B4BC3] data-[highlighted=true]:text-white hover:bg-[#4B4BC3] hover:text-white"
                          >
                            {item.city}, {item.state}, {item.country}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </>
                  )}
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Rooms & Guests Field */}
        <div className="grid items-center gap-1.5 lg:col-span-3">
          <Label htmlFor="travelers" className="text-cornflower text-sm sm:text-base">Rooms & Guests</Label>
          <Popover open={travelersOpen} onOpenChange={setTravelersOpen}>
            <PopoverTrigger asChild>
              <div className="relative flex items-center justify-center">
                <Button
                  variant="outline"
                  className="w-full text-base sm:text-lg text-[#F2F3FA] bg-[#080236] border-[#B4BBE8] rounded-full justify-center font-normal hover:bg-[#B4BBE8]/10 hover:text-[#F2F3FA] hover:border-[#F2F3FA] transition-all duration-200 flex items-center gap-2"
                  style={{height: "34px"}}
                >
                  <Users className="h-4 w-4 text-[#F2F3FA] flex-shrink-0" />
                  <span className="truncate">{getTravelersText()}</span>
                  <ChevronDown className="h-4 w-4 text-[#F2F3FA] flex-shrink-0" strokeWidth={3}/>
                </Button>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-80 sm:w-96 p-0" align="start">
              <div className="bg-white rounded-lg shadow-lg">
                <div className="flex justify-between p-4 border-b">
                  <div className="grid mx-auto">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-lucky-blue" />
                      <span className="font-bold text-xl sm:text-2xl text-lucky-blue">Travelers</span>
                    </div>
                    <span className="text-sm text-portage text-center">{travelers.adults + travelers.children} travelers</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setTravelersOpen(false)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="p-4 space-y-4">
                  {/* Adults */}
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-semibold text-base sm:text-lg text-lucky-blue">Adults</div>
                      <div className="text-sm text-portage">Ages 13 or above</div>
                    </div>
                    <div className="flex items-center gap-3 rounded-full border border-portage">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('adults', false)}
                        disabled={travelers.adults <= 0}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Minus className="h-4 w-4" strokeWidth={3}/>
                      </Button>
                      <span className="w-8 text-center font-semibold">{travelers.adults}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('adults', true)}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Plus className="h-4 w-4" strokeWidth={3}/>
                      </Button>
                    </div>
                  </div>

                  {/* Children */}
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-semibold text-base sm:text-lg text-lucky-blue">Children</div>
                      <div className="text-sm text-portage">Ages 2-12</div>
                    </div>
                    <div className="flex items-center gap-3 rounded-full border border-portage">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('children', false)}
                        disabled={travelers.children <= 0}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Minus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                      <span className="w-8 text-center font-semibold">{travelers.children}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('children', true)}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Plus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                    </div>
                  </div>

                  {/* Rooms */}
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-semibold text-base sm:text-lg text-lucky-blue">Rooms</div>
                    </div>
                    <div className="flex items-center gap-3 rounded-full border border-portage">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('rooms', false)}
                        disabled={travelers.rooms <= 0}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Minus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                      <span className="w-8 text-center font-semibold">{travelers.rooms}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('rooms', true)}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Plus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                    </div>
                  </div>

                  {/* Pets */}
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-semibold text-base sm:text-lg text-lucky-blue">Pets</div>
                      <div className="text-sm text-portage">Bringing a service animal?</div>
                    </div>
                    <div className="flex items-center gap-3 rounded-full border border-portage">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('pets', false)}
                        disabled={travelers.pets <= 0}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Minus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                      <span className="w-8 text-center font-semibold">{travelers.pets}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateTravelers('pets', true)}
                        className="h-8 w-8 rounded-full p-0"
                      >
                        <Plus className="h-4 w-4" strokeWidth={3} />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-t">
                  <Button
                    onClick={() => setTravelersOpen(false)}
                    className="w-full rounded-full text-lg sm:text-xl bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]"
                    style={{height: "34px"}}
                  >
                    Update
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Date and Search Section */}
        <div className="lg:col-span-6">
          <div className='grid grid-cols-1 lg:grid-cols-6 gap-4 sm:gap-6 lg:gap-8 items-end'>
            {/* Date Field with react-date-range */}
            <div className="grid items-center gap-1.5 lg:col-span-4">
              <Label htmlFor="dateRange" className="text-cornflower text-sm sm:text-base">Check In - Check Out</Label>
              <div className="relative">
                <Button
                  id="date"
                  variant="outline"
                  className={cn(
                    "w-full text-sm sm:text-base lg:text-lg text-[#F2F3FA] bg-[#080236] border-[#B4BBE8] rounded-full justify-center font-normal hover:bg-[#B4BBE8]/10 hover:text-[#F2F3FA] hover:border-[#F2F3FA] transition-all duration-200 flex items-center gap-2",
                    !dateRange[0]?.startDate && "text-[#F2F3FA]/70",
                    dateError && dateRange[0]?.startDate >= dateRange[0]?.endDate && "border-red-500"
                  )}
                  style={{height: "34px"}}
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  <CalendarIcon className="h-4 w-4 text-[#F2F3FA] flex-shrink-0" />
                  <span className="truncate">
                    {dateRange[0]?.startDate ? (
                      dateRange[0]?.endDate ? (
                        <>
                          <span className="hidden sm:inline">
                            {format(dateRange[0].startDate, "LLL dd, y")} -{" "}
                            {format(dateRange[0].endDate, "LLL dd, y")}
                            {getNightsText()}
                          </span>
                          <span className="sm:hidden">
                            {format(dateRange[0].startDate, "MMM dd")} - {format(dateRange[0].endDate, "MMM dd")}
                            {getNightsText()}
                          </span>
                        </>
                      ) : (
                        <>
                          <span className="hidden sm:inline">{format(dateRange[0].startDate, "LLL dd, y")}</span>
                          <span className="sm:hidden">{format(dateRange[0].startDate, "MMM dd")}</span>
                        </>
                      )
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </span>
                </Button>

                {/* Date Range Picker */}
                {showCalendar && (
                  <div className="absolute top-full rounded-xl z-20 mt-2 shadow-md" ref={calendarRef}>
                    <DateRange
                      className="rounded-xl"
                      editableDateInputs={true}
                      onChange={handleSelect}
                      moveRangeOnFirstSelection={false}
                      showDateDisplay={false}
                      months={calendarMonths}
                      direction="horizontal"
                      retainEndDateOnFirstSelection={false}
                      minDate={new Date()}
                      ranges={dateRange}
                      rangeColors={["#4B4BC3"]}
                    />
                  </div>
                )}
              </div>
              
              {/* Reserved space for error message to prevent layout shift */}
              <div className="h-4 flex items-start">
                {dateError && dateRange[0]?.startDate >= dateRange[0]?.endDate && (
                  <div className="text-red-500 text-xs">{dateError}</div>
                )}
              </div>
            </div>
            
            {/* Search Button */}
            <div className='lg:col-span-2 p-1.5'>
              <Button
                className='w-full rounded-full text-lg sm:text-xl bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))] hover:bg-[linear-gradient(to_right,rgba(95,95,215,1),rgba(132,147,255,1),rgba(181,169,255,1))] hover:shadow-lg hover:scale-[1.02] transition-all duration-200'
                style={{height: "34px"}}
                onClick={handleSearch}
                disabled={isSearching}
              >
                {isSearching ? "Searching..." : "Search"}
              </Button>
              
              {/* Reserved space to match date field alignment */}
              <div className="h-4"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelSearchBar;