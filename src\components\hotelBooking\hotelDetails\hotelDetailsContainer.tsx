"use client"

import { useState, useRef, useMemo } from "react"
import { hotelData as constantHotelData } from "@/lib/hotel-data"
import PhotoGallery from "./photo-gallery"
import ReviewsModal from "./reviews-modal"
import MapModal from "./map-modal"
import HotelHeader from "./hotel-header"
import PhotoGalleryGrid from "./photo-gallery-grid"
import TabNavigation from "./tab-navigation"
import HotelDescription from "./hotel-description"
import HotelContactInfo from "./hotel-contact-info"
import ReviewsSummary from "./reviews-summary"
import TopAmenities from "./top-amenities"
import GuestPolicies from "./guest-policies"
import AboutHotel from "./about-hotel"
import AmenitiesList from "./amenities-list"
import { Hotel } from "@/lib/types"

interface HotelImage {
    title: string;
    path: string;
    order: number;
}

interface HotelPhone {
    label: string;
    number: string;
}

interface HotelFacility {
    label: string;
    group: string;
}

interface RoomDetail {
    description: string;
    type: string;
    character: string;
    minPax: number;
    maxPax: number;
    maxAdults: number;
    maxChildren: number;
    minAdults: number;
    facilities: HotelFacility[];
}

interface HotelData {
    name: string;
    description: string;
    address: string;
    postalCode: string;
    city: string;
    email: string;
    phones: HotelPhone[];
    website: string | null;
    img_base_url: string;
    img_base_thumbnail_url: string;
    images: HotelImage[];
    room_images: Record<string, HotelImage>;
    facilities: HotelFacility[];
    interest_points: any[];
    nearest_terminals: any[];
    room_details: Record<string, RoomDetail>;
}

interface HotelDetailsContainerProps {
    hotelcontent: HotelData;
    selectedHotel: Hotel | null
}

export default function HotelDetailsContainer({ hotelcontent, selectedHotel }: HotelDetailsContainerProps) {
    // State management
    const [showPhotos, setShowPhotos] = useState(false)
    const [showReviews, setShowReviews] = useState(false)
    const [showMap, setShowMap] = useState(false)
    const [showFullDescription, setShowFullDescription] = useState(false)
    const [activeTab, setActiveTab] = useState("overview")
    const [showAllAmenities, setShowAllAmenities] = useState(false)

    // Refs for scrolling to sections
    const amenitiesSectionRef = useRef<HTMLDivElement>(null)
    const policiesSectionRef = useRef<HTMLDivElement>(null)

    // Transform API data to component format
    const transformedData = useMemo(() => {
        // Transform images with full URLs
        const images = hotelcontent.images.map(img => ({
            ...img,
            url: `${hotelcontent.img_base_url}${img.path}`,
            thumbnail: `${hotelcontent.img_base_thumbnail_url}${img.path}`
        }));

        // Group facilities by type for amenities
        const facilitiesByGroup = hotelcontent.facilities.reduce((acc, facility) => {
            if (!acc[facility.group]) {
                acc[facility.group] = [];
            }
            acc[facility.group].push(facility.label);
            return acc;
        }, {} as Record<string, string[]>);

        // Get main phone number
        const mainPhone = hotelcontent.phones.find(phone =>
            phone.label === 'PHONEHOTEL' || phone.label === 'PHONEBOOKING'
        )?.number || hotelcontent.phones[0]?.number || '';

        // Create full address
        const fullAddress = `${hotelcontent.address}, ${hotelcontent.city} ${hotelcontent.postalCode}`;
        console.log('facilitiesByGroup====>', facilitiesByGroup);

        const topAmenities = (() => {
            if (facilitiesByGroup['Facilities']?.length > 0) {
                return facilitiesByGroup['Facilities'].slice(0, 6);
            }

            const firstGroupKey = Object.keys(facilitiesByGroup)[0];
            if (firstGroupKey && facilitiesByGroup[firstGroupKey]?.length > 0) {
                return facilitiesByGroup[firstGroupKey].slice(0, 6);
            }

            return [];
        })();

        return {
            name: hotelcontent.name,
            rating: selectedHotel?.rating,
            reviewCount: 128, // Default review count
            location: hotelcontent.city,
            price: selectedHotel?.min_rate,
            images: images,
            allImages: images, // All images for photo gallery
            description: hotelcontent.description.substring(0, 500) + (hotelcontent.description.length > 500 ? '...' : ''),
            fullDescription: hotelcontent.description,
            address: fullAddress,
            phone: mainPhone,
            website: hotelcontent.website || '',
            email: hotelcontent.email,
            mapImage: '/api/placeholder/600/300', // Placeholder map image
            topAmenities: topAmenities,
            amenities: facilitiesByGroup
        };
    }, [hotelcontent]);

    // Handle tab changes
    const handleTabChange = (tab: string) => {
        setActiveTab(tab)

        switch (tab) {
            case "reviews":
                setShowReviews(true)
                break
            case "policies":
                setTimeout(() => {
                    policiesSectionRef.current?.scrollIntoView({ behavior: "smooth" })
                }, 100)
                break
            case "amenities":
                setShowAllAmenities(true)
                setTimeout(() => {
                    amenitiesSectionRef.current?.scrollIntoView({ behavior: "smooth" })
                }, 100)
                break
            case "location":
                setShowMap(true)
                break
        }
    }

    // Toggle amenities display
    const toggleAmenities = () => {
        setShowAllAmenities(!showAllAmenities)
        if (!showAllAmenities) {
            setTimeout(() => {
                amenitiesSectionRef.current?.scrollIntoView({ behavior: "smooth" })
            }, 100)
        }
    }

    // Scroll to amenities section
    const scrollToAmenities = () => {
        setShowAllAmenities(true)
        setTimeout(() => {
            amenitiesSectionRef.current?.scrollIntoView({ behavior: "smooth" })
        }, 100)
    }

    return (
        <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-[#4B4BC3]">
            <div className="p-4 md:p-6">
                {/* Hotel Header */}
                <HotelHeader
                    name={transformedData.name}
                    rating={transformedData.rating}
                    reviewCount={transformedData.reviewCount}
                    location={transformedData.location}
                    price={parseFloat(selectedHotel?.min_rate || '0.0')}
                    currency={selectedHotel?.currency}
                    onReviewsClick={() => setShowReviews(true)}
                />

                {/* Photo Gallery Grid */}
                <PhotoGalleryGrid
                    images={transformedData.images}
                    onShowAllPhotos={() => setShowPhotos(true)}
                />

                {/* Tabs Navigation */}
                <TabNavigation activeTab={activeTab} onTabChange={handleTabChange} />

                {/* Content Area */}
                <div className="mt-6">
                    <div className="space-y-8">
                        {/* Hotel Description */}
                        <HotelDescription
                            description={transformedData.description}
                            fullDescription={transformedData.fullDescription}
                            showFullDescription={showFullDescription}
                            onToggleDescription={() => setShowFullDescription(!showFullDescription)}
                        />

                        {/* Hotel Contact Info */}
                        <HotelContactInfo
                            address={transformedData.address}
                            phone={transformedData.phone}
                            website={transformedData.website}
                            email={transformedData.email}
                            mapImage={transformedData.mapImage}
                            onShowMap={() => setShowMap(true)}
                        />

                        {/* Reviews and Amenities Cards */}
                        <div className="grid grid-cols-12 gap-6">
                            <ReviewsSummary
                                rating={transformedData.rating}
                                reviewCount={transformedData.reviewCount}
                                onShowReviews={() => setShowReviews(true)}
                            />
                            <TopAmenities
                                topAmenities={transformedData.topAmenities}
                                onShowAllAmenities={scrollToAmenities}
                            />
                        </div>

                        {/* Guest Policies */}
                        <GuestPolicies
                            ref={policiesSectionRef}
                            policies={constantHotelData.policies}
                        />

                        {/* About the Hotel */}
                        <AboutHotel
                            aboutHotel={constantHotelData.aboutHotel}
                            aboutLuxuryCollection={constantHotelData.aboutLuxuryCollection}
                        />

                        {/* Amenities List */}
                        <AmenitiesList
                            ref={amenitiesSectionRef}
                            amenities={transformedData.amenities}
                            showAllAmenities={showAllAmenities}
                            onToggleAmenities={toggleAmenities}
                        />
                    </div>
                </div>
            </div>

            {/* Modals */}
            {showPhotos && (
                <PhotoGallery
                    images={transformedData.images}
                    hotelName={transformedData.name}
                    onClose={() => setShowPhotos(false)}
                />
            )}

            {showReviews && (
                <ReviewsModal
                    reviews={constantHotelData.reviews}
                    hotelName={transformedData.name}
                    rating={transformedData.rating}
                    reviewCount={transformedData.reviewCount}
                    onClose={() => setShowReviews(false)}
                />
            )}

            {showMap && (
                <MapModal
                    hotelName={transformedData.name}
                    address={transformedData.address}
                    mapImage={transformedData.mapImage}
                    onClose={() => setShowMap(false)}
                />
            )}
        </div>
    )
}