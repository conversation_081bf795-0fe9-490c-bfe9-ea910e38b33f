// FlightSeatMap.tsx
import React, { useState, useEffect } from "react";
import { FlightSeatService } from "./FlightService";
import { SeatSelectionManager } from "./SeatSelectionManager";
import { Seat, FlightSegment } from "./types";
import {
  ChevronLeft,
  ChevronRight,
  PlaneTakeoffIcon,
  Users,
} from "lucide-react";
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
  Tab,
  TabGroup,
  TabList,
  TabPanels,
  TabPanel

} from "@headlessui/react";

interface FlightSeatMapProps {
  flight: FlightSegment;
  onSeatsSelected?: (selectedSeats: string[]) => void;
}

const flightOptions = [
  {
    label: "Connecting Flight",
    value: "maa-dxb",
    route: "Chennai (Madras) - (MAA) → DXB Dubai",
  },
  {
    label: "Connecting Flight",
    value: "dxb-lhr",
    route: "DXB Dubai → London, England (LHR)",
  },
];
const FlightConnectionsDropdown = () => {
  const [selectedFlight, setSelectedFlight] = useState(flightOptions[0]);

  return (
    <div className="w-full">
      <div className="relative font-proxima-nova rounded-md w-full p-px h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
        <Listbox value={selectedFlight} onChange={setSelectedFlight}>
          <div className="relative rounded-md">
            <ListboxButton className="w-full rounded-md focus:outline-none px-4 xs:px-2 py-2 bg-white text-left">
              {selectedFlight ? (
                <div
                  className={`flex items-center gap-2 px-4 xs:px-2 py-2 cursor-pointer  bg-white`}
                >
                  <PlaneTakeoffIcon className="w-5 h-5" />
                  <div className="flex flex-col">
                    <span className="font-medium text-sm xs:text-xs text-[#B4BBE8]">
                      Connecting Flight
                    </span>
                    <span className="text-[#080236] font-semibold text-base xs:text-sm">
                      {selectedFlight?.route}
                    </span>
                  </div>
                </div>
              ) : (
                <div>{"Select a Flight"}</div>
              )}
            </ListboxButton>
            <ListboxOptions className="bg-white absolute z-10 w-full max-h-60 overflow-auto focus:outline-none">
              {flightOptions.map((flight, index) => (
                <ListboxOption className="relative font-proxima-nova w-full p-px rounded-b-md h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm" key={index} value={flight}>
                  {({ active }) => (
                    <div
                      className={`flex items-start rounded-b-md gap-2 px-4 py-2 cursor-pointer ${
                        active ? "bg-[#E6E3FF]" : " bg-white"
                      }`}
                    >
                      <PlaneTakeoffIcon className="w-5 h-5 mt-1" />
                      <div className="flex flex-col text-sm">
                        <span className="font-medium">Connecting Flight</span>
                        <span className="text-gray-500">{flight.route}</span>
                      </div>
                    </div>
                  )}
                </ListboxOption>
              ))}
            </ListboxOptions>
          </div>
        </Listbox>
      </div>
    </div>
  );
};

const FlightSeatMap: React.FC<FlightSeatMapProps> = ({
  flight,
  onSeatsSelected,
}) => {
  const [seatMap, setSeatMap] = useState<Seat[]>([]);
  const [activeTraveler, setActiveTraveler] = useState(0);
  const [selectedSeatsByTraveler, setSelectedSeatsByTraveler] = useState<string[][]>(
    [[], []]
  );  
  const [selectionManager, setSelectionManager] =
    useState<SeatSelectionManager | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeSlide, setActiveSlide] = useState(0);

  useEffect(() => {
    try {
      const flightService = new FlightSeatService();
      const generatedSeatMap = flightService.getSeatMap(flight);
      setSeatMap(generatedSeatMap);
      setSelectionManager(new SeatSelectionManager(generatedSeatMap));
    } catch (err) {
      setError("Failed to load seat map for this flight");
      console.error(err);
    }
  }, [flight]);

  const handleSeatClick = (seatId: string, travelerIndex: number) => {
    if (!selectionManager) return;
    
    const currentTravelerSeat = selectionManager.getTravelerSeat(travelerIndex)
    if(currentTravelerSeat === seatId){
        selectionManager.deselectSeat(seatId, travelerIndex)
    } else {
        const allSelected = selectionManager.getAllSelectedSeats();
        if (allSelected.includes(seatId)) return; // already picked by another traveler
        selectionManager.selectSeat(seatId, travelerIndex);
    }
  
    // const travelerSeat = selectionManager.getTravelerSeat(travelerIndex);
    // if (travelerSeat === seatId) {
    //   selectionManager.deselectSeat(seatId, travelerIndex);
    // } else {
    // }
  
    setSeatMap([...seatMap]); // trigger re-render
  };

  const getSeatColor = (seat: Seat) => {
    switch (seat.status) {
      case "available":
        return "bg-green-300 hover:bg-green-200";
      case "selected":
        return "bg-[#B4BBE8]";
      case "booked":
        return "bg-red-300 cursor-not-allowed";
      case "blocked":
        return "bg-red-600 cursor-not-allowed";
      default:
        return "bg-white";
    }
  };

  const nextSlide = () => setActiveSlide(1);
  const prevSlide = () => setActiveSlide(0);

  const renderCabin = (cabinClass: string, travelerIndex: number) => {
    const cabinSeats = seatMap.filter((seat) => seat.type === cabinClass);
    if (cabinSeats.length === 0) return null;

    const rows = [...new Set(cabinSeats.map((seat) => seat.row))].sort(
      (a, b) => a - b
    );

    const seatLetters = [
      ...new Set(cabinSeats.map((seat) => seat.letter)),
    ].sort((a, b) => a.localeCompare(b));

    return (
      <div key={cabinClass} className="rotate-90">
        {/* Seat letters header */}
        <div className="flex justify-center ml-8">
          {seatLetters.map((letter) => (
            <div
              key={letter}
              className="w-8 m-1 text-center -rotate-90 font-semibold text-gray-600"
            >
              {letter}
            </div>
          ))}
        </div>

        {/* Seat rows - now horizontal */}
        <div className="flex flex-col-reverse items-center">
          {rows.map((row) => {
            const rowSeats = cabinSeats
              .filter((seat) => seat.row === row)
              .sort((a, b) => a.letter.localeCompare(b.letter));

            return (
              <div key={row} className="flex items-center">
                <div className="flex">
                  {rowSeats.map((seat) => (
                    <button
                      key={seat.id}
                      onClick={() => handleSeatClick(seat.id, travelerIndex)}
                      disabled={
                        seat.status === 'booked' ||
                        seat.status === 'blocked' ||
                        (
                          seat.status === 'selected' &&
                          selectionManager?.getTravelerSeat(travelerIndex) !== seat.id
                        )
                      }
                      className={`w-8 h-8 m-1 rounded-md -rotate-90 flex items-center justify-center text-xs font-semibold ${getSeatColor(seat)}`}
                      title={`${seat.id} - ${seat.features?.join(", ") || "Standard"}`}
                    >
                      {(seat.status === "selected" || seat.status === "booked")
                        ? `${seat.row}${seat.letter}`
                        : ""}
                    </button>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (error) {
    return <div className="text-red-500 p-4">{error}</div>;
  }

  if (seatMap.length === 0) {
    return <div className="p-4">Loading seat map...</div>;
  }

  return (
    <div className="md:max-w-xl sm:max-w-lg xs:max-w-xs font-proxima-nova mx-auto sm:h-max xs:h-max p-4 xs:p-0 relative">
      {/* <div className="relative font-proxima-nova w-full p-px rounded-xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm"> */}
      <div className="relative bg-white rounded-xl overflow-hidden">
        <div
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${activeSlide * 100}%)` }}
        >
          <div className="w-full flex flex-col gap-1 xs:h-max flex-shrink-0 p-5 xs:p-0">
            <div className="flex flex-col gap-2 justify-between items-center mb-2">
              <div className="text-xl text-center font-bold text-[#080236]">
                Outbound
              </div>
              <div className="flex flex-row gap-2">
                <button className="focus:outline-none" onClick={prevSlide}>
                  <ChevronLeft />
                </button>
                <div className="items-center w-full text-center text-2xl xs:text-sm text-[#1E1E76] font-semibold">
                  Chennai (Madras) to London
                </div>
                <button className="focus:outline-none" onClick={nextSlide}>
                  <ChevronRight />
                </button>
              </div>
              <div className="flex flex-row gap-2 text-[#080236] w-full items-center">
                <div>Round Trip</div>
                <div>Economy</div>
                <div className="w-max flex items-center gap-1">
                  <Users className="w-5 h-5" />2
                </div>
              </div>
            </div>
            <FlightConnectionsDropdown />

            <TabGroup>
              <TabList className="flex space-x-1 rounded-xl">
                {[...Array(2)].map((_, i) => (
                  <Tab
                    key={i}
                    className={({ selected }) =>
                      `w-full py-0.5 text-sm font-medium leading-5 ${
                        selected
                          ? "focus:outline-none text-[#1E1E76]"
                          : "text-[#707FF5] focus:outline-none"
                      }`
                    }
                  >
                    {`Traveler ${i + 1}`}
                  </Tab>
                ))}
              </TabList>
            <div className="flex w-full h-0.5 bg-[#B4BBE8]"></div>
            <TabPanels>
            {[...Array(2)].map((_, travelerIndex) => (
                <TabPanel key={travelerIndex}>
                    <div className="mt-2">
                    {["first", "business", "premium", "economy"].map((cabinClass) =>
                        renderCabin(cabinClass, travelerIndex)
                    )}
                    </div>
                </TabPanel>
                ))}
            </TabPanels>
            </TabGroup>

            {/* {["first", "business", "premium", "economy"].map((cabinClass) =>
              renderCabin(cabinClass)
            )} */}

            <div className="flex justify-between">
              <div className="flex gap-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-100 mr-2"></div>
                  <span>Available</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#B4BBE8] mr-2"></div>
                  <span>Selected</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-red-300 mr-2"></div>
                  <span>Booked</span>
                </div>
              </div>
            </div>
          </div>
          {/*Inbound Section*/}
          <div className="w-full flex flex-col gap-1 xs:h-max flex-shrink-0 p-5 xs:p-0">
          <div className="flex flex-col gap-2 justify-between items-center mb-2">
              <div className="text-xl text-center font-bold text-[#080236]">
                Inbound
              </div>
              <div className="flex flex-row gap-2">
                <button className="focus:outline-none" onClick={prevSlide}>
                  <ChevronLeft />
                </button>
                <div className="items-center w-full text-center text-2xl xs:text-sm text-[#1E1E76] font-semibold">
                  London to Chennai (Madras)
                </div>
                <button className="focus:outline-none" onClick={nextSlide}>
                  <ChevronRight />
                </button>
              </div>
              <div className="flex flex-row gap-2 text-[#080236] w-full items-center">
                <div>Round Trip</div>
                <div>Economy</div>
                <div className="w-max flex items-center gap-1">
                  <Users className="w-5 h-5" />2
                </div>
              </div>
            </div>
            <FlightConnectionsDropdown />

            <TabGroup>
              <TabList className="flex space-x-1 rounded-xl">
                {[...Array(2)].map((_, i) => (
                  <Tab
                    key={i}
                    className={({ selected }) =>
                      `w-full py-0.5 text-sm font-medium leading-5 ${
                        selected
                          ? "focus:outline-none text-[#1E1E76]"
                          : "text-[#707FF5] focus:outline-none"
                      }`
                    }
                  >
                    {`Traveler ${i + 1}`}
                  </Tab>
                ))}
              </TabList>
            <div className="flex w-full h-0.5 bg-[#B4BBE8]"></div>
            <TabPanels>
            {[...Array(2)].map((_, travelerIndex) => (
                <TabPanel key={travelerIndex}>
                    <div className="mt-2">
                    {["first", "business", "premium", "economy"].map((cabinClass) =>
                        renderCabin(cabinClass, travelerIndex)
                    )}
                    </div>
                </TabPanel>
                ))}
            </TabPanels>
            </TabGroup>

            <div className="flex justify-between">
              <div className="flex gap-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-100 mr-2"></div>
                  <span>Available</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-[#B4BBE8] mr-2"></div>
                  <span>Selected</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-red-300 mr-2"></div>
                  <span>Booked</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    // </div>
  );
};

export default FlightSeatMap;
