import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  simpleFadeIn,
  imageFadeIn,
  slideFadeZigzag,
  fadeIn,
  slideFadeIn,
  fadeInBlur,
} from "@/utils/motion";

interface Feature {
  id: number;
  title: string;
  image: string;
  description: string;
}

interface CardProps {
  title: string;
  image: string;
  description: string;
  delay: number;
  duration: number;
}

const features: Feature[] = [
  {
    id: 1,
    title: "50+ Languages Supported",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/50%2B-Languages-Supported%201.png",
    description:
      "Wherever you go, I speak the language. Chat in your native tongue, and I’ll understand you instantly!",
  },
  {
    id: 2,
    title: "Smart Reminders",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Smart-Reminders%201.png",
    description:
      "Your flight is boarding, your guide is waiting, and the park closes soon. I’ll keep you updated on every little detail.",
  },
  {
    id: 3,
    title: "Custom Plan with Local Favourites",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/MAp%201.png",
    description:
      "Share your vibe, and I’ll create an itinerary packed with must-see spots and hidden gems of your taste!",
  },
  {
    id: 4,
    title: "Collaboration Tools",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Collaboration%201.png",
    description:
      "Are you planning with friends? Let them contribute in real time and make your trip even more special.",
  },
  {
    id: 5,
    title: "Smart Budget & Expense Tracking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Budget%201.png",
    description:
      "Keep your spending in check while making the most of every experience on your journey.",
  },
  {
    id: 6,
    title: "Visa & Travel Docs",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Visa-%26-Documentation%201.png",
    description:
      "I’ll help you prepare the right documents and keep them safe in one place, ready whenever you need them.",
  },
  {
    id: 7,
    title: "Pack Smarter with Shasa",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Group%20427321282.png",
    description:
      "I’ll check the weather and tell you exactly what to pack so you're always travel-ready.",
  },
  {
    id: 8,
    title: "Easy Bookings",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/Group%20427321281.png",
    description:
      "Whether it's flights, trains, buses, or cruises, I’ll find you the best options in seconds!",
  },
  {
    id: 9,
    title: "Top Picks for You",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Homepage/Features/410551136_027b0a8e-9e86-4695-8230-ba8bc4c1739d%201.png",
    description:
      "From must-visit spots to thrilling activities, I’ll curate the best experiences and guide you with photos, maps, and reviews!",
  },
];

const styles = {
  card: {
    color: "#080236",
    borderRadius: "20px",
    backgroundImage:
      "linear-gradient(238.16deg, #1E1E76 0.56%, #4B4BC3 25.53%, #707FF5 50.5%, #A195F9 75.47%, #F2A1F2 100.44%)",
    backgroundOrigin: "border-box",
    boxShadow: "inset 0 100vw white",
    border: "1px solid transparent",
  },
};

const CardComponent: React.FC<CardProps> = ({
  title,
  image,
  description,
  delay,
  duration,
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="show"
      viewport={{ once: false, amount: 0.2 }}
      variants={fadeInBlur(delay, duration)}
      style={styles.card}
      className="relative h-full bg-gradient-to-b"
    >
      {/* Inner Content with White Background */}
      <div className="lg:p-5 md:p-2 sm:p-2 xs:p-2 h-full justify-center items-center rounded-3xl bg-brand-whites">
        <div className="flex flex-col h-full gap-5 justify-around items-center">
          <div className="xl:text-3xl font-proxima-nova lg:text-xl md:text-base sm:text-sm w-[90%] mx-auto font-bold text-center">
            {title}
          </div>
          <div>
            <img
              className="xl:w-full xl:h-48 lg:h-36 md:h-28 sm:h-16 xs:h-20 p-2"
              src={image}
            />
          </div>
          <div className="flex font-proxima-nova xl:w-[80%] md:w-[95%] sm:w-[95%] xs:w-[95%] text-[#080236] xl:text-lg lg:text-sm md:text-xs sm:text-xs xs:text-xs mx-auto text-center">
            {description}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const Features = () => {
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  useEffect(() => {
    // Check screen size on the client side
    const handleResize = () => {
      if (typeof window !== "undefined") {
        setIsLargeScreen(window.innerWidth >= 1280); // Tailwind xl breakpoint
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return (
    <section className="flex flex-col w-full min-h-screen bg-brand-white py-10 xs:py-5">
      <div className="flex flex-col md:gap-6 sm:gap-4 xs:gap-4 w-[85%] mx-auto items-center py-16 xs:py-4 sm:py-8">
        <motion.div
          initial="hidden"
          whileInView="show"
          variants={fadeIn("", "", 0.5, 0.5)}
          className="flex font-proxima-nova xl:w-[70%] lg:w-[80%] md:w-[80%] sm:w-[80%] text-brand-black leading-snug mx-auto text-center lg:text-5xl md:text-4xl sm:text-3xl xs:text-2xl font-bold"
        >
          I’ve Got Everything You Need for Your Next Adventure
        </motion.div>
        <motion.div
          initial="hidden"
          whileInView="show"
          variants={fadeIn("", "", 0.5, 0.75)}
          className="flex font-proxima-nova xl:w-[60%] lg:w-[75%] text-brand-black md:text-base sm:text-xs xs:text-xs text-center"
        >
          Since I’m your personal travel assistant, your itinerary mentor, your
          pocket-sized guide, I’ve some great features which makes me your best
          travel companion. Here’s what I can do:
        </motion.div>
        <div className="xs:hidden flex w-full justify-center items-center py-10 sm:py-5">
          <div className="grid grid-flow-row sm:grid-cols-3 xs:grid-cols-2 place-items-center items-center xl:gap-10 lg:gap-5 md:gap-4 sm:gap-4 xs:gap-5">
            {features.map((feature, index) => (
              <CardComponent
                key={feature.id}
                title={feature.title}
                image={feature.image}
                description={feature.description}
                delay={index * 0.05}
                duration={index * 0.2}
              />
            ))}
          </div>
        </div>
        <div className="xs:flex sm:hidden w-full justify-center items-center py-10 sm:py-5">
          <div className="grid sm:grid-cols-2 xs:grid-cols-2 place-items-center items-center xl:gap-10 lg:gap-5 md:gap-4 sm:gap-4 xs:gap-5 w-full">
            {features.map((feature, index) => (
              <div
                key={feature.id}
                className={`w-full h-full flex ${features.length % 2 === 1 && index === features.length - 1
                  ? "justify-center col-span-full" // Center last item if odd count
                  : ""
                  }`}
              >
                <CardComponent
                  title={feature.title}
                  image={feature.image}
                  description={feature.description}
                  delay={index * 0.05}
                  duration={index * 0.2}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="flex py-10 xs:py-5 font-proxima-nova">
        <div className="flex flex-row w-[85%] h-72 xs:h-auto p-5 xs:p-8 mx-auto bg-[#1E1E76] rounded-2xl group transition-all duration-500 ease-in-out hover:w-[100%]">
          <div
            className={`${isLargeScreen ? "visible flex w-[90%] mx-auto" : "flex flex-row xs:flex xs:flex-col xs:items-center gap-4"} group-hover:w-[80%] group-hover:mx-auto `}
          >
            <div className="flex flex-col font-proxima-nova justify-center gap-4 lg:w-2/3 md:w-1/2 sm:w-1/2 xs:w-full">
              <motion.div
                initial={isLargeScreen ? "hidden" : "show"}
                whileInView={isLargeScreen ? "show" : "show"}
                variants={isLargeScreen ? simpleFadeIn(0, 2) : {}}
                className="2xl:text-5xl xl:text-5xl lg:text-4xl md:text-2xl sm:text-2xl xs:text-2xl font-bold leading-tight text-white"
              >
                Are You Too Busy to Type? <br />
                Just Talk to Me!
              </motion.div>
              <motion.div
                initial={isLargeScreen ? "hidden" : "show"}
                whileInView={isLargeScreen ? "show" : "show"}
                variants={isLargeScreen ? simpleFadeIn(0, 2) : {}}
                className="text-white 2xl:w-[75%] xl:w-[72%] lg:w-[80%] md:w-[95%] sm:w-[95%] 2xl:text-base xl:text-sm lg:text-sm md:text-sm sm:text-sm xs:text-sm"
              >
                Why type when you can just talk? Planning your trip is now as
                easy as having a quick
                {/* {isLargeScreen && (<br />)} */}
                chat with me! Just say the word, and I’ll whip up your itinerary
                in no time. Even if you need any last-minute changes, just tell
                me - I’m all ears!
                {/* {isLargeScreen && (<br />)} */}
                And hey, I bet my voice is pretty nice to hear too!
              </motion.div>
            </div>
            <div className="flex lg:w-1/3 md:w-1/2 sm:w-1/2 xs:w-4/5">
              <motion.img
                initial={isLargeScreen ? "hidden" : "show"}
                whileInView={isLargeScreen ? "show" : "show"}
                variants={imageFadeIn(0, 2)}
                className="w-full object-contain"
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Banners/Are-You-Too-Busy-to-Type-Just-Talk-to-Me%202.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
