import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import InputField from "@/components/input/InputField";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/input/Select";
import { User } from "@/constants/user";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ChevronDown, Loader2, CalendarIcon } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { agentPostMethod } from "@/utils/api";
import { debounce } from "lodash";
import { Calendar } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { useCustomSession } from "@/hooks/use-custom-session";

interface PassengerFormData {
  id?: string;
  title: string;
  firstName: string;
  middleName: string;
  lastName: string;
  nationality: string;
  gender: string;
  dob: string;
  email: string;
  mobile: string;
  travelerType: TravelerType | string;
  preferredSeat: SeatPreference | string;
  mealPreference: MealPreference | string;
  cabinClass: CabinClass | string;
  frequentFlyerNumber: string;
  passportNumber: string;
  passportExpiry: string;
  passportCountry: string;
  aadharNumber: string;
  [key: string]: any;
}

interface UnifiedPassengerProfileFormProps {
  profile?: PassengerFormData;
  user: User | null;
  onSave: (formData: PassengerFormData) => Promise<void> | void;
  onCancel: () => void;
  isEdit?: boolean;
}

const titles = ["Mr", "Ms", "Mrs", "Dr"];
const genders = ["Male", "Female", "Other"];
// Define enum types
type TravelerType = "adult" | "child" | "infant";
type SeatPreference = "window" | "aisle" | "middle";
type MealPreference = "vegetarian" | "non-vegetarian" | "no-preference";
type CabinClass = "economy" | "premium_economy" | "business" | "first";

// Define enum values for API
const travelerTypes: TravelerType[] = ["adult", "child", "infant"];
const seatPreferences: SeatPreference[] = ["window", "aisle", "middle"];
const mealPreferences: MealPreference[] = [
  "vegetarian",
  "non-vegetarian",
  "no-preference",
];
const cabinClasses: CabinClass[] = [
  "economy",
  "premium_economy",
  "business",
  "first",
];

// Define display labels for UI
const travelerTypeLabels: Record<TravelerType, string> = {
  adult: "Adult",
  child: "Child",
  infant: "Infant",
};

const seatPreferenceLabels: Record<SeatPreference, string> = {
  window: "Window",
  aisle: "Aisle",
  middle: "Middle",
};

const mealPreferenceLabels: Record<MealPreference, string> = {
  vegetarian: "Vegetarian",
  "non-vegetarian": "Non-Vegetarian",
  "no-preference": "No Preference",
};

const cabinClassLabels: Record<CabinClass, string> = {
  economy: "Economy",
  premium_economy: "Premium Economy",
  business: "Business",
  first: "First",
};
const countries = [
  "American",
  "Indian",
  "British",
  "Canadian",
  "Australian",
  "Singaporean",
  "UAE",
  "Other",
];

// This type is simplified for the example but should match your actual implementation
interface CountryState {
  search: string;
  loading: boolean;
  countryList: any[];
}

interface Country {
  code: string;
  name: string;
}

const UnifiedPassengerProfileForm: React.FC<
  UnifiedPassengerProfileFormProps
> = ({ profile, user, onSave, onCancel, isEdit = false }) => {
  // Initialize with empty values or existing profile values
  const defaultProfile: PassengerFormData = {
    title: "",
    firstName: "",
    middleName: "",
    lastName: "",
    nationality: "",
    gender: "",
    dob: "",
    email: "",
    mobile: "",
    travelerType: "",
    preferredSeat: "",
    mealPreference: "",
    cabinClass: "",
    frequentFlyerNumber: "",
    passportNumber: "",
    passportExpiry: "",
    passportCountry: "",
    aadharNumber: "",
  };

  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const [formData, setFormData] = useState<PassengerFormData>(
    profile || defaultProfile
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Toggle states for expandable sections
  const [showPassportForm, setShowPassportForm] = useState(
    !!formData.passportNumber
  );

  // Added for nationality select like ProfileDetails component
  const [country, setCountry] = useState<CountryState>({
    search: "",
    loading: false,
    countryList: [],
  });
  const [open, setOpen] = useState(false);

  // Added for passport country select
  const [passportCountry, setPassportCountry] = useState<CountryState>({
    search: "",
    loading: false,
    countryList: [],
  });
  const [passportCountryOpen, setPassportCountryOpen] = useState(false);

  // Added state for DOB calendar
  const [dobCalendarOpen, setDobCalendarOpen] = useState(false);
  const [passportExpiryCalendarOpen, setPassportExpiryCalendarOpen] =
    useState(false);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Auto-populate form fields from user data if available and not in edit mode
  useEffect(() => {
    if (user && !isEdit && !profile) {
      setFormData((prevData) => ({
        ...prevData,
        // Don't auto-populate profile fields with user info - let the user enter the passenger details
        // We only want to use the user's email for contact info
        email: user.email || "",
      }));
    }
  }, [user, isEdit, profile]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Save passport details
  const handlePassportSave = () => {
    setShowPassportForm(true);
  };

  // Handlers for nationality similar to ProfileDetails
  const debouncedHandleCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod(
              "flight/suggest-country",
              { query },
              token ?? ""
            );
            setCountry({
              search: query,
              loading: false,
              countryList: response?.detail?.data || [],
            });
          } catch (error) {
            console.log("Api error", error);
          }
        }
      }, 500),
    []
  );

  // Handlers for passport country
  const debouncedHandlePassportCountry = useMemo(
    () =>
      debounce(async (query: string) => {
        if (query !== "") {
          try {
            const response = await agentPostMethod(
              "flight/suggest-country",
              { query },
              token ?? ""
            );
            setPassportCountry({
              search: query,
              loading: false,
              countryList: response?.detail?.data || [],
            });
          } catch (error) {
            console.log("Api error", error);
          }
        }
      }, 500),
    []
  );

  const handleCountry = (query: string) => {
    setCountry({ ...country, loading: true, search: query });
    debouncedHandleCountry(query);
  };

  const handlePassportCountry = (query: string) => {
    setPassportCountry({ ...passportCountry, loading: true, search: query });
    debouncedHandlePassportCountry(query);
  };

  // Submit the form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const errors: { [key: string]: string } = {};
    if (!formData.lastName.trim()) errors.lastName = "Last name is required";
    if (!formData.email.trim()) errors.email = "Email is required";

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setFormErrors({});

    setLoading(true);
    setError(null);

    try {
      // Show success message
      setSuccessMessage(
        isEdit
          ? "Passenger profile updated successfully!"
          : "Passenger profile created successfully!"
      );

      // Pass the form data to the parent component
      setTimeout(() => {
        onSave(formData);
      }, 1000);
    } catch (err: any) {
      setError("Failed to save passenger profile");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Custom styles for responsive Calendar */}
      <style jsx>{`
        :global(.rdrCalendarWrapper) {
          width: 100% !important;
          max-width: 100vw !important;
        }
        :global(.rdrMonth) {
          width: 100% !important;
        }
        :global(.rdrWeekDays) {
          display: grid !important;
          grid-template-columns: repeat(7, 1fr) !important;
          gap: 0 !important;
        }
        :global(.rdrDays) {
          display: grid !important;
          grid-template-columns: repeat(7, 1fr) !important;
          gap: 0 !important;
        }
        :global(.rdrDay) {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          height: 40px !important;
          width: 100% !important;
          min-width: 40px !important;
        }
        :global(.rdrWeekDay) {
          text-align: center !important;
          padding: 8px 4px !important;
          font-size: 12px !important;
          font-weight: 500 !important;
        }
        :global(.rdrMonthAndYearWrapper) {
          font-size: 0.875rem !important;
        }
        @media (min-width: 640px) {
          :global(.rdrMonthAndYearWrapper) {
            font-size: 1rem !important;
          }
        }
        @media (min-width: 1024px) {
          :global(.rdrCalendarWrapper) {
            max-width: 350px !important;
          }
          :global(.rdrMonth) {
            margin: 0 !important;
          }
        }
      `}</style>

      <div className="p-4 md:p-6 space-y-6 md:space-y-10">
        <h2 className="text-xl md:text-3xl font-semibold text-brand-black mb-4 md:mb-6">
          {isEdit ? "Edit Passenger Profile" : "Add New Passenger Profile"}
        </h2>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
            {successMessage}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-brand-black mb-4">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-6 mb-4">
              <div>
                <label className="font-semibold text-brand-black mb-2 block">
                  Title
                </label>
                <Select
                  value={formData.title}
                  onValueChange={(value) => handleSelectChange("title", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Title" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {titles.map((t) => (
                      <SelectItem key={t} value={t}>
                        {t}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
              <InputField
                label="First Name"
                placeholder="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
              />
              <InputField
                label="Middle Name"
                placeholder="Middle Name"
                name="middleName"
                value={formData.middleName}
                onChange={handleInputChange}
              />
              <div>
                <InputField
                  label="Last Name"
                  placeholder="Last Name"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                />
                {formErrors.lastName && (
                  <p className="text-sm text-red-500 mt-1">
                    {formErrors.lastName}
                  </p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
              {/* Replaced static nationality select with the dynamic one from ProfileDetails */}
              <div>
                <label className="font-semibold text-[#1E1E76] block">
                  Nationality
                </label>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      className="w-full text-neutral-dark hover:text-neutral-dark hover:bg-brand-white justify-between  px-4 py-5 bg-white rounded-sm text-left font-normal border border-neutral flex"
                    >
                      {formData.nationality || "Select country"}
                      <ChevronDown className="h-4 w-4 shrink-0 text-neutral-dark stroke-[2.5]" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-brand-white">
                    <Command className="rounded-sm shadow-none">
                      <CommandInput
                        placeholder="Search country..."
                        onValueChange={handleCountry}
                      />
                      {country.loading ? (
                        <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      ) : (
                        <>
                          <CommandEmpty>No country found.</CommandEmpty>
                          <CommandGroup>
                            {country.countryList.map(
                              (item: any, index: number) => (
                                <CommandItem
                                  key={item.code}
                                  value={item.name}
                                  onSelect={() => {
                                    handleSelectChange(
                                      "nationality",
                                      item.name
                                    );
                                    setOpen(false);
                                  }}
                                  className="data-[selected=true]:text-brand-black data-[selected=true]:bg-neutral aria-selected:bg-neutral aria-selected:text-brand-black data-[highlighted=true]:bg-neutral data-[highlighted=true]:text-brand-black hover:bg-neutral hover:text-brand-black"
                                >
                                  {item.name}
                                </CommandItem>
                              )
                            )}
                          </CommandGroup>
                        </>
                      )}
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <label className="font-semibold text-brand-black block">
                  Gender
                </label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => handleSelectChange("gender", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Gender" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {genders.map((g) => (
                      <SelectItem key={g} value={g}>
                        {g}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date of Birth Calendar (from ProfileDetails) */}
              <div>
                <label className="font-semibold text-brand-black block">
                  Date of Birth
                </label>
                <Popover
                  open={dobCalendarOpen}
                  onOpenChange={setDobCalendarOpen}
                >
                  <PopoverTrigger asChild>
                    <div className="text-neutral-dark border-[1px] border-neutral p-px rounded-sm bg-brand-white hover:bg-brand-white hover:text-neutral-dark shadow-sm w-full">
                      <Button
                        type="button"
                        variant="ghost"
                        className={cn(
                          "w-full justify-start px-2 py-5 bg-brand-white hover:bg-brand-white rounded-sm text-left font-normal text-neutral-dark hover:text-neutral-dark ",
                          !formData.dob && "text-neutral-dark"
                        )}
                      >
                        {formData.dob ? (
                          format(new Date(formData.dob), "PPP")
                        ) : (
                          <span>Select date of birth</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-auto p-0 bg-[#F2F3FA]"
                    align="start"
                  >
                    <Calendar
                      className="rounded-xl font-proxima-nova w-full [&_.rdrCalendarWrapper]:!w-full [&_.rdrMonth]:!w-full [&_.rdrWeekDays]:!grid [&_.rdrWeekDays]:!grid-cols-7 [&_.rdrDays]:!grid [&_.rdrDays]:!grid-cols-7 [&_.rdrDay]:!flex [&_.rdrDay]:!items-center [&_.rdrDay]:!justify-center [&_.rdrDay]:!h-10 [&_.rdrDay]:!w-10 [&_.rdrMonthAndYearWrapper]:!text-sm [&_.rdrMonthAndYearWrapper]:sm:!text-base [&_.rdrWeekDay]:!text-xs [&_.rdrWeekDay]:!text-center [&_.rdrWeekDay]:!p-2"
                      onChange={(date) => {
                        setFormData((prev) => ({
                          ...prev,
                          dob: date.toISOString(),
                        }));
                        setDobCalendarOpen(false);
                      }}
                      maxDate={new Date()}
                      minDate={new Date("1900-01-01")}
                      date={formData.dob ? new Date(formData.dob) : new Date()}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold text-brand-black mb-4">
              Contact Info
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <div>
                <InputField
                  label="Primary Email"
                  placeholder="Email id"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                />
                {formErrors.email && (
                  <p className="text-sm text-red-500 mt-1">
                    {formErrors.email}
                  </p>
                )}
              </div>
              <InputField
                label="Mobile Number"
                placeholder="+91 12345 67890"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* Travel Preferences */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-brand-black mb-4">
              Travel Preferences
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <div>
                <label className="font-semibold text-brand-black mb-2 block">
                  Traveler Type
                </label>
                <Select
                  value={formData.travelerType}
                  onValueChange={(value) =>
                    handleSelectChange("travelerType", value as TravelerType)
                  }
                >
                  <SelectTrigger className="whitespace-nowrap w-[40vw]">
                    <SelectValue placeholder="Choose Traveler Type" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {travelerTypes.map((t) => (
                      <SelectItem key={t} value={t}>
                        {travelerTypeLabels[t]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="hidden md:block"></div>
              <div className="hidden md:block"></div>
              <div>
                <label className="font-semibold text-brand-black mb-2 block">
                  Preferred Seat
                </label>
                <Select
                  value={formData.preferredSeat}
                  onValueChange={(value) =>
                    handleSelectChange("preferredSeat", value as SeatPreference)
                  }
                >
                  <SelectTrigger className="whitespace-nowrap w-[40vw]">
                    <SelectValue placeholder="Choose Your Preferred Seat" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {seatPreferences.map((s) => (
                      <SelectItem key={s} value={s}>
                        {seatPreferenceLabels[s]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="hidden md:block"></div>
              <div className="hidden md:block"></div>
              <div>
                <label className="font-semibold text-brand-black mb-2 block">
                  Meal Preference
                </label>
                <Select
                  value={formData.mealPreference}
                  onValueChange={(value) =>
                    handleSelectChange(
                      "mealPreference",
                      value as MealPreference
                    )
                  }
                >
                  <SelectTrigger className="whitespace-nowrap w-[40vw]">
                    <SelectValue placeholder="Choose Your Meal Preference" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {mealPreferences.map((m) => (
                      <SelectItem key={m} value={m}>
                        {mealPreferenceLabels[m]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="hidden md:block"></div>
              <div className="hidden md:block"></div>
              {/* Frequent Flyer Section - Removed Add button */}
              <div className="whitespace-nowrap w-[40vw]">
                <InputField
                  label="Frequent Flyer Program & Number"
                  placeholder="Enter Your Frequent Flyer Program & Number"
                  name="frequentFlyerNumber"
                  value={formData.frequentFlyerNumber}
                  onChange={handleInputChange}
                />
              </div>
              <div className="hidden md:block"></div>
              <div className="hidden md:block"></div>
              <div className="whitespace-nowrap w-[40vw]">
                <label className="font-semibold text-brand-black mb-2 block">
                  Default Cabin Class
                </label>
                <Select
                  value={formData.cabinClass}
                  onValueChange={(value) =>
                    handleSelectChange("cabinClass", value as CabinClass)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose Your Default Cabin Class" />
                  </SelectTrigger>
                  <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
                    {cabinClasses.map((c) => (
                      <SelectItem key={c} value={c}>
                        {cabinClassLabels[c]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Documents */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-brand-black mb-4">
              Documents
            </h3>

            {/* Passport Section */}
            <div className="mb-6 md:mb-8">
              {showPassportForm ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
                    <InputField
                      label="Passport Number"
                      placeholder="Enter Your Passport Number"
                      name="passportNumber"
                      value={formData.passportNumber}
                      onChange={handleInputChange}
                    />
                    {/* Passport Expiry Date Calendar */}
                    <div>
                      <label className="font-semibold text-brand-black block">
                        Passport Expiry Date
                      </label>
                      <Popover
                        open={passportExpiryCalendarOpen}
                        onOpenChange={setPassportExpiryCalendarOpen}
                      >
                        <PopoverTrigger asChild>
                          <div className="text-neutral-dark p-px rounded-sm bg-brand-white border-[1px] border-neutral hover:bg-brand-white hover-text-neutral-dark shadow-sm w-full">
                            <Button
                              type="button"
                              variant="ghost"
                              className={cn(
                                "w-full justify-start px-2 py-5 bg-brand-white hover:bg-brand-white rounded-sm text-left font-normal text-neutral-dark hover:text-neutral-dark ",
                                !formData.passportExpiry && "text-neutral-dark"
                              )}
                            >
                              {formData.passportExpiry ? (
                                format(new Date(formData.passportExpiry), "PPP")
                              ) : (
                                <span>Select passport expiry date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </div>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0 bg-[#F2F3FA]"
                          align="start"
                        >
                          <Calendar
                            className="rounded-xl font-proxima-nova w-full [&_.rdrCalendarWrapper]:!w-full [&_.rdrMonth]:!w-full [&_.rdrWeekDays]:!grid [&_.rdrWeekDays]:!grid-cols-7 [&_.rdrDays]:!grid [&_.rdrDays]:!grid-cols-7 [&_.rdrDay]:!flex [&_.rdrDay]:!items-center [&_.rdrDay]:!justify-center [&_.rdrDay]:!h-10 [&_.rdrDay]:!w-10 [&_.rdrMonthAndYearWrapper]:!text-sm [&_.rdrMonthAndYearWrapper]:sm:!text-base [&_.rdrWeekDay]:!text-xs [&_.rdrWeekDay]:!text-center [&_.rdrWeekDay]:!p-2"
                            onChange={(date) => {
                              setFormData((prev) => ({
                                ...prev,
                                passportExpiry: date.toISOString(),
                              }));
                              setPassportExpiryCalendarOpen(false);
                            }}
                            minDate={new Date()}
                            date={
                              formData.passportExpiry
                                ? new Date(formData.passportExpiry)
                                : new Date()
                            }
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    {/* Replacing static country select with dynamic country select */}
                    <div>
                      <label className="font-semibold text-brand-black block">
                        Issuing Country
                      </label>
                      <Popover
                        open={passportCountryOpen}
                        onOpenChange={setPassportCountryOpen}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="ghost"
                            className="w-full text-neutral-dark justify-between px-4 py-5 bg-brand-white  hover:bg-brand-white hover:text-neutral-dark rounded-sm text-left font-normal border border-neutral flex"
                          >
                            {formData.passportCountry || "Select country"}
                            <ChevronDown className="h-4 w-4 shrink-0 text-[#080236] stroke-[2.5]" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                          <Command className="bg-brand-white rounded-sm shadow-none">
                            <CommandInput
                              placeholder="Search country..."
                              onValueChange={handlePassportCountry}
                            />
                            {passportCountry.loading ? (
                              <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Loading...
                              </div>
                            ) : (
                              <>
                                <CommandEmpty>No country found.</CommandEmpty>
                                <CommandGroup>
                                  {passportCountry.countryList.map(
                                    (item: Country) => (
                                      <CommandItem
                                        key={item.code}
                                        value={item.name}
                                        onSelect={() => {
                                          setFormData({
                                            ...formData,
                                            passportCountry: item.name,
                                          });
                                          setPassportCountryOpen(false);
                                        }}
                                        className="data-[selected=true]:text-brand-black data-[selected=true]:bg-neutral aria-selected:bg-neutral aria-selected:text-white data-[highlighted=true]:bg-neutral data-[highlighted=true]:text-brand-white hover:bg-neutral hover:text-brand-black"
                                      >
                                        {item.name}
                                      </CommandItem>
                                    )
                                  )}
                                </CommandGroup>
                              </>
                            )}
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                  <div className="flex-1">
                    <InputField
                      label="Passport"
                      placeholder="Passport Details"
                      disabled
                    />
                  </div>
                  <div className="md:mt-6">
                    <Button
                      className="rounded-[8px] bg-brand text-white hover:bg-brand hover:text-white px-6 w-full md:w-auto"
                      onClick={handlePassportSave}
                      type="button"
                    >
                      + Add
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Aadhar/PAN Section - Removed Add button */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <div>
                <InputField
                  label="Aadhar / PAN"
                  placeholder="Enter Your Aadhar / PAN Number"
                  name="aadharNumber"
                  value={formData.aadharNumber}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex w-full justify-center mt-10 gap-4">
            <Button
              type="submit"
              icon="save"
              className="rounded-[8px] bg-brand text-white hover:bg-brand hover:text-white px-8 py-2 font-semibold w-full md:w-auto"
              disabled={loading}
            >
              {loading ? "Saving..." : isEdit ? "Update" : "Save"}
            </Button>
            <Button
              type="button"
              className="px-8 py-2 rounded-[8px] bg-brand text-white hover:bg-brand hover:text-white text-lg font-semibold "
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </>
  );
};

export default UnifiedPassengerProfileForm;
