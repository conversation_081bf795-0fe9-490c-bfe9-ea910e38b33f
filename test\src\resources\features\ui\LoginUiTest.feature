@regression
@ui
Feature: Login tests

	Background: Start from a Home page
		Given The user starts from the "ui.nxvoy.appUrl" page
		And The user clicks on the Sign in button

	Scenario: Login success
		Given The user types the "nxvoy.users.standard" username on the login page
		And The user types the "nxvoy.users.standard" password on the login page
		When The user clicks on the login button
		Then the user should be logged in

	Scenario: Login failure
		Given The user types the "nxvoy.users.invalid" username on the login page
		And The user types the "nxvoy.users.standard" password on the login page
		When The user clicks on the login button
		# Then The user is on the "sample.home" page
		# And The login failure message is visible