"use client"

interface BaggageSectionProps {
    tripSummaryDetails: any
    setBaggageModal: (open: boolean) => void
}

const BaggageSection = ({ tripSummaryDetails, setBaggageModal }: BaggageSectionProps) => {
    return (
        <div className="flex flex-col gap-2">
            <div className="text-brand-black xs:w-full xs:text-center xs:justify-center font-semibold text-2xl">
                Baggage Information
            </div>
            <div className="relative font-proxima-nova w-full mt-2 p-px rounded-2xl h-auto border-1 border-brand-grey shadow-sm">
                <div className="flex flex-col justify-between w-full 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-3 xs:p-3  rounded-2xl shadow-md bg-brand-white relative">
                    <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-3">
                            <img
                                src={tripSummaryDetails?.selectedOutboundFlight?.segments?.[0]?.operator_logo || "/placeholder.svg"}
                                alt="British Airways"
                                className="w-6 h-6 rounded-full object-contain"
                            />
                            <span className="font-semibold text-base text-brand-black">
                                {tripSummaryDetails?.selectedOutboundFlight?.airline} Airways
                            </span>
                        </div>

                        <div className="text-sm font-semibold text-brand-black underline cursor-pointer">
                            <a href="#" target="_blank" rel="noopener noreferrer">
                                Airline baggage fees information{" "}
                            </a>
                        </div>
                        <div>
                            {tripSummaryDetails?.selectedLuggageInfo?._outward.length > 0 && (
                                <>
                                    <div className="text-[#080236] font-semibold text-base">Outbound Baggage</div>
                                    <ul>
                                        {tripSummaryDetails?.selectedLuggageInfo?._outward.map((item: any, index: number) => {
                                            const luggageInfo = tripSummaryDetails?.luggageOptions?._outward.find(
                                                (luggage: any) => luggage.option_value === item,
                                            )
                                            return (
                                                <li key={index} className="text-sm text-[#707FF5] flex items-center gap-1">
                                                    <span className="text-[#24C72F]">✔</span>
                                                    <span>{luggageInfo?.label}</span>
                                                </li>
                                            )
                                        })}
                                    </ul>
                                </>
                            )}

                            {tripSummaryDetails?.selectedLuggageInfo?._return.length > 0 && (
                                <>
                                    <div className="text-[#080236] font-semibold text-base">Inbound Baggage</div>
                                    <ul>
                                        {tripSummaryDetails?.selectedLuggageInfo?._return.map((item: any, index: number) => {
                                            const luggageInfo = tripSummaryDetails?.luggageOptions?._return.find(
                                                (luggage: any) => luggage.option_value === item,
                                            )
                                            return (
                                                <li key={index} className="text-sm text-[#707FF5] flex items-center gap-1">
                                                    <span className="text-[#24C72F]">✔</span>
                                                    <span>{luggageInfo?.label}</span>
                                                </li>
                                            )
                                        })}
                                    </ul>
                                </>
                            )}

                            {tripSummaryDetails?.selectedLuggageInfo?._outward.length === 0 &&
                                tripSummaryDetails?.selectedLuggageInfo?._return.length === 0 && (
                                    <div className="text-[#080236] font-semibold text-base">No baggage selected</div>
                                )}
                        </div>
                        {/* <div className="flex border border-[#B4BBE8] w-[35%] xs:hidden"></div> */}
                        <div className="flex w-[85%] xs:w-full">
                            <p className="text-sm text-brand-grey mt-1 leading-relaxed">
                                Baggage allowance and fee amounts are not guaranteed and are subject to change by the airline. Be sure
                                to verify the actual fees with your airline(s) before you travel.
                            </p>
                        </div>
                    </div>
                    <div className="flex w-full h-0.5 bg-brand-grey"></div>
                    <div className="flex flex-row xs:flex-col xs:gap-1 w-full xs:justify-center xs:items-center justify-between py-4">
                        <div className="flex flex-row xs:flex-col w-2/3 xs:w-full xs:text-center items-center gap-5">
                            <img
                                src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Baggage%20Blue.png"
                                alt=""
                                className="w-5 h-6"
                            />
                            <div className="text-brand-black w-full">
                                <strong>Got excess baggage?</strong> Don't stress, buy extra check-in baggage allowance at fab rates!
                            </div>
                        </div>
                        <div className="w-1/3 xs:w-full flex xs:justify-center justify-end">
                            <button
                                onClick={() => setBaggageModal(true)}
                                className="px-4 py-2 w-max h-max text-base bg-brand-white border-2 border-brand-black font-bold rounded-lg"
                            >
                                Add Baggage
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default BaggageSection
