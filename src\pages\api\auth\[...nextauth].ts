import NextAuth, { NextAuthOptions, User, Session } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { JWT } from "next-auth/jwt";
import axios from "axios";

// 🔹 Extend JWT token type
interface CustomToken extends JWT {
  user?: {
    id: string;
    email: string;
    name: string;
    isNewUser?: boolean;
    picture?: string;
    role?: string;
    phone?: string;
    phoneVerified?: string;
    firstName?: string;
    lastName?: string;
  };
  accessToken?: string;
  accessTokenExpireOn?: string;
  refreshToken?: string;
  refreshTokenExpireOn?: string;
  tokenType?: string;
  error?: string | null;
  tokenStartTime?: number; // Add this to track when token was issued
}

// Extend session type
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      isNewUser?: boolean;
      picture?: string;
      role?: string;
      phone?: string;
      phoneVerified?: string;
      firstName?: string;
      lastName?: string;
    };
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpireOn?: string;
    refreshTokenExpireOn?: string;
    tokenType?: string;
    error?: string | null;
  }

  interface User {
    id: string;
    email: string;
    user: any;
    accessToken: string;
    accessTokenExpireOn: string;
    refreshToken: string;
    refreshTokenExpireOn: string;
    tokenType: string;
  }
}

function isTokenExpired(expiryTimeSeconds?: string, tokenStartTime?: number): boolean {
  if (!expiryTimeSeconds || !tokenStartTime) return true;

  // Use 5-minute buffer to match useTokenMonitor
  const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
  const tokenDurationMs = parseInt(expiryTimeSeconds) * 1000; // Convert seconds to milliseconds
  const expiryTimestamp = tokenStartTime + tokenDurationMs;
  const currentTime = Date.now();

  console.log("NextAuth token expiry check:", {
    expiryTimeSeconds,
    tokenStartTime: new Date(tokenStartTime).toISOString(),
    expiryTime: new Date(expiryTimestamp).toISOString(),
    timeUntilExpiry: Math.floor((expiryTimestamp - currentTime) / 1000) + "s",
    isExpired: expiryTimestamp - bufferTime < currentTime,
  });

  return expiryTimestamp - bufferTime < currentTime;
}

async function refreshAccessToken(token: CustomToken): Promise<CustomToken> {
  try {
    console.log("Attempting token refresh...");

    const res = await axios.post(
      `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/refresh`,
      {
        refreshToken: token.refreshToken,
      },
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      }
    );

    console.log("Token refresh successful:", res.data);

    const refreshed = res.data?.detail?.data;
    if (!refreshed?.accessToken) {
      throw new Error("No access token returned from refresh");
    }

    return {
      ...token,
      accessToken: refreshed.accessToken,
      accessTokenExpireOn: refreshed.accessTokenExpireOn,
      refreshToken: refreshed.refreshToken || token.refreshToken,
      refreshTokenExpireOn:
        refreshed.refreshTokenExpireOn || token.refreshTokenExpireOn,
      tokenStartTime: Date.now(), // Reset start time for new token
      error: null,
    };
  } catch (err) {
    console.error("Token refresh failed:", err);

    if (axios.isAxiosError(err)) {
      const message = err.response?.data?.detail?.message || err.message;
      console.error("Refresh error details:", message);

      // If refresh token is invalid, mark for logout
      if (
        message === "Invalid or inactive refresh token." ||
        err.response?.status === 401
      ) {
        console.log("Refresh token invalid, marking for logout...");
        return {
          ...token,
          error: "RefreshAccessTokenError",
        };
      }
    }

    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Custom Login",
      credentials: {
        id: { type: "text" },
        email: { type: "text" },
        accessToken: { type: "text" },
        accessTokenExpireOn: { type: "text" },
        refreshToken: { type: "text" },
        refreshTokenExpireOn: { type: "text" },
        tokenType: { type: "text" },
      },
      async authorize(user): Promise<User | null> {
        if (!user?.id || !user.accessToken) return null;

        return {
          id: user.id,
          email: user.email!,
          user: user,
          accessToken: user.accessToken,
          accessTokenExpireOn: user.accessTokenExpireOn!,
          refreshToken: user.refreshToken!,
          refreshTokenExpireOn: user.refreshTokenExpireOn!,
          tokenType: user.tokenType!,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
    // Match token lifetime: 60 minutes
    maxAge: 60 * 60, // 60 minutes to match token lifetime
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user }): Promise<CustomToken> {
      // On initial sign-in
      if (user) {
        console.log("Initial sign-in, setting token...");
        const now = Date.now();
        token.user = {
          id: user.id,
          email: user.email,
          name: user.name,
          ...user.user,
        };
        token.accessToken = user.accessToken;
        token.accessTokenExpireOn = user.accessTokenExpireOn;
        token.refreshToken = user.refreshToken;
        token.refreshTokenExpireOn = user.refreshTokenExpireOn;
        token.tokenType = user.tokenType || "Bearer";
        token.tokenStartTime = now; // Track when token was issued
        token.error = null;
        return token;
      }

      // Return early if we have an error
      if (token.error) {
        console.log("Token has error, returning as-is");
        return token;
      }

      // Check if access token is expired
      if (isTokenExpired(token.accessTokenExpireOn as string, token.tokenStartTime as number | undefined)) {
        console.log("Access token expired, attempting refresh...");
        return await refreshAccessToken(token);
      }

      // Token is still valid
      console.log("Token is still valid, no refresh needed");
      return token;
    },

    async session({
      session,
      token,
    }: {
      session: Session;
      token: CustomToken;
    }) {
      if (token.user) {
        session.user = {
          ...token.user,
        };
      }

      session.accessToken = token.accessToken;
      session.accessTokenExpireOn = token.accessTokenExpireOn;
      session.refreshToken = token.refreshToken;
      session.refreshTokenExpireOn = token.refreshTokenExpireOn;
      session.tokenType = token.tokenType;
      session.error = token.error;

      return session;
    },
  },
  events: {
    async signOut({ token }) {
      console.log("User signed out, clearing tokens");
    },
  },
};

export default NextAuth(authOptions);