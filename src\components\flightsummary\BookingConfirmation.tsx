"use client";

import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useFlightContext } from "@/context/FlightContext";
import { useCustomSession } from "@/hooks/use-custom-session"
import type { AppState } from "@/store/store";
import { updateTripSummary } from "@/store/slices/tripSummary";

import { BookingHeader } from "../booking-confirmation/booking-header";
import { BookingAlert } from "../booking-confirmation/booking-alert";
import { PaymentSummary } from "../booking-confirmation/payment-summary";
import { FlightSummary } from "../booking-confirmation/flight-summary";
import { ActionSection } from "../booking-confirmation/action-section";
import LoadingOverlay from "../LoadingOverlay/LoadingOverlay";
import type { BookingStatus } from "@/types/booking";
import { useBookingAPI } from "@/hooks/useBookingAPI";
import { setSearchParam } from "@/lib/utils/flightUtils";

interface BookingConfirmationProps {
  handleBookingConfirmation: (success: boolean) => void;
  showErrorModal: (error: any) => void;
}

const BookingConfirmation = ({
  handleBookingConfirmation,
  showErrorModal,
}: BookingConfirmationProps) => {
  const [bookingStatus, setBookingStatus] = useState<BookingStatus | null>(
    null
  );

  const { data: session } = useCustomSession();
  const token = session?.accessToken;

  const { updateGlobalPopup } = useFlightContext();
  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );
  const dispatch = useDispatch();

  const {
    isFetching,
    flightTermResponse,
    bookingResponse,
    bookingDetails,
    bookingMessage,
    fetchFlightTerms,
    bookFlight,
    fetchBookingDetails,
  } = useBookingAPI({
    tripSummaryDetails,
    token,
    updateGlobalPopup,
    handleBookingConfirmation,
    showErrorModal,
    dispatch,
    updateTripSummary,
  });

  // Initial API call to fetch flight terms
  useEffect(() => {
    fetchFlightTerms();
  }, []);

  // Book flight when flight terms are available
  useEffect(() => {
    if (flightTermResponse && Object.keys(flightTermResponse).length > 0) {
      bookFlight(flightTermResponse);
    }
  }, [flightTermResponse]);

  // Fetch booking details when booking response is available
  useEffect(() => {
    if (bookingResponse && Object.keys(bookingResponse).length > 0) {
      fetchBookingDetails(flightTermResponse);

      // Set booking status based on response
      if (bookingResponse.status) {
        setTimeout(() => {
          setSearchParam("bookingStatus", "success")
        }, 2000)
        setBookingStatus(bookingResponse.status as BookingStatus);
      }
    }
  }, [bookingResponse]);

  if (isFetching || !flightTermResponse || bookingStatus === null)
    return <LoadingOverlay loadingText="Hang tight — securing your seat and confirming your booking!"/>;
  if (!flightTermResponse) return null;

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Demo Status Switcher - Remove in production */}
      {/* <div className="bg-white rounded-lg shadow-md p-4 mb-8">
        <h3 className="font-semibold mb-3">Demo: Change Booking Status</h3>
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => setBookingStatus("CONFIRMED")}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${bookingStatus === "CONFIRMED"
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
          >
            Confirmed
          </button>
          <button
            onClick={() => setBookingStatus("UNCONFIRMED")}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${bookingStatus === "UNCONFIRMED"
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
          >
            Unconfirmed
          </button>
          <button
            onClick={() => setBookingStatus("UNCONFIRMED_BY_SUPPLIER")}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${bookingStatus === "UNCONFIRMED_BY_SUPPLIER"
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
          >
            Unconfirmed by Supplier
          </button>
        </div>
      </div> */}

      {/* Booking Content */}
      <BookingHeader
        bookingStatus={bookingStatus}
        email={
          tripSummaryDetails?.bookingDetails?.actual_email ||
          tripSummaryDetails?.paymentDetails?.email
        }
      />

      <BookingAlert
        bookingStatus={bookingStatus}
        bookingMessage={bookingMessage}
      />

      <PaymentSummary
        bookingResponse={bookingResponse}
        flightTermResponse={flightTermResponse}
        bookingStatus={bookingStatus}
      />

      <FlightSummary tripSummaryDetails={tripSummaryDetails} />

      <ActionSection
        bookingStatus={bookingStatus}
        bookingDetails={bookingDetails}
      />
    </div>
  );
};

export default BookingConfirmation;
