import React, { createContext, useContext, useState } from "react";

interface ChatContextType {
  chatThreadId: string | null;
  setChatThreadId: (id: string) => void;
  newChatThreadId: string | null;
  setNewChatThreadId: (id: string) => void;
  refreshChatHistory: boolean;
  setRefreshChatHistory: (value: boolean) => void;
  currentThreadTitle: string | null;
  setCurrentThreadTitle: (text: string) => void;
  openLoginModal: any;
  setLoginModal: (value: boolean) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: React.ReactNode;
  initialLogin?: boolean; // 👈 Add this to accept the prop
}

export const ChatProvider: React.FC<ChatProviderProps> = ({
  children
}) => {
  const [chatThreadId, setChatThreadId] = useState<string | null>(null);
  const [newChatThreadId, setNewChatThreadId] = useState<string | null>(null);
  const [refreshChatHistory, setRefreshChatHistory] = useState<boolean>(false);
  const [currentThreadTitle, setCurrentThreadTitle] = useState<string | null>(
    "New Chat"
  );

  const [openLoginModal, setLoginModal] = useState<boolean>(false);

  return (
    <ChatContext.Provider
      value={{
        chatThreadId,
        setChatThreadId,
        newChatThreadId,
        setNewChatThreadId,
        refreshChatHistory,
        setRefreshChatHistory,
        currentThreadTitle,
        setCurrentThreadTitle,
        openLoginModal,
        setLoginModal,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context)
    throw new Error("useChatContext must be used within a ChatProvider");
  return context;
};
