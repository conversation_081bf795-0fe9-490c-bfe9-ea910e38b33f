// components/BookingStepper.tsx
import React from "react";

const steps = [
  { number: 1, label: "Choose Room" },
  { number: 2, label: "Review & Pay" },
  { number: 3, label: "Booking Confirmation" },
];

export default function BookingStepper({
  currentStep,
}: {
  currentStep: number;
}) {
  return (
    <div className="py-4 md:px-6">
      <div className="flex justify-center gap-8 mb-6 text-[#080236] font-medium mt-6 px-2">
        <div className="flex items-center gap-2">
          <span className="text-xl ">
            <img src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/calendar-schedule-line.svg" />
          </span>{" "}
          24-hour service
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xl">
            <img src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/secure-payment-line.svg" />{" "}
          </span>{" "}
          Secure payment
        </div>
      </div>


      <div className="relative">
        {/* Progress Line */}
        <div className="absolute top-4 left-0 right-0 h-0.5 bg-[#4B4BC3] mx-16"></div>
        <div className="absolute top-4 left-0 w-1/2 h-0.5 bg-[#4B4BC3] mx-16"></div>

        {/* Steps */}
        <div className="flex justify-between items-center relative z-10">
          {steps.map((step, index) => (
      <React.Fragment key={step.number}>
        <div className="flex flex-col items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center font-semibold  mb-3 ${
              step.number <= currentStep
                ? 'bg-green-500 text-white'
                : 'border border-[#4B4BC3] text-[#080236] bg-white'
            }`}>
              {step.number}
            </div>
            <span className="text-[#080236] text-center">
              {step.label}
            </span>
          </div>
      </React.Fragment>
    ))}
        </div>
      </div>
    </div>
  );
}
