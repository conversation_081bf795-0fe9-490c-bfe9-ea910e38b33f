import { expect } from "@playwright/test";
import { fixture } from "../fixtures/Fixture";

export default class Assert {

    static async assertTitle(title: string) {
        const pageTitle = await fixture.page.title();
        expect(pageTitle).toBe(title);
    }

    static async assertTitleContains(title: string) {
        const pageTitle = await fixture.page.title();
        expect(pageTitle).toContain(title);
    }

    static async assertURL(url: string) {
        const pageURL = await fixture.page.url();
        expect(pageURL).toBe(url);
    }

    static async assertURLContains(title: string) {
        const pageURL = await fixture.page.url();
        expect(pageURL).toContain(title);
    }

    static async assertTextEquals(actualText: string, expectedText: string) {
        expect(actualText).toEqual(expectedText);
    }

    static async assertEqual(actualList: string[], textToVerify: string[]) {
        expect(actualList).toEqual(textToVerify);
    }

}