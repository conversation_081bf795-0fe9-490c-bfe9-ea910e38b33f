import React from "react";
import { InfoIcon, PlaneLanding, PlaneTakeoff } from "lucide-react";
import { Flight } from "@/constants/models";
import { useFlightContext } from "@/context/FlightContext";
import { formatFlightTime } from "@/lib/utils/flightTime";
import { formatAirportDisplay } from "@/lib/utils/formatAirport";
import { getFormattedLayoverTime } from "@/lib/utils/layover";
import { getFlightDescription } from "@/lib/utils/flightDescription";

const SummaryCard = ({ flight }: { flight: Flight }) => {
  const { selectedOutboundFlight, selectedInboundFlight, sharedFlightResults, searchedFlightResults } =
    useFlightContext();

  const airportData =
    sharedFlightResults?.airport_data || searchedFlightResults?.airport_data;
  const firstSegment = flight?.segments?.[0];
  const lastSegment = flight?.segments?.[flight.segments.length - 1];
  const isConnecting = flight?.segments && flight.segments.length > 1;
  const dep1 = formatFlightTime(firstSegment.depart_date, firstSegment.origin);
  const arr1 = formatFlightTime(
    firstSegment.arrive_date,
    firstSegment.destination
  );
  const dep2 = formatFlightTime(lastSegment.depart_date, lastSegment.origin);
  const arr2 = formatFlightTime(
    lastSegment.arrive_date,
    lastSegment.destination
  );

  const getAirportDisplayName = (
    airportCode: any,
    airportOptions: any
  ): string => {
    // console.log("getAirportDisplayName=======", airportCode, airportOptions);
    if (!airportOptions || typeof airportOptions !== "object") {
      return airportCode;
    }

    const airport = airportOptions[airportCode];

    if (airport) {
      return `${airportCode}, ${airport.airport_name}, ${airport.city_name_original}`;
    }

    return airportCode; // fallback if not found
  };
  const firstSegmentOriginDisplay =
    firstSegment && getAirportDisplayName(firstSegment.origin, airportData);
  const firstSegmentDestDisplay =
    firstSegment &&
    getAirportDisplayName(firstSegment.destination, airportData);
  const lastSegmentOriginDisplay =
    lastSegment && getAirportDisplayName(lastSegment.origin, airportData);
  const lastSegmentDestDisplay =
    lastSegment && getAirportDisplayName(lastSegment.destination, airportData);
  const layoverStr =
    selectedOutboundFlight && getFormattedLayoverTime(selectedOutboundFlight);
  const flightDescription2 = lastSegment && getFlightDescription(lastSegment);
  const flightDescription1 = getFlightDescription(firstSegment);

  return (
    <>
      <div className="py-3 w-full h-full font-proxima-nova flex flex-col gap-2 justify-center items-center xs:hidden">
        <div className="flex flex-row w-[90%] mx-auto font-proxima-nova justify-center xl:gap-2 lg:gap-1 sm:gap-2 h-auto xl:p-2 lg:p-0">
          {/* First Leg: MAA → DEL */}
          <div className="flex flex-col xl:w-1/5 sm:w-1/4 justify-between text-left xl:py-2 lg:py-1 md:py-0">
            <div className="flex flex-col">
              <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                {firstSegment.departure_time_ampm}
              </p>
              <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-xs xs:text-xs text-brand-grey">
                Departure - {firstSegment.departure_date}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                {lastSegment.arrival_time_ampm}
              </p>
              <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-xs xs:text-xs text-brand-grey">
                Arrival - {lastSegment.arrival_date}
              </p>
            </div>
          </div>
          <div className="flex flex-col items-center justify-center p-4">
            <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

          </div>
          <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
            <div className="flex flex-col">
              <p className="font-bold flex-wrap 2xl:text-lg xl:text-lg sm:text-base text-brand-black">
                {firstSegmentOriginDisplay}
              </p>
              <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-brand-grey">
                {flightDescription1}
              </p>
            </div>
            <p className="xl:text-sm lg:text-xs sm:text-sm text-brand-black mt-1">
              {firstSegment.duration}
            </p>
            <div>
              <p className="text-brand-black 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                {firstSegmentDestDisplay}
              </p>
            </div>
          </div>
        </div>
        {isConnecting && (
          <>
            <div className="flex w-1/2">
              <div className="flex items-center gap-3 p-2 rounded-lg border border-[#B4BBE8] w-max">
                <InfoIcon color="#4B4BC3" />
                <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-brand-grey font-medium">
                  {`${layoverStr} ${lastSegmentOriginDisplay}`}
                </p>
              </div>
            </div>
            <div className="flex flex-row justify-center xl:gap-2 md:gap-1 sm:gap-2 w-[90%] mx-auto h-auto xl:p-2 lg:p-0">
              {/* Second Leg: MAA → DEL */}
              <div className="flex flex-col xl:w-1/5 sm:w-1/4 text-left justify-between xl:py-2 lg:py-1 md:py-0">
                <div className="flex flex-col">
                  <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-brand-black">
                    {dep2.timeLabel}
                  </p>
                  <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-xs xs:text-xs text-brand-grey">
                    Departure - {dep2.dayLabel}
                  </p>
                </div>
                <div className="flex flex-col">
                  <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-brand-black">
                    {arr2.timeLabel}
                  </p>
                  <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-xs xs:text-xs text-brand-grey">
                    Arrival - {arr2.dayLabel}
                  </p>
                </div>
              </div>
              <div className="flex flex-col-reverse items-center justify-center p-4">
                <img
                  src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                  alt="Landing"
                />

              </div>
              <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
                <div>
                  <p className="font-bold 2xl:text-lg xl:text-lg sm:text-base text-brand-black">
                    {lastSegmentOriginDisplay}
                  </p>
                  <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-brand-grey">
                    {flightDescription2}
                  </p>
                </div>
                <p className="xl:text-sm lg:text-xs sm:text-sm text-brand-black mt-1">
                  {lastSegment.duration}
                </p>
                <div>
                  <p className="text-brand-black 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                    {lastSegmentDestDisplay}
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <div className="md:hidden xs:flex w-full h-full justify-center items-center flex flex-col gap-2">
        <div className="flex w-1/6">
          <img
            src={
              selectedOutboundFlight?.supplier_logo ||
              selectedInboundFlight?.supplier_logo
            }
            alt={
              selectedOutboundFlight?.airline || selectedInboundFlight?.airline
            }
            className="w-full h-full rounded-full"
          />
        </div>
        {/* <div className="flex flex-row text-xl">
          <div className="text-[#FF3B3F] font-semibold">Departure&nbsp;</div>
          <div className="text-[#FF3B3F] font-semibold">
            {dep1 && dep1.dayLabel}
          </div>
        </div> */}
        <div className="flex flex-row gap-2 w-full p-2">
          <div className="flex flex-col w-3/6 justify-center items-center gap-1">
            <div className="flex flex-col w-full mx-auto">
              <div className="text-brand-black font-semibold mx-auto w-full text-start text-base">
                {dep1 && dep1.dayLabel}
              </div>
              <div className="text-brand-grey mx-auto w-full text-start text-sm xs:text-xs">
                Departure - {dep1 && dep1.dayLabel}
              </div>
            </div>
            <div className="flex items-center w-4/6">
              <div className="flex flex-col justify-center items-center gap-0.5">
                <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

              </div>
              <div className="flex text-sm">{firstSegment?.duration}</div>
            </div>
            <div className="flex flex-col w-full mx-auto">
              <div className="text-brand-black font-semibold mx-auto w-full text-start text-base">
                {arr1 && arr1.dayLabel}
              </div>
              <div className="text-brand-grey mx-auto w-full text-start text-sm xs:text-xs">
                Arrival - {arr1 && arr1.dayLabel}
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-1 justify-between w-4/6">
            <div className="flex flex-col w-full gap-0.5">
              <div className="text-sm font-semibold">
                {firstSegmentOriginDisplay}
              </div>
              <div className="text-xs text-brand-grey">{flightDescription1}</div>
            </div>
            <div className="flex flex-col w-full gap-0.5">
              <div className="text-sm font-semibold">
                {lastSegmentOriginDisplay}
              </div>
              <div className="text-xs text-brand-grey">{flightDescription2}</div>
            </div>
          </div>
        </div>
        {isConnecting && (
          <>
            <div className="flex w-full">
              <div className="flex items-center gap-3 p-2 rounded-lg border border-[#B4BBE8] w-full">
                <InfoIcon color="#4B4BC3" />
                <p className="text-xs text-brand-grey font-medium">
                  {`${layoverStr} ${lastSegmentOriginDisplay}`}
                </p>
              </div>
            </div>
            <div className="flex flex-row gap-2 w-full p-2">
              <div className="flex flex-col w-3/6 justify-center items-center gap-1">
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-brand-black font-semibold mx-auto w-full text-start text-base">
                    {dep2 && dep2.dayLabel}
                  </div>
                  <div className="text-brand-grey mx-auto w-full text-start text-sm">
                    Departure - {dep2 && dep2.dayLabel}
                  </div>
                </div>
                <div className="flex items-center w-4/6">
                  <div className="flex flex-col-reverse justify-center items-center gap-0.5">
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                      alt="Landing"
                    />
                  </div>
                  <div className="flex text-sm">{lastSegment?.duration}</div>
                </div>
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-brand-black font-semibold mx-auto w-full text-start text-base">
                    {arr2 && arr2.dayLabel}
                  </div>
                  <div className="text-brand-grey mx-auto w-full text-start text-sm">
                    Arrival - {arr2 && arr2.dayLabel}
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-1 justify-between w-4/6">
                <div className="flex flex-col gap-0.5">
                  <div className="text-sm font-semibold">
                    {lastSegmentOriginDisplay}
                  </div>
                  <div className="text-xs text-brand-grey">
                    {flightDescription2}
                  </div>
                </div>
                <div className="flex flex-col gap-0.5">
                  <div className="text-sm font-semibold">
                    {lastSegmentDestDisplay}
                  </div>
                  <div className="text-xs text-brand-grey">
                    {flightDescription2}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        {/* <div className="text-[#FF3B3F] text-sm">
          *Free Cancellation within 24 hours of booking
        </div> */}
      </div>
    </>
  );
};

export default SummaryCard;
