import React, { useEffect, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

const PhoneNumberInput = ({ handleCode, phoneNumberDetails = {} }: any) => {
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [localNumber, setLocalNumber] = useState('');

  const handleChange = (value: string, data: any, event: any, formattedValue: string) => {
    setPhone(value);
    setCountryCode(data.dialCode); // This is the country dial code
    setLocalNumber(value.replace(data.dialCode, '')); // Remaining part of the number
  };

  useEffect(() => {
    if (phoneNumberDetails && phoneNumberDetails?.phone && phoneNumberDetails?.phone_int_code && phoneNumberDetails?.contact_details_phone_number) {
      setPhone(phoneNumberDetails?.phone);
      setCountryCode(phoneNumberDetails?.phone_int_code);
      setLocalNumber(phoneNumberDetails?.contact_details_phone_number);
    }
  },[])

  useEffect(() => {
    if (phone) {
      handleCode(phone, countryCode, localNumber); 
    }
  }, [phone]);

  return (
    <PhoneInput
      country={'gb'}
      value={phone}
      onChange={handleChange}
      enableSearch={true}
      inputProps={{
        name: 'phone',
        required: true,
        autoFocus: true,
      }}
      inputStyle={{ width: '100%' }}
    />
  );
};

export default PhoneNumberInput;
