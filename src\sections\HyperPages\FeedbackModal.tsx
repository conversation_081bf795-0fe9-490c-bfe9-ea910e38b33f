// components/FeedbackModal.tsx
"use client";

import React from "react";

interface FeedbackModalProps {
  isOpen: boolean;
  message: string;
  onClose: () => void;
  success?: boolean;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({
  isOpen,
  message,
  onClose,
  success = true,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 backdrop-blur-sm flex justify-center items-center">
      <div className="bg-white rounded-lg shadow-lg max-w-sm w-full relative p-6">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-neutral-dark text-2xl"
        >
          ×
        </button>
        <h2 className="text-2xl font-bold text-center text-[#080236] mb-4">
          {success ? "Thank you!" : "Oops!"}
        </h2>
        <p className="text-center text-sm sm:text-base text-neutral-dark">
          {message}
        </p>
        <div className="flex justify-center mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-[#1E1E76] text-white rounded-md font-semibold"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeedbackModal;
