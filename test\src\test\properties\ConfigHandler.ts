import { FileUtils } from "../utils/FileUtils";
import { LocaleHelper } from "./LocaleHelper";
import { Properties } from "./Properties";
import * as fs from 'fs';

const PARALLEL_CONFIG_FILE = './parallel-config.json';

export class ConfigHandler {

    public static loadConfig(): void {
        let isMainThread = true;

        process.argv.forEach((arg) => {
            if (arg.includes('parallel')) {
                isMainThread = false;
            }

            if (arg.startsWith('env_')) {
                Properties.initialise(arg.replace('env_', ''));
            }

            if (arg.startsWith('locale_')) {
                LocaleHelper.initialise(arg.replace('locale_', ''));
            }
        });

        // Profiles are not passed to parallel threads - we must do this to pass this info along
        if (isMainThread) {
            const parallelConfig = {
                environment: Properties.ENVIRONMENT,
                locale: LocaleHelper.LOCALE,
            };
            FileUtils.saveTextFile(
                JSON.stringify(parallelConfig),
                PARALLEL_CONFIG_FILE
            );
        } else {
            const parallelConfig = JSON.parse(
                fs.readFileSync(PARALLEL_CONFIG_FILE).toString()
            );
            Properties.initialise(parallelConfig.environment);
            LocaleHelper.initialise(parallelConfig.locale);
        }
    }
}