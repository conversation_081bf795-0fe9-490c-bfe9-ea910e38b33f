"use client"

import { Clock, MessageCircle, Phone, Mail } from "lucide-react"
import type { BookingStatus } from "@/types/booking"

interface BookingAlertProps {
    bookingStatus: BookingStatus
    bookingMessage?: string
}

export const BookingAlert = ({ bookingStatus, bookingMessage }: BookingAlertProps) => {
    if (bookingStatus === "CONFIRMED") return null

    const handleViewBookingDetails = () => {
        alert("Redirecting to booking details...")
    }

    const handleContactSupport = () => {
        alert("Redirecting to support page...")
    }

    return (
        <div className="mb-8">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div className="flex-1">
                        <p className="text-yellow-800 mb-3">
                            {bookingMessage ||
                                "Your booking is being processed. We'll send you a confirmation email once it's confirmed."}
                        </p>
                        <div className="flex gap-2 flex-wrap">
                            <button
                                onClick={handleViewBookingDetails}
                                className="inline-flex items-center gap-2 px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium hover:bg-yellow-200 transition-colors"
                            >
                                <MessageCircle className="w-4 h-4" />
                                View Status
                            </button>
                            {/* <button
                                onClick={handleContactSupport}
                                className="inline-flex items-center gap-2 px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium hover:bg-yellow-200 transition-colors"
                            >
                                <Phone className="w-4 h-4" />
                                Contact Support
                            </button>
                            <button
                                onClick={handleContactSupport}
                                className="inline-flex items-center gap-2 px-3 py-1.5 bg-yellow-100 text-yellow-800 rounded-md text-sm font-medium hover:bg-yellow-200 transition-colors"
                            >
                                <Mail className="w-4 h-4" />
                                Email Us
                            </button> */}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
