import { Given, When, Then } from '@cucumber/cucumber';
import { ChatPage } from '../../pages/ChatPage';
import { fixture } from '../../fixtures/Fixture';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';
import { DateHelper } from '../../utils/DateHelper';
import { Properties } from '../../properties/Properties';
/**
 * Step definition for clicking the Chat with Shasa button
 * Enhanced to use Playwright's modern locators
 */
When('The user is click the chatShasa button', async function () {
    console.log('Step: The user is click the chatShasa button');
    
    try {
        // Wait for the page to stabilize
        await fixture.page.waitForTimeout(2000);
        
        // Take screenshot before clicking the button
        await ScreenshotHelper.takeScreenshot('before-chat-shasa-click');
        
        // First try to find the button with modern Playwright locators
        const chatShasaButton = fixture.page.getByRole('button', { name: /chat with shasa/i });
        
        if (await chatShasaButton.isVisible({ timeout: 3000 }).catch(() => false)) {
            console.log('Found Chat with Shasa button using modern getByRole locator');
            await chatShasaButton.click();
            console.log('Successfully clicked Chat with Shasa button with modern locator');
            
            // Add wait time after clicking
            await fixture.page.waitForTimeout(3000);
            await ScreenshotHelper.takeScreenshot('after-modern-chat-button-click');
        } else {
            // Fall back to ChatPage method which now includes both modern and legacy approaches
            console.log('Modern locator not found, using ChatPage method');
            const buttonClicked = await ChatPage.clickChatWithShasaButton();
            
            if (!buttonClicked) {
                throw new Error('Failed to click the Chat with Shasa button');
            }
        }
        
        // Wait longer for chat interface to appear (increased from 2s to 5s)
        console.log('Waiting for chat interface to appear...');
        await fixture.page.waitForTimeout(5000);
        
        // Take a screenshot after initial wait
        await ScreenshotHelper.takeScreenshot('after-chat-button-wait');
        
        // Try to verify the chat interface with multiple attempts
        let isChatVisible = false;
        let attempts = 0;
        const maxAttempts = 3;
        
        while (!isChatVisible && attempts < maxAttempts) {
            attempts++;
            console.log(`Attempt ${attempts}/${maxAttempts} to verify chat interface visibility`);
            
            // Check standard chat interface detection
            isChatVisible = await ChatPage.isChatInterfaceVisible();
            
            if (!isChatVisible) {
                // Try additional selectors from HTML reference before giving up
                console.log('Trying additional selectors from HTML reference...');
                
                // Try elements from the chat interface HTML provided in the reference
                const additionalSelectors = [
                    'div.flex.h-screen.bg-white.tk-proxima-nova',
                    'div.w-80.md\\:block',
                    'img[src*="main-logo-white.svg"]',
                    'button:has-text("New Chat")',
                    'div:has(h3:text("Chat History"))',
                    'form.flex.items-center.w-full.border',
                    'textarea[placeholder="Type a message..."]',
                    'div:has(svg.lucide-send)',
                    'div.absolute.bottom-80.left-1\\/2',
                    'video.w-full.md\\:h-\\[207px\\]',
                    'div.flex.md\\:gap-4.gap-2.mt-4'
                ];
                
                for (const selector of additionalSelectors) {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                        .catch(() => false);
                        
                    if (isVisible) {
                        console.log(`Found chat interface with selector: ${selector}`);
                        isChatVisible = true;
                        break;
                    }
                }
                
                if (!isChatVisible && attempts < maxAttempts) {
                    const waitTime = 3000 * attempts; // Progressive wait: 3s, 6s, 9s
                    console.log(`Chat interface not found yet, waiting ${waitTime/1000}s more...`);
                    await fixture.page.waitForTimeout(waitTime);
                    await ScreenshotHelper.takeScreenshot(`chat-interface-check-attempt-${attempts}`);
                }
            }
        }
        
        if (!isChatVisible) {
            // Take screenshot when chat interface is not visible after all attempts
            await ScreenshotHelper.takeScreenshot('chat-interface-not-visible-after-all-attempts', true);
            throw new Error('Chat interface did not appear after clicking the button');
        }
        
        console.log('Successfully clicked Chat with Shasa button and verified chat interface');
    } catch (error) {
        console.error('Error while clicking Chat with Shasa button:', error);
        
        // Take screenshot on error
        await ScreenshotHelper.takeScreenshot('chat-shasa-click-error', true);
          // Re-throw the error to fail the test
        throw error;
    }
});

/**
 * Step definition for verifying user is on the chat page
 * Enhanced to use Playwright's modern locators
 */
Then('Assert the user is on Chat page', async function () {
    console.log('Step: Assert the user is on Chat page');
    
    try {
        // Wait a moment for the chat interface to fully render
        await fixture.page.waitForTimeout(2000);
        
        // Take screenshot before verification
        await ScreenshotHelper.takeScreenshot('before-chat-page-verification');
        
        // First try to verify with modern Playwright locators
        console.log('Checking chat page with modern Playwright locators');
        
        // Look for input or textarea by placeholder
        const inputField = fixture.page.getByPlaceholder('Ask me anything...');
        const textareaField = fixture.page.getByPlaceholder('Type a message...');
        const findField = fixture.page.getByPlaceholder('Find');
        
        // Check for send button
        const sendButton = fixture.page.getByRole('button').filter({
            has: fixture.page.locator('svg.lucide-send').or(fixture.page.getByTestId('lucide-send'))
        });
        
        // Check if elements are visible
        const inputVisible = await inputField.isVisible({ timeout: 3000 }).catch(() => false);
        const textareaVisible = await textareaField.isVisible({ timeout: 2000 }).catch(() => false);
        const findFieldVisible = await findField.isVisible({ timeout: 2000 }).catch(() => false);
        const sendButtonVisible = await sendButton.isVisible({ timeout: 2000 }).catch(() => false);
        
        console.log(`Modern locator check results - Input: ${inputVisible}, Textarea: ${textareaVisible}, Find field: ${findFieldVisible}, Send button: ${sendButtonVisible}`);
        
        // If we found at least one input element and the send button, consider chat page open
        if ((inputVisible || textareaVisible || findFieldVisible) && sendButtonVisible) {
            console.log('Successfully verified user is on the Chat page using modern locators');
            return;
        }
        
        // If modern locators failed, fall back to the comprehensive check
        console.log('Modern locators did not verify chat page, trying comprehensive check');
        const isChatPageOpen = await ChatPage.verifyChatPageIsOpen();
        
        if (!isChatPageOpen) {
            // Take screenshot when verification fails
            await ScreenshotHelper.takeScreenshot('chat-page-verification-failed', true);
            throw new Error('Failed to verify that the user is on the Chat page');
        }
        
        console.log('Successfully verified user is on the Chat page');
    } catch (error) {
        console.error('Error while verifying chat page:', error);
        
        // Take screenshot on error
        await ScreenshotHelper.takeScreenshot('chat-page-verification-error', true);
        
        // Re-throw the error to fail the test
        throw error;
    }
});

/**
 * Step definition for clicking the New Chat button
 * Enhanced to use Playwright's modern locators
 */
When("User click the 'New chat' button", async function () {
    console.log("Step: User click the 'New chat' button");
    
    try {
        // Wait for the page to stabilize
        await fixture.page.waitForTimeout(1500);
        
        // Take screenshot before clicking the button
        await ScreenshotHelper.takeScreenshot('before-new-chat-click');
        
        // First try to find the button with modern Playwright locators
        console.log('Trying to locate New Chat button with modern locators');
        
        // Try both exact and case-insensitive matching
        const newChatButton = fixture.page.getByRole('button', { name: 'New Chat' })
            .or(fixture.page.getByRole('button', { name: 'New chat' }));
            
        if (await newChatButton.isVisible({ timeout: 3000 }).catch(() => false)) {
            console.log('Found New Chat button using modern getByRole locator');
            await newChatButton.click();
            console.log('Successfully clicked New Chat button using modern locator');
            
            // Wait for UI updates
            await fixture.page.waitForTimeout(2000);
            await ScreenshotHelper.takeScreenshot('after-modern-new-chat-click');
        } else {
            // Fall back to ChatPage method which now includes both modern and legacy approaches
            console.log('Modern locator not found, using ChatPage method with multiple selector strategies');
            const buttonClicked = await ChatPage.clickNewChatButton();
            
            if (!buttonClicked) {
                throw new Error("Failed to click the 'New chat' button");
            }
        }
        
        // Wait longer for the new chat to initialize (increased from 1500ms to 3000ms)
        console.log('Waiting for new chat to initialize...');
        await fixture.page.waitForTimeout(3000);
        
        // Take screenshot after waiting
        await ScreenshotHelper.takeScreenshot('after-new-chat-wait');
        
        // Verify that a new chat has been started with enhanced verification
        console.log('Verifying new chat has been started...');
        const isNewChatStarted = await ChatPage.verifyNewChatStarted();
        
        if (!isNewChatStarted) {
            // If the first verification fails, try again with a longer wait time
            console.log('First verification attempt failed, trying again with longer wait...');
            await fixture.page.waitForTimeout(2000);
            
            const secondAttempt = await ChatPage.verifyNewChatStarted();
            
            if (!secondAttempt) {
                // Take screenshot when verification fails
                await ScreenshotHelper.takeScreenshot('new-chat-not-started', true);
                throw new Error("New chat did not start after clicking the 'New chat' button");
            }
        }
        
        console.log("Successfully clicked 'New chat' button and verified new chat started");
    } catch (error) {
        console.error("Error while clicking 'New chat' button:", error);
        
        // Take screenshot on error
        await ScreenshotHelper.takeScreenshot('new-chat-click-error', true);
        
        // Re-throw the error to fail the test
        throw error;
    }
});

/**
 * Step definition for searching flights with a query
 */
When('User search the query for flights {string}', async function (queryKey: string) {
    console.log(`Step: User search the query for flights using config key: ${queryKey}`);
    
    try {
        // Take screenshot before searching
        await ScreenshotHelper.takeScreenshot('before-flight-search-query');
        
        // Get the query template from properties file
        // Format should be like "ui.nxvoy.flight_search_testdata.defaultquery" or "ui.nxvoy.chatPage.defaultquery"
        let queryText = Properties.getProperty(queryKey);
        
        if (!queryText || queryText.includes('PROPERTY_NOT_FOUND')) {
            console.warn(`Query key "${queryKey}" not found in properties, falling back to default query`);
            queryText = Properties.getProperty('ui.nxvoy.chatPage.defaultquery');
            
            // If we still don't have a valid query, use a default one
            if (!queryText || queryText.includes('PROPERTY_NOT_FOUND')) {
                console.warn('Default query not found in properties, using hardcoded default');
                queryText = 'I want to book a flight from London to New York on {DEPARTURE_DATE} for 2 adults in economy class';
            }
        }
        
        // Replace placeholders with dynamic dates
        const departureDateFormatted = DateHelper.getFutureDateFormatted(15);
        queryText = queryText.replace('{DEPARTURE_DATE}', departureDateFormatted);
        
        // Replace any other potential placeholders
        const returnDateFormatted = DateHelper.getFutureDateFormatted(22); // Return date 7 days after departure
        queryText = queryText.replace('{RETURN_DATE}', returnDateFormatted);
        
        console.log(`Searching with flight query: "${queryText}"`);
        
        // Check if the chat interface is in the correct state before sending
        const isChatReady = await ChatPage.verifyChatPageIsOpen();
        if (!isChatReady) {
            console.log('Chat page not ready, attempting to reload or recover...');
            
            // Take screenshot of the current state
            await ScreenshotHelper.takeScreenshot('chat-not-ready');
            
            // Check if there's a New Chat button we can click to reset the state using modern locators
            const newChatButton = fixture.page.getByRole('button', { name: /new chat/i });
            const newChatAvailable = await newChatButton.isVisible({ timeout: 2000 }).catch(() => false);
            
            if (newChatAvailable) {
                console.log('Found New Chat button, clicking to reset chat state');
                await newChatButton.click();
                await fixture.page.waitForTimeout(2000);
            } else {
                console.log('No obvious way to reset chat state, continuing with current state');
            }
        }
        
        // Now send the flight search query using the enhanced ChatPage method
        console.log('Sending flight search query...');
        const messageSent = await ChatPage.sendMessage(queryText);
        
        if (!messageSent) {
            console.error('Failed to send flight search query through normal means');
            await ScreenshotHelper.takeScreenshot('flight-query-send-failed', true);
            
            // Fallback attempt - try directly typing and clicking without the wrapper method
            console.log('Attempting direct input as fallback with modern locators...');
            
            // Try to find and interact with the input field directly using getByPlaceholder
            const inputField = fixture.page.getByPlaceholder('Ask me anything...');
            if (await inputField.isVisible({ timeout: 5000 }).catch(() => false)) {
                await inputField.fill(queryText);
                
                // Find and click the send button using getByRole and filter
                const sendButton = fixture.page.getByRole('button').filter({
                    has: fixture.page.locator('svg.lucide-send')
                });
                
                if (await sendButton.isVisible({ timeout: 2000 }).catch(() => false)) {
                    await sendButton.click();
                    console.log('Used modern locator fallback method to send message');
                } else {
                    // Try alternative send button approach
                    const iconButton = fixture.page.getByTestId('lucide-send');
                    if (await iconButton.isVisible({ timeout: 1000 }).catch(() => false)) {
                        await iconButton.click();
                        console.log('Used icon testId to click send button');
                    } else {
                        throw new Error('Failed to locate send button in fallback attempt');
                    }
                }
            } else {
                throw new Error('Failed to send flight search query - input field not found');
            }
        }
        
        // Wait for response - AI might take a moment to process and respond
        console.log('Waiting for response...');
        await fixture.page.waitForTimeout(5000); // Increased wait time
        
        // Take screenshot after the query has been sent and response may have started
        await ScreenshotHelper.takeScreenshot('after-flight-search-query');
        
        // Use modern locators to detect response
        const responseTexts = [
            'looking for flights', 'searching for flights', 'flight', 
            'booking', 'I\'m', 'searching'
        ];
        
        let hasResponse = false;
        
        // Try to find responses using getByText for each indicator
        for (const text of responseTexts) {
            const responseElement = fixture.page.getByText(text, { exact: false });
            if (await responseElement.isVisible({ timeout: 2000 }).catch(() => false)) {
                console.log(`Response detected with text: "${text}"`);
                hasResponse = true;
                break;
            }
        }
        
        // If text-based detection failed, look for chat message containers
        if (!hasResponse) {
            // Look for chat message role
            const aiMessage = fixture.page.locator('[role="region"]').filter({ 
                has: fixture.page.getByText('Shasa', { exact: false })
            });
            
            if (await aiMessage.isVisible({ timeout: 2000 }).catch(() => false)) {
                console.log('Response detected by AI message container');
                hasResponse = true;
            }
        }
        
        if (hasResponse) {
            console.log('Flight search response detected');
        } else {
            console.log('No specific flight search response detected, but query was sent');
            // Not failing the test here as the message might have been sent correctly
            // but the response doesn't contain the expected phrases
        }
        
        // Take another screenshot a bit later to capture more of the response
        await fixture.page.waitForTimeout(3000);
        await ScreenshotHelper.takeScreenshot('flight-search-response-continued');
        
        console.log('Successfully completed flight search query step');
        
    } catch (error) {
        console.error('Error while searching flight query:', error);
        await ScreenshotHelper.takeScreenshot('flight-search-query-error', true);
        throw error;
    }
});

/**
 * Step definition for waiting until Shasa shows flight search results
 */
Then('Wait shasa shown flight search result', async function () {
    console.log('Step: Wait for Shasa to show flight search results');
    
    try {
        // Take initial screenshot
        await ScreenshotHelper.takeScreenshot('waiting-for-flight-results');
        
        // Set a reasonable timeout for waiting (45 seconds)
        const timeoutMs = 45000;
        
        console.log(`Waiting up to ${timeoutMs/1000} seconds for flight search results...`);
        
        // Start time for our custom wait implementation
        const startTime = Date.now();
        let resultsShown = false;
        const checkInterval = 2000; // Check every 2 seconds
        
        // Define elements to look for using modern locators
        const resultIndicators = {
            // Text indicators using getByText
            texts: ['Best Value', 'More Flex', 'Fully Flexible', 'Duration', 
                   'Connect', 'for all passengers', 'per person', 'Show more flights'],
            
            // Button indicators using getByRole
            buttons: ['Show more flights', 'Select Flight', 'Select'],
            
            // Labels that might be present in flight results
            labels: ['Flight', 'Airline', 'Departure', 'Arrival', 'Price']
        };
        
        while (Date.now() - startTime < timeoutMs && !resultsShown) {
            console.log(`Checking for flight results (elapsed: ${Math.floor((Date.now() - startTime)/1000)}s)...`);
            
            // Try text-based indicators first (most reliable)
            for (const text of resultIndicators.texts) {
                const textElement = fixture.page.getByText(text, { exact: false });
                if (await textElement.isVisible({ timeout: 1000 }).catch(() => false)) {
                    console.log(`Found flight search results with text indicator: "${text}"`);
                    resultsShown = true;
                    break;
                }
            }
            
            // If text indicators didn't find anything, try buttons
            if (!resultsShown) {
                for (const buttonName of resultIndicators.buttons) {
                    const buttonElement = fixture.page.getByRole('button', { name: buttonName, exact: false });
                    if (await buttonElement.isVisible({ timeout: 1000 }).catch(() => false)) {
                        console.log(`Found flight search results with button: "${buttonName}"`);
                        resultsShown = true;
                        break;
                    }
                }
            }
            
            // If still not found, look for flight cards by their structure
            if (!resultsShown) {
                // Look for card containers with flight information
                const flightCards = fixture.page.locator('[role="region"]').filter({
                    has: fixture.page.getByText(/flight|airline|departure|arrival/i)
                });
                
                const cardCount = await flightCards.count().catch(() => 0);
                if (cardCount > 0) {
                    console.log(`Found ${cardCount} flight result cards`);
                    resultsShown = true;
                }
            }
            
            // If found results, break out of the loop
            if (resultsShown) break;
            
            // Wait before checking again
            await fixture.page.waitForTimeout(checkInterval);
            
            // Take periodic screenshots
            if ((Date.now() - startTime) % 10000 < checkInterval) {
                await ScreenshotHelper.takeScreenshot(`waiting-for-flight-results-${Math.floor((Date.now() - startTime)/1000)}s`);
            }
        }
        
        if (!resultsShown) {
            // Take screenshot of what we have at timeout
            await ScreenshotHelper.takeScreenshot('flight-results-timeout', true);
            throw new Error('Flight search results were not displayed within the timeout period');
        }
        
        console.log('Flight search results successfully displayed');
        
        // Take screenshot of the flight search results
        await ScreenshotHelper.takeScreenshot('flight-search-results-shown');
        
        // Check if the "Show more flights" button is enabled as an additional verification
        const showMoreButton = fixture.page.getByRole('button', { name: 'Show more flights', exact: false });
        const showMoreEnabled = await showMoreButton.isVisible({ timeout: 3000 })
            .catch(() => false);
        
        if (showMoreEnabled) {
            // Verify button is not disabled
            const isDisabled = await showMoreButton.getAttribute('disabled').catch(() => null);
            const isEnabled = isDisabled === null || isDisabled !== 'true';
            
            if (isEnabled) {
                console.log('"Show more flights" button is enabled and ready for interaction');
            } else {
                console.log('"Show more flights" button is present but disabled');
            }
        } else {
            console.log('Note: "Show more flights" button was not found');
            // This is not failing the test as it's not required by the step definition
        }
        
        console.log('Successfully completed flight search results verification');
    } catch (error) {
        console.error('Error while waiting for flight search results:', error);
        await ScreenshotHelper.takeScreenshot('flight-results-wait-error', true);
        throw error;
    }
});


/**
 * Step definition for asserting that the 'Show more flights' button is enabled
 */
Then('Assert {string} button enabled', async function (buttonText) {
    console.log(`Step: Assert "${buttonText}" button is enabled`);
    
    try {
        // Normalize the button text for more flexible matching
        const normalizedText = buttonText.toLowerCase().replace(/'/g, '');
        
        // Wait a moment for any UI transitions to complete
        await fixture.page.waitForTimeout(1000);
        
        // Take a screenshot before verification
        await ScreenshotHelper.takeScreenshot(`before-${normalizedText}-button-check`);
        
        let isEnabled = false;
        
        // Handle specific button types
        if (normalizedText.includes('show more flight')) {
            // Use the specific method for checking the Show more flights button
            isEnabled = await ChatPage.isShowMoreFlightsButtonEnabled();
        } else {
            // Generic button check for other buttons
            const buttonSelectors = [
                `button:has-text("${buttonText}")`,
                `button:has-text("${buttonText.replace(/'/g, '')}")`,
                `button:has-text("${buttonText.toLowerCase()}")`,
                `[role="button"]:has-text("${buttonText}")`,
                `div.cursor-pointer:has-text("${buttonText}")`
            ];
            
            // Try each selector
            for (const selector of buttonSelectors) {
                try {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                        .catch(() => false);
                        
                    if (isVisible) {
                        console.log(`Found button with selector: ${selector}`);
                        
                        // Check if the button is enabled
                        const isDisabled = await fixture.page.locator(selector).getAttribute('disabled')
                            .catch(() => null);
                            
                        isEnabled = isDisabled === null || isDisabled !== 'true';
                        break;
                    }
                } catch (error) {
                    console.log(`Error checking selector ${selector}:`, error);
                }
            }
        }
        
        // Assert that the button is enabled
        if (isEnabled) {
            console.log(`Successfully verified that "${buttonText}" button is enabled`);
            await ScreenshotHelper.takeScreenshot(`${normalizedText}-button-enabled`);
        } else {
            // Take screenshot when verification fails
            await ScreenshotHelper.takeScreenshot(`${normalizedText}-button-not-enabled`, true);
            throw new Error(`Failed to verify that "${buttonText}" button is enabled`);
        }
    } catch (error) {
        console.error(`Error while verifying "${buttonText}" button:`, error);
        await ScreenshotHelper.takeScreenshot('button-verification-error', true);
        throw error;
    }
});


/**
 * Step definition for asserting that the 'show more flight' button is enabled and clicking it
 */
Then('Assert {string} button enabled and click it', async function (buttonText) {
    console.log(`Step: Assert "${buttonText}" button is enabled and click it`);
    
    try {
        // Normalize button text for more flexible matching
        const normalizedText = buttonText.toLowerCase().replace(/'/g, '').trim();
        
        // Wait a moment for any UI transitions to complete
        await fixture.page.waitForTimeout(1000);
        
        // Take a screenshot before verification
        await ScreenshotHelper.takeScreenshot(`before-${normalizedText.replace(/\s+/g, '-')}-button-check`);
        
        // Handle specific button types using enhanced modern locators
        if (normalizedText.includes('show more flight')) {
            console.log('Looking for "Show more flights" button using enhanced modern locators');
            
            // First wait for the tab panel area to be fully rendered
            console.log('Waiting for flights tab panel to be fully loaded...');
            await fixture.page.locator('div[role="tabpanel"]').first().waitFor({ 
                state: 'visible', 
                timeout: 15000 
            }).catch(() => {
                console.log('Could not find explicit tab panel, continuing with button search anyway');
            });
            
            // Take a screenshot of the flight results area
            await ScreenshotHelper.takeScreenshot('flight-results-panel-loaded', true);
            
            console.log('Searching for "Show more flights" button using multiple strategies');
            
            // First attempt - most specific selector targeting parent structure too
            console.log('Approach 1: Using specific locator with parent context');
            const buttonWithContext = fixture.page.locator('div.flex.flex-col.px-3.pt-3 button:has-text("Show more flights")');
            
            // Check if button exists with this approach
            const contextButtonExists = await buttonWithContext.count() > 0;
            if (contextButtonExists) {
                console.log('Found button using parent context selector');
                
                // Scroll button into view before clicking
                await buttonWithContext.scrollIntoViewIfNeeded();
                await fixture.page.waitForTimeout(1000);
                
                const isEnabled = await buttonWithContext.isEnabled({ timeout: 5000 }).catch(() => false);
                if (!isEnabled) {
                    console.log('Button found but disabled, waiting for it to be enabled...');
                    await fixture.page.waitForTimeout(2000);
                }
                
                // Click with retry logic
                try {
                    await buttonWithContext.click({ timeout: 10000 });
                    console.log('Successfully clicked Show more flights button with context approach');
                    await fixture.page.waitForTimeout(3000); // Wait for UI to update
                    return;
                } catch (clickErr) {
                    console.log('Context button click failed, will try next approach', clickErr);
                    await ScreenshotHelper.takeScreenshot('context-button-click-failed', true);
                }
            }
            
            // Second approach - using role and exact text
            console.log('Approach 2: Using getByRole with exact text');
            const roleButton = fixture.page.getByRole('button', { name: 'Show more flights', exact: true });
            const roleButtonVisible = await roleButton.isVisible({ timeout: 5000 }).catch(() => false);
            
            if (roleButtonVisible) {
                console.log('Found button using getByRole');
                await roleButton.scrollIntoViewIfNeeded();
                await fixture.page.waitForTimeout(1000);
                
                try {
                    await roleButton.click({ timeout: 5000 });
                    console.log('Successfully clicked Show more flights button with role approach');
                    await fixture.page.waitForTimeout(3000); // Wait for UI to update
                    return;
                } catch (clickErr) {
                    console.log('Role button click failed, will try next approach', clickErr);
                    await ScreenshotHelper.takeScreenshot('role-button-click-failed', true);
                }
            }
            
            // Third approach - using class-based selector
            console.log('Approach 3: Using class-based selector');
            const classButton = fixture.page.locator('button.border-brand.text-brand:has-text("Show more flights")');
            const classButtonVisible = await classButton.isVisible({ timeout: 5000 }).catch(() => false);
            
            if (classButtonVisible) {
                console.log('Found button using class-based selector');
                await classButton.scrollIntoViewIfNeeded();
                await fixture.page.waitForTimeout(1000);
                
                try {
                    await classButton.click({ timeout: 5000 });
                    console.log('Successfully clicked Show more flights button with class-based approach');
                    await fixture.page.waitForTimeout(3000); // Wait for UI to update
                    return;
                } catch (clickErr) {
                    console.log('Class button click failed, will try next approach', clickErr);
                    await ScreenshotHelper.takeScreenshot('class-button-click-failed', true);
                }
            }
            
            // Fourth approach - using general text-based selector
            console.log('Approach 4: Using text-based selector');
            const textButton = fixture.page.getByText('Show more flights', { exact: true }).filter({ has: fixture.page.locator('button') });
            const textButtonVisible = await textButton.isVisible({ timeout: 5000 }).catch(() => false);
            
            if (textButtonVisible) {
                console.log('Found button using text-based selector');
                await textButton.scrollIntoViewIfNeeded();
                await fixture.page.waitForTimeout(1000);
                
                try {
                    await textButton.click({ timeout: 5000 });
                    console.log('Successfully clicked Show more flights button with text-based approach');
                    await fixture.page.waitForTimeout(3000); // Wait for UI to update
                    return;
                } catch (clickErr) {
                    console.log('Text button click failed, will try JavaScript approach', clickErr);
                    await ScreenshotHelper.takeScreenshot('text-button-click-failed', true);
                }
            }
            
            // Last resort - JavaScript-based click
            console.log('Approach 5: Using JavaScript evaluation');
            const clickResult = await fixture.page.evaluate(() => {
                // First try exact match with precise parent structure
                const findInContext = () => {
                    const containers = Array.from(document.querySelectorAll('div.flex.flex-col.px-3.pt-3'));
                    for (const container of containers) {
                        const button = container.querySelector('button');
                        if (button && button.textContent && button.textContent.includes('Show more flights')) {
                            (button as HTMLElement).click();
                            return true;
                        }
                    }
                    return false;
                };
                
                // Try finding by exact content match
                const findByContent = () => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const showMoreButton = buttons.find(btn => 
                        btn.textContent && btn.textContent.trim() === 'Show more flights'
                    );
                    if (showMoreButton) {
                        (showMoreButton as HTMLElement).click();
                        return true;
                    }
                    return false;
                };
                
                // Try finding by partial content match
                const findByPartialContent = () => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const showMoreButton = buttons.find(btn => 
                        btn.textContent && btn.textContent.includes('Show more flights')
                    );
                    if (showMoreButton) {
                        (showMoreButton as HTMLElement).click();
                        return true;
                    }
                    return false;
                };
                
                // Try each approach in sequence
                return findInContext() || findByContent() || findByPartialContent();
            });
            
            if (clickResult) {
                console.log('Successfully clicked Show more flights button with JavaScript approach');
                await fixture.page.waitForTimeout(3000); // Wait for UI to update
                return;
            }
            
            // If we get here, all approaches failed
            await ScreenshotHelper.takeScreenshot('show-more-flights-all-approaches-failed', true);
            
            // One final attempt - sometimes we need to wait longer for dynamic content
            console.log('All direct approaches failed. Waiting longer and trying one more time...');
            await fixture.page.waitForTimeout(5000);
            
            const finalButton = fixture.page
                .getByRole('button', { name: /show more flights/i })
                .or(fixture.page.locator('button:has-text("Show more flights")'));
            
            const finalVisible = await finalButton.isVisible({ timeout: 5000 }).catch(() => false);
            if (finalVisible) {
                console.log('Found button after extended wait');
                await finalButton.click({ force: true }).catch(console.error);
                await fixture.page.waitForTimeout(3000);
                return;
            }
            
            // If all approaches fail, throw an error with a screenshot
            await ScreenshotHelper.takeScreenshot('show-more-flights-button-not-found', true);
            throw new Error('"Show more flights" button not found after trying all approaches');
        }
        
        // Generic implementation for other buttons using modern locators
        console.log(`Looking for "${buttonText}" button using modern locators`);
        
        // Try to find the button with different role-based approaches
        const buttonElement = fixture.page.getByRole('button', { name: buttonText, exact: false })
            .or(fixture.page.getByRole('button', { name: buttonText.replace(/'/g, ''), exact: false }))
            .or(fixture.page.getByRole('button', { name: new RegExp(buttonText.replace(/'/g, ''), 'i') }));
        
        const isVisible = await buttonElement.isVisible({ timeout: 5000 }).catch(() => false);
        
        if (!isVisible) {
            // If role-based approach failed, try text-based approach as fallback
            console.log('Role-based approach failed, trying text-based approach');
            
            const textElement = fixture.page.getByText(buttonText, { exact: false });
            const isTextVisible = await textElement.isVisible({ timeout: 2000 }).catch(() => false);
            
            if (isTextVisible) {
                // Check if it's within a button or clickable element
                const parentButton = textElement.locator('xpath=./ancestor::button');
                const parentClickable = textElement.locator('xpath=./ancestor::*[@role="button"]');
                
                // Try parent button first
                if (await parentButton.isVisible({ timeout: 1000 }).catch(() => false)) {
                    console.log(`Found "${buttonText}" in a button element`);
                    
                    // Check if enabled
                    const isDisabled = await parentButton.getAttribute('disabled').catch(() => null);
                    const isEnabled = isDisabled === null || isDisabled !== 'true';
                    
                    if (isEnabled) {
                        await parentButton.click();
                        console.log(`Successfully clicked "${buttonText}" button via text-parent approach`);
                        await fixture.page.waitForTimeout(2000);
                        await ScreenshotHelper.takeScreenshot(`after-${normalizedText.replace(/\s+/g, '-')}-button-click`);
                        return;
                    } else {
                        throw new Error(`"${buttonText}" button is disabled`);
                    }
                } 
                // Try parent clickable next
                else if (await parentClickable.isVisible({ timeout: 1000 }).catch(() => false)) {
                    console.log(`Found "${buttonText}" in a clickable element`);
                    await parentClickable.click();
                    console.log(`Successfully clicked "${buttonText}" clickable element`);
                    await fixture.page.waitForTimeout(2000);
                    await ScreenshotHelper.takeScreenshot(`after-${normalizedText.replace(/\s+/g, '-')}-element-click`);
                    return;
                }
            }
            
            // If we get here, button was not found
            await ScreenshotHelper.takeScreenshot(`${normalizedText.replace(/\s+/g, '-')}-button-not-found`, true);
            throw new Error(`Failed to find "${buttonText}" button with modern locators`);
        }
        
        // Button found with role-based approach, check if it's enabled
        const isDisabled = await buttonElement.getAttribute('disabled').catch(() => null);
        const isEnabled = isDisabled === null || isDisabled !== 'true';
        
        if (!isEnabled) {
            console.log(`Found "${buttonText}" button but it's disabled`);
            await ScreenshotHelper.takeScreenshot(`${normalizedText.replace(/\s+/g, '-')}-button-disabled`, true);
            throw new Error(`"${buttonText}" button is disabled`);
        }
        
        // Button is enabled, click it
        console.log(`"${buttonText}" button is found and enabled, clicking it`);
        await buttonElement.click();
        
        // Wait for any UI updates after clicking
        await fixture.page.waitForTimeout(2000);
        
        console.log(`Successfully clicked "${buttonText}" button`);
        await ScreenshotHelper.takeScreenshot(`after-${normalizedText.replace(/\s+/g, '-')}-button-click`);
        
    } catch (error) {
        console.error(`Error while verifying or clicking "${buttonText}" button:`, error);
        await ScreenshotHelper.takeScreenshot('button-verification-click-error', true);
        throw error;
    }
});