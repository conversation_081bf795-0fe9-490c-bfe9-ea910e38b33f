import React from "react";
import { render, screen } from "@testing-library/react";
import FlightSegmentDetails from "./flight-segment-details";

const mockGetAirportDisplayName = jest.fn((code) => `Airport ${code}`);
const mockAirportOptions = {};

const mockSegments = [
    {
        origin: "JFK",
        destination: "LHR",
        departure_time_ampm: "10:00 AM",
        departure_date: "2024-06-01",
        arrival_time_ampm: "10:00 PM",
        arrival_date: "2024-06-01",
        duration: "7h",
        operator: "Delta",
        operator_code: "DL",
        flight_number: "100",
        travel_class: { class: "Economy" },
        wait_time: "2h 30m",
    },
    {
        origin: "LHR",
        destination: "CDG",
        departure_time_ampm: "1:00 AM",
        departure_date: "2024-06-02",
        arrival_time_ampm: "3:00 AM",
        arrival_date: "2024-06-02",
        duration: "2h",
        operator: "Air France",
        operator_code: "AF",
        flight_number: "200",
        travel_class: { class: "Business" },
        // no wait_time
    },
];

describe("FlightSegmentDetails", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders all segment details for desktop", () => {
        render(
            <FlightSegmentDetails
                segments={mockSegments}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
            />
        );

        // Departure and arrival times/dates for both segments
        expect(screen.getByText("10:00 AM")).toBeInTheDocument();
        expect(screen.getByText("Departure - 2024-06-01")).toBeInTheDocument();
        expect(screen.getByText("10:00 PM")).toBeInTheDocument();
        expect(screen.getByText("Arrival - 2024-06-01")).toBeInTheDocument();

        expect(screen.getByText("1:00 AM")).toBeInTheDocument();
        expect(screen.getByText("Departure - 2024-06-02")).toBeInTheDocument();
        expect(screen.getByText("3:00 AM")).toBeInTheDocument();
        expect(screen.getByText("Arrival - 2024-06-02")).toBeInTheDocument();

        // Airport display names
        expect(screen.getAllByText("Airport JFK")[0]).toBeInTheDocument();
        expect(screen.getAllByText("Airport LHR")[0]).toBeInTheDocument();
        expect(screen.getAllByText("Airport CDG")[0]).toBeInTheDocument();

        // Operator, class, flight number
        expect(screen.getByText("Delta · Economy · DL 100")).toBeInTheDocument();
        expect(screen.getByText("Air France · Business · AF 200")).toBeInTheDocument();

        // Duration
        expect(screen.getByText("7h")).toBeInTheDocument();
        expect(screen.getByText("2h")).toBeInTheDocument();

    });

    it("calls getAirportDisplayName with correct codes and options", () => {
        render(
            <FlightSegmentDetails
                segments={mockSegments}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
            />
        );
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("JFK", mockAirportOptions);
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("LHR", mockAirportOptions);
        expect(mockGetAirportDisplayName).toHaveBeenCalledWith("CDG", mockAirportOptions);
    });

    it("renders layover info as 'Layover' if wait_time is missing", () => {
        const segments = [
            { ...mockSegments[0] },
            { ...mockSegments[1], wait_time: undefined },
        ];
        render(
            <FlightSegmentDetails
                segments={segments}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
            />
        );
        expect(screen.getByText("Layover at LHR")).toBeInTheDocument();
    });

    it("renders correct classes for mobile", () => {
        render(
            <FlightSegmentDetails
                segments={mockSegments}
                getAirportDisplayName={mockGetAirportDisplayName}
                airportOptions={mockAirportOptions}
                isMobile
            />
        );
        // Check for mobile-specific class
        expect(screen.getAllByText("10:00 AM")[0].className).toMatch(/text-lg/);
        expect(screen.getAllByText("Departure - 2024-06-01")[0].className).toMatch(/text-xs/);
    });
    
});