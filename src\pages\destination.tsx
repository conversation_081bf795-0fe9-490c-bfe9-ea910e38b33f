import { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { fadeIn, fadeScaleIn, imageFadeIn, slideIn } from "@/utils/motion";
import { socialMedia, footerLinks } from "@/constants";
import Image from "next/image";
import Router, { useRouter } from "next/router";
import tracker from "@/utils/posthogTracker";

const travelOptions = [
  {
    label: "Hotel Booking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/hotel-420260_1920%201.png",
    position:
      "absolute lg:top-[20%] lg:left-[5%] md:top-[10%] md:left-[42.5%] sm:top-[10%] sm:left-[42.5%]",
    labelPosition: "top-[15%] -right-[45%]",
  },
  {
    label: "Car Rental",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/car-3153077_1920%201.png",
    position: "absolute lg:top-[30%] lg:left-[21%] sm:top-[15%] sm:left-[20%]",
    labelPosition: "top-[60%] -right-[50%]",
  },
  {
    label: "Flight Booking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/beautiful-girl-standing-airport%201.png",
    position: "absolute lg:top-[50%] lg:left-[7%] sm:top-[35%] sm:left-[10%]",
    labelPosition: "top-[60%] -right-[40%]",
  },
  {
    label: "Train Booking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/ai-generated-8006921_1920%201.png",
    position:
      "absolute lg:top-[60%] lg:left-[25%] sm:top-[55%] sm:left-[17.5%]",
    labelPosition: "top-[70%] right-[30%]",
  },
  {
    label: "Trekking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/hiking-7712678_1920%201.png",
    position:
      "absolute lg:top-[70%] lg:left-[44%] sm:top-[75%] sm:left-[27.5%]",
    labelPosition: "top-[70%] -right-[60%]",
  },
  {
    label: "Holidays",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/restaurant-1090136_1920%201.png",
    position:
      "absolute lg:top-[60%] lg:right-[25%] sm:top-[75%] sm:right-[27.5%]",
    labelPosition: "top-[65%] right-[25%]",
  },
  {
    label: "Activities",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/volume-4635710_1920%201.png",
    position:
      "absolute lg:top-[50%] lg:right-[7%] sm:top-[55%] sm:right-[17.5%]",
    labelPosition: "lg:top-[25%] lg:right-[35%] sm:top-[65%] sm:right-[35%]",
  },
  {
    label: "Road Trip",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/nature-2571115_1920%201.png",
    position:
      "absolute lg:top-[30%] lg:right-[21%] sm:top-[35%] sm:right-[10%]",
    labelPosition: "top-[65%] right-[25%]",
  },
  {
    label: "Bus Booking",
    image:
      "https://storage.googleapis.com/nxvoytrips-img/Destinations/england-1283853_1920%201.png",
    position: "absolute lg:top-[20%] lg:right-[5%] sm:top-[15%] sm:right-[20%]",
    labelPosition: "lg:top-[15%] lg:right-[45%] sm:top-[70%] sm:right-[50%]",
  },
];

const FeatureComingSoonModal = ({
  show,
  onClose,
}: {
  show: boolean;
  onClose: () => void;
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-[#080236] text-white rounded-xl shadow-xl px-10 py-8 text-center relative lg:max-w-xl sm:max-w-sm xs:max-w-xs">
        <button
          className="absolute top-2 right-4 text-white text-xl hover:text-gray-300"
          onClick={onClose}
        >
          &times;
        </button>
        <h2 className="lg:text-2xl md:text-xl sm:text-lg xs:text-base font-semibold mb-2">
          Oops, you’re a bit early!
        </h2>
        <p className="lg:text-2xl md:text-xl sm:text-lg xs:text-base">
          This feature isn’t live yet, but it’s on the way.
        </p>
      </div>
    </div>
  );
};

const Destination = () => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const handleOptionClick = (option: string) => {
    tracker.trackEvent("Travel option clicked", { label: option });
    if (option.toLowerCase() === "flight booking") {
      router.push({
        pathname: "/chat",
        query: { traveler: option.toLowerCase() },
      });
    } else {
      setShowModal(true);
    }
  };
  return (
    <>
      <div className="relative w-full flex bg-[#F2F3FA] mx-auto min-h-screen overflow-y-hidden justify-center">
        <div className="flex flex-col xl:w-[90%] lg:w-[90%] sm:w-[90%] gap-8 xl:gap-4 xs:w-[90%] items-center pt-10 xl:pt-5 overflow-y-hidden">
          <div className="font-proxima-nova xl:text-4xl lg:text-3xl md:text-2xl sm:text-2xl xs:text-2xl xs:w-[40%] xs:text-center font-bold">
            Where do you want to go?
          </div>
          <div className="w-full xs:h-max xl:max-h-max sm:flex xs:flex mx-auto gap-5 justify-center md:items-center lg:items-start sm:items-center xs:items-start">
            <motion.div
              initial="hidden"
              whileInView="show"
              variants={imageFadeIn(0, 0)}
              className="flex xl:w-3/6 lg:w-2/6 md:w-4/5 sm:w-5/6 xs:w-3/5 lg:h-2/3 md:h-2/4 sm:h-2/4 xs:h-max"
            >
              <video autoPlay muted loop className="w-full h-full">
                <source
                  className="flex w-full h-full"
                  src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/SHASA%20face%20AI%20640x640.mp4"
                  type="video/mp4"
                />
              </video>
            </motion.div>
          </div>
          <div className="absolute w-full h-full xs:hidden">
            {travelOptions.map((option, index) => (
              <motion.div
                key={index}
                initial="hidden"
                whileInView="show"
                animate="animate"
                variants={imageFadeIn(index * 0.5, index * 0.2)}
                className={`bg-white rounded-xl shadow-lg hover:scale-105 transition transform duration-300 flex flex-col items-center ${option.position}`}
                whileHover={{
                  y: [0, -25, 0], // Float animation
                  transition: {
                    duration: 2, // Smooth float duration
                    ease: "easeInOut",
                    repeat: Infinity,
                    repeatType: "mirror",
                  },
                }}
                onClick={() => handleOptionClick(option.label)}
              >
                <img
                  src={option.image}
                  alt={option.label}
                  className="xl:w-40 xl:h-40 lg:w-32 lg:h-32 md:w-28 md:h-28 sm:w-24 sm:h-24 object-cover rounded-lg"
                />
                <div className={`absolute w-full ${option.labelPosition}`}>
                  <button className="flex xl:text-base lg:text-sm lg:px-3 sm:px-2 sm:text-xs md:text-xs xl:px-5 py-1 text-white rounded-full bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]">
                    {option.label}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
          <div className="sm:hidden xs:w-full mx-auto xs:flex justify-center overflow-x-hidden items-center pb-10">
            <div className="grid grid-flow-row grid-cols-2 gap-5">
              {travelOptions.map((option, index) => (
                <motion.div
                  onClick={() => handleOptionClick(option.label)}
                  initial="hidden"
                  whileInView="show"
                  variants={fadeIn(
                    index % 2 !== 0 ? "left" : "right",
                    "tween",
                    0.5,
                    0.5,
                  )}
                  className="flex relative w-full h-full"
                >
                  <img
                    className="w-full h-44 object-cover rounded-lg"
                    src={option.image}
                    alt=""
                  />
                  <div className="absolute font-proxima-nova flex w-full bottom-2 justify-center items-center ">
                    <button className="flex text-sm px-4 py-1 text-white rounded-full bg-[linear-gradient(to_right,rgba(75,75,195,1),rgba(112,127,245,1),rgba(161,149,249,1))]">
                      {option.label}
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <section
        id="subscribe"
        className="relative flex flex-col items-center justify-center 
        bg-cover bg-center"
        style={{ backgroundImage: "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/background.png')" }}
      >
        <div className="mt-10 xs:mt-5 xs:gap-0 mx-auto w-[90%] md:flex md:flex-row xs:flex-col sm:flex-col gap-5 flex-wrap">
          <div className="flex flex-col md:w-1/3 xs:w-full sm:w-full items-center xs:text-center sm:text-center md:text-start md:items-start">
            <a href="/">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png"
                alt="logo"
                className="w-52 xs:w-40 h-10 object-contain"
              />
            </a>
            <p className="mt-6 xs:mt-3 sm:mt-4 font-proxima-nova font-medium max-md:text-center text-sm lg:text-lg md:text-base xs:w-[80%] sm:w-[80%] md:w-[100%] xs:mx-auto md:max-w-lg text-white">
              Plan smarter; travel better! You now have your personal AI trip
              planner, travel assistant, and itinerary expert at your service!
            </p>
          </div>
          <hr
            className="md:hidden w-full mt-5 border-0 h-[2px]"
            style={{
              background:
                "linear-gradient(to right, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
            }}
          />
          <div className="flex flex-1 flex-col md:w-2/3 xs:gap-6 sm:gap-6 md:gap-0 sm:w-full xs:w-full sm:justify-around xs:justify-center xs:items-center md:flex-row gap-2">
            <div className="flex justify-around md:w-1/2 xs:w-full sm:w-full xs:items-center sm:items-center xs:justify-center sm:justify-center lg:gap-10 gap-20 flex-wrap">
              {footerLinks.map((item) => (
                <div
                  key={item.title}
                  className="max-md:w-full xs:mt-2 sm:mt-2 md:mt-0 xs:w-full sm:w-full xs:justify-center sm:justify-center xs:items-center sm:items-center xs:flex xs:flex-col sm:flex sm:flex-col"
                >
                  <h4
                    className="font-proxima-nova sm:text-2xl xs:text-xl leading-normal 
                          font-bold text-base sm:font-semibold
                          text-white max-md:text-center"
                  >
                    {item.title}
                  </h4>
                  <ul className="max-md:flex max-md:flex-row md:flex-col xs:flex-row xs:flex sm:flex sm:flex-row sm:justify-around sm:w-[80%] xs:w-[90%] sm:mx-auto xs:mx-auto xs:justify-around xs:items-center max-md:gap-2 max-md:justify-center max-md:items-center">
                    {item.links.map((link) => (
                      <li
                        className="mt-3 font-proxima-nova leading-normal text-white lg:text-xl md:text-lg
                                font-norml max-sm:text-sm xs:text-base xs:flex sm:flex xs:justify-center sm:justify-center xs:items-center sm:items-center"
                        key={link.name}
                      >
                        <a href={link.link}>{link.name}</a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            <div className="md:hidden sm:flex xs:flex text-white sm:w-[100%] xs:w-[100%] text-center justify-center">
              Privacy Policy | Terms of Service.
            </div>
            <div className="flex flex-start md:w-1/2 sm:w-full xs:w-full xs:justify-center sm:justify-center md:items-start">
              <div className="flex flex-col xs:gap-4 sm:gap-4 md:gap-0 max-md:w-full sm:w-full xs:w-full xs:justify-center xs:items-center sm:justify-center sm:items-center ">
                <h4
                  className="font-proxima-nova sm:text-2xl xs:text-xl leading-normal 
                       text-white max-md:text-center
                      font-bold text-base max-md:mt-5"
                >
                  Follow Us
                </h4>
                <div className="flex flex-row xs:w-[80%] sm:w-[80%] sm:mx-auto xs:mx-auto xs:justify-around gap-2">
                  {socialMedia.map((item) => (
                    <div
                      className="justify-center items-center flex w-full mt-2"
                      key={item.alt}
                    >
                      <Image
                        src={item.src}
                        alt={item.alt}
                        width={24}
                        height={24}
                        className="bg-none cursor-pointer"
                        onClick={() => window.open(item.link)}
                      />
                    </div>
                  ))}
                </div>
                <p
                  className="text-white mt-10 md:flex sm:hidden xs:hidden md:text-sm lg:text-base justify-center
                      font-medium w-full max-md:text-center"
                >
                  Privacy Policy | Terms of Service.
                </p>
              </div>
            </div>
          </div>
        </div>
        <hr
          className="w-[90%] mt-5 border-0 h-[2px]"
          style={{
            background:
              "linear-gradient(to right, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
          }}
        />
        <div className="w-[90%] my-5 flex flex-col gap-5 sm:flex-row justify-between items-center">
          <p
            className="text-white
                    font-medium w-full mx-auto max:text-sm max-sm:text-center xs:text-center"
          >
            &copy; 2025 Nxvoy & Shasa. All Rights Reserved.
          </p>
          <p
            className="text-[#707FF5] sm:text-sm
                  text-sm font-medium whitespace-nowrap"
          >
            Design by Unified Web Services Ltd.
          </p>
        </div>
      </section>
      <FeatureComingSoonModal
        show={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
};

export default Destination;
