import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { User } from "@/constants/user";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/input/Select";
import InputField from "@/components/input/InputField";
import EditProfileForm from "./EditProfileForm";

interface ProfileDetailsProps {
  user: User | null;
}

const ProfileDetails: React.FC<ProfileDetailsProps> = ({ user }) => {
  const [editingProfile, setEditingProfile] = useState<boolean>(false);
  const [selectedTitle, setSelectedTitle] = useState<string>("");
  const [selectedGender, setSelectedGender] = useState<string>("");
  const [isPasswordEditable, setIsPasswordEditable] = useState<boolean>(false);

  const showEditProfileForm = () => {
    setEditingProfile(true);
  };

  const handleSaveProfile = () => {
    setEditingProfile(false);
  }

  const handleChangePassword = () => {
    setIsPasswordEditable(!isPasswordEditable);
  }

  return (
    <div className="p-4 md:p-6 space-y-10">
      {editingProfile ? <EditProfileForm user={user} onSave={handleSaveProfile} /> : 
      (
        <>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 md:gap-0">
            <h2 className="text-lg md:text-3xl font-semibold text-[#4B4BC3]">
              Personal Information
            </h2>
            <Button variant="outline" icon="edit" className="text-sm text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={showEditProfileForm}>
              Edit Details
            </Button>
          </div>
          <div className="flex flex-col gap-1">
            <label className="text-[#1E1E76] text-base md:text-lg font-semibold">Title</label>
            <div className="grid grid-cols-1 md:grid-cols-4">
              <Select value={selectedTitle} disabled={true}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Mr.">Mr.</SelectItem>
                  <SelectItem value="Mrs.">Mrs.</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5">
            <InputField
              label="First Name"
              placeholder="User First Name"
              name="firstName"
              readonly={true}
              disabled={true}
            />
            <InputField
              label="Middle Name"
              placeholder="User Middle Name"
              name="middleName"
              readonly={true}
              disabled={true}
            />
            <InputField
              label="Last Name"
              placeholder="User Last Name"
              name="lastName"
              readonly={true}
              disabled={true}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 md:gap-8">
            <div>
              <label className="text-[#1E1E76] font-semibold">Gender</label>
              <Select value={selectedGender} disabled={true}>
                <SelectTrigger>
                  <SelectValue placeholder="Gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Male">Male</SelectItem>
                  <SelectItem value="Female">Female</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <InputField
              label="Date of Birth"
              placeholder="DOB"
              type="date"
              name="dob"
              readonly={true}
              disabled={true}
            />
            <InputField
              label="Nationality"
              placeholder="Nationality"
              name="nationality"
              readonly={true}
              disabled={true}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5">
            <InputField
              label="Primary Email"
              placeholder="Primary Name"
              type="email"
              name="primaryEmail"
              readonly={true}
              disabled={true}
            />
            <InputField
              label="Mobile Number"
              placeholder="+91 1234567890"
              type="tel"
              name="mobileNumber"
              readonly={true}
              disabled={true}
            />
            <InputField
              label="Alternate Phone Number(optional)"
              placeholder="+91 1234567890"
              type="tel"
              name="alternateNumber"
              readonly={true}
              disabled={true}
            />
          </div>
          <p className="text-green-600 text-sm w-full p-2 text-center">Changes Updated Successfully</p>
          <div className="flex w-full h-[0.5px] bg-[#B4BBE8]"></div>
          <div className="mt-6">
            <h2 className="text-lg md:text-3xl font-semibold text-[#4B4BC3] mb-3">
              Security and Safety Information
            </h2>
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-0">
              <InputField
                label="Password"
                placeholder="*************"
                type="password"
                name="password"
                readonly={!isPasswordEditable}
                disabled={!isPasswordEditable}
              />
              <Button variant="outline" icon="edit" className="text-md text-white bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={handleChangePassword}>
                Change password
              </Button>
            </div>
            <div className="text-sm mt-2 text-[#1E1E76]">
              Privacy Policy | Terms of Service
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProfileDetails;
