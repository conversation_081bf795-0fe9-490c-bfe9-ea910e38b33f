"use client";

import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { AppState } from "@/store/store";
import RoomOptions from "@/components/hotelBooking/hotelRoomSelection/room-options";
import RoomList from "@/components/hotelBooking/hotelRoomSelection/room-list";
import { Hotel, Room } from "@/lib/types";
import { ChevronLeft } from "lucide-react";
import BookingStepper from "@/components/hotelBooking/HotelBookStepper";
import Navbar from "@/components/NavBar";

export default function HotelRoomSelectionPage() {
  const router = useRouter();
  const selectedHotel = useSelector(
    (state: AppState) => state.hotelDetails.selectedHotel
  ) as Hotel | null;

  useEffect(() => {
    if (!selectedHotel) {
      router.push("/hotels");
      return;
    }

    // Check if hotel has enhanced data
    const hasEnhancedData =
      selectedHotel.rooms &&
      selectedHotel.rooms.some((room: any) => "room_images" in room);

    if (!hasEnhancedData) {
      router.push(`/hotel-details?id=${selectedHotel.code}`);
      return;
    }
  }, [selectedHotel, router]);

  if (!selectedHotel) {
    return (
      <main className="min-h-screen bg-[#F2F3FA] overflow-x-hidden">
        <div className="container mx-auto p-4 md:p-6 max-w-7xl">
          <div className="bg-[#F2F3FA] rounded-lg shadow-sm border border-[#1E1E76] p-6 font-proxima-nova">
            <div className="flex justify-center items-center h-64">
              <div className="text-lg text-[#1E1E76]">
                No hotel selected. Redirecting...
              </div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  const hasEnhancedData =
    selectedHotel.rooms &&
    selectedHotel.rooms.some((room: any) => "room_images" in room);

  if (!hasEnhancedData) {
    return (
      <main className="min-h-screen bg-[#F2F3FA] overflow-x-hidden">
        <div className="container mx-auto p-4 md:p-6 max-w-7xl">
          <div className="bg-[#F2F3FA] rounded-lg shadow-sm border border-[#1E1E76] p-6 font-proxima-nova">
            <div className="flex justify-center items-center h-64">
              <div className="text-lg text-[#1E1E76]">
                Loading room details...
              </div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  const handleSignIn = () => {
    // setIsSignInClicked(true);
  };

  return (
    <main className="min-h-screen bg-[#F2F3FA] overflow-x-hidden">
      <div className="z-50 w-full mx-auto items-center flex flex-col justify-center">
        <div className="w-[95%] fixed top-0 mx-auto z-50">
          <Navbar handleSignIn={handleSignIn} />
        </div>
      </div>
      <div className="container mx-auto p-4 md:p-6 max-w-7xl">
        <div className="max-w-3xl mx-auto mt-6">
          <BookingStepper currentStep={1} />
        </div>
        {/* Back to search button */}
        <button
          onClick={() => router.push("/hotels")}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-6 transition-colors "
        >
          <ChevronLeft size={20} strokeWidth={3} color="#080236" />
          <span className="font-medium underline text-[20px] text-[#080236]">
            Back to search
          </span>
        </button>
        <div className="bg-[#F2F3FA] rounded-lg shadow-sm border border-[#1E1E76] p-6 font-proxima-nova">
          <RoomOptions />
          <RoomList
            rooms={selectedHotel.rooms as Room[]}
            selectedHotel={selectedHotel}
          />
        </div>
      </div>
    </main>
  );
}
