"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/router";
import {
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  Loader,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

import { useSelector, useDispatch } from "react-redux";
import { toast } from "@/hooks/use-toast";
import { AppState } from "@/store/store";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { useChatHistory } from "@/hooks/chat/useChatHistory"; // Import our new hook

type Thread = {
  thread_id: string;
  thread_name: string;
  updated_at: string;
  created_at: string;
  message_count: number;
};

type DialogType = "edit" | "delete" | null;

export function ChatHistory() {
  const dispatch = useDispatch();
  const router = useRouter();
  const { isMobile } = useSidebar();

  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
  const { currentChatPageThreadId } = chatThreadDetails;

  // Use our custom hook - this handles all the chat history logic
  const {
    chatHistory,
    allThreads,
    showSkeleton,
    editThreadName,
    deleteThread,
  } = useChatHistory();

  // Local dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<DialogType>(null);
  const [selectedThread, setSelectedThread] = useState<Thread | null>(null);
  const [editName, setEditName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Dialog handlers
  const closeDialog = useCallback(() => {
    setDialogOpen(false);
    
    setTimeout(() => {
      setDialogType(null);
      setSelectedThread(null);
      setEditName("");
      setIsProcessing(false);
    }, 150);
  }, []);

  const openEditDialog = useCallback((thread: Thread) => {
    setSelectedThread(thread);
    setEditName(thread.thread_name);
    setDialogType("edit");
    setDialogOpen(true);
  }, []);

  const openDeleteDialog = useCallback((thread: Thread) => {
    setSelectedThread(thread);
    setDialogType("delete");
    setDialogOpen(true);
  }, []);

  const handleChatThread = (item: Thread) => {
    router.push(`/chat/${item.thread_id}`);

    dispatch(
      updateCurrentThreadInfo({
        currentChatPageThreadId: item.thread_id,
        currentThreadName: item.thread_name,
        history: true,
        newThread: false,
        allFlights: {},
        tripType: "",
        chatResult: {},
      })
    );
  };

  const handleEditThread = async () => {
    if (!editName.trim() || !selectedThread || isProcessing) return;

    setIsProcessing(true);
    
    const success = await editThreadName(selectedThread.thread_id, editName.trim());
    
    if (success) {
      // Update current thread name if it's the active one
      const currentThreadId = router.query.chatThreadId as string;
      if (currentThreadId === selectedThread.thread_id) {
        dispatch(updateCurrentThreadInfo({ currentThreadName: editName.trim() }));
      }

      toast({
        title: "Thread renamed successfully",
        duration: 2000,
      });

      closeDialog();
    } else {
      toast({
        title: "Failed to rename thread",
        description: "Please try again.",
        variant: "destructive",
      });
    }
    
    setIsProcessing(false);
  };

  const handleDeleteThread = async () => {
    if (!selectedThread || isProcessing) return;

    setIsProcessing(true);
    
    const success = await deleteThread(selectedThread.thread_id);
    
    if (success) {
      // Handle navigation if deleting current thread
      const currentThreadId = router.query.chatThreadId as string;
      if (currentThreadId === selectedThread.thread_id) {
        dispatch(
          updateCurrentThreadInfo({
            currentChatPageThreadId: "",
            currentThreadName: "New Chat",
            history: false,
            newThread: true,
            showMoreFlightsOption: false,
            chatResult: {},
            allFlights: {},
            tripType: "",
          })
        );
        router.push("/chat");
      }

      toast({
        title: "Thread deleted successfully",
        duration: 2000,
      });

      closeDialog();
    } else {
      toast({
        title: "Failed to delete thread",
        description: "Please try again.",
        variant: "destructive",
      });
    }
    
    setIsProcessing(false);
  };

  // Keyboard handler for input
  const handleInputKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Enter" && editName.trim() && !isProcessing) {
      e.preventDefault();
      handleEditThread();
    }
  }, [editName, isProcessing]);

  // Dialog content based on type
  const getDialogContent = () => {
    if (dialogType === "edit") {
      return {
        title: "Rename Chat",
        description: "Enter a new name for this chat thread.",
        content: (
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            placeholder="Enter new thread name"
            onKeyDown={handleInputKeyDown}
            disabled={isProcessing}
            autoFocus={true}
            className="mt-2"
          />
        ),
        actions: (
          <div className="mt-4 gap-4 flex">
            <Button 
              variant="outline" 
              onClick={closeDialog} 
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditThread}
              disabled={!editName.trim() || isProcessing}
            >
              {isProcessing ? <Loader className="h-4 w-4 animate-spin mr-2" /> : null}
              Save
            </Button>
          </div>
        ),
      };
    }

    if (dialogType === "delete") {
      return {
        title: "Delete Chat",
        description: `Are you sure you want to delete "${selectedThread?.thread_name}"? This action cannot be undone.`,
        content: null,
        actions: (
          <>
            <Button 
              variant="outline" 
              onClick={closeDialog} 
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteThread}
              variant="destructive"
              disabled={isProcessing}
            >
              {isProcessing ? <Loader className="h-4 w-4 animate-spin mr-2" /> : null}
              Delete
            </Button>
          </>
        ),
      };
    }

    return { title: "", description: "", content: null, actions: null };
  };

  const dialogContent = getDialogContent();

  return (
    <>
      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel className="text-sm text-[#B3B3B3]">Recent chats</SidebarGroupLabel>

        <SidebarMenu>
          {showSkeleton ? (
            // Loading skeleton - only show when we've never loaded data
            Array.from({ length: 5 }).map((_, i) => (
              <SidebarMenuItem key={i}>
                <Skeleton className="h-2 w-full" />
              </SidebarMenuItem>
            ))
          ) : allThreads.length === 0 ? (
            <SidebarMenuItem>
              <div className="text-sm text-muted-foreground px-2 py-1 text-gray-200">
                No chat history yet
              </div>
            </SidebarMenuItem>
          ) : (
            // Group threads by month and display - using cached data
            Object.entries(chatHistory).map(([month, items]) => (
              <div key={month}>
                <div className="text-sm text-muted-foreground px-2 py-1 font-medium text-gray-100">
                  {month}
                </div>
                {items.map((thread) => (
                  <SidebarMenuItem key={thread.thread_id}>
                    <SidebarMenuButton
                      onClick={() => handleChatThread(thread)}
                      isActive={currentChatPageThreadId === thread.thread_id}
                      className="w-full justify-start "
                    >
                      <span className="truncate text-gray-100" title={thread.thread_name}>
                        {thread.thread_name}
                      </span>
                    </SidebarMenuButton>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuAction
                          className="rounded-sm"
                        >
                          <MoreHorizontalIcon className="text-white ml-4" />
                          <span className="sr-only">More</span>
                        </SidebarMenuAction>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-40 rounded-lg"
                        side={isMobile ? "bottom" : "right"}
                        align={isMobile ? "end" : "start"}
                      >
                        <DropdownMenuItem onClick={() => openEditDialog(thread)}>
                          <EditIcon className="h-4 w-4" />
                          <span>Rename</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(thread)}
                          className="text-destructive"
                        >
                          <TrashIcon className="h-4 w-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </SidebarMenuItem>
                ))}
              </div>
            ))
          )}
        </SidebarMenu>
      </SidebarGroup>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent  className="sm:max-w-md p-4 [&>div:first-child]:hidden">
          <div>
            <DialogTitle>{dialogContent.title}</DialogTitle>
            <DialogDescription>{dialogContent.description}</DialogDescription>
          </div>
          {dialogContent.content}
          <DialogFooter>{dialogContent.actions}</DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}