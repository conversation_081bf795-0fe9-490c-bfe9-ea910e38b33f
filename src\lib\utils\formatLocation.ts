type AirportData = {
    [code: string]: {
      iata_code: string;
      city_name: string;
      city_name_original: string;
      airport_name: string;
      country_name?: string;
    };
  };
  
  export function formatLocationDisplay(code: string, airportData: AirportData): string {
    const { country_name } = airportData;
    const location = formatShortLocationDisplay(code, airportData);
    return `${location}, ${country_name}`;
  }

  export function formatShortLocationDisplay(
    code: string, 
    airportData: AirportData, 
    short: boolean = false,
    showFullName: boolean = false) : string 
  {
    const airport = airportData?.[code];
    if (!airport) return code;
  
    const { city_name, city_name_original, country_name } = airport;
  
    const cityDisplay =
        city_name.trim().toLowerCase() === city_name_original.trim().toLowerCase()
        ? city_name
        : `${city_name} (${city_name_original})`;


    return short ? `${cityDisplay}` : showFullName? `${city_name_original}, ${country_name}` : `${cityDisplay}, ${country_name}`;
  }