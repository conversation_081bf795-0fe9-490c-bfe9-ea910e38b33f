import Carousel from "@/components/carousel/Carousel";
import { destinations } from "@/constants";
import { useInView } from "framer-motion";
import React, { useRef } from "react";
import { motion } from "framer-motion";
import { textVariant } from "@/utils/motion";

const Destinations = () => {
  const sectionRef = useRef(null);
  const inView = useInView(sectionRef, { margin: "0px 0px -40% 0px" });
  return (
    <section className="bg-brand-white w-full h-full py-5">
      <div
        ref={sectionRef}
        className="w-[90%] mx-auto xs:h-max flex flex-col gap-0 sm:mt-10"
      >
        <div className="flex flex-col gap-2 items-center justify-center xs:mx-[30px]">
          <motion.h1
            initial="hidden"
            whileInView="show"
            variants={textVariant(0.5)}
            className="xs:text-3xl font-bold text-brand-black 
                  font-proxima-nova text-center xs:mt-2 sm:leading-none sm:mt-5
                  sm:mx-10 sm:text-[38px] lg:text-[52px]"
          >
            Trending Destinations for This Season
          </motion.h1>
          <motion.p
            initial="hidden"
            whileInView="show"
            variants={textVariant(0.5)}
            className="font-medium text-brand-black sm:leading-2 
                font-proxima-nova xs:leading-normal xs:text-sm lg:text-lg text-wrap 
                xs:w-full sm:w-2/3 text-center sm:text-[16px]"
          >
            Are you looking for the hottest spots to explore right now? I’ve
            rounded up the must-visit destinations of the season - just pick
            one, and I’ll prepare the perfect itinerary for you!
          </motion.p>
        </div>
        <Carousel slides={destinations} autoPlay={inView} />
      </div>
    </section>
  );
};

export default Destinations;
