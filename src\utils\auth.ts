import axios from "axios";
const apiBaseURL = process.env.NEXT_PUBLIC_API_ENDPOINT;
console.log(apiBaseURL);

const API = axios.create({
  baseURL: `${apiBaseURL}/api/v1/`,
  headers: {
    "Content-Type": "application/json",
  },
});

// utils/auth.ts

export const authPostMethod = async (method: string, params: object) => {
  try {
    const response = await API.post(method, params);
    return {
      success: true,
      message: response.data?.message,
      data: response.data,
    };
  } catch (error: any) {
    if (error.response) {
      return {
        success: false,
        message: error.response.data?.message,
        data: error.response.data,
      };
    } else if (error.request) {
      return {
        success: false,
        message: "No response from server",
        data: error.request,
      };
    } else {
      return {
        success: false,
        message: error.message || "Request setup error",
        data: null,
      };
    }
  }
};

export const authGetMethod = async (method: string, params?: object) => {
  try {
    const response = await API.get(method, { params });
    return response.data;
  } catch (error) {
    console.error("Error in GET request:", error);
    throw error;
  }
};

// Create async thunk for logout functionality
export const logoutMethod = async (method: string, token: any) => {
  try {
    const response = await API.post(
      method,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );

    return {
      success: true,
      message: response.data?.message || "Logout successful",
    };
  } catch (error: any) {
    // Error handling remains the same
    if (error.response) {
      return {
        success: false,
        message:
          error.response.data?.message || `Error: ${error.response.status}`,
      };
    } else if (error.request) {
      return {
        success: false,
        message: "No response received from server",
      };
    } else {
      return {
        success: false,
        message: error.message || "Request setup error",
      };
    }
  }
};
