import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface PriceSummaryAccordionProps {
  baseTicketPrice: number;
  totalTaxAndCharges: number;
  total: number;
  currency: string;
  formatFlightPrice: (params: { amount: number; currency: string }) => string;
}

const PriceSummaryAccordion: React.FC<PriceSummaryAccordionProps> = ({
  baseTicketPrice,
  totalTaxAndCharges,
  total,
  currency,
  formatFlightPrice,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | string>(0);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    if (isOpen) {
      const contentEl = contentRef.current;
      if (contentEl) {
        // Set to auto during measurement to get the real height
        setHeight('auto');
        const actualHeight = contentEl.scrollHeight;
        // Set back to 0 and force reflow
        setHeight(0);
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        contentEl.offsetHeight; // Force reflow
        // Now set to the actual height for animation
        setHeight(actualHeight);
      }
    } else {
      setHeight(0);
    }
  }, [isOpen]);

  return (
    <div className="w-full">
      {/* Accordion Header */}
      <div
        onClick={toggleAccordion}
        className={`${isOpen ? "text-brand-grey" : "text-brand-grey"} cursor-pointer flex items-center gap-1 text-base w-full`}
      >
        {isOpen ? "Hide price summary" : "Show price summary"}
        <span
          className="transform transition-transform duration-300"
          style={{
            transform: isOpen
              ? "rotate(180deg)"
              : "rotate(0deg)",
          }}
        >
          <ChevronDown className="w-6 h-6" />
        </span>
      </div>

      {/* Accordion Content */}
      <div
        className="overflow-hidden transition-all duration-300 ease-in-out w-full"
        style={{ height: typeof height === 'number' ? `${height}px` : height }}
      >
        <div ref={contentRef} className="rounded-md py-2 w-full text-brand-black text-base">
          <div className="flex w-full justify-between mb-2">
            <span>Ticket Price</span>
            <span>
              {formatFlightPrice({
                amount: baseTicketPrice,
                currency,
              })}
            </span>
          </div>
          <div className="flex w-full justify-between mb-2">
            <span>Taxes & fees</span>
            <span>
              {formatFlightPrice({
                amount: totalTaxAndCharges,
                currency,
              })}
            </span>
          </div>
          <div className="flex w-full justify-between">
            <span>Total per ticket</span>
            <span>
              {formatFlightPrice({ amount: total, currency })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceSummaryAccordion;
