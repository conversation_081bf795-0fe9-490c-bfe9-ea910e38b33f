export interface Message {
  sender: "human" | "ai";
  text?: string;
}

export interface RawMessage {
  role: "human" | "ai";
  content?: string;
  timestamp: string;
  type?: "flights";
  flights?: any[];
  card?: {
    data?: {
      flights?: any[];
    };
  };
}

export type ChatMessage =
  | {
      sender: "human" | "ai";
      text: string;
      timestamp?: string;
      type?: undefined;
      messageId?: string;
    }
  | {
      sender: "ai";
      type: "flights";
      flights: any[];
      timestamp?: string;
      routingId?: string;
    };

export interface ChatScreenProps {}

export interface WebSocketMessage {
  type: string;
  message?: string;
  token?: string;
  content?: string;
  timestamp?: string;
  id?: string;
  name?: string;
  data?: any;
  routing_id?: string;
  cards?: {
    data?: {
      flights?: any[];
      routing_id?: string;
    };
  };
  tool?: string;
  thread_id?: string;
}

export interface FlightSearchPayload {
  flightSearch: {
    trip_type: string;
    departureLocation: string;
    departureValue: string;
    destinationLocation: string;
    destinationValue: string;
    dateRange: Array<{
      startDate: string;
      endDate: string;
    }>;
    travel_class: string;
    dept_airport_name: string;
    arr_airport_name: string;
    adults: number;
    children: number;
    infants: number;
  };
  flightSearchRespnse: any;
  sharedFlightResults: any;
}

export interface InitialMessage {
  message: string;
  time: string;
}