import { Star } from "lucide-react"

interface StarRatingProps {
    rating: number
    maxRating?: number
}

export function StarRating({ rating, maxRating = 5 }: StarRatingProps) {
    // Ensure rating is within bounds
    const clampedRating = Math.max(0, Math.min(rating, maxRating));
    
    return (
        <div className="flex">
            {Array.from({ length: maxRating }).map((_, index) => {
                const filled = clampedRating - index;
                
                if (filled >= 1) {
                    // Full star
                    return (
                        <Star
                            key={index}
                            size={16}
                            className="text-[#4B4BC3]"
                            fill="#4B4BC3"
                        />
                    );
                } else if (filled > 0) {
                    // Half star
                    return (
                        <div key={index} className="relative">
                            <Star
                                size={16}
                                className="fill-none text-gray-300"
                            />
                            <div 
                                className="absolute top-0 left-0 overflow-hidden"
                                style={{ width: `${filled * 100}%` }}
                            >
                                <Star
                                    size={16}
                                    className="text-[#4B4BC3]"
                                    fill="#4B4BC3"
                                />
                            </div>
                        </div>
                    );
                } else {
                    // Empty star
                    return (
                        <Star
                            key={index}
                            size={16}
                            className="fill-none text-gray-300"
                        />
                    );
                }
            })}
        </div>
    );
}