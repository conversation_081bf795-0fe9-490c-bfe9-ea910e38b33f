export interface FooterLinkItem {
  name: string;
  link: string;
  externalPage?: boolean;
}

export interface FooterSection {
  title: string;
  links: FooterLinkItem[];
}

export const footerLinks: FooterSection[] = [
  {
    title: "Links",
    links: [
      { name: "Home", link: "/" },
      { name: "FAQs", link: "/faq", externalPage: true },
      { name: "About Us", link: "/about-us", externalPage: true },
      { name: "Membership", link: "/membership", externalPage: true },
      { name: "Contact Us", link: "/contact-us", externalPage: true },
    ],
  },
];

export const socialMedia = [
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/instagram.png",
    alt: "instagram logo",
    link: process.env.NEXT_PUBLIC_INSTAGRAM_URL,
  },
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/facebook.png",
    alt: "facebook logo",
    link: process.env.NEXT_PUBLIC_FACEBOOK_URL,
  },
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/linkedin.png",
    alt: "linkedin logo",
    link: process.env.NEXT_PUBLIC_LINKEDIN_URL,
  },
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/tiktok.png",
    alt: "tiktok logo",
    link: process.env.NEXT_PUBLIC_TIKTOK_URL,
  },
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/twitter.png",
    alt: "twitter logo",
    link: process.env.NEXT_PUBLIC_X_URL,
  },
  {
    src: "https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/bluesky.png",
    alt: "bluesky logo",
    link: process.env.NEXT_PUBLIC_BLUESKY_URL,
  },
  // { src: whatsapp, alt: "whatsapp logo", link:"https://wa.me/message/2JJIQSSU57SOK1"}
];

export const faqs = [
  {
    id: "1",
    question: "How does Shasa help me plan my trip?",
    answer:
      "I provide personalised travel recommendations, itineraries, and real-time updates based on your preferences.",
  },
  {
    id: "2",
    question: "Can I use Shasa for group trips?",
    answer:
      "I provide personalised travel recommendations, itineraries, and real-time updates based on your preferences.",
  },
  {
    id: "3",
    question: "Does Shasa provide real-time travel updates?",
    answer:
      "I provide personalised travel recommendations, itineraries, and real-time updates based on your preferences.",
  },
  {
    id: "4",
    question: "Is Shasa free to use?",
    answer:
      "I provide personalised travel recommendations, itineraries, and real-time updates based on your preferences.",
  },
  {
    id: "5",
    question: "Can I book flights and hotels through Shasa?",
    answer:
      "I provide personalised travel recommendations, itineraries, and real-time updates based on your preferences.",
  },
];

export const destinations = [
  {
    id: "1",
    name: "Japan",
    imageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/destinations/Japan.png",
  },
  {
    id: "2",
    name: "Italy",
    imageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/destinations/Italy.png",
  },
  {
    id: "3",
    name: "Bali",
    imageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/destinations/Bali.png",
  },
  {
    id: "4",
    name: "Greece",
    imageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/destinations/Greece.png",
  },
  {
    id: "5",
    name: "UK",
    imageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/destinations/UK.png",
  },
];

export const reviews = [
  {
    customerName: "Rajathi Shri Gop",
    location: "India",
    feedback:
      "Perfect for Last-Minute Travelers!As someone who books trips at the last minute, NxVoy has been a lifesaver! The AI helps me find flights and hotels instantly, and I love that it considers my preferences. No more stressing about missing out on good deals—this app does it all!",
    rating: 5,
  },
  {
    customerName: "Bala Shan",
    location: "Manchester",
    feedback:
      "Traveling with Kids Made Easy!As a parent, planning trips with kids is overwhelming—finding kid-friendly hotels, activities, and restaurants takes so much time. NxVoy has completely changed that! It recommends family-friendly destinations, kid-safe attractions, and even budget-friendly stays. Now, I can actually enjoy the trip instead of stressing over logistics. A must-have for parents who love traveling!",
    rating: 5,
  },
  {
    customerName: "Dhatchayani",
    location: "London",
    feedback:
      "Absolutely blown away by how quickly and perfectly my Turkey travel plan came together!I honestly didn’t expect it to be this easy. I just gave a few details about what I wanted—some history, nature, food, and a bit of downtime—and boom, within minutes, I had a full itinerary mapped out. From exploring the magical streets of Istanbul to hot air ballooning over Cappadocia and soaking in the thermal pools of Pamukkale, everything was curated so thoughtfully.",
    rating: 5,
  },
];

export const partners = [
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/stripe-logo.png",
    desc: "Stripe",
    width: 105,
  },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/cloud-logo.png",
    desc: "Google Cloud",
    width: 255,
  },
  // {
  //   imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/sherpa-logo.png",
  //   desc: "Sherpa",
  //   width: 185,
  // },
  // {
  //   imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/expedia-logo.png",
  //   desc: "Expedia",
  //   width: 198,
  // },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/uws-logo.png",
    desc: "UWS",
    width: 209,
  },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/langsmith-logo.png",
    desc: "Langsmith",
    width: 285,
  },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/travelfusion-logo.png",
    desc: "TravelFusion",
    width: 260,
  },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/viator-logo.png",
    desc: "Viator",
    width: 175,
  },
  {
    imageURL: "https://storage.googleapis.com/nxvoytrips-img/Homepage/partners/post-qode-logo.png",
    desc: "PostQode",
    width: 232,
  },
];

export type TimeKeeper = {
  step: number;
  title: string;
  message: string;
  overlayImageUrl: string;
  classname?: string;
};
export const timeKeepers: TimeKeeper[] = [
  {
    step: 2,
    title: "Check-in Alert",
    message:
      "I guess you might need a reminder to check in at 10 AM in the hotel. I’ll ping you.",
    overlayImageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/timekeeper/reminder.png",
    classname:
      "lg:w-[200px] md:w-[180px] sm:[150px] lg:h-[180px] md:h-[160px] sm:h-[120px] xs:w-[120px] xs:h-[80px] lg:top-[-90px] md:top-[-80px] sm:top-[-60px] xs:top-[-40px] sm:right-[-50px] xs:right-[-40px] md:right-[-70px]",
  },
  {
    step: 3,
    title: "Your tour guide is waiting!",
    message:
      "Your guide, Mr. Richard, awaits you at the activity spot. Time for adventure!",
    overlayImageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/timekeeper/guide.png",
    classname:
      "lg:w-[180px] xl:h-[360px] lg:h-[320px] md:h-[280px] sm:w-[150px] xs:w-[100px] xs:h-[200px] sm:h-[220px] lg:top-[-77px] md:top-[-30px] sm:top-[-30px] lg:right-[-104px] md:right-[-80px] sm:right-[-70px] xs:right-[-50px]",
  },
  {
    step: 4,
    title: "Museum closes at 5 PM!",
    message: "The museum closes at 5 PM. Set off now to get there on time!",
    overlayImageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/timekeeper/tourist-places.png",
    classname:
      "lg:w-[250px] md:h-[250px] md:h-[220px] sm:w-[180px] xs:w-[120px] xs:h-[160px] sm:h-[220px] lg:top-[-132px] md:top-[-112px] sm:top-[-110px] xs:top-[-80px] xs:right-[-45px] sm:right-[-90px] md:right-[-104px]",
  },
  {
    step: 5,
    title: "Breakfast is Ready!",
    message:
      "Breakfast is served at 9 AM. Start your day with something delicious!",
    overlayImageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/timekeeper/breakfast-meal.png",
    classname:
      "lg:w-[250px] md:h-[150px] md:w-[220px] sm:w-[150px] xs:w-[120px] xs:h-[80px] sm:h-[120px] md:top-[-42px] sm:top-[-35px] xs:top-[-25px] xs:right-[-40px] sm:right-[-70px] lg:right-[-108px] md:right-[-90px]",
  },
  {
    step: 6,
    title: "Flight Reminder!",
    message:
      "Your flight to Paris departs at 6:30 PM. Time to head to the airport!",
    overlayImageUrl: "https://storage.googleapis.com/nxvoytrips-img/Homepage/timekeeper/flight.png",
    classname:
      "lg:w-[300px] md:w-[225px] sm:w-[180px] xs:w-[120px] xs:h-[80px] md:h-[150px] sm:h-[120px] lg:top-[-67px] md:top-[-60px] sm:top-[-50px] xs:top-[-40px] xs:right-[-40px] sm:right-[-80px] md:right-[-108px]",
  },
];

export const travellerPlans = [
  {
    title: "Weekend Getaway",
    desc: "A short and sweet trip to recharge your soul!",
    img: "https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/weekend-getaway.png",
  },
  {
    title: "Heritage Visits",
    desc: "Explore history like never before!",
    img: "https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/heritage.png",
  },
  {
    title: "Luxury Retreats",
    desc: "Serene spots to pamper yourself in pure bliss!",
    img: "https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/luxury.png",
  },
  {
    title: "Road Trips & Bike Tours",
    desc: "Scenic drives and open roads to fuel your wanderlust!",
    img: "https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/road-trip.png",
  },
  {
    title: "Adventure Escape",
    desc: 'Thrilling experiences that will leave you saying, "WOW!"',
    img: "https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/adventure.png",
  },
];
