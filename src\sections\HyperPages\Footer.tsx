import { footerLinks, socialMedia } from "@/constants";
import Image from "next/image";

const Footer = () => (
  <footer className="bg-[#0e0b2b] text-white py-4 px-6 md:px-24 font-proxima-nova text-[15px]">
    <div className="mt-10 xs:mt-5 xs:gap-0 mx-auto w-full md:flex md:flex-row xs:flex-col sm:flex-col gap-5 flex-wrap">
      <div className="flex flex-col md:w-1/3 xs:w-full sm:w-full items-center xs:text-center sm:text-center md:text-start md:items-start">
        <a href="/">
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png"
            alt="logo"
            className="w-52 xs:w-40 h-10 object-contain"
          />
        </a>
        <p className="mt-6 pl-4 xs:mt-3 sm:mt-4 font-proxima-nova font-medium max-md:text-center text-sm lg:text-lg md:text-base xs:w-[80%] sm:w-[80%] md:w-[100%] xs:mx-auto md:max-w-lg text-white">
          Plan smarter; travel better! You now have your personal AI trip
          planner, travel assistant, and itinerary expert at your service!
        </p>
      </div>
      <hr
        className="md:hidden w-full mt-5 border-0 h-[0.5px]"
        style={{
          background:
            "linear-gradient(to left, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)",
        }}
      />
      <div className="flex flex-1 flex-col md:w-2/3 xs:gap-6 sm:gap-6 md:gap-0 sm:w-full xs:w-full sm:justify-around xs:justify-center xs:items-center md:flex-row gap-2">
        <div className="flex justify-around md:w-1/2 xs:w-full sm:w-full xs:items-center sm:items-center xs:justify-center sm:justify-center lg:gap-10 gap-20 flex-wrap">
          {footerLinks.map((item) => (
            <div
              key={item.title}
              className="max-md:w-full xs:mt-2 sm:mt-2 md:mt-0 xs:w-full sm:w-full xs:justify-center sm:justify-center xs:items-center sm:items-center xs:flex xs:flex-col sm:flex sm:flex-col"
            >
              <div className="flex flex-col w-max xs:w-full mx-auto md:items-start xs:justify-center sm:justify-center xs:items-center sm:items-center">
                <h4 className="font-proxima-nova w-full sm:text-2xl xs:text-xl leading-normal font-bold text-base sm:font-semibold text-white max-md:text-center xs:text-center">
                  {item.title}
                </h4>
                <ul className="max-md:flex xs:w-[90%] max-md:flex-row md:flex-col xs:flex-row xs:flex sm:flex sm:flex-row sm:justify-around w-max sm:mx-auto xs:mx-auto xs:justify-around xs:items-center items-start max-md:gap-2 max-md:justify-center max-md:items-center">
                  {item.links.map((link) => (
                    <li
                      className="mt-3 font-proxima-nova leading-normal text-white lg:text-xl md:text-lg font-norml max-sm:text-sm xs:text-base xs:flex sm:flex xs:justify-center sm:justify-start xs:items-center sm:items-center cursor-pointer"
                      key={link.name}
                    >
                      {link.externalPage ? (
                        <a
                          href={link.link}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {link.name}
                        </a>
                      ) : (
                        <a href={link.link}>{link.name}</a>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
        <div className="md:hidden sm:flex xs:flex text-white sm:w-[100%] xs:w-[100%] text-center justify-center">
          Privacy Policy | Terms of Service.
        </div>
        <div className="flex flex-start md:w-1/2 sm:w-full xs:w-full xs:justify-center sm:justify-center md:items-start">
          <div className="flex flex-col xs:gap-4 sm:gap-4 md:gap-0 max-md:w-full sm:w-full xs:w-full xs:justify-center xs:items-center sm:justify-center sm:items-center ">
            <h4 className="font-proxima-nova sm:text-2xl xs:text-xl leading-normal text-white max-md:text-center font-bold text-base max-md:mt-5">
              Follow Us
            </h4>
            <div className="flex flex-row xs:w-[80%] sm:w-[80%] sm:mx-auto xs:mx-auto xs:justify-around gap-2">
              {socialMedia.map((item) => (
                <div
                  className="justify-center items-center flex w-full mt-2"
                  key={item.alt}
                >
                  <Image
                    src={item.src}
                    alt={item.alt}
                    width={24}
                    height={24}
                    className="bg-none cursor-pointer"
                    onClick={() => window.open(item.link)}
                  />
                </div>
              ))}
            </div>
            <div className="text-white mt-10 md:flex sm:hidden xs:hidden md:text-sm lg:text-base justify-center font-medium w-full max-md:text-center">
              <a
                href="/privacy-policy"
                target="_blank"
                rel="noopener noreferrer"
              >
                Privacy Policy
              </a>
              <p className="px-2">|</p>
              <a
                href="/terms-of-service"
                target="_blank"
                rel="noopener noreferrer"
              >
                Terms of Service.
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="w-full my-5 flex flex-col gap-5 sm:flex-row justify-between items-center">
      <p className="text-white font-medium w-full mx-auto max:text-sm max-sm:text-center xs:text-center">
        &copy; 2025 Nxvoy & Shasa. All Rights Reserved.
      </p>
      <p className="text-[#707FF5] sm:text-sm text-sm font-medium whitespace-nowrap">
        Design by Unified Web Services Ltd.
      </p>
    </div>
  </footer>
);

export default Footer;
