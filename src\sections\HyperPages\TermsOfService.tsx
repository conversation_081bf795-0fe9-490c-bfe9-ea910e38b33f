import { useEffect } from "react";
import Header from "./Header";
import Footer from "./Footer";
import { termsAndConditionsSections } from "@/constants/termsAndConditionsContent";

const TermsAndConditions = () => {
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const el = document.querySelector(hash);
      if (el) el.scrollIntoView({ behavior: "smooth" });
    }
  }, []);

  return (
    <div className="text-[#0e0b2b]">
      <Header title="Terms & Conditions" />
      <main className="p-6 flex flex-col md:flex-row gap-6 max-w-7xl mx-auto">
        {/* Main Content */}
        <section className="md:w-3/4 space-y-12 text-[#0e0b2b]">
          <div className="space-y-5">
            <h1 className="text-3xl font-bold mb-0 leading-tight text-[#080236]">
              NxVoy <PERSON>rms & Conditions
            </h1>
            <h2 className="text-base font-semibold mt-0 leading-none text-gray-400">
              Effective Date: 26-06-2025
            </h2>
            <h2 className="text-base font-semibold mt-0 leading-none text-gray-400">
              Last Updated: 26-06-2025
            </h2>
          </div>

          {termsAndConditionsSections.map(({ id, title, content }) => (
            <div key={id} id={id} className="mb-6">
              <h3 className="text-3xl font-bold mb-2">{title}</h3>
              <div className="space-y-2 pl-2">
                {content.map((line, index) =>
                  line.startsWith("- ") ? (
                    <div
                      key={index}
                      className="relative pl-4 font-proxima-nova font-normal text-base18 text-brand-black"
                    >
                      <span className="absolute left-0 top-0 text-brand-black">
                        •
                      </span>
                      {line.replace(/^- /, "")}
                    </div>
                  ) : (
                    <p
                      key={index}
                      className="font-proxima-nova font-normal text-base18 text-brand-black"
                    >
                      {line}
                    </p>
                  )
                )}
              </div>
            </div>
          ))}

          <div className="mt-0">
            <p className="text-lg italic text-gray-400 mb-1">
              © 2025 <span className="font-medium">NxVoy Labs Ltd.</span> All rights reserved.
            </p>
            <p className="text-lg italic text-gray-400">
              This policy covers compliance with the{" "}
              <span className="italic font-medium">UK GDPR, EU GDPR, CCPA</span>, and other relevant data protection laws.
            </p>
            <p className="text-lg italic text-gray-400">
              For specific jurisdictional queries, please contact us at{" "}
              <span className="not-italic underline"><EMAIL></span>.
            </p>
          </div>
        </section>

        {/* Sidebar */}
        <aside className="md:w-1/4 bg-white rounded-lg shadow p-4 sticky top-24 h-fit">
          <ul className="space-y-3 font-proxima-nova text-base18 text-brand-black font-normal">
            {termsAndConditionsSections.map(({ id, title }) => (
              <li key={id}>
                <a href={`#${id}`} className="hover:underline text-brand-black">
                  {title}
                </a>
              </li>
            ))}
          </ul>
        </aside>
      </main>
      <Footer />
    </div>
  );
};

export default TermsAndConditions;
