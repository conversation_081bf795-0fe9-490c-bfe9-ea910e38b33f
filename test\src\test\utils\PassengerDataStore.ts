/**
 * Export a singleton instance to store generated passenger data
 * This allows us to generate once and reuse across multiple steps
 */
export class PassengerDataStore {
    private static _instance: PassengerDataStore;
    private _passengerData: any = null;
    
    private constructor() {}
    
    public static getInstance(): PassengerDataStore {
        if (!PassengerDataStore._instance) {
            PassengerDataStore._instance = new PassengerDataStore();
        }
        return PassengerDataStore._instance;
    }
    
    /**
     * Get the stored passenger data
     * @returns The stored passenger data or null if not set
     */
    get passengerData(): any {
        return this._passengerData;
    }
    
    /**
     * Store new passenger data
     * @param data The passenger data to store
     */
    set passengerData(data: any) {
        this._passengerData = data;
    }
    
    /**
     * Reset the stored data
     */
    reset(): void {
        this._passengerData = null;
    }
}
