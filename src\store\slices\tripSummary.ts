import { initialFlightState } from "@/lib/utils/initialValues";
import { createSlice } from "@reduxjs/toolkit";

const initialState: any = initialFlightState;

const tripSummarySlice = createSlice({
  name: "tripSummary",
  initialState,
  reducers: {
    updateTripSummary: (state, action) => {
      console.log("tripSummary", state, action);
      return { ...state, ...action.payload };
    },
    resetTripSummary: (state) => {
      return initialFlightState;
    },
  },
});

export const { updateTripSummary, resetTripSummary } = tripSummarySlice.actions;
export default tripSummarySlice.reducer;