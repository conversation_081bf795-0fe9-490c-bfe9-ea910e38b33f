import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Hotel } from "@/lib/types";

interface HotelDetailsState {
    selectedHotel: Hotel | null;
}

// Load initial state from localStorage if available (optional)
const loadFromLocalStorage = (): Hotel | null => {
  if (typeof window !== 'undefined') {
    try {
      const savedHotel = localStorage.getItem('selectedHotel');
      return savedHotel ? JSON.parse(savedHotel) : null;
    } catch (error) {
      console.error('Error loading hotel from localStorage:', error);
      return null;
    }
  }
  return null;
};

// Save to localStorage (optional)
const saveToLocalStorage = (hotel: Hotel | null) => {
  if (typeof window !== 'undefined') {
    try {
      if (hotel) {
        localStorage.setItem('selectedHotel', JSON.stringify(hotel));
      } else {
        localStorage.removeItem('selectedHotel');
      }
    } catch (error) {
      console.error('Error saving hotel to localStorage:', error);
    }
  }
};

const initialState: HotelDetailsState = {
  selectedHotel: loadFromLocalStorage(),
};

const hotelDetailsSlice = createSlice({
  name: "hotelDetails",
  initialState,
  reducers: {
    setSelectedHotel: (state, action: PayloadAction<Hotel>) => {
      state.selectedHotel = action.payload;
      saveToLocalStorage(action.payload);
    },
    clearSelectedHotel: (state) => {
      state.selectedHotel = null;
      saveToLocalStorage(null);
    },
    // Add a reducer to update only the hotel content without replacing the entire hotel
    updateHotelContent: (state, action: PayloadAction<Hotel['hotel_content']>) => {
      if (state.selectedHotel) {
        state.selectedHotel.hotel_content = action.payload;
        saveToLocalStorage(state.selectedHotel);
      }
    },
  },
});

export const { setSelectedHotel, clearSelectedHotel, updateHotelContent } = hotelDetailsSlice.actions;
export default hotelDetailsSlice.reducer;