import archiver from 'archiver';
import * as fs from 'fs';

export class FileUtils {
    public static async zipFolder(source: string, out: string) {
        const archive = await archiver('zip', { zlib: { level: 9 } });
        const stream = fs.createWriteStream(out);
        await archive.directory(source, false);
        await archive.pipe(stream);
        await archive.finalize();
    }
    
    public static createDirectories(path: string) {
        const directories: Array<string> = path.split("/");

        for (let i = 1; i <= directories.length; i++) {
            const path = directories.slice(0, i).join("/");
            if (!path.includes(".") && !fs.existsSync(path)) {
                console.log(`Creating directory: ${path}`);
                fs.mkdirSync(path);
            }
        }
    }

    public static saveTextFile(contents: string, filename: string): void {
      fs.writeFileSync(filename, contents);
    }
}