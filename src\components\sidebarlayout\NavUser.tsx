"use client"

import {
  BellIcon,
  CreditCardIcon,
  Home,
  LogOutIcon,
  MoreVerticalIcon,
  UserCircleIcon,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { signOut } from "next-auth/react"
import { useSelector } from "react-redux"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { AppState } from "@/store/store"
import { useCustomSession } from "@/hooks/use-custom-session"
import { logoutMethod } from "@/utils/auth"
import { clearReduxOnLogout } from "@/store/clearRedux"
import { getInitials } from "@/screens/dashboard/DashboardNavbar"
import { Skeleton } from "../ui/skeleton"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { data: session } = useCustomSession()
  const token = session?.accessToken
  const router = useRouter()

  //console.log("session======", session);
  
  // Get user details from Redux store (similar to ChatHeader)
  const { currentUserDetails } = useSelector(
    (state: AppState) => state.userDetails
  )
  const currentUser = useSelector((state: AppState) => state.loggedInUser)

  // Use currentUserDetails if available, otherwise fallback to prop user
  const userDetails = currentUserDetails

  const handleLogout = async () => {
    try {
      const response = await logoutMethod("auth/signout", token)
      if (response.success) {
        await clearReduxOnLogout()
        signOut({ redirect: false })
        router.push("/")
      }
    } catch (error) {
      console.error("Logout failed:", error)
      // Fallback: force logout even if API call fails
      await clearReduxOnLogout()
      signOut({ redirect: false })
      router.push("/")
    }
  }

  const handleNavigateToAccount = () => {
    router.push("/userprofile")
  }

  const handleNavigateToHome = () => {
    router.push("/")
  }

  if(!session) return (<></>);
  if(session && !session?.user.email) return (<>
    <Skeleton className="h-[50px]" />
  </>)
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage 
                  src={session.user.picture || ""} 
                  alt={session.user.firstName +' '+ session.user.lastName} 
                />
                <AvatarFallback className="rounded-full text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                  {getInitials(session.user)}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium text-white">{session.user.firstName +' '+ session.user.lastName}</span>
                <span className="truncate text-xs text-muted-foreground">
                  {session.user?.email}
                </span>
              </div>
              <MoreVerticalIcon className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage 
                    src={session.user.picture || ""} 
                    alt={session.user.firstName +' '+ session.user.lastName} 
                  />
                  <AvatarFallback className="rounded-full text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                    {getInitials(userDetails)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{session.user.firstName +' '+ session.user.lastName}</span>
                  <span className="truncate text-xs text-muted-foreground">
                    {session.user.email}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={handleNavigateToAccount}>
                <UserCircleIcon />
                My Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleNavigateToHome}>
                <Home />
                Go to Home
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOutIcon />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}