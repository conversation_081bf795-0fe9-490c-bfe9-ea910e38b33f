import { useEffect } from "react";

const CaptachaCookieHide = () => {
  useEffect(() => {
    let foundOnce = false;
    let timeoutId: NodeJS.Timeout;

    const interval = setInterval(() => {
      const reCaptchaBadge = document.querySelector('div.grecaptcha-badge');
      const cookieYesBadge = document.querySelector('div.cky-btn-revisit-wrapper.cky-revisit-bottom-left');

      if (reCaptchaBadge) {
        console.log("reCAPTCHA badge found");
        reCaptchaBadge.style.display = "none";
        if (!foundOnce) {
          foundOnce = true;
          timeoutId = setTimeout(() => {
            clearInterval(interval);
            console.log("Stopped checking after 6s.");
          }, 6000);
        }
      }

      if (cookieYesBadge) {
        console.log("CookieYes badge found");
        cookieYesBadge.style.display = "none";
        if (!foundOnce) {
          foundOnce = true;
          timeoutId = setTimeout(() => {
            clearInterval(interval);
            console.log("Stopped checking after 6s.");
          }, 6000);
        }
      }

      // Optional: Stop early if both are hidden
      if (
        reCaptchaBadge?.style.display === "none" &&
        cookieYesBadge?.style.display === "none"
      ) {
        clearInterval(interval);
        clearTimeout(timeoutId);
        console.log("Both badges handled and interval cleared.");
      }
    }, 500);

    return () => {
      clearInterval(interval);
      clearTimeout(timeoutId);
    };
  }, []);

  return null;
};

export default CaptachaCookieHide;
