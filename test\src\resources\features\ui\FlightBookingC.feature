
@ui
@regression
@flightbook
Feature: Flight Booking

Scenario: booking a Flight with valid data

Given The user starts from the "ui.nxvoy.appUrl" page
And The user clicks on the Sign in button
Given The user types the "nxvoy.users.standard" username on the login page
And The user types the "nxvoy.users.standard" password on the login page
When The user clicks on the login button
Then Assert user is logged in home page
When The user is click the chatShasa button
Then Assert the user is on Chat page
And User click the 'New chat' button
When User search the query for flights "ui.nxvoy.flight_search_testdata.defaultquery"
Then Wait shasa shown flight search result
And Assert 'show more flight' button enabled and click it
And user select the flight 
Then Assert user is on the flightsummary screen 
When user click "continue and checkout" button
Then Assert the user is on trip summary page 
When user fill the passenger info filling with dynamic data
Then user click the review&pay button
And Assert user is on Review&Pay screen
And fill the credit card informtion if required
And user fill the contact info using "ui.nxvoy.flight_search_testdata.validinput.contactDetails"
When user click the checkbox & "Pay Now" button
And Wait until booking confirmation page to displayed
# Then Click on the "Download as pdf" button, download the ticket
Then click the user account icon
And click the logout button
 





