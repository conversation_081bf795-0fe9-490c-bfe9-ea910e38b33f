"use client"

import { useState, useEffect } from "react"
import { Trash2 } from "lucide-react"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"

interface DeleteThreadDialogProps {
    isOpen: boolean
    threadName: string
    onCancel: () => void
    onConfirm: () => void
}

export function DeleteThreadDialog({ isOpen, threadName, onCancel, onConfirm }: DeleteThreadDialogProps) {
    return (
        <AlertDialog open={isOpen} onOpenChange={onCancel}>
            <AlertDialogContent className="flex flex-col items-center">
                <AlertDialogHeader className="flex flex-col items-center">
                    <div>
                        <Trash2 />
                    </div>
                    <AlertDialogTitle className="text-[#080236]">Confirm Delete Trip</AlertDialogTitle>
                    <AlertDialogDescription className="font-medium text-[#080236]">
                        Are sure you want to delete {threadName}?
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex w-full items-center gap-y-2">
                    <AlertDialogAction
                        className="bg-white text-black border border-[#1E1E76] w-3/6 rounded-2xl hover:bg-white"
                        onClick={onConfirm}
                    >
                        Delete
                    </AlertDialogAction>
                    <AlertDialogCancel className="hover:bg-[#4B4BC3] w-3/6 hover:text-[#F2F3FA] bg-[#4B4BC3] text-[#F2F3FA] rounded-2xl">
                        Cancel
                    </AlertDialogCancel>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}

interface EditThreadDialogProps {
    isOpen: boolean
    threadName: string
    onCancel: () => void
    onConfirm: (newName: string) => void
}

export function EditThreadDialog({ isOpen, threadName, onCancel, onConfirm }: EditThreadDialogProps) {
    const [newName, setNewName] = useState(threadName)

    // Reset input when dialog opens with new thread
    useEffect(() => {
        setNewName(threadName)
    }, [threadName])

    return (
        <AlertDialog open={isOpen} onOpenChange={onCancel}>
            <AlertDialogContent className="flex flex-col items-center">
                <AlertDialogHeader className="flex flex-col items-center">
                    <div>
                        <img className="h-5" src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/edit-2-fill.svg" />
                    </div>
                    <AlertDialogTitle className="text-[#080236]">Edit trip name</AlertDialogTitle>
                </AlertDialogHeader>

                <Input
                    value={newName}
                    onChange={(e) => setNewName(e.target.value)}
                    placeholder="Enter new thread name"
                    className="mt-2 border-[#B4BBE8] rounded-full text-[#080236]"
                />

                <AlertDialogFooter className="flex w-full items-center">
                    <AlertDialogCancel className="bg-white w-3/6 text-[#080236] border border-[#1E1E76] rounded-2xl hover:bg-white">
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="hover:bg-[#4B4BC3] hover:text-[#F2F3FA] bg-[#4B4BC3] rounded-2xl w-3/6 text-[#F2F3FA]"
                        onClick={() => onConfirm(newName)}
                    >
                        Save
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
