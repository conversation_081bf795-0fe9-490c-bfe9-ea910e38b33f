import { GetServerSideProps } from "next";
import ChatLayout from "@/components/chat/ChatLayout";
import { ChatScreen } from "@/components/chat/ChatScreen";
import { useRouter } from "next/router";

function parseCookies(cookieHeader = ""): Record<string, string> {
  return cookieHeader.split(";").reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split("=");
    acc[key] = decodeURIComponent(value || "");
    return acc;
  }, {} as Record<string, string>);
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const cookieHeader = context.req.headers.cookie || "";
  const cookies = parseCookies(cookieHeader);
  const isLoggedIn = cookies.isLoggedIn === "true";
  return { props: { isLoggedIn } };
};

export default function NewChatThreadPage({ isLoggedIn }: { isLoggedIn: boolean }) {
  const router = useRouter();
  const { chatThreadId: threadId } = router.query;
  console.log("threadId======", router.query);

  if (!router.isReady || !threadId || Array.isArray(threadId)) {
    return <div>Loading…</div>;
  }

  return (
    <ChatLayout>
      <ChatScreen/>
    </ChatLayout>
  );
}
