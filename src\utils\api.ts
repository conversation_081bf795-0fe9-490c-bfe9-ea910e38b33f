import axios from "axios";


const apiBaseURL = process.env.NEXT_PUBLIC_API_ENDPOINT;
const agentApiBaseURL = process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT;

const API = axios.create({
  baseURL: `${apiBaseURL}/api/v1/campaign/`,
  headers: {
    "Content-Type": "application/json",
  },
});

// Agent API client
const AgentAPI = axios.create({
  baseURL: `${agentApiBaseURL}/api/v1/`,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to add auth token dynamically
AgentAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

// Flag to prevent multiple token refresh requests
let isRefreshing = false;
// Array to hold pending requests while token is being refreshed
let failedRequestsQueue: any = [];


// Add response interceptor to handle 401 errors
AgentAPI.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and we haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If token is already being refreshed, add to queue
        return new Promise((resolve, reject) => {
          failedRequestsQueue.push({ resolve, reject });
        }).then(() => {
          originalRequest.headers.Authorization = `Bearer ${localStorage.getItem("accessToken")}`;
          return AgentAPI(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Call your refresh token endpoint
        const refreshToken = localStorage.getItem("refreshToken");
        if (!refreshToken) {
          throw new Error("No refresh token available");
        }

        const response = await axios.post(`${agentApiBaseURL}/api/v1/auth/refresh`, {
          refreshToken: refreshToken
        });

        const { accessToken, refreshToken: newRefreshToken } = response.data?.detail.data;

        // Update the original request header
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;

        // Process queued requests
        failedRequestsQueue.forEach((pending: any) => pending.resolve());
        failedRequestsQueue = [];

        // Retry the original request
        return AgentAPI(originalRequest);
      } catch (refreshError) {
        // If refresh fails, clear tokens and redirect to login
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        document.cookie = "isLoggedIn=; path=/; max-age=0";
        // Reject all queued requests
        failedRequestsQueue.forEach((pending: any) => pending.reject(refreshError));
        failedRequestsQueue = [];

        // Redirect to login page or handle as needed
        window.location.href = '/';
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // For other errors, just reject
    return Promise.reject(error);
  }
);

export const postMethod = async (method: string, params: object) => {
  try {
    const response = await API.post(method, params);
    return response.data;
  } catch (error: any) {
    if (error.response) {
      return error.response.data;
    } else if (error.request) {
      return { message: "No response from server", details: error.request };
    } else {
      return { message: "Request setup error", details: error.message };
    }
  }
};

export const getMethod = async (method: string, params?: object) => {
  try {
    const response = await API.get(method, { params });
    return response.data;
  } catch (error) {
    console.error("Error in GET request:", error);
    throw error;
  }
};

// Agent API methods
export const agentPostMethod = async (endpoint: string, params: object, accessToken: string) => {
  try {
    //console.log("agentPostMethod I have accessToken======", accessToken);
    // const response = await AgentAPI.post(method, params);
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/${endpoint}`,
      params,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response) {
      return error.response.data;
    } else if (error.request) {
      return {
        message: "No response from agent server",
        details: error.request,
      };
    } else {
      return { message: "Request setup error", details: error.message };
    }
  }
};

export const agentGetMethod = async (method: string, params?: object) => {
  try {
    const response = await AgentAPI.get(method, { params });
    console.log(method);
    return response.data;
  } catch (error) {
    console.error("Error in agent GET request:", error);
    throw error;
  }
};

export const getWebSocketBaseURL = () => {
  if (!agentApiBaseURL) return null;

  const baseUrl = agentApiBaseURL
    .replace("http://", "ws://")
    .replace("https://", "wss://");
  return baseUrl;
};
