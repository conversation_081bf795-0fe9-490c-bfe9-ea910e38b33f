import { BasePage } from './BasePage';
import { fixture } from '../fixtures/Fixture';

export class SignupPage extends BasePage {
  static async goto(url: string) {
    await fixture.page.goto(url);
  }

  static async typeFirstName(firstName: string) {
    await fixture.page.getByPlaceholder('Enter your First Name').fill(firstName);
  }

  static async typeLastName(lastName: string) {
    await fixture.page.getByPlaceholder('Enter your Last Name').fill(lastName);
  }

  static async typeUsername(username: string) {
    await fixture.page.getByPlaceholder('Enter your email ID / Phone Number').fill(username);
  }

  static async typePassword(password: string) {
    await fixture.page.getByPlaceholder('Enter your password').first().fill(password);
  }

  static async typeConfirmPassword(confirmPassword: string) {
    await fixture.page.getByPlaceholder('Enter Confirm Password').fill(confirmPassword);
  }

  static async submit() {
    await fixture.page.getByRole('button', { name: 'Send Verification Code' }).click();
  }

  static async isVerificationPromptVisible() {
    // Implement logic to check for verification code prompt
    return await fixture.page.locator('text=Verification Code').isVisible();
  }

  static async isSignupErrorVisible() {
    // Implement logic to check for error message
    return await fixture.page.locator('text=error').isVisible();
  }
} 