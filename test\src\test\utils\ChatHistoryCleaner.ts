import { fixture } from '../fixtures/Fixture';

/**
 * Helper class for cleaning up chat history in the Shasa chatbot
 * Enhanced with modern Playwright locators
 */
export class ChatHistoryCleaner {
    // The cookie name for the Shasa chat thread
    private static readonly THREAD_COOKIE_NAME = 'nxvoy_thread_id';

    /**
     * Clears the Shasa chat history by removing the thread cookie
     * Uses modern Playwright methods with fallbacks for reliability
     * @returns A promise that resolves to true if the operation was successful
     */
    public static async clearChatHistory(): Promise<boolean> {
        try {
            console.log('🧹 Clearing Shasa chat history...');
            
            // First try using <PERSON>wright's context API (more reliable)
            try {
                const context = fixture.page.context();
                
                // Clear cookies by name with proper API
                await context.clearCookies({
                    name: this.THREAD_COOKIE_NAME
                    // Don't specify domain/path to make it match any cookie with this name
                });
                
                console.log('Used Playwright context API to clear cookies');
            } catch (contextError) {
                console.log(`Context API approach failed: ${contextError}, falling back to JavaScript`);
                
                // Execute JavaScript to delete the chat thread cookie as fallback
                await fixture.page.evaluate((cookieName) => {
                    // Try with multiple path combinations to ensure we catch the cookie
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/chat;`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC;`;
                    console.log(`Attempted to remove cookie: ${cookieName} with multiple paths`);
                }, this.THREAD_COOKIE_NAME);
            }

            // Verify the cookie was removed using Playwright context API first
            let cookieValue = null;
            try {
                const context = fixture.page.context();
                const cookies = await context.cookies();
                const threadCookie = cookies.find(c => c.name === this.THREAD_COOKIE_NAME);
                cookieValue = threadCookie ? threadCookie.value : null;
            } catch (verifyError) {
                // Fall back to JavaScript verification if the API approach fails
                console.log(`Verify with context API failed: ${verifyError}, using JavaScript check`);
                
                cookieValue = await fixture.page.evaluate((cookieName) => {
                    const match = document.cookie.match(new RegExp(`(^| )${cookieName}=([^;]+)`));
                    return match ? match[2] : null;
                }, this.THREAD_COOKIE_NAME);
            }

            if (cookieValue === null) {
                console.log('✅ Successfully cleared Shasa chat history');
                return true;
            } else {
                console.error(`❌ Failed to clear Shasa chat history, cookie still exists: ${cookieValue}`);
                // Try one more aggressive approach
                try {
                    console.log('Trying more aggressive cookie clearing...');
                    await fixture.page.context().clearCookies();
                    console.log('Cleared ALL cookies as a last resort');
                    return true;
                } catch {
                    return false;
                }
            }
        } catch (error) {
            console.error(`❌ Error clearing Shasa chat history: ${error}`);
            return false;
        }
    }
}
