'use client';

import { TravelerDisplayInfo } from '@/constants/models';
import { capitalize } from '@/lib/utils/capitalize';
import { formatFlightPrice } from '@/lib/utils/formatPrice';
import React from 'react'

interface TravelerSummaryProps {
    title: string;
    travelers: TravelerDisplayInfo[];
    counts: Record<'adult' | 'child' | 'infant', number>;
}

const TravelerSummary = ({
    title,
    travelers,
    counts
}: TravelerSummaryProps) => {

    const renderCountText = (counts: Record<'adult' | 'child' | 'infant', number>) => {
        const parts = [];
        if (counts.adult) parts.push(`${counts.adult} ${counts.adult === 1 ? 'adult' : 'adults'}`);
        if (counts.child) parts.push(`${counts.child} ${counts.child === 1 ? 'child' : 'children'}`);
        if (counts.infant) parts.push(`${counts.infant} ${counts.infant === 1 ? 'infant' : 'infants'}`);
        return parts.join(', ');
    };

    return (
        <>
            <div className="text-lg text-brand-black font-semibold">
                {title}
            </div>
            <p className="text-brand-grey text-sm mb-2">
                {Object.values(counts).reduce((a, b) => a + b, 0)} tickets: {renderCountText(counts)}
            </p>
            <div className="w-full"></div>
            {travelers.map((t, i) => (
                <React.Fragment key={i}>
                    <div className="space-y-2 text-[#080236] py-5" key={i}>
                        <div className="flex font-semibold justify-between">
                            <span>Traveler {i + 1} : {capitalize(t.travelerType)}</span>
                            <span>
                                {formatFlightPrice({ amount: t.amount, currency: t.currency })}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span>Ticket Price</span>
                            <span>
                                {formatFlightPrice({ amount: t.ticketPrice, currency: t.currency })}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span>Tax and fees</span>
                            <span>
                                {formatFlightPrice({ amount: t.taxes, currency: t.currency })}
                            </span>
                        </div>
                    </div>
                    <div className="w-full"></div>
                </React.Fragment>
            ))}
        </>
    )
}

export default TravelerSummary