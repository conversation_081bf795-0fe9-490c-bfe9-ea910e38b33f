import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { User, UserResponse } from "@/constants/user";
import { User as Profile } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getInitials } from "@/screens/dashboard/DashboardNavbar";
import axios from "axios";
import tracker from "@/utils/posthogTracker";
import { useCustomSession } from "@/hooks/use-custom-session";
import { signOut } from "next-auth/react";
import { clearReduxOnLogout } from "@/store/clearRedux";

interface NavbarProps {
  handleSignIn?: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ handleSignIn }) => {
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;
  const router = useRouter();

  const [isOpen, setIsOpen] = useState(false);
  const [isLandscape, setIsLandscape] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUserDetails = async () => {
    try {
      if (!token) {
        setLoading(false);
        return;
      }
      console.log("fetchUserDetails======", token);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/get-details`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to fetch user details");
      }

      const data: UserResponse = await response.data;
      setUser(data?.detail?.data);
    } catch (error: any) {
      console.error("Error fetching user details:", error.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchUserDetails();
  }, [token]);

  useEffect(() => {
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    checkOrientation();
    window.addEventListener("resize", checkOrientation);

    return () => {
      window.removeEventListener("resize", checkOrientation);
    };
  }, []);

  const handleLogout = async () => {
    await clearReduxOnLogout();
    signOut({ redirect: false });
  };

  const handleStartBookingClick = () => {
    console.log("Start Booking clicked");
    try {
      tracker.trackEvent("Start Booking Clicked", {
        location: "Homepage",
      });
    } catch (err) {
      console.error("Tracking failed", err);
    }
    router.push("/destination");
  };

  const handleNavigateHome = () => {
    router.push("/");
  };

  return (
    <>
      {/* Desktop/Navbar View */}
      <div
        className={`xs:hidden flex w-full 2xl:h-[58px] xl:h-[58px] lg:h-[48px] md:h-[44px] sm:h-[40px] p-2 justify-around rounded-full ${
          router.pathname === "/" ? "bg-[#B4BBE8]" : "bg-white shadow-md"
        }`}
      >
        <div
          className="flex 2xl:w-1/5 xl:w-1/5 lg:w-1/5 md:w-1/5 sm:w-1/6 relative left-8 sm:left-2 items-center cursor-pointer"
          onClick={handleNavigateHome}
        >
          <img
            className="xl:w-[110px] lg:w-[100px] 2xl:h-[28px] xl:h-[26px] lg:h-[22px] md:h-[18px] sm:h-[14px]"
            src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png"
            alt="Logo"
          />
        </div>
        <div className="flex font-proxima-nova 2xl:w-5/6 xl:w-5/6 lg:w-5/6 md:w-4/5 sm:w-5/6 2xl:gap-6 xl:gap-6 md:gap-4 sm:gap-2 lg:gap-8 relative right-8 sm:right-2 justify-end items-center">
          {/* Page Links */}
          <div className={`${router.pathname === "/home" ? "" : ""}`}>
            {[
              { name: "Home", path: "/" },
              { name: "Membership", path: "/membership" },
              { name: "About Us", path: "/about-us" },
            ].map((tab) => (
              <Link key={tab.name} href={tab.path} legacyBehavior>
                <a
                  className={`2xl:text-lg xl:text-lg lg:text-base md:text-sm sm:text-sm px-2 font-medium cursor-pointer font-proxima-nova rounded-full transition-all duration-300 ${
                    router.pathname === tab.path
                      ? "text-black hover:text-gray-700"
                      : "text-black hover:text-gray-700"
                  }`}
                >
                  {tab.name}
                </a>
              </Link>
            ))}
          </div>

          {/* Chat Button */}
          <div className="relative p-0.5 rounded-full before:absolute before:inset-0 before:rounded-full before:bg-[linear-gradient(to_right,rgba(112,127,245,0.9),rgba(161,149,249,0.9),rgba(242,161,242,0.3))] before:-z-10">
            <button
              onClick={() => {
                tracker.trackEvent("Chat with Shasa Clicked", {
                  topic: "flight booking",
                  location: "Homepage",
                });
                router.push("/chat");
              }}
              className="relative flex flex-row gap-2 items-center text-center px-4 py-1 rounded-[8px] justify-around bg-brand text-white"
            >
              <img
                className="w-4 h-4"
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                alt="Chat"
              />
              <div>Chat with Shasa</div>
            </button>
          </div>

          {/* Auth/User Avatar */}
          {token ? (
            loading ? (
              <div className="w-10 h-10 rounded-full bg-gray-300 animate-pulse" />
            ) : user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Avatar className="cursor-pointer">
                    <AvatarImage src={user.profile_picture || ""} />
                    <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                      {getInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  className="w-[150px] rounded-b-[10px] rounded-t-[0px]"
                >
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={() => router.push("/userprofile")}
                    >
                      <Profile />
                      <span>My Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleLogout}>
                      <img
                        src="https://storage.googleapis.com/nxvoytrips-img/navbar/sign-out.svg"
                        className="w-4 h-4 mr-2"
                        alt="Logout"
                      />
                      <span>Logout</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : null
          ) : (
            <button
              onClick={handleSignIn}
              className="flex flex-row gap-4 px-4 py-1 rounded-[8px] justify-around bg-white text-brand border border-brand"
            >
              <div>Sign In</div>
            </button>
          )}
        </div>
      </div>
      <div
        className={`md:hidden sm:hidden xs:flex w-full p-2 justify-around items-center rounded-full ${router.pathname === "/home" ? "bg-[#B4BBE8]" : "bg-white shadow-md"}`}
      >
        <div className="relative flex w-1/3 left-2">
          <img
            className="w-20"
            src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Logo.png"
            alt=""
          />
        </div>
        {isOpen ? (
          <div className="relative flex flex-row w-2/3 justify-end gap-2 right-2">
            <img
              onClick={() => setIsOpen(!isOpen)}
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Expansion.png"
              alt=""
            />
          </div>
        ) : (
          <div className="relative flex flex-row w-2/3 justify-end gap-2 right-2">
            <img
              onClick={() => setIsOpen(!isOpen)}
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Vector.png"
              alt=""
            />
          </div>
        )}
      </div>
      {isOpen && (
        <div
          className={`absolute top-10 left-0 w-full ${
            isOpen ? "translate-y-0" : "-translate-y-full"
          }`}
        >
          <div className="w-[85%] mx-auto rounded-t-[10px] rounded-b-[10px] bg-brand-white transition-transform duration-300 ">
            <div className="flex w-[95%] mx-auto justify-end">
              <img
                onClick={() => setIsOpen(!isOpen)}
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Expansion.png"
                alt=""
                className="m-2"
              />
            </div>
            <ul className="flex flex-col gap-4 pb-10">
              <div className="w-full divide-y divide-neutral border-t border-neutral">
                <div
                  className={`w-full flex py-4 ${
                    !token ? "justify-center" : "justify-start pl-6"
                  }`}
                >
                  {token ? (
                    <div
                      onClick={() => router.push("/userprofile")}
                      className="flex gap-4 items-center cursor-pointer"
                    >
                      <Avatar>
                        <AvatarImage src={user?.profile_picture || ""} />
                        <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                          {getInitials(user)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-bold ml-4 sm:ml-8 md:ml-16 lg:ml-24">
                        {user
                          ? `${user.firstName} ${user.lastName ?? ""}`.trim()
                          : "User Name"}
                      </span>
                    </div>
                  ) : (
                    <button
                      onClick={handleSignIn}
                      className="px-4 py-2 bg-white rounded-[8px]"
                    >
                      Sign In
                    </button>
                  )}
                </div>

                {[
                  { name: "Home", path: "/" },
                  { name: "Membership", path: "/membership" },
                  { name: "About Us", path: "/about-us" },
                ].map((tab) => (
                  <Link key={tab.name} href={tab.path} legacyBehavior>
                    <a
                      className="w-full flex justify-center py-4
          2xl:text-lg xl:text-lg lg:text-base md:text-sm sm:text-sm
          px-2 font-medium cursor-pointer font-proxima-nova rounded-full
          transition-all duration-300
          text-black hover:text-gray-700"
                    >
                      {tab.name}
                    </a>
                  </Link>
                ))}
              </div>

              <div className="w-full p-[1px] border-neutral border-t border-b flex justify-center py-2 mt-2 gap-6">
                <div className="flex flex-col font-proxima-nova gap-6 xl:gap-6 lg:gap-6 py-4 xl:py-4 lg:py-2 items-center">
                  <button
                    onClick={() => {
                      tracker.trackEvent("Chat with Shasa Clicked", {
                        topic: "flight booking",
                        location: "Homepage",
                      });
                      router.push("/chat");
                    }}
                    className="flex w-max font-medium px-4 xl:px-4 py-1 cursor-pointer xl:text-base lg:text-base bg-brand rounded-[8px] text-white"
                  >
                    <img
                      className="w-5 h-5 mr-[8px]"
                      src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                    />
                    Chat with Shasa
                  </button>
                  <button
                    onClick={handleStartBookingClick}
                    className="flex w-max font-medium px-4 xl:px-4 py-1 xl:text-base lg:text-base cursor-pointer bg-white text-brand border border-brand rounded-[8px]"
                  >
                    Start Booking
                  </button>
                </div>
              </div>

              {token && (
                <div className="flex flex-col justify-start w-full pl-6 gap-4">
                  <div
                    onClick={() => router.push("/userprofile")}
                    className="flex flex-row items-center"
                  >
                    <div className="ml-[-5px]">
                      <Profile className="fill-black stroke-black w-6 h-6" />
                    </div>
                    <span className="ml-[5px]">My Profile</span>
                  </div>
                  <div
                    onClick={handleLogout}
                    className="flex flex-row items-center"
                  >
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/navbar/sign-out.svg"
                      className="w-4 h-4 mr-2 object-contain"
                      alt="Logout"
                    />
                    <span>Sign out</span>
                  </div>
                </div>
              )}
            </ul>
          </div>
        </div>
      )}
    </>
  );
};

export default Navbar;