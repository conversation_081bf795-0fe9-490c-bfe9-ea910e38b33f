import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the sidebar is collapsed
 * @returns boolean - true if sidebar is collapsed, false if expanded
 */
export const useIsCollapsed = (): boolean => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    const checkSidebarState = () => {
      // Check if sidebar is collapsed by looking for data-state="collapsed"
      const isCollapsedState = document.querySelector('[data-state="collapsed"]') !== null;
      setIsCollapsed(isCollapsedState);
    };

    // Check initial state
    checkSidebarState();

    // Create a MutationObserver to watch for changes
    const observer = new MutationObserver(checkSidebarState);
    
    // Watch for changes on the document body
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['data-state', 'data-collapsible', 'class'],
      subtree: true,
    });

    // Also listen for resize events as a fallback
    window.addEventListener('resize', checkSidebarState);

    return () => {
      observer.disconnect();
      window.removeEventListener('resize', checkSidebarState);
    };
  }, []);

  return isCollapsed;
};