export const textVariant = (delay) => {
  return {
    hidden: {
      y: 50,
      opacity: 0,
    },
    show: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        duration: 1.25,
        delay: delay,
      },
    },
  };
};

export const fadeIn = (direction, type, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? 100 : direction === "right" ? -100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: type,
        delay: delay,
        duration: duration,
        ease: "easeOut",
      },
    },
  };
};

export const slideIn = (direction, type, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? "-500%" : direction === "right" ? "100%" : 0,
      y: direction === "up" ? "100%" : direction === "down" ? "100%" : 0,
    },
    show: {
      x: 0,
      y: 0,
      transition: {
        type: type,
        delay: delay,
        duration: duration,
        ease: "easeOut",
      },
    },
  };
};

export const staggerContainer = (staggerChildren, delayChildren) => {
  return {
    hidden: {},
    show: {
      transition: {
        staggerChildren: staggerChildren,
        delayChildren: delayChildren || 0,
      },
    },
  };
};

export const bounceIn = (delay, duration) => {
  return {
    hidden: {
      scale: 0.5,
      opacity: 0,
    },
    show: {
      scale: [1.2, 0.9, 1.1, 1],
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
        stiffness: 300,
      },
    },
  };
};

export const fadeScaleIn = (delay, duration) => {
  return {
    hidden: {
      scale: 0.8,
      opacity: 0,
    },
    show: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
  };
};
export const fadeInBlur = (delay, duration) => {
  return {
    hidden: {
      filter: 'blur(10px)',
      opacity: 0,
    },
    show: {
      filter: 'blur(0px)',
      opacity: 1,
      transition: {
        type: "tween",
        delay: delay,
        duration: duration,
        ease: "easeOut",
      },
    },
  };
};

export const simpleFadeIn = (delay, duration) => {
  return {
    hidden: {
      opacity: 0,
    },
    show: {
      opacity: 1,
      transition: {
        type: "tween",
        delay: delay,
        duration: duration,
        ease: "easeOut",
      },
    },
  };
};

export const imageFadeIn = (delay, duration) => {
  return {
    hidden: {
      opacity: 0,
      scale: 0.95,
    },
    show: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
        stiffness: 100,
        damping: 10,
      },
    },
  };
};


export const slideFadeIn = (direction, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
  };
};

export const wave = (delay, duration) => {
  return {
    hidden: {
      x: 0,
      y: 0,
    },
    show: {
      x: [0, 20, -20, 20, -20, 0],
      y: [0, -10, 10, -10, 10, 0],
      transition: {
        repeat: Infinity,
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
  };
};

export const slideFadeFromCorner = (corner, delay, duration) => {
  const positions = {
    topLeft: { x: -100, y: -100 },
    topRight: { x: 100, y: -100 },
    bottomLeft: { x: -100, y: 100 },
    bottomRight: { x: 100, y: 100 },
  };

  return {
    hidden: {
      x: positions[corner]?.x || 0,
      y: positions[corner]?.y || 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
  };
};


export const slideFadeBounce = (direction, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        bounce: 0.5,
        delay: delay,
        duration: duration,
      },
    },
  };
};


export const slideFadeOut = (direction, delay, duration, hold) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
    exit: {
      x: direction === "left" ? 100 : direction === "right" ? -100 : 0,
      y: direction === "up" ? -100 : direction === "down" ? 100 : 0,
      opacity: 0,
      transition: {
        type: "tween",
        delay: hold,
        duration: duration,
        ease: "easeInOut",
      },
    },
  };
};

export const slideFadeScale = (direction, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      scale: 0.5,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        delay: delay,
        duration: duration,
      },
    },
  };
};

export const slideFadeZigzag = (delay, duration) => {
  return {
    hidden: {
      x: -100,
      opacity: 0,
    },
    show: {
      x: [0, 30, -30, 15, -15, 0],
      opacity: 1,
      transition: {
        type: "tween",
        delay: delay,
        duration: duration,
        ease: "easeOut",
      },
    },
  };
};

export const slideFadeElastic = (direction, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 50,
        damping: 10,
        delay: delay,
        duration: duration,
      },
    },
  };
};

export const slideFadeOvershoot = (direction, delay, duration) => {
  return {
    hidden: {
      x: direction === "left" ? -100 : direction === "right" ? 100 : 0,
      y: direction === "up" ? 100 : direction === "down" ? -100 : 0,
      opacity: 0,
    },
    show: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 8,
        delay: delay,
        duration: duration,
      },
    },
  };
};
