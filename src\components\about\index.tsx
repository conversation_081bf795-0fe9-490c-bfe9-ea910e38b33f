"use client";

import { useEffect, useState, useRef } from "react";
import {
  motion,
  useScroll,
  useTransform,
  useMotionValueEvent,
} from "framer-motion";
import { fadeIn, fadeScaleIn, slideIn } from "@/utils/motion";

export const About: React.FC = () => {
  const [isReversed, setIsReversed] = useState<boolean>(false);
  const [videoCompleted, setVideoCompleted] = useState<boolean>(false);
  const [animationsCompleted, setAnimationsCompleted] =
    useState<boolean>(false);
  const [aboutInView, setAboutInView] = useState<boolean>(false);

  const aboutRef = useRef<HTMLDivElement | null>(null);
  const sectionRef = useRef<HTMLDivElement | null>(null);

  const { scrollYProgress } = useScroll();
  const xTransform = useTransform(scrollYProgress, [0, 0.05], ["100vw", "0vw"]);
  const opacity = useTransform(scrollYProgress, [0.8, 1], [0, 1]);

  useMotionValueEvent(xTransform, "change", (latest: string) => {
    setVideoCompleted(latest === "0vw");
  });

  const handleAnimationComplete = () => {
    setAnimationsCompleted(true);
  };

  const { scrollYProgress: sectionScrollProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  const titleOpacity = useTransform(
    scrollYProgress,
    [0, 0.2],
    videoCompleted ? [0, 1] : [0, 0],
  );
  const subtitleOpacity = useTransform(
    scrollYProgress,
    [0.2, 0.3],
    videoCompleted ? [0, 1] : [0, 0],
  );
  const textOpacity = useTransform(
    scrollYProgress,
    [0.3, 0.4],
    videoCompleted ? [0, 1] : [0, 0],
  );

  useEffect(() => {
    let lastScrollY = window.scrollY;

    const handleScroll = () => {
      setIsReversed(window.scrollY < lastScrollY);
      lastScrollY = window.scrollY;
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setAboutInView(entry.isIntersecting),
      { threshold: 1 },
    );

    if (aboutRef.current) {
      observer.observe(aboutRef.current);
    }

    return () => {
      if (aboutRef.current) {
        observer.unobserve(aboutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (aboutInView && !animationsCompleted) {
      document.body.style.overflowX = "hidden"; // Lock scroll
    } else {
      document.body.style.overflowY = "auto"; // Unlock scroll
    }
  }, [aboutInView, animationsCompleted]);

  return (
    <div
      ref={sectionRef}
      className="relative w-full flex bg-brand-white mx-auto h-max py-10 overflow-y-auto justify-center"
      id="aboutus"
    >
      <div
        ref={aboutRef}
        className="xs:hidden sm:hidden md:flex xl:w-[90%] lg:w-[90%] md:w-[90%] mx-auto gap-5 items-center justify-center"
      >
        {/* Video Section */}
        <motion.div
          style={{ x: xTransform }}
          className="flex xl:w-3/6 md:w-2/4 h-full"
        >
          <video autoPlay muted loop playsInline className="w-full h-full">
            <source
              className="flex w-full h-full"
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/SHASA%20face%20AI%20640x640.mp4"
              type="video/mp4"
            />
          </video>
        </motion.div>

        {/* Text Section */}
        <div className="flex flex-col font-proxima-nova overflow-y-hidden xl:w-full md:w-2/4">
          <div className="flex flex-col gap-4 xl:w-[90%] mx-auto">
            <div className="flex flex-col gap-2">
              <motion.div
                // style={{opacity:titleOpacity}}
                // initial="hidden"
                animate={videoCompleted ? "show" : "hidden"} // Trigger animation only when video is done
                variants={fadeIn("up", "tween", 0, 0.5)}
                className="2xl:text-6xl xl:text-5xl lg:text-4xl md:text-3xl font-bold bg-gradient-to-b from-[#707FF5] via-[#A195F9] to-[#F2A1F2] bg-clip-text text-transparent"
              >
                A Trip Without Me?
              </motion.div>
              <motion.div
                // style={{opacity:subtitleOpacity}}
                initial="hidden"
                animate={videoCompleted ? "show" : "hidden"}
                variants={fadeIn("up", "tween", 0.25, 0.5)}
                className="2xl:text-6xl xl:text-5xl lg:text-4xl md:text-3xl font-medium bg-gradient-to-b from-[#707FF5] via-[#A195F9] to-[#F2A1F2] bg-clip-text text-transparent"
              >
                Now, That's No Fun!
              </motion.div>
            </div>
            <motion.div
              // style={{opacity:textOpacity}}
              initial="hidden"
              animate={videoCompleted ? "show" : "hidden"}
              variants={fadeIn("up", "tween", 0.75, 1)}
              className="2xl:text-xl xl:text-lg lg:text- text-[#080236] md:text-sm font-proxima-nova leading-6"
            >
              Have you ever wished for a travel companion who remembers
              everything, keeps you on track, and ensures you have the best
              experience without lifting a finger? That’s me - Shasa.
            </motion.div>
            <motion.div
              // style={{opacity:textOpacity}}
              initial="hidden"
              animate={videoCompleted ? "show" : "hidden"}
              variants={fadeIn("up", "tween", 0.75, 1)}
              className="2xl:text-xl xl:text-lg lg:text-base md:text-sm text-[#080236] font-proxima-nova leading-6"
            >
              I might be an AI-powered travel partner, but I promise to stay as
              close to you as your shadow, remembering every detail that matters
              to you on your trip. I’m here to plan, remind, and guide you
              through every step of your journey.
            </motion.div>
            <motion.div
              // style={{opacity:textOpacity}}
              initial="hidden"
              animate={videoCompleted ? "show" : "hidden"}
              variants={fadeIn("up", "tween", 0.75, 1)}
              className="2xl:text-xl xl:text-lg lg:text-base md:text-sm text-[#080236] font-proxima-nova leading-6"
              onAnimationComplete={handleAnimationComplete}
            >
              Whether it’s a solo adventure, a family getaway, a bachelor party,
              or a friends’ trip, I’ll make sure every moment is filled with
              great experiences.
            </motion.div>
          </div>
        </div>
      </div>
      <div className="md:hidden sm:flex xs:flex w-[85%] mx-auto flex-col gap-4 items-center text-center overflow-hidden justify-center">
        <div className="flex flex-col gap-2">
          <motion.div
            initial="hidden"
            whileInView="show"
            variants={fadeIn("right", "", 0.5, 0.5)}
            viewport={{ once: false, amount: 0.25 }}
            className="sm:text-3xl xs:text-3xl font-bold bg-gradient-to-b from-[#707FF5] via-[#A195F9] to-[#F2A1F2] bg-clip-text text-transparent"
          >
            A Trip Without Me?
          </motion.div>
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: false, amount: 0.25 }}
            variants={fadeIn("right", "", 1, 1)}
            className="sm:text-3xl xs:text-3xl font-bold bg-gradient-to-b from-[#707FF5] via-[#A195F9] to-[#F2A1F2] bg-clip-text text-transparent"
          >
            Now, That's No Fun!
          </motion.div>
        </div>
        <motion.div
          initial="hidden"
          whileInView="show"
          variants={fadeScaleIn(1.5, 1)}
          className="flex sm:w-full xs:w-full justify-center items-center h-full"
        >
          <video
            autoPlay
            muted
            loop
            playsInline
            className=" w-full sm:w-4/5 h-full"
          >
            <source
              className="flex w-full h-full"
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/SHASA%20face%20AI%20640x640.mp4"
              type="video/mp4"
            />
          </video>
        </motion.div>
        <div className="flex flex-col gap-2 w-full justify-center items-center text-center">
          <motion.div
            initial="hidden"
            whileInView="show"
            // animate={videoCompleted ? "show" : "hidden"}
            variants={fadeIn("right", "spring", 0.5, 2)}
            className="sm:text-base text-sm font-proxima-nova leading-6 text-[#080236]"
          >
            Have you ever wished for a travel companion who remembers
            everything, keeps you on track, and ensures you have the best
            experience without lifting a finger? That’s me - Shasa.
          </motion.div>
          <motion.div
            initial="hidden"
            whileInView="show"
            // animate={videoCompleted ? "show" : "hidden"}
            variants={fadeIn("left", "spring", 0.5, 2.5)}
            className="sm:text-base text-sm font-proxima-nova leading-6 text-[#080236]"
          >
            I might be an AI-powered travel partner, but I promise to stay as
            close to you as your shadow, remembering every detail that matters
            to you on your trip. I’m here to plan, remind, and guide you through
            every step of your journey.
          </motion.div>
          <motion.div
            initial="hidden"
            whileInView="show"
            // animate={videoCompleted ? "show" : "hidden"}
            variants={fadeIn("right", "spring", 0.5, 3)}
            className="sm:text-base text-sm font-proxima-nova leading-6 text-[#080236]"
          >
            Whether it’s a solo adventure, a family getaway, a bachelor party,
            or a friends’ trip, I’ll make sure every moment is filled with great
            experiences.
          </motion.div>
        </div>
      </div>
    </div>
  );
};
