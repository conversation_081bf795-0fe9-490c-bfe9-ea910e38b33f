"use client"

import { useState } from "react"
import RoomCard from "./room-card"
import RoomDetailsModal from "./room-details-modal"
import { Room } from "@/lib/types"

interface RoomListProps {
    rooms: Room[]
    selectedHotel: any
}

export default function RoomList({ rooms, selectedHotel }: RoomListProps) {
    const [selectedRoom, setSelectedRoom] = useState<any>(null)
    const [selectedRateIndex, setSelectedRateIndex] = useState<number>(0)
    const [isModalOpen, setIsModalOpen] = useState(false)

    const openRoomDetails = (room: any, rateIndex: number = 0) => {
        setSelectedRoom(room)
        setSelectedRateIndex(rateIndex)
        setIsModalOpen(true)
    }

    const closeModal = () => {
        setIsModalOpen(false)
    }

    return (
        <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {rooms.map((room: any) => (
                    <RoomCard 
                        key={room.room_code} 
                        room={room} 
                        selectedHotel={selectedHotel}
                        onViewDetails={(rateIndex) => openRoomDetails(room, rateIndex)} 
                    />
                ))}
            </div>

            {isModalOpen && selectedRoom && (
                <RoomDetailsModal 
                    room={selectedRoom} 
                    selectedRateIndex={selectedRateIndex}
                    selectedHotel={selectedHotel}
                    isOpen={isModalOpen} 
                    onClose={closeModal} 
                />
            )}
        </div>
    )
}