import { toast } from "@/hooks/use-toast";
import { Copy, ThumbsDown, ThumbsUp } from "lucide-react";
import { useState } from "react";

const reactions = [
  { emoji: <ThumbsUp className="h-4" />, label: "like" },
  { emoji: <ThumbsDown className="h-4" />, label: "dislike" },
  { emoji: <Copy className="h-4" />, label: "copy" },
];

type MyComponentProps = {
  onSelect: (reaction: string, messageId: string) => void;
  messageId: string;
  copyText?: string;
};

export const ChatReactionBar: React.FC<MyComponentProps> = ({
  onSelect,
  messageId,
  copyText = "",
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  return (
    <div className="relative flex items-center rounded-full  w-fit">
      {reactions.map((reaction, index) => (
        <div
          key={index}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
          className="relative flex flex-col items-center group"
          onClick={() => {
            if (reaction.label === "copy") {
              navigator.clipboard.writeText(copyText).then(() => {
                toast({
                  title: "Text copied",
                  className: "bg-[#1E1E76]  text-white top-10 left-1/2 -translate-x-1/2 absolute z-[9999]",
                  duration: 2000,
                });
              });
            } else {
              onSelect(reaction.label, messageId);
            }
          }}
        >
          {/* Hover circle effect */}
          <button
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            aria-label={reaction.label}
          >
            <span className="text-xl">{reaction.emoji}</span>
          </button>

          {/* Tooltip */}
          {/* {hoveredIndex === index && (
            <div className="absolute -bottom-8 px-2 py-1 text-xs text-white bg-black rounded shadow">
              {reaction.label}
            </div>
          )} */}
        </div>
      ))}
    </div>
  );
};
