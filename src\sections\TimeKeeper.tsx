import OverlayCard from "@/components/phone-overlay-card/OverlayCard";
import { timeKeepers } from "@/constants";
import { AnimatePresence, motion, useInView } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";

const TimeKeeper = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const phaseRef = useRef<HTMLDivElement>(null);
  const isSectionInView = useInView(sectionRef, { margin: "-50% 0px" });

  const [step, setStep] = useState(0);
  const [scrollLocked, setScrollLocked] = useState(false);
  const [shouldReset, setShouldReset] = useState(false);
  const [isFullyInView, setIsFullyInView] = useState(false);
  const [reenteredFromBottom, setReenteredFromBottom] = useState(false);
  const [showPersistentStep6, setShowPersistentStep6] = useState(false);

  const maxStep = 7;

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsFullyInView(entry.intersectionRatio >= 0.95);
      },
      { threshold: [0, 0.25, 0.5, 0.75, 0.95, 1] },
    );

    if (sectionRef.current) observer.observe(sectionRef.current);
    return () => {
      if (sectionRef.current) observer.unobserve(sectionRef.current);
    };
  }, []);

  useEffect(() => {
    let previousRatio = 0;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const currentRatio = entry.intersectionRatio;
        if (
          currentRatio > previousRatio &&
          entry.isIntersecting &&
          step === 0
        ) {
          setReenteredFromBottom(true);
        }
        previousRatio = currentRatio;
      },
      { threshold: [0, 0.25, 0.5, 0.75, 1] },
    );

    if (phaseRef.current) observer.observe(phaseRef.current);
    return () => {
      if (phaseRef.current) observer.unobserve(phaseRef.current);
    };
  }, [step]);

  useEffect(() => {
    if (isSectionInView && shouldReset) {
      setStep(0);
      setShouldReset(false);
      setShowPersistentStep6(false);
    } else if (!isSectionInView && step > 0) {
      setShouldReset(true);
    }
  }, [isSectionInView, step]);

  useEffect(() => {
    if (step === 6) {
      setShowPersistentStep6(true);
    }
  }, [step]);

  useEffect(() => {
    if (!isFullyInView) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (scrollLocked) return;

      if (e.key === "ArrowDown" || e.key === "PageDown") {
        e.preventDefault();
        setScrollLocked(true);

        if (step === maxStep - 1) {
          // When at step 6, scroll to next section naturally
          window.scrollBy({ top: window.innerHeight, behavior: "smooth" });
        } else {
          setStep((prev) => Math.min(prev + 1, maxStep));
        }

        setTimeout(() => setScrollLocked(false), 300);
      } else if (e.key === "ArrowUp" || e.key === "PageUp") {
        e.preventDefault();
        setScrollLocked(true);
        setStep((prev) => {
          if (reenteredFromBottom && prev === 0) {
            setReenteredFromBottom(false);
            return 1;
          }
          return Math.max(prev - 1, 0);
        });
        setTimeout(() => setScrollLocked(false), 300);
      }
    };

    const handleScroll = (e: WheelEvent) => {
      if (scrollLocked) return;

      e.preventDefault();
      setScrollLocked(true);

      if (e.deltaY > 0) {
        // Scrolling down
        if (step === maxStep - 1) {
          // When at step 6, scroll to next section naturally
          window.scrollBy({ top: window.innerHeight, behavior: "smooth" });
        } else {
          setStep((prev) => Math.min(prev + 1, maxStep));
        }
      } else {
        // Scrolling up
        setStep((prev) => {
          if (reenteredFromBottom && prev === 0) {
            setReenteredFromBottom(false);
            return 1;
          }
          return Math.max(prev - 1, 0);
        });
      }

      setTimeout(() => setScrollLocked(false), 300);
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("wheel", handleScroll, { passive: false });

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("wheel", handleScroll);
    };
  }, [scrollLocked, isFullyInView, reenteredFromBottom, step]);

  const getOverlayImage = () => {
    if (step === maxStep && showPersistentStep6) {
      return timeKeepers.find((item) => item.step === maxStep - 1);
    }
    return timeKeepers.find((item) => item.step === step);
  };

  const getPhoneImage = () => {
    switch (step) {
      case 1:
        return "/images/timekeeper/phone-main.png";
      case 2:
        return "/images/timekeeper/phone-alert-bg.png";
      case 3:
        return "/images/timekeeper/tour-guide-bg.png";
      case 4:
        return "/images/timekeeper/museum-bg.png";
      case 5:
        return "/images/timekeeper/flight-reminder.png";
      case 6:
      case 7:
        return "/images/timekeeper/blank.png";
      default:
        return null;
    }
  };

  return (
    <section
      ref={sectionRef}
      className="relative flex w-full min-h-screen flex-col items-center justify-start my-10"
    >
      <div ref={phaseRef} className="absolute top-0 h-[1px] w-full" />

      <AnimatePresence>
        {isSectionInView && (
          <motion.div
            key="text-content"
            className="text-center w-[85%] mx-auto"
            initial={{ opacity: 0, y: 150 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 150 }}
            transition={{ duration: 0.3 }}
          >
            <h1 className="font-bold text-[#1E1E76] w-full text-center text-2xl md:text-5xl sm:text-3xl leading-tight font-proxima-nova">
              I'm your personal travel timekeeper!
            </h1>
            <span className="font-proxima-nova font-medium tex-[#080236] text-sm sm:text-sm leading-none md:text-base text-center inline-block max-w-3xl mt-2">
              I'll never let you miss a check-in, arrive late to an activity, or
              skip a meal - I've scheduled them all, so you can enjoy every
              moment happily!
            </span>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="w-full h-full flex">
        <AnimatePresence>
          {isFullyInView && (
            <div className="absolute flex w-full h-full justify-center items-center py-5">
              <motion.div
                key="phone-container"
                initial={{ opacity: 0, x: 10 }}
                animate={{ opacity: 1, x: "0%", left: "50%" }}
                transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
                className="flex justify-center items-center xl:w-72 lg:w-64 h-max"
              >
                {getPhoneImage() && (
                  <img
                    src={getPhoneImage() || ""}
                    alt="phone-constant"
                    className="w-full h-full object-contain"
                  />
                )}

                {getOverlayImage() && (
                  <motion.div
                    key={`overlay-${step}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                  >
                    <OverlayCard {...getOverlayImage()} />
                  </motion.div>
                )}

                {/* Persistent step 6 overlay when at step 7 */}
                {step === maxStep && showPersistentStep6 && (
                  <motion.div
                    key="persistent-step6-overlay"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                  >
                    <OverlayCard
                      {...timeKeepers.find(
                        (item) => item.step === maxStep - 1,
                      )!}
                    />
                  </motion.div>
                )}
              </motion.div>
            </div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default TimeKeeper;
