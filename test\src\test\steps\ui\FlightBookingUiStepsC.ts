import { Given, When, Then } from '@cucumber/cucumber';
import { FlightSearchPage } from '../../pages/FlightSearchPage';
import { Properties } from '../../properties/Properties';
import Assert from '../../utils/AssertionsHelper';
import { fixture } from '../../fixtures/Fixture';
import { expect } from '@playwright/test';
import { WaitHelper } from '../../waits/WaitHelper';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';
import * as fs from 'fs';
import * as path from 'path';
import { fillPassengerInfo } from './optimized-passenger-info'; // Add import for the optimized passenger info function
import { fillPassportCountry } from './PassportCountryFix'; // Import the enhanced passport country handling
import { ReviewAndPayPage } from '../../pages/ReviewAndPayPage'; // Import ReviewAndPayPage for navigation checks
import { generateRandomPassenger, logPassengerData } from '../../utils/RandomPassengerDataGenerator';
import { PassengerDataStore } from '../../utils/PassengerDataStore';

// Add PageHelper implementation directly in this file since import is failing
class PageHelper {
    /**
     * Capture detailed debug information about the current page state
     */    public static async captureDebugInfo(context: string, forceScreenshot: boolean = false): Promise<void> {
        try {
            console.log(`Capturing debug info for: ${context}`);
            
            // Get current URL first as it's most reliable
            const url = await fixture.page.url();
            console.log(`Current URL: ${url}`);
            
            // Only take screenshot if forced or if it appears to be an error context
            const isErrorContext = context.includes('error') || context.includes('fail');
            if (forceScreenshot || isErrorContext) {
                await ScreenshotHelper.takeScreenshot(`debug-${context}`, true, isErrorContext);
            } else {
                console.log(`Debug mode: Skipping screenshot for ${context}`);
            }
            
            // Get page title
            try {
                const title = await fixture.page.title();
                console.log(`Page title: ${title}`);
            } catch (e) {
                console.log('Could not get page title');
            }
            
            // Try to get some page content
            try {
                const bodyText = await fixture.page.$eval('body', (el) => el.innerText.substring(0, 1000));
                console.log(`Page content sample: ${bodyText.substring(0, 500)}...`);
            } catch (e) {
                console.log('Could not get page content');
            }
            
            console.log('Debug info capture completed');
        } catch (error) {
            console.error(`Error capturing debug info: ${error}`);
        }
    }
    
    /**
     * Perform a robust click operation with multiple strategies
     */
    public static async robustClick(
        selector: string, 
        options: { 
            timeout?: number, 
            force?: boolean,
            description?: string,
            fallbackSelectors?: string[]
        } = {}
    ): Promise<boolean> {
        const {
            timeout = 10000,
            force = false,
            description = selector,
            fallbackSelectors = []
        } = options;
        
        try {
            console.log(`Attempting to click: ${description}`);
            
            // First try with standard click
            await fixture.page.click(selector, { timeout });
            console.log(`Successfully clicked: ${description}`);
            return true;
        } catch (initialError) {
            console.log(`Initial click failed: ${initialError}. Trying alternative approaches...`);
            
            // Try fallback selectors if provided
            if (fallbackSelectors.length > 0) {
                for (const fallbackSelector of fallbackSelectors) {
                    try {
                        console.log(`Trying fallback selector: ${fallbackSelector}`);
                        await fixture.page.click(fallbackSelector, { timeout: timeout / 2 });
                        console.log(`Successfully clicked fallback: ${fallbackSelector}`);
                        return true;
                    } catch (fallbackError) {
                        console.log(`Fallback selector ${fallbackSelector} failed: ${fallbackError}`);
                    }
                }
            }
            
            // Try force click option
            try {
                console.log('Trying force click option');
                await fixture.page.click(selector, { force: true, timeout: timeout / 2 });
                console.log(`Successfully force-clicked: ${description}`);
                return true;
            } catch (forceError) {
                console.log(`Force click failed: ${forceError}`);
            }
            
            // Try JavaScript click as last resort
            try {
                console.log('Trying JavaScript click');
                await fixture.page.$eval(selector, (element) => {
                    if (element instanceof HTMLElement) {
                        element.click();
                        return true;
                    }
                    return false;
                });
                console.log(`Successfully JavaScript-clicked: ${description}`);
                return true;
            } catch (jsError) {
                console.log(`JavaScript click failed: ${jsError}`);
                
                // Take a debug screenshot
                await ScreenshotHelper.takeScreenshot(`click-failed-${description.replace(/[^a-zA-Z0-9]/g, '-')}`, true, true);
                
                // Log failure but don't throw
                console.error(`All click strategies failed for: ${description}`);
                return false;
            }
        }
    }
}

When('user enter the valid username {string} for signin', async function (userType: string) {
    const username = Properties.getProperty(userType);
    await FlightSearchPage.setUsername(username);
});

When('user enter the valid password {string} for signin', async function (userType: string) {
    const password = Properties.getProperty(userType);
    await FlightSearchPage.setPassword(password);
});

When('user click the signin button', async function () {
    await FlightSearchPage.clickSigninButton();
});

Then('user is on the homepage loggedin', async function () {
    // Verify user is logged in - could check for profile icon or welcome message
    await fixture.page.waitForSelector('[data-testid="user-profile"]', { timeout: 10000 });
    const isLoggedIn = await fixture.page.isVisible('[data-testid="user-profile"]');
    expect(isLoggedIn).toBeTruthy();
});

Given('User is on the flights search {string} page directly', async function (urlKey: string) {
    const url = Properties.getProperty(urlKey);
    await fixture.page.goto(url);
    await Assert.assertURL(url);
});

When('user select the {string} trip type', async function (tripTypeKey: string) {
    try {
        console.log(`Selecting trip type from property: ${tripTypeKey}`);
        const tripType = Properties.getProperty(tripTypeKey);
        console.log(`Resolved trip type value: ${tripType}`);
        
        // Take a screenshot before trip type selection
        await takeScreenshot('before-trip-type-selection');
        
        // Open trip type selection
        await FlightSearchPage.openTripTypeSelection();
        
        // Wait for a moment
        await fixture.page.waitForTimeout(1000);
        
        // Select the trip type
        await FlightSearchPage.selectTripType(tripType);
        
        // Take a screenshot after trip type selection
        await takeScreenshot('after-trip-type-selection');
    } catch (error) {
        console.error('Error during trip type selection:', error);
        await takeScreenshot('trip-type-selection-error');
        throw error;
    }
});

When('user select {string} passengers', async function (passengerCountKey: string) {
    try {
        console.log(`Selecting passenger count from property: ${passengerCountKey}`);
        const passengerData = Properties.getProperty(passengerCountKey);
        console.log(`Resolved passenger count data:`, passengerData);
          // Take a screenshot before passenger selection
        await takeScreenshot('before-passenger-selection');
        
        // Check if the passengers button is visible before clicking
        const isButtonVisible = await FlightSearchPage.isPassengersButtonVisible();
        if (!isButtonVisible) {
            console.warn("Passengers button not visible, taking screenshot");
            await ScreenshotHelper.takeScreenshot('passenger-button-not-visible', true, true);
        }
        
        // Open passenger selection dialog
        await FlightSearchPage.openPassengersSelection();
          // Wait for the dialog to appear with proper error handling
        await fixture.page.waitForTimeout(1000);
        const isDialogVisible = await FlightSearchPage.isPassengerDialogVisible();
        if (!isDialogVisible) {
            console.warn("Passenger dialog not visible after clicking button");
            // Only take screenshot on errors
            await ScreenshotHelper.takeScreenshot('passenger-dialog-not-visible', true, true);
        }
        
        // Handle different types of passenger data
        if (passengerData) {
            if (typeof passengerData === 'object') {
                // Handle object format with adult, child, infant counts
                const passengerObj = passengerData as {adult?: number, child?: number, infant?: number};
                console.log('Using passenger data format with counts:', 
                    `Adults: ${passengerObj.adult || 0}, Children: ${passengerObj.child || 0}, Infants: ${passengerObj.infant || 0}`);
                
                // Use the object method with properly typed object
                await FlightSearchPage.setPassengerCountFromObject({
                    adult: passengerObj.adult || 0,
                    child: passengerObj.child || 0,
                    infant: passengerObj.infant || 0
                });
            } else {
                // Handle simple string format
                console.log('Using legacy passenger data format (simple count):', passengerData);
                await FlightSearchPage.setPassengerCount(String(passengerData));
            }
        } else {
            // Handle null/undefined case
            console.warn('No passenger data provided, using default of 1 adult');
            await FlightSearchPage.setPassengerCount('1');
        }
        
        // Take a screenshot after passenger selection
        await takeScreenshot('after-passenger-selection');
    } catch (error) {
        console.error('Error during passenger selection:', error);
        await takeScreenshot('passenger-selection-error');
        throw error;
    }
});

When('user select the class type {string}', async function (classType: string) {
    try {
        console.log(`Selecting class type: ${classType}`);
        
        // Take a screenshot before class selection
        await takeScreenshot('before-class-selection');
        
        // Open class selection dialog
        await FlightSearchPage.openClassSelection();
        
        // Wait for a moment for the dialog to appear
        await fixture.page.waitForTimeout(1000);
        
        // Select the class type
        await FlightSearchPage.selectClassType(classType);
        
        // Take a screenshot after class selection
        await takeScreenshot('after-class-selection');
    } catch (error) {
        console.error('Error during class type selection:', error);
        await takeScreenshot('class-selection-error');
        throw error;
    }
});

When('User enter the Departure Location {string}', async function (locationKey: string) {
    try {
        console.log(`Setting departure location from property: ${locationKey}`);
        const location = Properties.getProperty(locationKey);
        console.log(`Resolved departure location value: ${location}`);        // Removed screenshot captures for non-error scenarios
        
        // Set the departure location
        await FlightSearchPage.setDepartureLocation(location);
    } catch (error) {
        console.error('Error during departure location entry:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('departure-location-error', true, true);
        throw error;
    }
});

When('Assert the departure Location suggestion dropdown is displayed', async function () {
    try {
        const isVisible = await FlightSearchPage.isLocationSuggestionsVisible();
          // Take a screenshot only in debug mode
        const isDebuggingLocations = process.env.DEBUG_FLIGHT_SEARCH === 'true';
        if (isDebuggingLocations) {
            await ScreenshotHelper.takeScreenshot('departure-location-suggestions', true, true);
        }
        
        // Assert that the dropdown is visible
        expect(isVisible).toBeTruthy();
        
        console.log('Successfully verified that location suggestions dropdown is displayed');
    } catch (error) {
        console.error('Error verifying departure location suggestions:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('departure-suggestions-error', true, true);
        throw error;
    }
});

When('user selected the Departure location based on airportcode {string}', async function (airportCodeKey: string) {
    try {
        console.log(`Selecting departure location based on airport code from property: ${airportCodeKey}`);
        const airportCode = Properties.getProperty(airportCodeKey);
        console.log(`Resolved airport code value: ${airportCode}`);        // Removed screenshot captures for non-error scenarios
        
        // Select location by airport code
        await FlightSearchPage.selectLocationByAirportCode(airportCode);
        
        console.log(`Successfully selected departure location with airport code ${airportCode}`);
    } catch (error) {
        console.error('Error during departure airport selection:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('departure-airport-selection-error', true, true);
        throw error;
    }
});

When('user enter the Arrival Location {string}', async function (locationKey: string) {
    try {
        console.log(`Setting arrival location from property: ${locationKey}`);
        const location = Properties.getProperty(locationKey);
        console.log(`Resolved arrival location value: ${location}`);        // Removed screenshot captures for non-error scenarios
        
        // Set the arrival location
        await FlightSearchPage.setArrivalLocation(location);
    } catch (error) {
        console.error('Error during arrival location entry:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('arrival-location-error', true, true);
        throw error;
    }
});

When('Assert the Arrival Location suggestion dropdown is displayed', async function () {
    try {
        const isVisible = await FlightSearchPage.isLocationSuggestionsVisible();
        
        // Removed screenshot capture for non-error scenarios
        
        // Assert that the dropdown is visible
        expect(isVisible).toBeTruthy();
        
        console.log('Successfully verified that location suggestions dropdown is displayed');
    } catch (error) {
        console.error('Error verifying arrival location suggestions:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('arrival-suggestions-error', true, true);
        throw error;
    }
});

When('user selected the Arrival location based on airportcode {string}', async function (airportCodeKey: string) {
    try {        console.log(`Selecting arrival location based on airport code from property: ${airportCodeKey}`);
        const airportCode = Properties.getProperty(airportCodeKey);
        console.log(`Resolved airport code value: ${airportCode}`);
        
        // Removed screenshot captures for non-error scenarios
        
        // Select location by airport code
        await FlightSearchPage.selectLocationByAirportCode(airportCode);
        
        console.log(`Successfully selected arrival location with airport code ${airportCode}`);
    } catch (error) {
        console.error('Error during arrival airport selection:', error);
        // Force screenshot on error
        await ScreenshotHelper.takeScreenshot('arrival-airport-selection-error', true, true);
        throw error;
    }
});

When('user select the departure date {string}', async function (dateKey: string) {
    try {
        console.log(`Setting departure date from property: ${dateKey}`);
        const date = Properties.getProperty(dateKey);
        console.log(`Resolved departure date value: ${date}`);
        
        // Take a screenshot before setting date
        await takeScreenshot('before-departure-date');
        
        // Get the current trip type to dynamically handle date selection
        let currentTripType;
        try {
            currentTripType = await FlightSearchPage.getTripType();
            console.log(`Current trip type: ${currentTripType}`);
        } catch (tripTypeError) {
            console.log(`Could not determine current trip type: ${tripTypeError}`);
            console.log('Continuing with date selection without specifying trip type');
        }
        
        // For round trip, we need to check if the date format is YYYY-MM-DD,YYYY-MM-DD
        // If not, we'll use the same date for departure and return
        let dateToUse = date;
        if (currentTripType && currentTripType.toLowerCase().includes('round')) {
            // If date doesn't already contain a comma (i.e., it's not already in departure,return format)
            if (!date.includes(',')) {
                // Use the same date for both departure and return
                dateToUse = `${date},${date}`;
                console.log(`Round trip detected but only one date provided. Using same date for return: ${dateToUse}`);
            }
        }
        
        // Set the date(s) based on trip type
        await FlightSearchPage.setDepartureDate(dateToUse, currentTripType);
        
        // Wait for a moment for the UI to update
        await fixture.page.waitForTimeout(1500);
        
        // Take a screenshot after setting date
        await takeScreenshot('after-departure-date');
    } catch (error) {
        console.error('Error during departure date selection:', error);
        await takeScreenshot('departure-date-error');
        throw error;
    }
});

When('user select the departure date {string} and return date {string}', async function (departureDateKey: string, returnDateKey: string) {
    try {
        console.log(`Setting departure date from property: ${departureDateKey} and return date from property: ${returnDateKey}`);
        const departureDate = Properties.getProperty(departureDateKey);
        const returnDate = Properties.getProperty(returnDateKey);
        console.log(`Resolved departure date: ${departureDate}, return date: ${returnDate}`);
        
        // Take a screenshot before setting dates
        await takeScreenshot('before-departure-return-dates');
        
        // Set both dates
        await FlightSearchPage.setDepartureAndReturnDates(departureDate, returnDate);
        
        // Wait for a moment for the UI to update
        await fixture.page.waitForTimeout(1500);
        
        // Take a screenshot after setting dates
        await takeScreenshot('after-departure-return-dates');
    } catch (error) {
        console.error('Error during departure and return date selection:', error);
        await takeScreenshot('departure-return-dates-error');
        throw error;
    }
});

When('user click the search button', async function () {
    try {
        console.log('Clicking search button');
        
        // Take a screenshot before clicking search
        await ScreenshotHelper.takeScreenshot('before-search-click', true, true);
        
        // Try robust click with multiple fallback strategies
        const clickSuccess = await PageHelper.robustClick(
            'button[role="button"]:has-text("Search")', 
            { 
                timeout: 10000, 
                description: 'Search button',
                fallbackSelectors: [
                    'button:has-text("Search")',
                    'button[type="submit"]',
                    'button.primary',
                    'button[class*="search"]'
                ]
            }
        );
        
        if (!clickSuccess) {
            // Try the standard method as final fallback
            console.log('Robust click failed, trying standard method');
            await FlightSearchPage.clickSearchButton();
        }
        
        // Wait for search to start processing
        console.log('Waiting after search button click...');
        await fixture.page.waitForTimeout(5000);
        
        // Capture debug info after clicking search
        await PageHelper.captureDebugInfo('after-search-click');
    } catch (error) {
        console.error('Error clicking search button:', error);
        await ScreenshotHelper.takeScreenshot('search-click-error', true, true);
        throw error;
    }
});

/**
 * Helper function to take screenshots with consistent naming
 */
async function takeScreenshot(name: string, fullPage: boolean = true, asError: boolean = false): Promise<void> {
    try {
        await ScreenshotHelper.takeScreenshot(name, fullPage, asError);
    } catch (error) {
        console.error(`Error taking screenshot ${name}: ${error}`);
    }
}

/**
 * Helper function to take screenshots for debugging
 */
async function debugScreenshot(name: string): Promise<void> {
    // Only take screenshots for error cases or when debugging is explicitly requested
    const isErrorCase = name.includes('error') || name.includes('fail');
    const isDebuggingExplicitlyEnabled = process.env.DEBUG_SCREENSHOTS === 'true' || 
                                        process.env.DEBUG_FLIGHT_SEARCH === 'true';
    
    // Skip all screenshots except for errors or when explicitly debugging
    if (isErrorCase || (isDebuggingExplicitlyEnabled)) {
        await ScreenshotHelper.takeScreenshot(name, true, true);
    } else {
        // Just log that we're skipping the screenshot
        console.log(`Screenshot skipped for ${name}`);
    }
}

Then('flight search results are displayed', async function () {
    try {
        console.log('Starting enhanced flight search results verification...');
        
        // Only take screenshot if we're troubleshooting this specific issue
        const isDebuggingSearchResults = process.env.DEBUG_FLIGHT_SEARCH === 'true';
        if (isDebuggingSearchResults) {
            await ScreenshotHelper.takeScreenshot('before-flight-search-results', true, true);
            console.log('Debug mode: Taking extra screenshots for flight search');
        }
        
        // Capture debug information - only force screenshot in debug mode
        await PageHelper.captureDebugInfo('flight-search_start', isDebuggingSearchResults);
        
        // Try waiting for the search button to disappear first (indicates search is in progress)
        try {
            await fixture.page.waitForSelector('button:has-text("Search")', { state: 'hidden', timeout: 5000 });
            console.log('Search button is no longer visible, search likely in progress');
        } catch (btnError) {
            console.log('Search button visibility check failed, continuing anyway');
        }
        
        console.log('Using specialized flight results detection with increased timeout...');
        // Use the specialized method with increased maximum wait time
        const resultsFound = await FlightSearchPage.waitForFlightResults(75000);
        
        if (resultsFound) {
            console.log('Flight search results were found using specialized detection');
            // Only take success screenshot in debug mode
            if (isDebuggingSearchResults) {
                await ScreenshotHelper.takeScreenshot('flight-search-results-success', true, true);
            }
        } else {
            console.log('Specialized detection failed, trying original verification steps...');
            
            // Wait for loading indicators to disappear (if any)
            try {
                await fixture.page.waitForSelector('div[class*="loading"], div[class*="spinner"]', { state: 'hidden', timeout: 20000 });
                console.log('Loading indicators are no longer visible');
            } catch (loadingError) {
                console.log('No loading indicators found or they did not disappear');
            }
            
            // Wait a bit for results to start appearing
            console.log('Waiting for flight search results to load...');
            await fixture.page.waitForTimeout(5000);
            
            // First try standard selector with retry mechanism
            console.log('Trying standard flight results selector...');
            const resultsVisible = await WaitHelper.waitForSelectorWithRetry(
                '[class*="flight-search-results"]', 
                { timeout: 30000, state: 'visible' }, 
                4
            );
            
            if (resultsVisible) {
                console.log('Standard flight results selector found');
            } else {
                // Last resort, try the comprehensive check
                console.log('Standard selector failed, trying comprehensive check...');
                const { found, selector } = await FlightSearchPage.checkForFlightSearchResults();
                
                if (!found) {
                    console.error('No flight results found with any selector');
                    await ScreenshotHelper.takeScreenshot('missing-flight-results', true, true);
                    
                    // Log the current page state
                    const url = await fixture.page.url();
                    console.log(`Current URL: ${url}`);
                    
                    try {
                        const pageTitle = await fixture.page.title();
                        console.log(`Page title: ${pageTitle}`);
                        
                        // Capture some of the page content
                        const bodyText = await fixture.page.$eval('body', el => el.innerText.substring(0, 500));
                        console.log('Page content sample: ', bodyText);
                    } catch (e) {
                        console.log('Could not get page content: ', e);
                    }
                    
                    throw new Error('Flight search results not displayed after exhausting all detection methods');
                }
                
                console.log(`Flight results found with alternative selector: ${selector}`);
            }        }
        
        // Only take a success screenshot if we're in debug mode
        if (isDebuggingSearchResults) {
            await ScreenshotHelper.takeScreenshot('flight-search-results-success', true, true);
        }
        console.log('Successfully verified flight search results are displayed');
    } catch (error) {
        console.error('Error verifying flight search results:', error);
        
        // Take a screenshot of the failure state
        await ScreenshotHelper.takeScreenshot('flight-search-results-failure', true, true);
        throw error;
    }
});

When('user expand the flight details', async function () {
    try {
        console.log('Attempting to expand flight details...');
        
        // Wait for page to be stable with longer timeout
        await WaitHelper.waitForPageStable(10000);
        
        // Take screenshot before expanding
        await ScreenshotHelper.takeScreenshot('before-expand-details', true, true);
        
        // Try robust click on the expand icon with multiple fallback strategies
        const clickSuccess = await PageHelper.robustClick(
            'svg.lucide-chevron-down', 
            { 
                timeout: 10000, 
                description: 'Flight details expand icon',
                fallbackSelectors: [
                    'svg.lucide-chevron-down',
                    'button[aria-label="Expand"]',
                    'button[aria-expanded="false"]',
                    '.flight-result-item',
                    'svg[class*="chevron"]',
                    'svg[class*="expand"]'
                ]
            }
        );
        
        if (!clickSuccess) {
            console.log('All click strategies failed, using direct method as final attempt');
            // Try the direct method as final fallback
            await FlightSearchPage.expandFlightDetails();
        }
        
        // Wait a moment for expansion to complete
        await fixture.page.waitForTimeout(2000);
        
        // Take screenshot after expanding
        await ScreenshotHelper.takeScreenshot('after-expand-details', true, true);
        
        // Capture full debug info
        await PageHelper.captureDebugInfo('after-expand-details');
    } catch (error) {
        console.error('Failed to expand flight details:', error);
        await ScreenshotHelper.takeScreenshot('expand-details-error', true, true);
        throw error;
    }
});

When('user select the flight', async function () {
    try {
        console.log('Attempting to select flight...');
        
        // Wait for page to be stable with longer timeout
        await WaitHelper.waitForPageStable(10000);
        
        // Only take screenshot if we're in debug mode
        const isDebuggingFlightSelection = process.env.DEBUG_FLIGHT_SEARCH === 'true';
        if (isDebuggingFlightSelection) {
            await ScreenshotHelper.takeScreenshot('before-select-flight', true, true);
            console.log('Debug mode: Taking screenshot before flight selection');
        }
        
        // Try first with modern Playwright locators
        console.log('Trying to select flight using modern Playwright locators');
        
        // First attempt: Use FlightSearchPage's modern locator
        try {
            console.log('Attempt 1: Using FlightSearchPage.elements.selectFlightButton');
            const selectButton = FlightSearchPage.elements.selectFlightButton();
            
            if (await selectButton.isVisible({ timeout: 5000 }).catch(() => false)) {
                console.log('Found Select Flight button with modern locator');
                await selectButton.click({ timeout: 5000 });
                console.log('Successfully clicked Select Flight button using modern locator');
                
                // Take screenshot after click if debugging
                if (isDebuggingFlightSelection) {
                    await ScreenshotHelper.takeScreenshot('after-select-flight-modern-locator', true);
                }
                
                // Wait a moment for navigation/UI update
                await fixture.page.waitForTimeout(2000);
                return;
            }
        } catch (error) {
            console.log(`First attempt with modern locator failed: ${error.message}`);
        }
        
        // Second attempt: Try with different role locators
        try {
            console.log('Attempt 2: Using different role-based locators');
            
            // Array of role-based locators to try
            const buttonLocators = [
                fixture.page.getByRole('button', { name: 'Select Flight' }),
                fixture.page.getByRole('button', { name: 'Select' }),
                fixture.page.getByText('Select Flight').filter({ has: fixture.page.locator('button') }),
                fixture.page.getByRole('button').filter({ hasText: /Select Flight/i })
            ];
            
            for (const locator of buttonLocators) {
                if (await locator.isVisible({ timeout: 3000 }).catch(() => false)) {
                    console.log('Found Select Flight button with alternative modern locator');
                    await locator.click({ timeout: 3000 });
                    console.log('Successfully clicked button with alternative modern locator');
                    
                    if (isDebuggingFlightSelection) {
                        await ScreenshotHelper.takeScreenshot('after-select-flight-alt-locator', true);
                    }
                    
                    // Wait for navigation/UI update
                    await fixture.page.waitForTimeout(2000);
                    return;
                }
            }
        } catch (error) {
            console.log(`Second attempt with alternative locators failed: ${error.message}`);
        }
        
        // Fall back to legacy approach if modern locators fail
        console.log('Modern locator attempts failed, falling back to legacy approach');
        
        // Try robust click on the select flight button with multiple fallback strategies
        const clickSuccess = await PageHelper.robustClick(
            'button:has-text("Select Flight")', 
            { 
                timeout: 15000, 
                description: 'Select flight button',
                fallbackSelectors: [
                    'button:has-text("Select")',
                    'button:has-text("Select Flight")',
                    'button.bg-\\[linear-gradient\\(to_right\\,\\#4B4BC3\\,\\#707FF5\\,\\#A195F9\\)\\]',
                    'button[class*="select"]',
                    'button[class*="flight"]',
                    'button.primary',
                    'button.cta'
                ]
            }
        );
        
        if (!clickSuccess) {
            console.log('All click strategies failed, using direct method as final attempt');
            // Try the direct method as final fallback
            await FlightSearchPage.selectFlight();
        }
        
        // Wait for navigation after selection with a longer timeout
        console.log('Waiting for navigation after selecting flight...');
        await fixture.page.waitForTimeout(5000);
          // Capture detailed debug info - only force screenshot in debug mode
        await PageHelper.captureDebugInfo('after-select-flight', isDebuggingFlightSelection);
    } catch (error) {
        console.error('Failed to select flight:', error);
        await ScreenshotHelper.takeScreenshot('select-flight-error', true, true);
        
        // Capture detailed debug info on error
        await PageHelper.captureDebugInfo('select-flight-error');
        throw error;
    }
});

Then('user is on the {string} page', async function (urlKey: string) {
    try {
        console.log(`Verifying user is on the ${urlKey} page`);
        const expectedUrl = Properties.getProperty(urlKey);
        console.log(`Expected URL: ${expectedUrl}`);
          // Removed screenshot before verification
        
        // Wait for navigation with a longer timeout
        console.log('Waiting for navigation to complete...');
          // Try different wait strategies
        let navigationSuccessful = false;
        
        // Strategy 1: waitForNavigationToUrl with retries
        console.log(`Using waitForNavigationToUrl with URL: ${expectedUrl}`);
        navigationSuccessful = await WaitHelper.waitForNavigationToUrl(expectedUrl, 4, 15000);
        
        // Strategy 2: manual check
        if (!navigationSuccessful) {
            console.log('Navigation wait failed, checking current URL directly');
            const currentUrl = fixture.page.url();
            navigationSuccessful = currentUrl.includes(expectedUrl);
            console.log(`Current URL: ${currentUrl}, includes expected URL: ${navigationSuccessful}`);
        }
        
        if (!navigationSuccessful) {
            // If still not successful, take a screenshot and log details
            await ScreenshotHelper.takeScreenshot('navigation-failed', true, true);
            const currentUrl = await fixture.page.url();
            throw new Error(`Navigation failed. Expected URL: ${expectedUrl}, but got: ${currentUrl}`);
        }
        
        // Final assertion
        await Assert.assertURL(expectedUrl);
          // Removed success screenshot
        console.log(`Successfully verified user is on ${urlKey} page`);
    } catch (error) {
        console.error('URL assertion failed:', error);
        await ScreenshotHelper.takeScreenshot('url-assertion-error', true, true);
        throw error;
    }
});

When('user add the Travelers count {string}', async function (travelersDataKey: string) {
    try {
        console.log(`Adding travelers count from property: ${travelersDataKey}`);
        
        // Get the travelers data from properties
        const travelersData = Properties.getProperty(travelersDataKey);
        
        // Log what we resolved from properties
        console.log(`Resolved travelers data:`, JSON.stringify(travelersData, null, 2));
        
        // Take a screenshot before travelers selection
        await takeScreenshot('before-travelers-selection');
        
        // Type checking and validation for travelers data
        if (!travelersData) {
            console.warn('No travelers data found for key: ' + travelersDataKey);
            console.warn('Using default of 1 adult');
            await FlightSearchPage.setPassengerCount('1');
            return;
        }
          // Ensure we have a properly structured object
        if (typeof travelersData !== 'object' || travelersData === null) {
            console.warn(`Unexpected travelers data type: ${typeof travelersData}. Expected an object.`);
            
            // Try to use it as a simple count if it's a number or string
            if (typeof travelersData === 'number' || typeof travelersData === 'string') {
                console.log('Treating as simple total count:', travelersData);
                await FlightSearchPage.setPassengerCount(String(travelersData));
            } else {
                console.warn('Invalid travelers data, using default of 1 adult');
                await FlightSearchPage.setPassengerCount('1');
            }
            return;
        }
        
        // At this point, we know travelersData is a non-null object
        // TypeScript will now allow accessing properties without null checks
        // Extract and validate the traveler counts from the object
        const adultCount = (travelersData as any).adult !== undefined ? Number((travelersData as any).adult) : 0;
        const childCount = (travelersData as any).child !== undefined ? Number((travelersData as any).child) : 0;
        const infantCount = (travelersData as any).infant !== undefined ? Number((travelersData as any).infant) : 0;
        
        // Validate that we have numeric values
        if (isNaN(adultCount) || isNaN(childCount) || isNaN(infantCount)) {
            console.warn('Non-numeric traveler count detected, using default values');
            await FlightSearchPage.setPassengerCount('1');
            return;
        }
        
        console.log('Using travelers data with counts:', 
            `Adults: ${adultCount}, Children: ${childCount}, Infants: ${infantCount}`);
        console.log(`Total expected travelers: ${adultCount + childCount + infantCount}`);
          // Check if the travelers button is visible before clicking
        const isButtonVisible = await FlightSearchPage.isPassengersButtonVisible();
        if (!isButtonVisible) {
            console.warn("Travelers button not visible, taking screenshot on error");
            await ScreenshotHelper.takeScreenshot('travelers-button-not-visible', true, true);
        }
        
        // The setPassengerCountFromObject method now handles dialog opening and closing
        await FlightSearchPage.setPassengerCountFromObject({
            adult: adultCount,
            child: childCount,
            infant: infantCount
        });
        
        // Take a screenshot after travelers selection
        console.log('Travelers selection completed');
        await takeScreenshot('after-travelers-selection');
        
    } catch (error) {
        console.error('Error during travelers selection:', error);
        await takeScreenshot('travelers-selection-error');
        throw error;
    }
});

Then('Assert user is on the flightsummary screen', async function () {
    try {
        console.log('Verifying user is on the flight summary screen...');
        
        // Wait for page to be stable
        await WaitHelper.waitForPageStable(10000);
        
        // Only take screenshots when actively debugging this feature
        const isDebuggingFlightSummary = process.env.DEBUG_FLIGHT_SUMMARY === 'true';
        
        // Take screenshot only if debugging
        if (isDebuggingFlightSummary) {
            await ScreenshotHelper.takeScreenshot('flight-summary-screen', true, true);
            // Capture detailed debug info with screenshot
            await PageHelper.captureDebugInfo('flight-summary-screen', true);
        } else {
            // Capture basic debug info without screenshot
            await PageHelper.captureDebugInfo('flight-summary-screen', false);
        }
          // Check for the Total Price element - this is our key indicator for the flight summary page
        try {
            // Primary approach: use modern locators to find the Total Price element
            await Promise.race([
                // Try to locate by role with name
                fixture.page.getByText('Total Price', { exact: false }).first().waitFor({ timeout: 10000 }),
                // Or by role heading if it's a heading element
                fixture.page.getByRole('heading', { name: /Total Price/i }).waitFor({ timeout: 10000 })
            ]);
            console.log('Found "Total Price" element on flight summary page using modern locators');
        } catch (error) {
            console.error('Could not find "Total Price" element with modern locators, trying fallbacks:', error);
            
            // Fallback: try with legacy selectors if modern locators don't work
            try {
                await Promise.race([
                    fixture.page.waitForSelector('div.text-brand-black.text-xl.font-semibold:has-text("Total Price")', { timeout: 5000 }),
                    fixture.page.waitForSelector('div:has-text("Total Price")', { timeout: 5000 })
                ]);
                console.log('Found "Total Price" element using legacy selectors');
            } catch (fallbackError) {
                console.error('All selectors for Total Price failed:', fallbackError);
                
                // Always take screenshot on failure
                await ScreenshotHelper.takeScreenshot('total-price-not-found', true, true);
                throw new Error('Flight summary screen validation failed: "Total Price" element not found');
            }
        }
          // Additional verification - check URL contains flightsummary
        const currentUrl = await fixture.page.url();
        const isCorrectUrl = currentUrl.includes('flightsummary');
        
        if (!isCorrectUrl) {
            console.warn(`URL ${currentUrl} does not contain 'flightsummary'`);
        } else {
            console.log(`Verified URL ${currentUrl} contains 'flightsummary'`);
        }
        
        // Also look for the "Continue to checkout" button as an additional verification
        try {
            // Try using modern locator first
            const buttonLocator = fixture.page.getByRole('button', { name: /continue to checkout/i });
            const checkoutButtonVisible = await buttonLocator.isVisible({ timeout: 3000 })
                .catch(() => false);
            
            if (checkoutButtonVisible) {
                console.log('Found "Continue to checkout" button on flight summary page using modern locators');
            } else {
                // Fallback to legacy selector
                const legacyCheckoutButtonVisible = await fixture.page.isVisible('button:has-text("Continue to checkout")')
                    .catch(() => false);
                    
                if (legacyCheckoutButtonVisible) {
                    console.log('Found "Continue to checkout" button on flight summary page using legacy selector');
                }
            }
        } catch (error) {
            console.warn('Could not verify checkout button:', error);
        }
        
        // Take success screenshot only when debugging
        if (isDebuggingFlightSummary) {
            await ScreenshotHelper.takeScreenshot('flight-summary-verified', true, true);
        }
        console.log('Successfully verified user is on the flight summary screen');
    } catch (error) {
        console.error('Error verifying flight summary screen:', error);
        // Always take screenshot on error
        await ScreenshotHelper.takeScreenshot('flight-summary-verification-error', true, true);
        throw error;
    }
});

/**
 * User clicks continue and checkout button on flight summary screen
 */
When('user click "continue and checkout" button', async function () {
    try {
        console.log('Clicking the continue to checkout button...');
        
        // Wait for page to be stable
        await WaitHelper.waitForPageStable(5000);
        
        // Take screenshot before clicking
        await takeScreenshot('before-continue-to-checkout');
        
        // First try modern Playwright locators
        let clickSuccessful = false;
        
        try {
            // Primary approach: use button role with exact text
            const continueButtonLocator = fixture.page.getByRole('button', { name: 'Continue to checkout', exact: false });
            
            if (await continueButtonLocator.isVisible({ timeout: 3000 })) {
                await continueButtonLocator.click();
                console.log('Successfully clicked continue button using modern button role locator');
                clickSuccessful = true;
            }
        } catch (error) {
            console.log('Failed to click using modern button role locator:', error);
        }
        
        // If that fails, try looser text matching
        if (!clickSuccessful) {
            try {
                const continueTextLocator = fixture.page.getByText('Continue to checkout', { exact: false });
                
                if (await continueTextLocator.isVisible({ timeout: 2000 })) {
                    await continueTextLocator.click();
                    console.log('Successfully clicked continue button using text locator');
                    clickSuccessful = true;
                }
            } catch (error) {
                console.log('Failed to click using text locator:', error);
            }
        }
        
        // Fallback to legacy selectors if modern locators fail
        if (!clickSuccessful) {
            // Define fallback selectors
            const continueButtonSelectors = [
                'button.flex.px-4.py-2', // Main selector from HTML
                'button:has-text("Continue to checkout")', 
                'button:has-text("Continue")', 
                'button[style*="background: linear-gradient"]' 
            ];
            
            // Try each selector
            for (const selector of continueButtonSelectors) {
                try {
                    // Check if element exists before attempting click
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
                    if (isVisible) {
                        await fixture.page.click(selector);
                        console.log(`Successfully clicked continue button using fallback selector: ${selector}`);
                        clickSuccessful = true;
                        break;
                    }
                } catch (error) {
                    console.log(`Failed to click using fallback selector ${selector}: ${error}`);
                }
            }
        }
        
        // Last resort: JavaScript evaluation
        if (!clickSuccessful) {
            try {
                await fixture.page.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const continueButton = buttons.find(button => 
                        button.textContent?.includes('Continue to checkout') || 
                        button.textContent?.includes('Continue') ||
                        button.style.background?.includes('linear-gradient')
                    );
                    if (continueButton) continueButton.click();
                    return !!continueButton;
                });
                console.log('Used JavaScript evaluation to find and click the button');
                clickSuccessful = true;
            } catch (jsError) {
                console.error('JavaScript click also failed:', jsError);
            }
        }
        
        if (!clickSuccessful) {
            throw new Error('Failed to click the continue to checkout button using all available methods');
        }
        
        // Wait for navigation to complete
        await WaitHelper.waitForPageStable(5000);
        
        // Take screenshot after clicking
        await takeScreenshot('after-continue-to-checkout');
    } catch (error) {
        console.error('Error clicking continue to checkout button:', error);
        await ScreenshotHelper.takeScreenshot('continue-to-checkout-error', true, true);
        throw error;
    }
});

/**
 * Assert that user is on the trip summary page
 */
Then('Assert the user is on trip summary page', async function () {
    try {
        console.log('Verifying user is on the trip summary page...');
        
        // Wait for page to be stable
        await WaitHelper.waitForPageStable(5000);
        
        // Take screenshot for verification
        await takeScreenshot('trip-summary-screen');
        // Try modern Playwright locators first - these are more reliable
        let isOnTripSummaryPage = false;
        
        // Array of locators to try - in order of preference
        const modernLocators = [
            // Complete your passenger details heading
            {
                type: 'heading',
                locator: () => fixture.page.getByRole('heading', { 
                    name: /complete your passenger details/i,
                    exact: false 
                })
            },
            // Adult 1 text (likely in accordion)
            {
                type: 'text',
                locator: () => fixture.page.getByText('Adult 1', { exact: true })
            },
            // Trip Summary heading
            {
                type: 'heading',
                locator: () => fixture.page.getByRole('heading', { 
                    name: /trip summary/i,
                    exact: false 
                })
            },
            // Trip Summary text
            {
                type: 'text',
                locator: () => fixture.page.getByText('Trip Summary', { exact: true })
            },
            // Putting the final touches text
            {
                type: 'text',
                locator: () => fixture.page.getByText('Putting the final touches on your flight Itinerary', { exact: false })
            },
            // Almost there text
            {
                type: 'text',
                locator: () => fixture.page.getByText('Almost there', { exact: true })
            }
        ];
        
        // Try each modern locator with a short timeout
        for (const { type, locator } of modernLocators) {
            try {
                const isVisible = await locator().isVisible({ timeout: 3000 });
                if (isVisible) {
                    console.log(`Trip Summary indicator found with ${type} locator`);
                    isOnTripSummaryPage = true;
                    break;
                }
            } catch (error) {
                console.log(`${type} locator not found: ${error.message}`);
            }
        }
        
        // Fallback to legacy selectors if modern locators didn't work
        if (!isOnTripSummaryPage) {
            console.log('Modern locators failed, trying legacy selectors...');
            
            const tripSummarySelectors = [
                // New primary selector for identifying trip summary page - passenger details section
                'div.flex.text-2xl.font-semibold.text-brand-black:has-text("Complete your passenger details")',
                // Accordion element for passenger details
                'span.font-semibold.text-\\[\\#1E1E76\\].text-left:has-text("Adult 1")',
                // Legacy selectors as fallback
                'div.text-center.font-medium:has-text("Trip Summary")',
                'div:has-text("Trip Summary")',
                'div.sm\\:text-sm.md\\:text-base.lg\\:text-lg.h-full.xs\\:text-sm.text-center.font-medium.text-\\[\\#080236\\]',
                // Additional selectors that might indicate the summary page
                'div:has-text("Putting the final touches on your flight Itinerary")',
                'div:has-text("Almost there")'
            ];
            
            // Check for presence of the trip summary heading using multiple selectors
            for (const selector of tripSummarySelectors) {
                try {
                    isOnTripSummaryPage = await fixture.page.isVisible(selector, { timeout: 2000 });
                    if (isOnTripSummaryPage) {
                        console.log(`Trip Summary indicator found with legacy selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    console.log(`Legacy selector ${selector} not found: ${error.message}`);
                }
            }
        }
        
        // Last resort: JavaScript evaluation if all else fails
        if (!isOnTripSummaryPage) {
            try {
                isOnTripSummaryPage = await fixture.page.evaluate(() => {
                    const elements = Array.from(document.querySelectorAll('div'));
                    
                    // Check for various text indicators
                    return elements.some(el => 
                        (el.textContent?.includes('Complete your passenger details') ||
                         el.textContent?.includes('Adult 1') ||
                         el.textContent?.includes('Trip Summary') || 
                         el.textContent?.includes('Putting the final touches') ||
                         el.textContent?.includes('Almost there') ||
                         el.textContent?.includes('Itinerary')) && 
                        (el.className.includes('text-center') || 
                         el.className.includes('font-medium') ||
                         el.className.includes('font-semibold'))
                    );
                });
                console.log(`JavaScript evaluation for Trip Summary heading: ${isOnTripSummaryPage}`);
            } catch (jsError) {
                console.error('JavaScript evaluation failed:', jsError);
            }
        }
        
        // Assert we're on the trip summary page
        expect(isOnTripSummaryPage).toBeTruthy();
        
        // If we reach here, we're definitely on the trip summary page
        console.log('Successfully verified user is on the trip summary page');
    } catch (error) {
        console.error('Error verifying trip summary page:', error);
        await ScreenshotHelper.takeScreenshot('trip-summary-verification-error', true, true);
        throw error;
    }
});

/**
 * Fill passenger information from test data
 */
When('user fill the passenger info using {string}', { timeout: 120000 }, async function (passengerInfoKey: string) {
    try {
        console.log(`Filling passenger info from property: ${passengerInfoKey}`);
        const passengerInfoData = Properties.getProperty(passengerInfoKey);
        
        if (!passengerInfoData) {
            throw new Error(`Passenger info not found for key: ${passengerInfoKey}`);
        }
        
        console.log('Passenger info data:', passengerInfoData);
        
        // Take screenshot before filling form
        await takeScreenshot('before-passenger-info');
        
        // Use the optimized implementation to fill all passenger information
        await fillPassengerInfo(passengerInfoData);
        
        // Take screenshot after filling form
        await takeScreenshot('after-passenger-info');
        
    } catch (error) {
        console.error(`Error filling passenger information: ${error}`);
        await takeScreenshot('passenger-info-error');
        throw error;
    }
});

/**
 * Assert that passenger information is filled correctly
 */
Then('Assert the passenger info is filled', { timeout: 30000 }, async function () {
    try {
        console.log('Verifying passenger information has been filled...');
        
        // Wait for form to stabilize
        await fixture.page.waitForTimeout(1000);
        
        // Take screenshot for verification
        await takeScreenshot('passenger-info-verification');
        
        // Verify title field has a selection
        const titleField = await fixture.page.locator('button#title');
        const titleText = await titleField.textContent();
        
        // Title should not be empty or contain "Select Title"
        const isValidTitle = titleText && titleText.trim() !== '' && !titleText.includes('Select Title');
        console.log(`Title field value: "${titleText}", isValid: ${isValidTitle}`);
        
        // Verify first name is filled
        const firstNameField = await fixture.page.locator('#firstName');
        const firstNameValue = await firstNameField.inputValue();
        const isValidFirstName = firstNameValue && firstNameValue.trim() !== '';
        console.log(`First name field value: "${firstNameValue}", isValid: ${isValidFirstName}`);
        
        // Verify last name is filled
        const lastNameField = await fixture.page.locator('#lastName');
        const lastNameValue = await lastNameField.inputValue();
        const isValidLastName = lastNameValue && lastNameValue.trim() !== '';
        console.log(`Last name field value: "${lastNameValue}", isValid: ${isValidLastName}`);        // Verify date of birth field has a value
        // Date of birth is displayed in a div element, not an input
        const dobField = await fixture.page.locator('div.peer.w-full.px-3.cursor-pointer');
        const dobText = await dobField.textContent();
        const isValidDob = dobText && dobText.trim() !== '' && !dobText.includes('Date of Birth');
        console.log(`DOB field value: "${dobText}", isValid: ${isValidDob}`);
        
        // Additional verification for date of birth
        if (isValidDob && dobText) {
            try {
                // Check for valid date in DD/MM/YYYY format or YYYY-MM-DD format
                const dateFormatRegex = /^(\d{1,2}\/\d{1,2}\/\d{4})|(\d{4}-\d{1,2}-\d{1,2})$/;
                if (dateFormatRegex.test(dobText.trim())) {
                    console.log('Date of birth is in valid format');
                } else {
                    console.warn(`Date of birth format may be invalid: ${dobText}`);
                }
            } catch (e) {
                console.warn(`Error validating date format: ${e}`);
            }
        }
          // Verify gender field has a selection
        const genderField = await fixture.page.locator('button#gender');
        const genderText = await genderField.textContent();
        const isValidGender = genderText && genderText.trim() !== '' && !genderText.includes('Select Gender');
        console.log(`Gender field value: "${genderText}", isValid: ${isValidGender}`);
        
        // NOTE: Passport country verification removed as per UI changes
        
        // Collect all validation results
        const validationResults = [
            isValidTitle,
            isValidFirstName,
            isValidLastName,
            isValidDob,
            isValidGender
        ];
        
        // Check if all required fields are filled
        const allFieldsValid = validationResults.every(result => result === true);
        
        // If any required fields are not filled, log which ones and fail
        if (!allFieldsValid) {
            const missingFields = [];
            if (!isValidTitle) missingFields.push('Title');
            if (!isValidFirstName) missingFields.push('First Name');
            if (!isValidLastName) missingFields.push('Last Name');
            if (!isValidDob) missingFields.push('Date of Birth');
            if (!isValidGender) missingFields.push('Gender');
            
            console.error(`Passenger info verification failed. Missing or invalid fields: ${missingFields.join(', ')}`);
            await ScreenshotHelper.takeScreenshot('passenger-info-verification-failure', true, true);
            throw new Error(`Passenger info verification failed. Missing or invalid fields: ${missingFields.join(', ')}`);
        }
        
        console.log('Successfully verified all passenger information fields are filled correctly');
    } catch (error) {
        console.error('Error verifying passenger info:', error);
        await ScreenshotHelper.takeScreenshot('passenger-info-verification-error', true, true);
        throw error;
    }
});

/**
 * Click review & pay button
 */
Then('user click the review&pay button', async function () {
    try {
        console.log('Preparing to click the Review & Pay button...');
        // NOTE: Passport country handling has been removed as per UI changes
        
        // Wait for page to be stable after handling passport fields - increased timeout for reliability
        await fixture.page.waitForTimeout(2000);
        
        // Take screenshot before clicking
        await takeScreenshot('before-review-and-pay');
        
        // Try first with modern Playwright locators
        let clickSuccessful = false;
        
        try {
            // Look for button by its role and name
            const reviewPayButton = fixture.page.getByRole('button', { name: /review & pay/i, exact: false });
            if (await reviewPayButton.isVisible({ timeout: 5000 })) {
                await reviewPayButton.click();
                console.log('Successfully clicked Review & Pay button using role locator');
                clickSuccessful = true;
            }
        } catch (roleError) {
            console.log('Failed to click using role locator:', roleError.message);
        }
        
        // If role locator failed, try with text locator
        if (!clickSuccessful) {
            try {
                const reviewPayText = fixture.page.getByText('Review & Pay', { exact: true });
                if (await reviewPayText.isVisible({ timeout: 3000 })) {
                    await reviewPayText.click();
                    console.log('Successfully clicked Review & Pay button using text locator');
                    clickSuccessful = true;
                }
            } catch (textError) {
                console.log('Failed to click using text locator:', textError.message);
            }
        }
        
        // Fall back to CSS selectors if modern locators fail
        if (!clickSuccessful) {
            // Define selectors - primary and fallbacks for better reliability
            const reviewPayButtonSelectors = [
                'button:has-text("Review & Pay")',
                'button:has-text("Review")',
                'button.flex.px-4.py-2.w-full.justify-center.items-center',
                'button[style*="background: linear-gradient"]'
            ];
            
            // Try each CSS selector
            for (const selector of reviewPayButtonSelectors) {
                try {
                    // Check if element exists before attempting click
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 5000 });
                    if (isVisible) {
                        await fixture.page.click(selector);
                        console.log(`Successfully clicked Review & Pay button using selector: ${selector}`);
                        clickSuccessful = true;
                        break;
                    }
                } catch (error) {
                    console.log(`Failed to click using selector ${selector}: ${error}`);
                }
            }
        }
        
        if (!clickSuccessful) {
            // Last resort: try JavaScript click on any matching element
            try {
                await fixture.page.evaluate(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const reviewPayButton = buttons.find(button => 
                        button.textContent?.includes('Review & Pay') || 
                        button.textContent?.includes('Review') ||
                        (button.className?.includes('justify-center') && button.style.background?.includes('linear-gradient'))
                    );
                    if (reviewPayButton) reviewPayButton.click();
                    return !!reviewPayButton;
                });
                console.log('Used JavaScript evaluation to find and click the Review & Pay button');
                clickSuccessful = true;
            } catch (jsError) {
                console.error('JavaScript click also failed:', jsError);
            }
        }
        
        if (!clickSuccessful) {
            throw new Error('Failed to click the Review & Pay button using all available methods');
        }
        
        // Improved navigation wait logic to handle timeouts more gracefully
        console.log('Waiting for page to stabilize after clicking Review & Pay button...');
        
        try {
            // Try to wait for page load with increased timeout (15 seconds instead of 5)
            await Promise.race([
                WaitHelper.waitForPageStable(15000),
                fixture.page.waitForSelector('div.text-lg.text-[#1E1E76].font-semibold, div:has-text("Credit Card Information")', { timeout: 15000 })
            ]).catch(err => {
                console.log(`Initial page load wait failed: ${err.message}, trying alternative approach...`);
            });
        } catch (waitError) {
            console.warn(`Initial page load wait failed: ${waitError.message}`);
            console.log('Attempting alternative waiting strategy...');
        }
        
        // Additional wait to ensure the page has time to render
        await fixture.page.waitForTimeout(5000);
        
        // Take screenshot after clicking
        await takeScreenshot('after-review-and-pay');
        
        console.log('Successfully clicked the Review & Pay button');
    } catch (error) {
        console.error('Error clicking Review & Pay button:', error);
        await ScreenshotHelper.takeScreenshot('review-and-pay-error', true, true);
        throw error;
    }
});

/**
 * Step definition for waiting for the flight search page to fully load after chat transition
 */
Then('Wait for the flight search page to load', async function () {
    try {
        console.log('Waiting for flight search page to fully load...');
        
        // Only take a screenshot if we're in debug mode
        const isDebuggingSearchResults = process.env.DEBUG_FLIGHT_SEARCH === 'true';
        if (isDebuggingSearchResults) {
            await ScreenshotHelper.takeScreenshot('flight-search-page-loading', true, true);
            console.log('Debug mode: Taking screenshot during flight search page loading');
        }
        
        // Wait for the page to stabilize
        await WaitHelper.waitForPageStable(10000);
        
        // Define selectors that indicate the flight search page has loaded
        const flightPageIndicators = [
            // Flight listing container
            '.flight-search-results', 
            '.search-results',
            'div[class*="flight-result"]',
            // Common flight page elements
            'div:has-text("Departure")',
            'div:has-text("Duration")',
            'div:has-text("Arrival")',
            'button:has-text("Select")',
            'button:has-text("Select Flight")'
        ];
        
        // Attempt to wait for any of the indicators with a generous timeout
        let flightPageLoaded = false;
        for (const selector of flightPageIndicators) {
            try {
                console.log(`Looking for flight page indicator: ${selector}`);
                await fixture.page.waitForSelector(selector, { timeout: 5000 });
                console.log(`Found flight page indicator: ${selector}`);
                flightPageLoaded = true;
                break;
            } catch (error) {
                // Continue to the next selector if this one isn't found
                console.log(`Selector not found: ${selector}`);
            }
        }
        
        if (!flightPageLoaded) {
            // If none of our indicators were found, try the advanced detection
            console.log('Standard indicators not found, trying advanced detection...');
            const { found } = await FlightSearchPage.checkForFlightSearchResults();
            flightPageLoaded = found;
        }
        
        if (!flightPageLoaded) {
            await ScreenshotHelper.takeScreenshot('flight-search-page-not-loaded', true, true);
            throw new Error('Flight search page did not load within the expected time');
        }
          // Take a success screenshot only if debugging
        if (isDebuggingSearchResults) {
            await ScreenshotHelper.takeScreenshot('flight-search-page-loaded', true, true);
        }
        console.log('Successfully confirmed flight search page is loaded');
        
    } catch (error) {
        console.error('Error while waiting for flight search page:', error);
        await ScreenshotHelper.takeScreenshot('flight-search-page-error', true, true);
        throw error;
    }
});

/**
 * Fill passenger information with dynamically generated data
 * This step will generate random passenger data for each test run
 */
When('user fill the passenger info filling with dynamic data', { timeout: 120000 }, async function () {
    try {
        console.log('Filling passenger info with dynamically generated data');
        
        // Wait for page to be stable before starting
        await WaitHelper.waitForPageStable(5000);
        
        // // Take screenshot of initial page state
        // await ScreenshotHelper.takeScreenshot('passenger-info-page-initial', true, true);
        
        // Generate random passenger data
        const dynamicPassengerInfo = generateRandomPassenger();
        
        // Store the generated data for later reference if needed
        const dataStore = PassengerDataStore.getInstance();
        dataStore.passengerData = dynamicPassengerInfo;
        
        // Log the generated data for debugging
        logPassengerData(dynamicPassengerInfo);
        
        // Take screenshot before filling form
        await takeScreenshot('before-dynamic-passenger-info');
        
        // Capture detailed debug info before filling
        await PageHelper.captureDebugInfo('before-passenger-info');
        
        // Use the optimized implementation to fill all passenger information
        await fillPassengerInfo(dynamicPassengerInfo);
        
        // Take screenshot after filling form
        await takeScreenshot('after-dynamic-passenger-info');
        
        // Capture detailed debug info after filling
        await PageHelper.captureDebugInfo('after-passenger-info');
        
        console.log('Successfully filled passenger info with dynamic data');
        
    } catch (error) {
        console.error(`Error filling passenger information with dynamic data: ${error}`);
        await takeScreenshot('dynamic-passenger-info-error');
        throw error;
    }
});