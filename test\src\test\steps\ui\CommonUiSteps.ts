import { Given, DataTable } from '@cucumber/cucumber';
import { fixture } from "../../fixtures/Fixture";
import { Properties } from '../../properties/Properties';
import Assert from '../../utils/AssertionsHelper';
import { EndpointHelper } from '../../properties/EndpointHelper';
import AccessibilityHelper from '../../utils/AccessibilityHelper';
import { HomePage } from '../../pages/HomePage';
import { ConsentHelper } from '../../utils/ConsentHelper';

Given('The user starts from the {string} page', async function (url: string) {
    try {
        console.log(`Starting test from URL property: ${url}`);
        const baseUrl = Properties.getProperty(url);
        console.log(`Resolved base URL: ${baseUrl}`);
        
        // Use more resilient navigation with timeout and waitUntil options
        await fixture.page.goto(baseUrl, { 
            timeout: 30000,
            waitUntil: 'networkidle' 
        });
        
        fixture.baseUrl = baseUrl;
        
        // Take a screenshot after navigation
        const { ScreenshotHelper } = require('../../utils/ScreenshotHelper');
        await ScreenshotHelper.takeScreenshot('initial-page-load');
        
        console.log('Successfully navigated to starting page');
        
        // Handle cookie consent dialog if it appears
        await ConsentHelper.handleConsentDialog('accept');
        
    } catch (error) {
        console.error(`Failed to navigate to starting page: ${error}`);
        throw error;
    }
});

Given('The user is on the {string} page', async function (page: string) {
    await Assert.assertURL(
        fixture.baseUrl + EndpointHelper.getEndpoint(page)
    );
});

Given('I check the accessbility of the page', async function () {
    await AccessibilityHelper.checkAccessibility(fixture.page)
});

Given('The user clicks on the Sign in button', async function () {
    await HomePage.clickSignInButton();
});

Given('The user cliclk on the Signup link', async function () {
    await HomePage.clickSignupLink();
});

Given('The user accepts cookies', async function () {
    await ConsentHelper.handleConsentDialog('accept');
});