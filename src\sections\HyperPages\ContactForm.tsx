import React, { useState } from "react";

type FieldKey =
  | "firstName"
  | "lastName"
  | "email"
  | "phone"
  | "query"
  | "message";

type FormDataType = Record<FieldKey, string>;

const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState<FormDataType>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    query: "",
    message: "",
  });

  const [errors, setErrors] = useState<Partial<FormDataType>>({});

  const specialCharRegex = /[^a-zA-Z0-9\s]/g;

  const sanitizeInput = (field: FieldKey, value: string) => {
    if (field !== "email" && field !== "query" && field !== "message") {
      return value.replace(specialCharRegex, "").slice(0, 20);
    }
    if (field === "message") {
      return value.slice(0, 200);
    }
    return value;
  };

  const handleChange = (field: <PERSON><PERSON>ey, value: string) => {
    const sanitized = sanitizeInput(field, value);
    setFormData((prev) => ({ ...prev, [field]: sanitized }));
    setErrors((prev) => ({ ...prev, [field]: "" }));
  };

  const handleKeyUp = (field: FieldKey, value: string) => {
    if (field !== "email" && field !== "query") {
      const cleaned = value.replace(specialCharRegex, "");
      if (cleaned !== value) {
        setFormData((prev) => ({ ...prev, [field]: cleaned }));
      }
    }
  };

  const validate = (): boolean => {
    const newErrors: Partial<FormDataType> = {};
    const requiredFields: FieldKey[] = [
      "firstName",
      "lastName",
      "email",
      "query",
      "message",
    ];

    requiredFields.forEach((field) => {
      const value = formData[field];
      if (!value.trim()) {
        newErrors[field] = "This field is required";
      } else if (
        field !== "email" &&
        field !== "query" &&
        field !== "message" &&
        specialCharRegex.test(value)
      ) {
        newErrors[field] = "Special characters not allowed";
      }
    });

    if (formData.phone.trim() && specialCharRegex.test(formData.phone)) {
      newErrors.phone = "Special characters not allowed";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      alert("Form submitted successfully!");
    }
  };

  const renderInputField = (
    field: FieldKey,
    label: string,
    isRequired = false,
    type = "text",
    placeholder = ""
  ) => (
    <div className="flex-1">
      <label className="block text-sm font-medium mb-1">
        {label}
        {isRequired && <span className="text-red-500"> *</span>}
      </label>
      <input
        type={type}
        value={formData[field]}
        onChange={(e) => handleChange(field, e.target.value)}
        onKeyUp={(e) => handleKeyUp(field, e.currentTarget.value)}
        onFocus={() => setErrors((prev) => ({ ...prev, [field]: "" }))}
        placeholder={placeholder}
        className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm"
      />
      {errors[field] && (
        <span className="text-red-500 text-xs mt-1 block text-right">
          {errors[field]}
        </span>
      )}
    </div>
  );

  return (
    <form
      onSubmit={handleSubmit}
      className="w-full max-w-xl p-6 md:p-8 rounded-lg space-y-4 shadow-sm bg-white"
    >
      <div className="flex flex-col md:flex-row gap-4">
        {renderInputField(
          "firstName",
          "First Name",
          true,
          "text",
          "Your first name here"
        )}
        {renderInputField(
          "lastName",
          "Last Name",
          true,
          "text",
          "Your last name here"
        )}
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        {renderInputField(
          "email",
          "Email Address",
          true,
          "email",
          "Your email address"
        )}
        <div className="flex-1">
          <label className="block text-sm font-medium mb-1">
            Phone Number{" "}
            <span className="text-gray-500 text-xs">(Optional)</span>
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
            onKeyUp={(e) => handleKeyUp("phone", e.currentTarget.value)}
            onFocus={() => setErrors((prev) => ({ ...prev, phone: "" }))}
            placeholder="Your phone number"
            className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm"
          />
          {errors.phone && (
            <span className="text-red-500 text-xs mt-1 block text-right">
              {errors.phone}
            </span>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          What's Your Query About? <span className="text-red-500">*</span>
        </label>
        <select
          value={formData.query}
          onChange={(e) => handleChange("query", e.target.value)}
          onFocus={() => setErrors((prev) => ({ ...prev, query: "" }))}
          className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="">Choose Your Options</option>
          <option value="Booking">Booking</option>
          <option value="Payment">Payment</option>
          <option value="Support">Support</option>
        </select>
        {errors.query && (
          <span className="text-red-500 text-xs mt-1 block text-right">
            {errors.query}
          </span>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Message <span className="text-red-500">*</span>
        </label>
        <textarea
          rows={4}
          value={formData.message}
          onChange={(e) => handleChange("message", e.target.value)}
          onKeyUp={(e) => handleKeyUp("message", e.currentTarget.value)}
          onFocus={() => setErrors((prev) => ({ ...prev, message: "" }))}
          placeholder="Tell us more about how we can help you..."
          className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm resize-none"
          maxLength={200}
        />
        {errors.message && (
          <span className="text-red-500 text-xs mt-1 block text-right">
            {errors.message}
          </span>
        )}
      </div>

      <button
        type="submit"
        className="bg-[#4B4BC3] hover:bg-[#3e3eb5] text-white font-medium px-6 py-2 rounded-md text-sm transition-all"
      >
        Submit Request
      </button>
    </form>
  );
};

export default ContactForm;
