{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"data": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "avatar": {"type": "string"}}, "required": ["id", "email", "first_name", "last_name", "avatar"], "additionalProperties": false}, "support": {"type": "object", "properties": {"url": {"type": "string"}, "text": {"type": "string"}}, "required": ["url", "text"], "additionalProperties": false}}, "required": ["data", "support"], "additionalProperties": false}