"use client";

import { useState, useEffect } from "react";

export const useDashboardNavigation = (initialSection = "User Profile") => {
  const [activeSection, setActiveSection] = useState(initialSection);

  useEffect(() => {
    const handleNavigation = (event: CustomEvent) => {
      if (event.detail && event.detail.section) {
        setActiveSection(event.detail.section);
      }
    };

    // Add event listener for custom navigation event
    window.addEventListener(
      "dashboard-navigation" as any,
      handleNavigation as EventListener
    );

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener(
        "dashboard-navigation" as any,
        handleNavigation as EventListener
      );
    };
  }, []);

  return {
    activeSection,
    setActiveSection,
  };
};
