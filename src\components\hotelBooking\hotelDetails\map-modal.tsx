"use client"

import { <PERSON>, MapP<PERSON>, ZoomIn, <PERSON>mO<PERSON>, Maximize } from "lucide-react"
import Image from "next/image"

interface MapModalProps {
    hotelName: string
    address: string
    mapImage: string
    onClose: () => void
}

export default function MapModal({ hotelName, address, mapImage, onClose }: MapModalProps) {
    return (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
                <div className="sticky top-0 bg-white p-4 border-b flex items-center justify-between">
                    <div>
                        <h2 className="font-bold text-lg">{hotelName}</h2>
                        <p className="text-sm text-gray-500">{address}</p>
                    </div>
                    <button onClick={onClose} className="h-8 w-8 rounded-full flex items-center justify-center hover:bg-gray-100">
                        <X className="h-5 w-5" />
                    </button>
                </div>

                <div className="relative h-[600px] w-full">
                    <Image
                        src={
                            mapImage ||
                            "/placeholder.svg?height=600&width=800&query=google map of Chennai Guindy area with hotel marker"
                        }
                        alt="Hotel location map"
                        fill
                        className="object-cover"
                    />

                    {/* Google Maps-like marker */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="flex flex-col items-center">
                            <div className="bg-red-500 rounded-full h-6 w-6 flex items-center justify-center border-2 border-white">
                                <MapPin className="h-4 w-4 text-white" />
                            </div>
                            <div className="w-2 h-2 bg-red-500 rotate-45 -mt-1"></div>
                        </div>
                    </div>

                    {/* Google Maps-like controls */}
                    <div className="absolute top-4 right-4 flex flex-col gap-2">
                        <button className="bg-white rounded-sm shadow-md p-2 hover:bg-gray-100">
                            <ZoomIn className="h-5 w-5 text-gray-700" />
                        </button>
                        <button className="bg-white rounded-sm shadow-md p-2 hover:bg-gray-100">
                            <ZoomOut className="h-5 w-5 text-gray-700" />
                        </button>
                        <button className="bg-white rounded-sm shadow-md p-2 hover:bg-gray-100">
                            <Maximize className="h-5 w-5 text-gray-700" />
                        </button>
                    </div>

                    {/* Google Maps-like footer */}
                    <div className="absolute bottom-4 left-4 right-4 flex justify-between">
                        <div className="bg-white rounded-md shadow-md p-2 text-xs text-gray-500">Map data ©2025 Google</div>
                        <div className="bg-white rounded-md shadow-md p-2">
                            <a
                                href="https://maps.google.com"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 font-medium text-sm"
                            >
                                View larger map
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
