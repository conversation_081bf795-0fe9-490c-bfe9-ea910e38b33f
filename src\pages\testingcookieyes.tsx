import React, { useEffect, useState } from 'react';
import Head from 'next/head';

const TestingCookieYes: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [cookieData, setCookieData] = useState<any>(null);

  useEffect(() => {
    // Add event listener for CookieYes consent updates
    window.addEventListener('cookieyes_consent_update', handleConsentUpdate);
    
    // Log that we're ready
    addLog('Listening for CookieYes consent update events');
    
    // Check if there's already consent data
    checkCurrentCookies();
    
    // Set up a periodic check as a fallback
    const cookieCheckInterval = setInterval(() => {
      checkCurrentCookies();
    }, 5000);
    
    return () => {
      // Clean up
      window.removeEventListener('cookieyes_consent_update', handleConsentUpdate);
      clearInterval(cookieCheckInterval);
    };
  }, []);
  
  // Handler for the cookieyes_consent_update event
  const handleConsentUpdate = () => {
    const timestamp = new Date().toISOString();
    addLog(`[${timestamp}] CookieYes consent update event triggered`);
    
    // Check cookies right away when the event is triggered
    checkCurrentCookies();
    
    // Also process consent data and send to backend
    sendConsentToBackend();
  };
  
  // Function to send consent data to backend
  const sendConsentToBackend = () => {
    try {
      // Get consent data
      const acceptedTypes = getAcceptedTypes();
      const sessionId = getCookie('CookieYes-Session');
      const userId = null; // Replace with actual user ID if available
      
      // Prepare data for backend
      const data = {
        user_id: userId,
        session_id: sessionId,
        accepted_types: acceptedTypes
      };
      
      // Log the data that will be sent
      addLog(`Data to send: ${JSON.stringify(data)}`);
      
      // Send to backend API
      fetch('/api/v1/cookie-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}`);
        }
        return response.json();
      })
      .then(result => {
        addLog(`[${new Date().toISOString()}] Successfully sent to backend: ${JSON.stringify(result)}`);
      })
      .catch(error => {
        addLog(`[${new Date().toISOString()}] Error sending to backend: ${error.message}`);
      });
    } catch (error) {
      addLog(`Error processing consent: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
  
  // Helper function to get accepted types from the cookie
  const getAcceptedTypes = (): Record<string, boolean> => {
    const acceptedTypes: Record<string, boolean> = {};
    
    // Get CookieYes cookie
    const cookieYesConsent = getCookie('cookieyes-consent');
    if (!cookieYesConsent) return acceptedTypes;
    
    // Parse consent
    const consentParts: Record<string, string> = {};
    cookieYesConsent.split(',').forEach(part => {
      const [key, value] = part.split(':');
      if (key && value) {
        consentParts[key] = value;
      }
    });
    
    // Add each category with boolean value
    for (const [key, value] of Object.entries(consentParts)) {
      // Skip non-category fields
      if (key !== 'consentid' && key !== 'consent' && key !== 'action') {
        acceptedTypes[key] = value === 'yes';
      }
    }
    
    return acceptedTypes;
  };
  
  // Helper function to add a log message
  const addLog = (message: string) => {
    setLogs(prev => [message, ...prev]);
    console.log(message);
  };
  
  // Helper function to get a cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };
  
  // Check current cookie state
  const checkCurrentCookies = () => {
    try {
      // Get CookieYes cookie
      const cookieYesConsent = getCookie('cookieyes-consent');
      if (!cookieYesConsent) return;
      
      // Parse consent
      const consentParts: Record<string, string> = {};
      cookieYesConsent.split(',').forEach(part => {
        const [key, value] = part.split(':');
        if (key && value) {
          consentParts[key] = value;
        }
      });
      
      // Create accepted_types as an object with boolean values
      const acceptedTypes: Record<string, boolean> = {};
      
      // Add each category with boolean value
      for (const [key, value] of Object.entries(consentParts)) {
        // Skip non-category fields
        if (key !== 'consentid' && key !== 'consent' && key !== 'action') {
          acceptedTypes[key] = value === 'yes';
        }
      }
      
      // Get session ID
      const sessionId = getCookie('CookieYes-Session');
      
      // Update state (but don't log repeatedly)
      const newData = {
        user_id: null,
        session_id: sessionId,
        accepted_types: acceptedTypes
      };
      
      // Only update if data has changed
      if (JSON.stringify(newData) !== JSON.stringify(cookieData)) {
        setCookieData(newData);
        addLog(`Cookie consent detected: ${JSON.stringify(newData)}`);
      }
    } catch (error) {
      console.error('Error checking cookies:', error);
    }
  };

  // Function to manually trigger sending data to backend
  const handleManualSend = () => {
    addLog('Manually triggering send to backend');
    sendConsentToBackend();
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Head>
        <title>CookieYes Detection Test</title>
      </Head>
      
      <h1 className="text-2xl font-bold mb-6">CookieYes Detection Test</h1>
      <p className="mb-4">
        This page listens for CookieYes consent update events and sends data to your backend API.
      </p>
      
      <button 
        onClick={handleManualSend}
        className="px-4 py-2 mb-6 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Manually Send Current Consent
      </button>
      
      {cookieData && (
        <div className="mb-6 p-4 bg-gray-100 rounded border">
          <h2 className="text-lg font-semibold mb-2">Current Cookie Data</h2>
          <pre className="whitespace-pre-wrap overflow-auto bg-white p-3 rounded border">
            {JSON.stringify(cookieData, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mb-6 p-4 bg-gray-100 rounded border">
        <h2 className="text-lg font-semibold mb-2">Activity Log</h2>
        <div className="max-h-96 overflow-y-auto">
          {logs.length > 0 ? (
            <ul className="space-y-1">
              {logs.map((log, index) => (
                <li key={index} className="border-b border-gray-200 py-1">
                  {log}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">No activity detected yet</p>
          )}
        </div>
      </div>
      
      <div className="mb-6 p-4 bg-gray-100 rounded border">
        <h2 className="text-lg font-semibold mb-2">All Cookies</h2>
        <button 
          onClick={() => {
            const cookies = document.cookie.split(';')
              .map(cookie => cookie.trim())
              .reduce((acc, current) => {
                const [name, value] = current.split('=');
                if (name) acc[name] = value || '';
                return acc;
              }, {} as Record<string, string>);
            
            addLog(`All cookies: ${JSON.stringify(cookies)}`);
          }}
          className="px-3 py-1 mb-3 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
        >
          Show All Cookies
        </button>
      </div>
    </div>
  );
};

export default TestingCookieYes;