import React, { useState } from "react";
import SavedAddresses from "./SavedAddresses";
import AddressForm from "./AddressForm";

interface Address {
  memorableName: string;
  userName: string;
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

interface AddressDetailsProps {
  user: any;
}

const AddressDetails: React.FC<AddressDetailsProps> = ({ user }) => {
  const [addresses, setAddresses] = useState<Address[]>([
    {
      memorableName: "Memorable Name",
      userName: "User Full Name",
      street: "Street Address",
      city: "City",
      state: "State/Province",
      country: "Country",
      postalCode: "Postal Code"
    },
    {
      memorableName: "Memorable Name",
      userName: "User Full Name",
      street: "Street Address",
      city: "City",
      state: "State/Province",
      country: "Country",
      postalCode: "Postal Code"
    },
    {
      memorableName: "Memorable Name",
      userName: "User Full Name",
      street: "Street Address",
      city: "City",
      state: "State/Province",
      country: "Country",
      postalCode: "Postal Code"
    }
  ]);
  const [showForm, setShowForm] = useState(false);
  const [editIdx, setEditIdx] = useState<number | null>(null);
  const [editData, setEditData] = useState<Address | undefined>(undefined);

  const handleAddNew = () => {
    setEditIdx(null);
    setEditData(undefined);
    setShowForm(true);
  };

  const handleEdit = (address: Address, idx: number) => {
    setEditIdx(idx);
    setEditData(address);
    setShowForm(true);
  };

  const handleDelete = (idx: number) => {
    setAddresses(addresses.filter((_, i) => i !== idx));
  };

  const handleSave = (data: Address) => {
    if (editIdx !== null && editIdx !== undefined) {
      // Edit existing
      setAddresses(addresses.map((addr, i) => (i === editIdx ? data : addr)));
    } else {
      // Add new
      setAddresses([...addresses, data]);
    }
    setShowForm(false);
    setEditIdx(null);
    setEditData(undefined);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditIdx(null);
    setEditData(undefined);
  };

  return (
    <div className="w-full">
      {showForm ? (
        <AddressForm onCancel={handleCancel} onSave={handleSave} initialData={editData} />
      ) : (
        <SavedAddresses
          addresses={addresses}
          onAddNew={handleAddNew}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      )}
    </div>
  );
};

export default AddressDetails;
