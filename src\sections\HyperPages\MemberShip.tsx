"use client";

import React, { useState } from "react";
import Footer from "./Footer";
import Navbar from "./Navbar";
import MembershipScroll from "./MembershipScroll";
import ContactForm from "./ContactForm";
import { ChevronDown, ChevronUp } from "lucide-react";
import AuthContainer from "@/components/layout/AuthContainer";
import { MemberShipFaqData } from "@/constants/MemberShip";

const MemberShipPage: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  const [activePlan, setActivePlan] = useState("monthly");
  const [selectedBox, setSelectedBox] = useState("free");
  const [authStartScreen, setAuthStartScreen] = useState<"auth" | "signup">(
    "auth"
  );
  const [showAuthModal, setShowAuthModal] = useState(false);

  const handleOpenAuth = () => {
    setShowAuthModal(true);
  };

  const handleCloseAuth = () => {
    setShowAuthModal(false);
  };

  const handleOpenSignUp = () => {
    setAuthStartScreen("signup");
    setShowAuthModal(true);
  };

  const isFree = true;
  const selected = true;

  const handleRedirect = () => {
    window.open("/coming-soon", "_blank");
  };

  const toggleIndex = (index: number) => {
    setActiveIndex((prev) => (prev === index ? null : index));
  };

  return (
    <div className="flex flex-col min-h-screen bg-white text-[#0e0b2b] font-proxima-nova">
      <div
        className="w-full min-h-screen bg-cover bg-center relative flex flex-col"
        style={{
          backgroundImage:
            "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png')",
        }}
      >
        <Navbar />
        <div className="flex-grow flex w-[85%] mx-auto justify-between items-center py-2">
          <h1 className="text-3xl md:text-5xl font-bold leading-snug bg-gradient-to-r from-[#707FF5] via-[#A195F9] to-[#F2A1F2] text-transparent bg-clip-text text-center md:text-left max-w-full md:max-w-[60%]">
            Whether you travel once a year or every month, NxVoy has a plan
            designed to fit your style.
          </h1>
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/MembershipIMG.png"
            alt="FAQ Letters"
            className="max-w-[46%] h-auto"
          />
        </div>
      </div>

      <section className="bg-white px-2 md:px-24 flex flex-col lg:flex-row items-stretch justify-between gap-10 text-[#0E0B2B] py-4">
        <div className="w-full max-w-xl flex flex-col justify-center">
          <h2 className="text-[28px] md:text-[32px] font-bold mb-4">
            Why Join NxVoy
          </h2>
          <p className="text-[13px] md:text-[14px] leading-relaxed mb-3">
            With NxVoy Membership, you get more than just a trip planner...
          </p>
          <p className="text-[13px] md:text-[14px] leading-relaxed mb-4">
            From last-minute bookings to fully curated itineraries...
          </p>
          <ul className="list-disc list-inside text-[13px] md:text-[14px] space-y-2">
            <li>Instant AI Itineraries</li>
            <li>Real-Time Travel Updates</li>
            <li>Unlimited Trip Planning Tools</li>
            <li>No More Stress, Just Travel Joy</li>
          </ul>
        </div>
        <div className="w-full max-w-[578px] flex items-center justify-center">
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/MembershipIMG2.png"
            alt="Membership Benefits"
            className="h-full w-auto object-contain"
          />
        </div>
      </section>

      <section className="bg-white px-6 md:px-12 text-[#0E0B2B] py-8">
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex-1 w-full md:w-1/2">
            <div className="flex gap-2 mb-6 bg-[#F1F1F7] p-1 rounded-full justify-center w-fit mx-auto">
              {["monthly", "yearly"].map((plan) => (
                <button
                  key={plan}
                  onClick={() => plan === "monthly" && setActivePlan(plan)}
                  disabled={plan === "yearly"}
                  className={`px-4 py-1 rounded-full text-xs font-medium transition min-w-[110px] ${
                    activePlan === plan
                      ? "bg-[#2F2F7B] text-white"
                      : "bg-white text-[#2F2F7B]"
                  } ${plan === "yearly" ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  {plan === "monthly" ? "Pay Monthly" : "Pay Yearly"}
                </button>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row justify-center items-stretch gap-6 w-full">
              {["free", "premium"].map((type) => {
                const isFree = type === "free";
                const selected = selectedBox === type;
                const isPremium = type === "premium";
                return (
                  <div
                    key={type}
                    onClick={() => type !== "premium" && setSelectedBox(type)}
                    className={`border ${
                      selected ? "border-[#2F2F7B]" : "border-[#BFBFE5]"
                    } rounded-xl w-full sm:w-[360px] flex flex-col transition ${
                      isPremium
                        ? "opacity-10 cursor-not-allowed"
                        : "cursor-pointer"
                    }`}
                  >
                    <div className="bg-[#F1F1F7] p-6 rounded-t-xl h-[160px] relative">
                      {isPremium && (
                        <div className="absolute top-3 right-4">
                          <span className="bg-[#10B981] text-white text-[10px] px-2 py-[2px] rounded-full font-semibold">
                            Recommended
                          </span>
                        </div>
                      )}
                      <h3 className="text-[18px] font-bold mb-2">
                        {isFree ? "Free Plan" : "Premium Plan – £9.99/month"}
                      </h3>
                      <p className="text-[13px] text-[#6B6B84] leading-relaxed">
                        {isFree
                          ? "Perfect for occasional travellers who want to explore NxVoy and get a taste of Shasa’s smart planning power — without any commitment. Access to limited Shasa features"
                          : "Ideal for frequent flyers who want an effortless travel experience — with full AI support, real-time updates, and unlimited planning power."}
                      </p>
                    </div>
                    <div
                      className={`${
                        isPremium ? "bg-[#F8F8FE]" : "bg-white"
                      } p-6 rounded-b-xl flex flex-col justify-between h-[370px]`}
                    >
                      <ul className="space-y-3 text-[14px] text-[#0E0B2B]">
                        {(isFree
                          ? [
                              "5 chats allowed, 50 messages per session",
                              "Unlimited trip bookings",
                              "Full access to itinerary builder",
                              "No credit card needed",
                            ]
                          : [
                              "Full access to all Shasa features",
                              "Personalised, AI-curated itineraries",
                              "Unlimited trip updates & revisions",
                              "Offline access to saved itineraries",
                              "Unlimited chat with Shasa",
                              "24/7 priority travel support",
                            ]
                        ).map((item, idx) => (
                          <li key={idx} className="flex items-start gap-2 pl-1">
                            <img
                              src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/MembershipTick.png"
                              alt="tick"
                              className="w-[18px] h-[18px] mt-0.5"
                            />
                            <span className="block leading-snug">{item}</span>
                          </li>
                        ))}
                      </ul>
                      <button
                        onClick={handleOpenSignUp}
                        className={`mt-8 text-xs font-medium px-6 py-2.5 rounded-lg w-[160px] mx-auto ${
                          selected
                            ? "bg-[#2F2F7B] text-white"
                            : "bg-white border border-[#2F2F7B] text-[#2F2F7B]"
                        }`}
                      >
                        {isFree ? "Join for Free" : "Upgrade to Premium"}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      <section className="py-2">
        <MembershipScroll />
      </section>

      <section className="bg-white px-2 md:px-20  text-[#0E0B2B] py-4">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-[24px] md:text-1xl font-bold text-center mb-10">
            What Travellers Ask Before They Take Off
          </h2>
          <div className="space-y-6 divide-y divide-[#E6E6E6]">
            {MemberShipFaqData.map((faq, index) => (
              <div key={index} className="pt-6">
                <button
                  className="w-full flex justify-between items-center text-left"
                  onClick={() => toggleIndex(index)}
                >
                  <span className="font-semibold text-[16px] md:text-[18px] leading-tight">
                    {index + 1}. {faq.question}
                  </span>
                  <div className="w-7 h-7 border border-[#0E0B2B] rounded-full flex items-center justify-center ml-4">
                    {activeIndex === index ? (
                      <ChevronUp className="w-4 h-4 text-[#0E0B2B]" />
                    ) : (
                      <ChevronDown className="w-4 h-4 text-[#0E0B2B]" />
                    )}
                  </div>
                </button>
                {activeIndex === index && (
                  <p className="mt-3 text-[14px] md:text-[15px] text-[#333] leading-relaxed pr-6">
                    {faq.answer}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="w-full px-2 bg-white flex flex-col lg:flex-row justify-center items-start gap-6 py-4">
        <div className="w-full max-w-xl space-y-4">
          <h2 className="text-3xl md:text-4xl font-semibold leading-snug">
            Have Questions? <br /> Let’s Chat
          </h2>
          <p className="text-sm leading-relaxed">
            Your next trip should be smooth, exciting, and personalised. Fill in
            the form, and our team (or Shasa herself!) will get right back to
            you.
          </p>
        </div>
        <ContactForm />
      </section>

      <section
        id="subscribe"
        className="relative flex justify-center min-h-[80vh] bg-cover bg-center py-16 md:py-24"
        style={{
          backgroundImage:
            "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/background.png')",
        }}
      >
        <div className="flex flex-col md:flex-row-reverse w-[90%] md:w-[85%] mx-auto gap-10 items-center">
          <div className="w-full md:w-1/3 mt-6 md:mt-0 flex justify-end">
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Subscribe%20Section/Hello-Shasa%202.png"
              alt="shasa"
              className="w-full max-w-[260px] h-auto object-contain"
            />
          </div>
          <div className="w-full md:w-2/3 flex flex-col items-start gap-6 mt-8 md:mt-0">
            <h1 className="bg-gradient-to-r from-[#4D4DC5] via-[#707FF5] to-[#A195F9] bg-clip-text text-transparent font-proxima-nova font-bold text-[15px] sm:text-[20px] md:text-[24px] lg:text-[32px] xl:text-[36px] 2xl:text-[40px] leading-snug mb-0">
              Scan the QR code and download the app.
              <br />
              Shasa is waiting there to guide you!
            </h1>
            <h3 className="bg-gradient-to-r from-[#4D4DC5] via-[#707FF5] to-[#A195F9] bg-clip-text text-transparent font-proxima-nova font-bold text-[10px] sm:text-[12px] md:text-[14px] lg:text-[18px] xl:text-[20px] 2xl:text-[24px] leading-snug">
              Let's make your trip seamless, right from your phone.
            </h3>
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/QR.png"
              alt="QR"
              className="w-28 h-28 cursor-pointer"
              onClick={handleRedirect}
            />
            <button
              onClick={handleRedirect}
              className="flex items-center gap-3 bg-black text-white px-4 py-2 rounded-lg hover:opacity-90 transition w-[200px]"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/social/google-icon.svg"
                alt="Google Play"
                className="h-6"
              />
              <div className="text-left leading-tight text-sm">
                <div className="text-xs">GET IT ON</div>
                <div className="font-semibold">Google Play</div>
              </div>
            </button>
            <button
              onClick={handleRedirect}
              className="flex items-center gap-3 bg-black text-white px-4 py-2 rounded-lg hover:opacity-90 transition w-[200px]"
            >
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/social/apple-icon.svg"
                alt="Apple Store"
                className="h-6"
              />
              <div className="text-left leading-tight text-sm">
                <div className="text-xs">Download on the</div>
                <div className="font-semibold">App Store</div>
              </div>
            </button>
          </div>
        </div>
      </section>

      <section>
        <Footer />
      </section>
      <>
        {showAuthModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
            <AuthContainer
              onCloseAuth={() => setShowAuthModal(false)}
              initialScreen={authStartScreen}
            />
          </div>
        )}
      </>
    </div>
  );
};

export default MemberShipPage;
