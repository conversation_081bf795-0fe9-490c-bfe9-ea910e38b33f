import React, { useEffect, useRef, useState } from "react";
import { AppState } from "@/store/store";
import { useSelector, useDispatch } from "react-redux";
import axios from "axios";
import AuthContainer from "@/components/layout/AuthContainer";
import { useChatContext } from "@/context/ChatContext";
import { useRouter } from "next/router";
import { updateTripSummary } from "@/store/slices/tripSummary";
import { activePages, TripOptions } from "@/constants/flight";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { NEW_THREAD_DEFAULT_NAME } from "@/constants/chat";
import LoadingOverlay from "../LoadingOverlay/LoadingOverlay";
import { useLoadingTransition } from "@/hooks/useLoadingTransition";
import { processFlightSelection } from "@/lib/utils/processFlightSelection";
import { useFlightContext } from "@/context/FlightContext";
import { useCustomSession } from "@/hooks/use-custom-session";
import { toast } from "@/hooks/use-toast";
import { ErrorModal } from "./errorModal/error-modal";
import { useWebSocket } from "@/hooks/chat/useWebSocket";
import {
  AIMessage,
  HumanMessage,
  FlightMessage,
  StreamingMessage,
  LoadingIndicator,
  WelcomeScreen,
} from "@/components/chat/ChatMessage";
import { CHAT_CONSTANTS } from "@/constants/chat";
import {
  generateTimestamp,
  generateThreadIdWithRetry,
  updateMessageFeedback,
  createFlightSearchPayload,
} from "@/lib/chatUtils";
import { ChatMessage, ChatScreenProps, InitialMessage } from "@/types/chat";
import { ChatInput } from "./ChatInput";

export const ChatScreen: React.FC<ChatScreenProps> = () => {
  const { isFetching, runWithLoading } = useLoadingTransition();
  const currentUser = useSelector((state: AppState) => state.loggedInUser);
  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
  const tripSummaryDetails = useSelector(
    (state: AppState) => state.tripSummary
  );

  const {
    history,
    currentChatPageThreadId,
    newThread,
    showMoreFlightsOption,
    chatResult,
    allFlights,
    tripType,
  } = chatThreadDetails;

  const dispatch = useDispatch();
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken as string;

  const { setRefreshChatHistory, setCurrentThreadTitle } = useChatContext();

  const {
    updateFareOptions,
    updatePassengerList,
    updateLuggageOptions,
    updateSupplierInfo,
    updateServiceFee,

  } = useFlightContext();

  const router = useRouter();

  // State
  const [message, setMessage] = useState("");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isSignInClicked, setIsSignInClicked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isShasaProcessing, setShasaProcessing] = useState(false);
  const [shasaMessage, setShasaMessage] = useState<string>(
    CHAT_CONSTANTS.DEFAULT_MESSAGES.SHASA_THINKING
  );
  const [agentEventMessage, setAgentEventMessage] = useState("");
  const [flightResults, setFlightResults] = useState<any>({});
  const [initialMessage, setInitialMessage] = useState<InitialMessage>({
    message: "",
    time: "",
  });
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);

  const [chatErrorType, setChatErrorType] = useState('')
  const [currentErrorImage, setCurrentErrorImage] = useState(
    CHAT_CONSTANTS.DEFAULT_ERROR_IMAGE
  );
  const [currentErrorMessage, setCurrentErrorMessage] = useState(
    CHAT_CONSTANTS.DEFAULT_MESSAGES.ERROR_GENERAL
  );

  // NEW: Add state to track streaming message ID and manage transitions
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(
    null
  );
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Cache refs to prevent unnecessary re-fetches (similar to ChatHistory optimization)
  const lastFetchTime = useRef<Record<string, number>>({});
  const messagesCache = useRef<Record<string, ChatMessage[]>>({});
  const threadNamesCache = useRef<Record<string, string>>({});
  const isInitialLoad = useRef<boolean>(true);

  // Refs
  const chatBoxRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { currentUserDetails } = useSelector(
    (state: AppState) => state.userDetails
  );
  const { selectedOutboundFlight, selectedInboundFlight } = tripSummaryDetails;

  // WebSocket hook with enhanced message handling
  const {
    streamingMessage,
    connectWebSocket,
    sendMessage: wsSendMessage,
    closeConnection,
    isConnected,
  } = useWebSocket({
    onMessageReceived: (msg) => {
      // Enhanced message handling with smooth transitions
      setChatMessages((prev) => {
        // If we have a streaming message being replaced, handle the transition
        if (streamingMessageId && msg.messageId) {
          setIsTransitioning(true);
          setTimeout(() => {
            setStreamingMessageId(null);
            setIsTransitioning(false);
          }, 50);
        }

        const newMessages = [...prev, msg];

        // NEW: If this is an AI message in a new thread, trigger chat history refresh
        if (msg.sender === "ai" && newThread) {
          dispatch(updateCurrentThreadInfo({ shouldRefreshHistory: true }));
        }

        return newMessages;
      });
      setIsLoading(false);
    },
    onFlightDataReceived: (flights, timestamp, routingId) => {
      setChatMessages((prev) => [
        ...prev,
        {
          sender: "ai",
          type: "flights",
          flights,
          timestamp,
          routingId,
          messageId: `flight-${Date.now()}`, // Ensure unique ID
        },
      ]);
    },
    onProcessingStatusChange: (isProcessing, message) => {
      setShasaProcessing(isProcessing);
      if (message) setShasaMessage(message);
    },
    onAgentEventChange: setAgentEventMessage,
    onStreamingChange: (isStreaming, messageId) => {
      // Track when streaming starts
      if (isStreaming && messageId) {
        setStreamingMessageId(messageId);
      }
    },
    onThreadNameUpdate: (name) => {
      setCurrentThreadTitle(name);
      setRefreshChatHistory(true);
    },
    onFlightResultsUpdate: setFlightResults,
    onError: (error:any) => {
      console.log("[CHAT ERROR========]", error)
      // if (message.trim().length > 0) {
      //   setIsErrorModalOpen(true);
      //   setCurrentErrorMessage(CHAT_CONSTANTS.DEFAULT_MESSAGES.CONNECTION_CLOSED);
      // }
    },
    setRefreshChatHistory,
    setCurrentThreadTitle,
  });

  // Enhanced utility functions
  let isGenerating = false;
  const generateThreadId = async () => {
    if (isGenerating) return;
    isGenerating = true;

    try {
      const newThreadId = await generateThreadIdWithRetry(token);
      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: newThreadId,
          currentThreadName: NEW_THREAD_DEFAULT_NAME,
          history: false,
          newThread: true,
          showMoreFlightsOption: false,
          allFlights: {},
          chatResult: {},
          tripType: "",
        })
      );
      return newThreadId;
    } catch (err:any) {
      if (
        err?.message?.includes("You've reached the maximum number of conversations (10) for a free account. Please upgrade your account for unlimited conversations")
      ) {
        console.log("10-chat-reached");
        setChatErrorType('10-chat-reached')
        setIsErrorModalOpen(true);
        setCurrentErrorImage('https://storage.googleapis.com/nxvoytrips-img/ChatPage/ChatError/thread-limit-reached.svg');
        setCurrentErrorMessage(`But don’t worry - we can still plan more adventures together. Just upgrade your membership to unlock unlimited conversations with me. `);
      } else {
        console.log("GENERAL ERROR:", err.message);
      }
    } finally {
      isGenerating = false;
    }
  };

  const handleInitiateChat = () => {
    if (chatThreadDetails?.currentChatPageThreadId === "" && !history) {
      generateThreadId();
    }
  };

  const handleReactionSelect = async (reaction: string, messageId: string) => {
    if (reaction.length > 0) {
      try {
        await updateMessageFeedback(
          reaction,
          messageId,
          currentChatPageThreadId,
          token
        );
        toast({
          title: CHAT_CONSTANTS.DEFAULT_MESSAGES.FEEDBACK_SUCCESS,
          className:
            "bg-[#1E1E76] text-white top-10 left-1/2 -translate-x-1/2 absolute z-[9999]",
          duration: CHAT_CONSTANTS.UI_CONFIG.TOAST_DURATION,
        });
      } catch (error) {
        console.error("Error submitting feedback:", error);
        toast({
          title: CHAT_CONSTANTS.DEFAULT_MESSAGES.FEEDBACK_ERROR,
          className:
            "bg-[#1E1E76] text-white top-10 left-1/2 -translate-x-1/2 absolute z-[9999]",
          duration: CHAT_CONSTANTS.UI_CONFIG.TOAST_DURATION,
        });
      }
    }
  };

  const scrollToBottom = () => {
    // Use requestAnimationFrame to ensure DOM has updated before scrolling
    requestAnimationFrame(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: CHAT_CONSTANTS.UI_CONFIG.SCROLL_BEHAVIOR,
        block: "end",
      });

      // Fallback: Also try direct scroll on container
      if (chatBoxRef.current) {
        chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight;
      }
    });
  };

  const handleChatMessage = (textToSend: string, timestamp: string) => {
    if (!isConnected) return;

    const messageId = `human-${Date.now()}-${Math.random()}`;

    setChatMessages((prev) => [
      ...prev,
      {
        sender: "human",
        text: textToSend,
        timestamp,
        messageId,
      },
    ]);

    setIsLoading(true);
    if (currentChatPageThreadId) {
      const success = wsSendMessage(textToSend, currentChatPageThreadId);
      if (success) {
        setMessage("");
      }
    }
  };

  const sendMessage = async (customMessage?: string) => {
    const timestamp = generateTimestamp();

    if (!currentUser?.email) {
      setIsSignInClicked(true);
    } else {
      const textToSend = customMessage ?? message;
      if (!textToSend.trim()) return;

      if (!isConnected) {
        connectWebSocket(chatThreadDetails.currentChatPageThreadId as string);
        setInitialMessage({
          message: customMessage || message,
          time: timestamp,
        });
        return;
      }
      handleChatMessage(textToSend, timestamp);
    }
  };

  const handleCustomMessage = (text: string) => {
    sendMessage(text);
  };

  // Optimized fetchMessages with caching
  const fetchMessages = async (id: string, forceRefresh = false) => {
    // Don't fetch if we have recent data (within 30 seconds) unless forced
    const now = Date.now();
    const timeSinceLastFetch = now - (lastFetchTime.current[id] || 0);
    const hasRecentData = timeSinceLastFetch < 30000; // 30 seconds

    if (!forceRefresh && hasRecentData && messagesCache.current[id]) {
      setChatMessages(messagesCache.current[id]);
      
      const cachedThreadName = threadNamesCache.current[id] || "";
      setCurrentThreadTitle(cachedThreadName);
      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: id || "",
          currentThreadName: cachedThreadName || "",
        })
      );
      return;
    }

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}${CHAT_CONSTANTS.API_ENDPOINTS.CHAT_MESSAGES}`,
        { thread_id: id },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      const {
        data: { detail },
      } = response;
      const rawMessages: any[] = detail.data.data;
      const threadName = detail?.data?.thread_info?.thread_name || "";

      const formatted: ChatMessage[] = rawMessages.map((msg, index) => ({
        sender: msg.role,
        text: msg.content,
        timestamp: msg.timestamp,
        messageId: msg.id || `msg-${index}-${Date.now()}`, // Ensure unique ID
      }));

      // Check if any message has flights in cards
      const flightMsg = rawMessages.find(
        (msg) =>
          msg.role === "ai" &&
          msg.cards?.data?.flights &&
          Array.isArray(msg.cards.data.flights) &&
          msg.cards.data.flights.length > 0
      );

      if (flightMsg) {
        formatted.push({
          sender: "ai",
          type: "flights",
          flights: flightMsg.cards.data.flights,
          timestamp: flightMsg.timestamp,
          routingId: flightMsg.cards.data.routing_id,
          messageId: `flight-${flightMsg.id || Date.now()}`, // Ensure unique ID
        });
      }

      // Update cache
      messagesCache.current[id] = formatted;
      threadNamesCache.current[id] = threadName;
      lastFetchTime.current[id] = now;

      setChatMessages(formatted);
      setCurrentThreadTitle(threadName);
      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: id || "",
          currentThreadName: threadName || "",
        })
      );
      dispatch(
        updateTripSummary({
          selectedInboundFlight: {},
          selectedOutboundFlight: {},
        })
      );
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      
      // If we have cached data, use it on error
      if (messagesCache.current[id]) {
        setChatMessages(messagesCache.current[id]);
        const cachedThreadName = threadNamesCache.current[id] || "";
        setCurrentThreadTitle(cachedThreadName);
        dispatch(
          updateCurrentThreadInfo({
            currentChatPageThreadId: id || "",
            currentThreadName: cachedThreadName || "",
          })
        );
      }
    }
  };

  const handleAuthClose = () => {
    setIsSignInClicked(false);
  };

  const handleMoreFlights = () => {
    if (chatResult && Object.keys(chatResult).length > 0) {
      try {
        const getFlightSearchPayload = createFlightSearchPayload(
          chatResult,
          tripSummaryDetails
        );
        if (!getFlightSearchPayload) return;
        dispatch(updateTripSummary(getFlightSearchPayload));
        router.push(
          `/flights?departureValue=${chatResult?.input?.origin}&destinationValue=${chatResult?.input?.destination}`
        );
      } catch (err) {
        setIsErrorModalOpen(true);
        console.error("Error in more flights", err);
      }
    } else {
      if (tripType.trim().length === 0) {
        setIsErrorModalOpen(true);
        setCurrentErrorMessage(
          isConnected
            ? CHAT_CONSTANTS.DEFAULT_MESSAGES.TOKEN_EXPIRED
            : CHAT_CONSTANTS.DEFAULT_MESSAGES.CONNECTION_CLOSED
        );
      }
    }
  };

  const flightProceesing = async () => {
    runWithLoading(async () => {
      const getFlightSearchPayload = createFlightSearchPayload(
        chatResult,
        tripSummaryDetails
      );
      if (!getFlightSearchPayload) return;

      try {
        const response: any = await processFlightSelection({
          routing_id: allFlights?.routing_id,
          outward_id: selectedOutboundFlight?.id,
          return_id: selectedInboundFlight?.id || "",
          updateFareOptions,
          updatePassengerList,
          updateLuggageOptions,
          updateSupplierInfo,
          updateServiceFee,
          router,
          token: token,
        });

        if (response.status === "failed") {
          setIsErrorModalOpen(true);
          setCurrentErrorMessage(response.message);
          return;
        }

        dispatch(
          updateTripSummary({
            ...tripSummaryDetails,
            ...getFlightSearchPayload,
            passengersList: response.passenger_price_segregation,
            luggageOptions: response.luggage_options,
            supplierInfo: response.supplier_info,
            activePage: activePages.flight_summary,
            from: activePages.flight_search,
            serviceFee: response.service_fee_percentage,
          })
        );
        router.push("/flightsummary");
      } catch (error) {
        scrollToBottom();
        dispatch(
          updateTripSummary({
            selectedInboundFlight: {},
            selectedOutboundFlight: {},
          })
        );
      }
    });
  };

  // useEffect(() => {
  //   return () => {
  //     closeConnection();
  //     setIsErrorModalOpen(false);
  //     setCurrentErrorMessage(CHAT_CONSTANTS.DEFAULT_MESSAGES.ERROR_GENERAL);
  //     setStreamingMessageId(null);
  //     setIsTransitioning(false);
  //   };
  // }, [closeConnection]);

  useEffect(() => {
    if (
      tripType.trim().length === 0 &&
      Object.keys(selectedOutboundFlight).length > 0
    ) {
      setIsErrorModalOpen(true);
      setCurrentErrorMessage(
        isConnected
          ? CHAT_CONSTANTS.DEFAULT_MESSAGES.TOKEN_EXPIRED
          : CHAT_CONSTANTS.DEFAULT_MESSAGES.CONNECTION_CLOSED
      );
    }

    if (tripType === TripOptions[1]) {
      if (
        Object.keys(selectedOutboundFlight).length > 0 &&
        Object.keys(selectedInboundFlight).length > 0
      ) {
        flightProceesing();
      }
    }

    if (tripType === TripOptions[0]) {
      if (Object.keys(selectedOutboundFlight).length > 0) {
        flightProceesing();
      }
    }
  }, [selectedInboundFlight, selectedOutboundFlight]);

  // Enhanced scroll effect with proper timing for loading indicators
  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(scrollTimeout);
  }, [
    chatMessages,
    streamingMessage,
    isTransitioning,
    isShasaProcessing,
    agentEventMessage,
  ]);

  // Immediate scroll for loading indicators
  useEffect(() => {
    if (isShasaProcessing || agentEventMessage.length > 0) {
      // Immediate scroll when loading indicators appear
      scrollToBottom();

      // Additional scroll after a short delay to ensure DOM has updated
      const scrollTimeout = setTimeout(() => {
        scrollToBottom();
      }, 50);

      return () => clearTimeout(scrollTimeout);
    }
  }, [isShasaProcessing, agentEventMessage]);

  useEffect(() => {
    if (newThread) {
      setTimeout(() => {
        setShasaProcessing(false);
        setAgentEventMessage("");
        setStreamingMessageId(null);
        setIsTransitioning(false);
      }, 1000);
      setChatMessages([]);
      setFlightResults({});
    }
  }, [newThread]);

  // Track whether initial message has already been sent once
  const hasSentInitialMessage = useRef(false);
  useEffect(() => {
    if (
      !hasSentInitialMessage.current &&
      initialMessage &&
      initialMessage.message !== "" &&
      initialMessage.time !== "" &&
      isConnected
    ) {
      handleChatMessage(initialMessage?.message, initialMessage?.time);
      hasSentInitialMessage.current = true;
    }
  }, [isConnected, initialMessage]);

  useEffect(() => {
    // Reset on thread change
    hasSentInitialMessage.current = false;
  }, [router.query.chatThreadId]);

  // Keep this for general message updates
  useEffect(() => {
    scrollToBottom();
  }, [chatMessages, streamingMessage]);

  // Clean up WebSocket when thread changes
  useEffect(() => {
    return () => {
      closeConnection();
      setStreamingMessageId(null);
      setIsTransitioning(false);
    };
  }, [router.query.chatThreadId]);

  // Main effect for handling route changes and authentication with caching optimization
  useEffect(() => {
    if (!router.isReady || status === "loading") return;

    if (status === "authenticated" && session?.user?.email) {
      const chatThreadId = router.query.chatThreadId as string | undefined;

      if (chatThreadId) {
        // Clear previous state when switching threads
        setChatMessages([]);
        setShasaProcessing(false);
        setAgentEventMessage("");
        setStreamingMessageId(null);
        setIsTransitioning(false);

        console.log(
          "fetchMessages-2 ========",
          currentChatPageThreadId,
          status
        );

        // Only force refresh on initial load
        const shouldForceRefresh = isInitialLoad.current;
        fetchMessages(chatThreadId, shouldForceRefresh);
        connectWebSocket(chatThreadId);
        isInitialLoad.current = false;
      } else {
        setIsSignInClicked(false);
        handleInitiateChat();
      }
    } else if (status === "unauthenticated") {
      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: "",
          currentThreadName: "",
          history: false,
          newThread: false,
          showMoreFlightsOption: false,
          chatResult: {},
          allFlights: {},
        })
      );
      setIsSignInClicked(true);
    }
  }, [session, status, router.isReady, router.query.chatThreadId]);

  console.log(
    "main element======",
    newThread,
    chatMessages,
    router.query.chatThreadId,
    session
  );

  useEffect(() => {
    if(chatMessages.length > 50){
      setChatErrorType('50-message-limit')
      setIsErrorModalOpen(true);
      setCurrentErrorImage('https://storage.googleapis.com/nxvoytrips-img/ChatPage/ChatError/Shasa-Jet%202.svg');
      setCurrentErrorMessage(`You’ve reached the 50-message limit for this chat. Let’s jump into a new chat and pick up right where we left off.`);
    }
  }, [chatMessages])
  return isFetching ? (
    <LoadingOverlay />
  ) : (
    <div className="flex flex-col h-full bg-white overflow-hidden pb-[50px]">
      {/* Scrollable Messages Area with bottom padding for fixed input */}
      <div
        className="overflow-y-auto bg-white h-full pb-12"
        ref={chatBoxRef}
        style={{
          height: "calc(100vh - 150px)",
          scrollbarWidth: "thin",
          scrollbarColor: "#cbd5e1 transparent",
        }}
      >
        <div className="max-w-3xl mx-auto md:px-4 py-6 px-2">
          {/* Welcome Screen */}
          {chatMessages.length === 0  && (
            <div className="flex flex-col items-center justify-center min-h-[60vh] text-center pointer-events-none">
              <WelcomeScreen onCustomMessage={handleCustomMessage} />
            </div>
          )}


          {/* Chat Messages */}
          <div className="space-y-6">
            {chatMessages.map((message: ChatMessage, index) => {
              // Generate a stable key that includes messageId
              const messageKey = message.messageId
                ? `${message.messageId}-${index}`
                : `${message.sender}-${index}-${message.timestamp || Date.now()}`;

              if (message.type === "flights" && message.flights?.length > 0) {
                return (
                  <div key={messageKey} className="w-full">
                    <FlightMessage
                      message={message}
                      showMoreFlightsOption={showMoreFlightsOption}
                      onMoreFlights={handleMoreFlights}
                    />
                  </div>
                );
              }

              return (
                <div
                  key={messageKey}
                  className={`w-full transition-all duration-300 ${
                    isTransitioning && message.messageId === streamingMessageId
                      ? "opacity-50"
                      : "opacity-100"
                  }`}
                >
                  {message.sender === "ai" && (
                    <div className="flex items-start space-x-3">
                      <div className="flex-1 min-w-0">
                        <div className="">
                          <AIMessage
                            message={message as ChatMessage & { sender: "ai" }}
                            onReactionSelect={handleReactionSelect}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {message.sender === "human" && (
                    <div className="flex items-start justify-end space-x-3">
                      <div className="flex-1 min-w-0 flex justify-end">
                        <div className=" rounded-2xl rounded-tr-md px-4 py-3 max-w-2xl">
                          <HumanMessage
                            message={
                              message as ChatMessage & { sender: "human" }
                            }
                            userDetails={currentUserDetails}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Streaming Message */}
            {streamingMessage && streamingMessageId && (
              <div
                className={`w-full transition-all duration-300 ${
                  isTransitioning ? "opacity-50" : "opacity-100"
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-1 min-w-0">
                    <div className="rounded-2xl rounded-tl-md px-4 py-3 max-w-4xl">
                      <StreamingMessage message={streamingMessage} />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Loading Indicators */}
            {isShasaProcessing && (
              <div className="flex items-start space-x-3">
                <div className="flex-1 min-w-0">
                  <LoadingIndicator message={shasaMessage} />
                </div>
              </div>
            )}

            {agentEventMessage.length > 0 && !isShasaProcessing && (
              <div className="flex items-start space-x-3">
                <div className="flex-1 min-w-0">
                  <div className="rounded-2xl rounded-tl-md px-4 py-3 max-w-4xl">
                    <LoadingIndicator message={agentEventMessage} />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed Chat Input - Now rendered here but positioned fixed */}
      <div className="">
        <ChatInput
        message={message}
        setMessage={setMessage}
        sendMessage={sendMessage}
        chatMessages={chatMessages}
      />
      </div>

      {/* Modals */}
      {isSignInClicked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center backdrop-blur-sm">
          <div className="">
            <AuthContainer onCloseAuth={handleAuthClose} />
          </div>
        </div>
      )}

      <ErrorModal
        isOpen={isErrorModalOpen}
        onClose={() => {
          setIsErrorModalOpen(false);
          setInitialMessage({ message: "", time: "" });
          setMessage("");
        }}
        errorMessage={currentErrorMessage}
        errorImage={currentErrorImage}
        errorType={chatErrorType}
      />
    </div>
  );
};