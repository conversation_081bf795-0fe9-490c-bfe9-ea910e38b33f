"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import Footer from "./Footer";
import { Mail, PhoneCall } from "lucide-react";
import Navbar from "./Navbar";
import FeedbackModal from "./FeedbackModal";

const ContactUs: React.FC = () => {
  type FormField =
    | "firstName"
    | "lastName"
    | "email"
    | "phone"
    | "country"
    | "reason_for_contact"
    | "is_existing_user"
    | "how_did_you_hear"
    | "message";

  type FormDataType = Record<FormField, string>;

  
  const noSpecialChar = /^[a-zA-Z0-9 ]*$/;
  const router = useRouter();
  const queryReason = router.query.reason as string | undefined;

  const reasonMap: Record<string, string> = {
    DELETE: "I need help with Account deletion",
    FEEDBACK: "I have feedback or suggestions",
    COLLAB: "I want to collaborate/partner",
    CREATOR: "I’m a content creator / influencer",
    HELP: "I need help with my trip",
    TECH: "I’m facing a technical issue",
  };


  const [formData, setFormData] = useState<FormDataType>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    country: "",
    reason_for_contact: "",
    is_existing_user: "",
    how_did_you_hear: "",
    message: "",
  });

  const [errors, setErrors] = useState<Partial<FormDataType>>({});
  const [touched, setTouched] = useState<Partial<Record<FormField, boolean>>>(
    {}
  );

  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [isSuccess, setIsSuccess] = useState(true);
  const [reason, setReason] = useState("");
  const [connectHeading, setConnectHeading] = useState("Let’s Connect");

  
  useEffect(() => {
    if (queryReason) {
      const matchedReason = reasonMap[queryReason.toUpperCase()];
      if (matchedReason) {
        setFormData((prev) => ({
          ...prev,
          reason_for_contact: matchedReason,
        }));
        setReason(matchedReason);
        setConnectHeading(matchedReason);
      }
    }
  }, [queryReason]);

  // Input Handling
  const handleChange = useCallback(
    (field: FormField, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      if (touched[field]) validateField(field, value);
    },
    [touched]
  );

  const handleBlur = (field: FormField) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
    validateField(field, formData[field]);
  };

  const handleFocus = (field: FormField) => {
    setErrors((prev) => ({ ...prev, [field]: "" }));
  };

  // Validation
  const validateField = (field: FormField, value: string) => {
    let error = "";

    if (!value && field !== "phone") {
      error = "This field is required.";
    } else if (["firstName", "lastName"].includes(field) && value.length > 20) {
      error = "Maximum 20 characters allowed.";
    } else if (field === "email" && !/^\S+@\S+\.\S+$/.test(value)) {
      error = "Invalid email format.";
    } else if (field === "message" && value.length > 200) {
      error = "Message must be within 200 characters.";
    }

    setErrors((prev) => ({ ...prev, [field]: error }));
  };

  const validateForm = () => {
    const newErrors: Partial<FormDataType> = {};
    (Object.keys(formData) as FormField[]).forEach((field) => {
      const value = formData[field];
      let error = "";

      if (!value && field !== "phone") {
        error = "This field is required.";
      } else if (
        ["firstName", "lastName"].includes(field) &&
        value.length > 20
      ) {
        error = "Maximum 20 characters allowed.";
      } else if (field === "email" && !/^\S+@\S+\.\S+$/.test(value)) {
        error = "Invalid email format.";
      } else if (field === "message" && value.length > 200) {
        error = "Message must be within 200 characters.";
      }

      if (error) newErrors[field] = error;
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form Submit
  const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();

  setTouched(
    Object.keys(formData).reduce(
      (acc, key) => ({ ...acc, [key as FormField]: true }),
      {} as Record<FormField, boolean>
    )
  );

  if (!validateForm()) return;

  const payload = {
    ...formData,
    sessionid: "dummy-session-id-or-fetch-from-cookie",
  };

  try {
    const res = await axios.post(
      `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/feedback/submit`,
      payload,
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      }
    );

    const data = res.data;

    if (res.status === 200) {
      setIsSuccess(true);
      setModalMessage("Form submitted successfully!");
      setShowModal(true);

      const retainedReason = formData.reason_for_contact;
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        country: "",
        reason_for_contact: retainedReason,
        is_existing_user: "",
        how_did_you_hear: "",
        message: "",
      });

      setErrors({});
      setTouched({});
    } else {
      setIsSuccess(false);
      setModalMessage(data?.message || "There was an error submitting the form.");
      setShowModal(true);
    }
  } catch (error: any) {
    console.error("Form submission error:", error.message);
    setIsSuccess(false);
    setModalMessage("There was an error submitting the form.");
    setShowModal(true);
  }
};



  const getSubmitLabel = () => {
    if (reason.toLowerCase().includes("help")) return "Request Support";
    if (reason.toLowerCase().includes("feedback")) return "Submit Feedback";
    if (
      reason.toLowerCase().includes("collaborate") ||
      reason.toLowerCase().includes("creator")
    )
      return "Collaborate with Us";
    return "Submit Request";
  };

  const getOptions = (field: FormField): string[] => {
    switch (field) {
      case "country":
        return ["India", "USA", "UK"];
      case "reason_for_contact":
        return [
          "I need help with my trip",
          "I’m facing a technical issue",
          "I have feedback or suggestions",
          "I need help with Account deletion",
          "I want to collaborate/partner",
          "I have a question about your service",
          "I’m a content creator / influencer",
          "Something else",
        ];
      case "is_existing_user":
        return ["Yes, I use NxVoy", "Not yet", "I’m exploring"];
      case "how_did_you_hear":
        return [
          "Google",
          "Social media",
          "From a friend",
          "LinkedIn",
          "Press / article",
          "Other",
        ];
      default:
        return [];
    }
  };

  const handleEmailClick = () => {
    window.location.href =
      "mailto:<EMAIL>?subject=Support Inquiry&body=Hello,";
  };

  const handleWhatsAppClick = () => {
    window.open("https://wa.me/************", "_blank");
  };

  const handleKeyUp = (field: FormField, value: string) => {
    const maxLengths: Partial<Record<FormField, number>> = {
      firstName: 20,
      lastName: 20,
      message: 200,
    };

    let cleanValue = value;

    if (["firstName", "lastName", "message"].includes(field)) {
      cleanValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    }

    const maxLength = maxLengths[field];
    if (maxLength && cleanValue.length > maxLength) {
      cleanValue = cleanValue.slice(0, maxLength);
    }

    if (formData[field] !== cleanValue) {
      setFormData((prev) => ({ ...prev, [field]: cleanValue }));
    }
  };
  const inputClass = "p-3 border rounded-md w-full";
  const labelClass = "block mb-1 font-semibold text-[#080236]";
  const errorClass = "text-red-500 text-sm text-right mt-1";

  return (
    <div className="flex flex-col min-h-screen bg-white text-[#0e0b2b] font-proxima-nova">
      <div
        className="w-full min-h-screen bg-cover bg-center relative flex flex-col"
        style={{
          backgroundImage:
            "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png')",
        }}
      >
        <Navbar />
        <div className="flex-grow flex w-[85%] mx-auto justify-between items-center">
          <h1 className="text-3xl md:text-5xl font-bold leading-snug bg-gradient-to-r from-[#707FF5] via-[#A195F9] to-[#F2A1F2] text-transparent bg-clip-text text-center md:text-left max-w-full md:max-w-[60%]">
            Need Help with
            <br />
            Your Next Adventure?
          </h1>
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/ContactUs.png"
            alt="ContactUs Letters"
            className="max-w-[41%] h-auto"
          />
        </div>
      </div>

      <div className="w-[85%] mx-auto py-16">
        {/* Enquiries and WhatsApp cards */}
        <div className="flex flex-col md:flex-row justify-center gap-10 mb-16 text-[#0e0b2b] items-stretch">
          <div className="flex-1 max-w-lg rounded-xl border border-gray-200 bg-white p-10 shadow-md flex flex-col justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-4 flex items-center gap-2">
                General Enquiries
              </h3>
              <p className="text-lg mb-6 text-gray-500">
                Have a question, suggestion, or need help with your trip?
                <br />
                Email us at <strong><EMAIL></strong> and we’ll get
                back to you as soon as possible.
              </p>
            </div>
            <div className="flex justify-center">
              <button
                onClick={handleEmailClick}
                className="flex items-center justify-center gap-2 px-6 py-3 rounded-md text-white text-base bg-[#1E1E76] w-full max-w-xs"
              >
                <Mail className="w-5 h-5" />
                Send us an email
              </button>
            </div>
          </div>

          <div className="flex-1 max-w-lg rounded-xl border border-gray-200 bg-white p-10 shadow-md flex flex-col justify-between">
            <div>
              <h3 className="text-2xl font-bold mb-4 flex items-center gap-2">
                Chat with Us on WhatsApp
              </h3>
              <p className="text-lg mb-6 text-gray-500">
                Want instant support?
                <br />
                Click below to start a conversation with our team on WhatsApp —
                we're here to help, wherever you are.
              </p>
            </div>
            <div className="flex justify-center">
              <button
                onClick={handleWhatsAppClick}
                className="flex items-center justify-center gap-2 px-6 py-3 rounded-md text-white text-base bg-[#1E1E76] w-full max-w-xs"
              >
                <PhoneCall className="w-5 h-5" />
                Start WhatsApp Chat
              </button>
            </div>
          </div>
        </div>
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-4 text-[#080236]">
            {connectHeading}
          </h2>
          <p className="text-lg text-gray-400 mb-10">
            Got a question, feedback, or need help? Fill out the form and our
            team will get back to you within 24 hours.
          </p>

          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 md:grid-cols-2 gap-6 text-[#0e0b2b]"
          >
            {["firstName", "lastName", "email", "phone"].map((field) => (
              <div key={field}>
                <label className={labelClass}>
                  {field === "phone" ? (
                    <>
                      Phone Number{" "}
                      <span className="text-gray-400 text-sm">(Optional)</span>
                    </>
                  ) : (
                    field
                      .replace(/([A-Z])/g, " $1")
                      .replace(/^./, (s) => s.toUpperCase())
                  )}
                </label>
                <input
                  type={
                    field === "email"
                      ? "email"
                      : field === "phone"
                        ? "tel"
                        : "text"
                  }
                  placeholder={`Enter ${field}`}
                  className={inputClass}
                  value={formData[field as FormField]}
                  onChange={(e) =>
                    handleChange(field as FormField, e.target.value)
                  }
                  onKeyUp={(e) =>
                    handleKeyUp(field as FormField, e.currentTarget.value)
                  }
                  onBlur={() => handleBlur(field as FormField)}
                  onFocus={() => handleFocus(field as FormField)}
                />
                {touched[field as FormField] &&
                  errors[field as FormField] &&
                  field !== "phone" && (
                    <div className={errorClass}>
                      {errors[field as FormField]}
                    </div>
                  )}
              </div>
            ))}

            {[
              "country",
              "reason_for_contact",
              "is_existing_user",
              "how_did_you_hear",
            ].map((field) => (
              <div key={field}>
                <label className={labelClass}>
                  {field
                    .replace(/_/g, " ")
                    .replace(/^./, (s) => s.toUpperCase())}
                </label>
                <div className="relative">
                  <select
                    className={inputClass + " appearance-none text-gray-700"}
                    value={formData[field as FormField]}
                    onChange={(e) =>
                      handleChange(field as FormField, e.target.value)
                    }
                    onBlur={() => handleBlur(field as FormField)}
                    onFocus={() => handleFocus(field as FormField)}
                  >
                    <option value="">Choose Your Options</option>
                    {getOptions(field as FormField).map((opt) => (
                      <option key={opt} value={opt}>
                        {opt}
                      </option>
                    ))}
                  </select>
                  <div className="absolute top-1/2 right-4 transform -translate-y-1/2 pointer-events-none">
                    <svg
                      className="w-4 h-4 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M19 9l-7 7-7-7"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
                {touched[field as FormField] && errors[field as FormField] && (
                  <div className={errorClass}>{errors[field as FormField]}</div>
                )}
              </div>
            ))}

            <div className="col-span-1 md:col-span-2">
              <label className={labelClass}>Message</label>
              <textarea
                placeholder="Tell us more about how we can help you..."
                className="p-3 border rounded-md w-full h-32"
                value={formData.message}
                onChange={(e) => handleChange("message", e.target.value)}
                onKeyUp={(e) => handleKeyUp("message", e.currentTarget.value)}
                onBlur={() => handleBlur("message")}
                onFocus={() => handleFocus("message")}
              />
              {touched.message && errors.message && (
                <div className={errorClass}>{errors.message}</div>
              )}
            </div>

            <div className="col-span-1 md:col-span-2">
              <button
                type="submit"
                className="flex items-center gap-2 px-6 py-3 rounded-md text-white text-base bg-[#1E1E76]"
              >
                {getSubmitLabel()}
              </button>
            </div>
          </form>
        </div>
      </div>

      <Footer />
      <FeedbackModal
        isOpen={showModal}
        message={modalMessage}
        onClose={() => setShowModal(false)}
        success={isSuccess}
      />
    </div>
  );
};

export default ContactUs;
