import React from "react";
import { motion } from "framer-motion";
import Markdown from "react-markdown";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChatReactionBar } from "./ChatReactionBar";
import { FlightCarousel } from "./FlightCarousel";
import { CHAT_CONSTANTS } from "../../constants/chat";
import { formatMessageTime } from "@/lib/chatUtils";
import { getInitials } from "@/screens/dashboard/DashboardNavbar";
import { imageFadeIn } from "@/utils/motion";
import { ChatMessage } from "@/types/chat";

interface MessageBubbleProps {
  children: React.ReactNode;
  sender: "human" | "ai";
  timestamp?: string;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  children,
  sender,
  timestamp,
}) => {
  const bubbleClass =
    sender === "ai" ? " rounded-lg px-2 py-2 " : " px-5 py-1 text-gray-700 bg-gray-50 rounded-xl";

  const timeClass =
    sender === "ai"
      ? "text-[8px] text-gray-500 mt-[10px] flex justify-end"
      : "text-[8px] text-gray-500 mt-[10px] flex justify-end";

  return (
    <div className={bubbleClass}>
      <div className="text-[15px]">{children}</div>
      {timestamp && (
        <div className={timeClass}>
          <span>{formatMessageTime(timestamp)}</span>
        </div>
      )}
    </div>
  );
};

interface AIMessageProps {
  message: ChatMessage & { sender: "ai" };
  onReactionSelect: (reaction: string, messageId: string) => void;
}

export const AIMessage: React.FC<AIMessageProps> = ({
  message,
  onReactionSelect,
}) => {
  return (
    <div className="flex flex-col items-start">
      <div className="mr-2 flex-shrink-0 mt-2">
        <img
          src={CHAT_CONSTANTS.SHASA_AVATAR}
          alt="Assistant"
          className="w-[35px] h-[35px]"
        />
      </div>

      <div className="relative group">
        <MessageBubble sender="ai" timestamp={message.timestamp}>
          {message.text && (
            <Markdown
              components={{
                p: ({ node, ...props }) => (
                  <p className="mb-3 text-gray-700 text-[15px]" {...props} />
                ),
                a: ({ node, ...props }) => (
                  <a className="text-blue-600 underline" {...props} />
                ),
                ul: ({ node, ...props }) => (
                  <ul className="list-disc list-inside mb-3" {...props} />
                ),
                ol: ({ node, ...props }) => (
                  <ol className="list-decimal list-inside mb-3" {...props} />
                ),
                li: ({ node, ...props }) => (
                  <li className="mb-1 pl-1 text-gray-800" {...props} />
                ),
                code: ({ node, ...props }) => (
                  <code
                    className="bg-gray-100 px-1 rounded  font-mono"
                    {...props}
                  />
                ),
                blockquote: ({ node, ...props }) => (
                  <blockquote
                    className="border-l-4 border-gray-300 pl-4 italic text-gray-600"
                    {...props}
                  />
                ),
              }}
            >
              {message.text}
            </Markdown>
          )}
        </MessageBubble>
        <div className="relative bottom-5 right-0  transition-opacity duration-200">
          <ChatReactionBar
            onSelect={onReactionSelect}
            messageId={message?.messageId}
            copyText={message.text}
          />
        </div>
      </div>
    </div>
  );
};

interface HumanMessageProps {
  message: ChatMessage & { sender: "human" };
  userDetails: any;
}

export const HumanMessage: React.FC<HumanMessageProps> = ({
  message,
  userDetails,
}) => {
  return (
    <div className="flex flex-col items-end">
      <div className="ml-2 flex-shrink-0">
        <Avatar className="h-[35px] w-[35px]">
          <AvatarImage src={userDetails?.profile_picture || ""} alt="Profile" />
          <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
            {getInitials(userDetails)}
          </AvatarFallback>
        </Avatar>
      </div>
      <div className="relative pt-2">
        <MessageBubble sender="human" timestamp={message.timestamp}>
          {message.text && (
            <Markdown
              components={{
                p: ({ node, ...props }) => (
                  <p className="mb-3 text-[15px]" {...props} />
                ),
              }}
            >
              {message.text}
            </Markdown>
          )}
        </MessageBubble>
      </div>
    </div>
  );
};

interface FlightMessageProps {
  message: ChatMessage & { type: "flights" };
  showMoreFlightsOption: boolean;
  onMoreFlights: () => void;
}

export const FlightMessage: React.FC<FlightMessageProps> = ({
  message,
  showMoreFlightsOption,
  onMoreFlights,
}) => {
  return (
    <div>
      <div className="flex items-start">
        <div className="mr-2 flex-shrink-0">
          <img
            src={CHAT_CONSTANTS.SHASA_AVATAR}
            alt="Assistant"
            className="w-[26px] h-[26px] rounded-[13px]"
          />
        </div>
        <div className="relative">
          <MessageBubble sender="ai" timestamp={message.timestamp}>
            <Markdown>{`Best Flights for you..`}</Markdown>
          </MessageBubble>
        </div>
      </div>

      <FlightCarousel showMoreFlightsOption={showMoreFlightsOption} onMoreFlights={onMoreFlights} flights={message.flights} routingId={message.routingId} />

      {/* {showMoreFlightsOption && (
        <div className="ml-16 my-6">
          <button
            onClick={onMoreFlights}
            className="px-4 py-2 text-sm rounded-full text-white bg-gradient-to-r from-[#4B4BC3] via-[#707FF5] to-[#A195F9]"
          >
            Show more flights
          </button>
        </div>
      )} */}
    </div>
  );
};

interface StreamingMessageProps {
  message: string;
}

export const StreamingMessage: React.FC<StreamingMessageProps> = ({
  message,
}) => {
  return (
    <div className="flex flex-col items-start">
      <div className="mr-2 flex-shrink-0">
        <img
          src={CHAT_CONSTANTS.SHASA_AVATAR}
          alt="Assistant"
          className="w-[26px] h-[26px] rounded-[13px]"
        />
      </div>
      <div className="relative">
        <MessageBubble sender="ai">
          <Markdown>{message}</Markdown>
        </MessageBubble>
      </div>
    </div>
  );
};

interface LoadingIndicatorProps {
  message: string;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message,
}) => {
  return (
    <div className="flex justify-center items-center py-2">
      <div className="bg-[#EBE9FF] text-[#1E1E76] px-4 py-2 rounded-full flex items-center gap-2 shadow-sm">
        <div className="shashaloading h-2" />
        <div className="text-[14px]">
          <Markdown>{message}</Markdown>
        </div>
      </div>
    </div>
  );
};

interface WelcomeScreenProps {
  onCustomMessage: (text: string) => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onCustomMessage,
}) => {
  return (
    <div className="mt-2 pointer-events-none">
      <div className="absolute bottom-80 left-1/2 -translate-x-1/2 text-center bg-white">
        <div>
          <motion.div
            initial="hidden"
            whileInView="show"
            variants={imageFadeIn(0, 0)}
            className="flex"
          >
            <video
              autoPlay
              muted
              loop
              playsInline
              webkit-playsinline
              className="w-full md:h-[207px] h-[120px]"
            >
              <source
                className="flex w-full h-full"
                src={CHAT_CONSTANTS.SHASA_VIDEO}
                type="video/mp4"
              />
            </video>
          </motion.div>
        </div>
        <div>
          <h2 className="text-[14px] md:text-[30px] font-bold text-[#2a1a6e]">
            Ready to Explore?
          </h2>
          <p className="text-gray-600 md:text-lg text-sm">
            I'm Sasha, your travel assistant. I can help you find flights, plan trips, and discover the best travel deals — all in one place!
          </p>
        </div>
      </div>
      {/* <div className="flex md:gap-4 gap-2 mt-4 flex-wrap justify-start absolute bottom-20 md:pb-6 pb-2 md:left-[95px] md:w-[800px] left-[15px]">
        {CHAT_CONSTANTS.SUGGESTED_MESSAGES.map((text, i) => (
          <div
            key={i}
            onClick={() => onCustomMessage(text)}
            className="text-[14px] cursor-pointer font-medium text-[#F2F3FA] p-1 px-4 rounded-full bg-[linear-gradient(267.79deg,_#F2A1F2_-60.95%,_#A195F9_6.26%,_#707FF5_73.47%,_#4B4BC3_140.67%,_#1E1E76_207.88%)]"
          >
            {text}
          </div>
        ))}
      </div> */}
    </div>
  );
};
