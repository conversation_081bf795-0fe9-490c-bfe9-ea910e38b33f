// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectLanguage matches snapshot 1`] = `
<DocumentFragment>
  <button
    aria-autocomplete="none"
    aria-controls="radix-:r9:"
    aria-expanded="false"
    class="flex items-center justify-between rounded-md px-3 py-2 ring-offset-background placeholder:text-muted-foreground focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 md:text-[12px] text-[8px] md:w-[150px] h-[30px] w-[110px] bg-[#E6E3FF] border border-[#B4BBE8] text-[#080236] outline-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 md:[&>svg]:h-[12px] md:[&>svg]:w-[12px] [&>svg]:h-4 [&>svg]:w-3 [&>svg]:stroke-[3]"
    data-placeholder=""
    data-state="closed"
    dir="ltr"
    role="combobox"
    style="font-weight: 500;"
    type="button"
  >
    <span
      style="pointer-events: none;"
    >
      Choose Language
    </span>
    <svg
      aria-hidden="true"
      class="lucide lucide-chevron-down h-4 w-4 opacity-50"
      fill="none"
      height="24"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m6 9 6 6 6-6"
      />
    </svg>
  </button>
</DocumentFragment>
`;
