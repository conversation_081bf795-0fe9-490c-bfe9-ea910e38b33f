@ui
Feature: Signup Flow
  As a new user
  I want to sign up for an account
  So that I can access NxVoy features

  Background: Start from a Home page
		Given The user starts from the "ui.nxvoy.appUrl" page
		And The user clicks on the Sign in button
        And The user cliclk on the Signup link

  @signup @positive
  Scenario: Successful signup with valid details
    When The user enters the valid first name for signup
    And The user enters the valid last name for signup
    And The user enters the valid username for signup
    And The user enters the valid password for signup
    And The user enters the valid confirm password for signup
    And The user submits the signup form
    Then The user should see a verification code prompt

# @signup @negative
#  Scenario: Signup with invalid details
#    When The user enters the invalid first name for signup
#    And The user enters the invalid last name for signup
#    And The user enters the invalid username for signup
#    And The user enters the invalid password for signup
#    And The user enters the invalid confirm password for signup
#    And The user submits the signup form
#    Then The user should see a signup error message 