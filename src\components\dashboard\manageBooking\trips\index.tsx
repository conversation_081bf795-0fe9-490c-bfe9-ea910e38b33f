import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import TripCard from "./tripCard";
import FlightInformation from "./flightInformation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Plane } from "lucide-react";
import axios from "axios";
import { useCustomSession } from "@/hooks/use-custom-session";
import { Card } from "@/components/ui/card";

// Skeleton loader component for trip cards
const TripCardSkeleton = () => {
  return (
    <div className="relative mt-4">
      {/* Status Badge Skeleton */}
      <div className="absolute -top-3 left-4 z-10 h-6 w-20 bg-gray-200 rounded-full animate-pulse"></div>

      {/* Main Card Skeleton */}
      <Card className="flex justify-between p-4 bg-white border border-neutral rounded-lg shadow-sm mt-3">
        <div className="flex space-x-4 w-full">
          {/* Trip Image Skeleton */}
          <div className="w-[88px] h-[88px] rounded-lg bg-gray-200 animate-pulse flex-shrink-0"></div>

          {/* Trip Details Skeleton */}
          <div className="flex flex-col justify-between w-full">
            {/* Type Badge Skeleton */}
            <div className="h-6 w-16 bg-gray-200 rounded-full animate-pulse mb-1"></div>

            <div className="flex flex-row items-center justify-between w-full">
              <div className="flex-1">
                {/* Title Skeleton */}
                <div className="h-6 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>

                {/* Date and Duration Skeleton */}
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 w-2 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Button Skeleton */}
          <div className="flex flex-col justify-end h-full">
            <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Component to render multiple skeleton loaders
const TripSkeletonLoader = ({ count = 3 }: { count?: number }) => {
  return (
    <div>
      {Array.from({ length: count }).map((_, index) => (
        <TripCardSkeleton key={index} />
      ))}
    </div>
  );
};

const Trips = () => {
  const [activeTrip, setActiveTrip] = useState("trip-001");
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);
  const [selectedTripStatus, setSelectedTripStatus] = useState<
    "confirmed" | "pending" | "cancelled" | "completed" | null
  >(null);
  const [showFlightInfo, setShowFlightInfo] = useState(false);
  const [completedTrips, setCompletedTrips] = useState<any[]>([]);
  const [upcomingTrips, setUpcomingTrips] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  function handleViewTrip(
    tripId: string,
    status?: "confirmed" | "pending" | "cancelled" | "completed"
  ): void {
    setSelectedTripId(tripId);
    setSelectedTripStatus(status || null);
    setShowFlightInfo(true);
    // You can now use the status parameter here
    // console.log("Trip ID:", tripId, "Status:", status);
  }

  function handleBackToTrips(): void {
    setShowFlightInfo(false);
    setSelectedTripId(null);
  }

  // Get flight data for the selected trip
  const getFlightDataForTrip = (tripId: string) => {
    // Search in upcomingTrips and completedTrips
    const trip =
      upcomingTrips.find((t) => t.id === tripId) ||
      completedTrips.find((t) => t.id === tripId);
    if (!trip) return {};
    return trip;
  };

  // Default trips list view
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const fetchUserDetails = async () => {
    try {
      if (!token) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/flight/trip-details`,
        {
          headers: {
            Authorization: `Bearer ${session?.accessToken || token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        setLoading(false);
        throw new Error("Failed to fetch user details");
      }

      const data = await response.data;
      // console.log(data);
      // console.log(data.detail.data?.CompletedTrips);
      // console.log(data.detail.data?.UpcomingTrips);
      setCompletedTrips(data.detail.data?.CompletedTrips || []);
      setUpcomingTrips(data.detail.data?.UpcomingTrips || []);
      setLoading(false);

      // dispatch(setCurrentUser(data?.detail?.data as UserDetails));
    } catch (error: any) {
      if (error.status === 401) {
        console.log("I am unauthorized, logout now");
        // handleInvalidSession();
      }
      console.error("Error fetching user details:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserDetails();
  }, [token]);

  console.log(upcomingTrips);

  return (
    <>
      {showFlightInfo && selectedTripId ? (
        <div className="relative">
          <FlightInformation
            {...getFlightDataForTrip(selectedTripId)}
            handleBackToTrips={handleBackToTrips}
            status={selectedTripStatus}
          />
        </div>
      ) : (
        <div>
          <Tabs defaultValue="Upcoming-trips" className="">
            <TabsList className="rounded-full h-10 p-0 mb-3 bg-neutral">
              <TabsTrigger
                value="Upcoming-trips"
                className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
              >
                Upcoming trips
              </TabsTrigger>
              <TabsTrigger
                value="Past-trips"
                className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
              >
                Past trips
              </TabsTrigger>
            </TabsList>

            {/* Upcoming Trips */}
            <TabsContent value="Upcoming-trips">
              {loading ? (
                <TripSkeletonLoader count={3} />
              ) : upcomingTrips.length > 0 ? (
                upcomingTrips.map((trip) => (
                  <TripCard
                    key={trip.id}
                    id={trip.id}
                    status={trip.status || "Confirmed"}
                    type={trip.type}
                    title={trip.location}
                    datesrc={trip.flightDetails.source.date}
                    datedes={trip.flightDetails.destination.date}
                    duration={trip.flightDetails.duration}
                    isActive={activeTrip === trip.id}
                    onViewTrip={handleViewTrip}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center min-h-[200px] text-center">
                  <div className="text-gray-400 mb-2">
                    <Plane className="h-12 w-12 mx-auto mb-3" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-600 mb-1">
                    No upcoming trips
                  </h3>
                  <p className="text-gray-500">
                    Your upcoming trips will appear here
                  </p>
                </div>
              )}
            </TabsContent>

            {/* Past Trips */}
            <TabsContent value="Past-trips">
              {loading ? (
                <TripSkeletonLoader count={3} />
              ) : completedTrips.length > 0 ? (
                completedTrips.map((trip) => (
                  <TripCard
                    key={trip.id}
                    id={trip.id}
                    status={trip.status || "Completed"}
                    type={trip.type}
                    title={trip.location}
                    datesrc={trip.flightDetails.source.date}
                    datedes={trip.flightDetails.destination.date}
                    duration={trip.flightDetails.duration}
                    isActive={activeTrip === trip.id}
                    onViewTrip={handleViewTrip}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center min-h-[200px] text-center">
                  <div className="text-gray-400 mb-2">
                    <Plane className="h-12 w-12 mx-auto mb-3" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-600 mb-1">
                    No past trips
                  </h3>
                  <p className="text-gray-500">
                    Your completed trips will appear here
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </>
  );
};

export default Trips;
