import { fixture } from '../fixtures/Fixture';
import { ScreenshotHelper } from './ScreenshotHelper';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Helper class for common page interactions and debugging
 */
export class PageHelper {
    /**
     * Capture detailed debug information about the current page state
     * @param context - Context description for the debug info
     */
    public static async captureDebugInfo(context: string): Promise<void> {
        try {
            console.log(`Capturing debug info for: ${context}`);
            
            // Take a screenshot
            await ScreenshotHelper.takeScreenshot(`debug-${context}`, true, true);
            
            // Get current URL
            const url = await fixture.page.url();
            console.log(`Current URL: ${url}`);
            
            // Get page title
            try {
                const title = await fixture.page.title();
                console.log(`Page title: ${title}`);
            } catch (e) {
                console.log('Could not get page title');
            }
            
            // Try to get some page content
            try {
                const bodyText = await fixture.page.$eval('body', (el) => el.innerText.substring(0, 1000));
                console.log(`Page content sample: ${bodyText.substring(0, 500)}...`);
            } catch (e) {
                console.log('Could not get page content');
            }
            
            // Check for common error messages
            const errorSelectors = [
                '.error', 
                '[class*="error"]', 
                '.alert', 
                '[class*="alert"]',
                'text="error"',
                'text="failed"',
                'text="not found"'
            ];
            
            for (const selector of errorSelectors) {
                try {
                    const isVisible = await fixture.page.isVisible(selector, { timeout: 1000 });
                    if (isVisible) {
                        console.log(`Found error element matching: ${selector}`);
                        const text = await fixture.page.$eval(selector, (el) => {
                            return (el as HTMLElement).innerText || (el as Element).textContent || '';
                        });
                        console.log(`Error text: ${text}`);
                    }
                } catch (e) {
                    // Ignore errors for selectors that don't exist
                }
            }
              // Save HTML source for deeper debugging if needed
            try {
                // Only save HTML source for error contexts or when explicitly debugging
                const isErrorContext = context.includes('error') || context.includes('fail');
                const isDebugging = process.env.DEBUG_SCREENSHOTS === 'true' || process.env.DEBUG_HTML === 'true';
                
                if (isErrorContext || isDebugging) {
                    const html = await fixture.page.content();
                    const debugDir = path.join('test-reports', 'debug');
                    
                    // Create debug directory if it doesn't exist
                    if (!fs.existsSync(debugDir)) {
                        fs.mkdirSync(debugDir, { recursive: true });
                    }
                    
                    const timestamp = new Date().toISOString().replace(/:/g, '-');
                    const filename = path.join(debugDir, `${context}-${timestamp}.html`);
                    
                    fs.writeFileSync(filename, html);
                    console.log(`Saved HTML source to: ${filename}`);
                } else {
                    console.log(`Skipping HTML source capture for non-error context: ${context}`);
                }
            } catch (e) {
                console.log(`Could not save HTML source: ${e}`);
            }
            
            console.log('Debug info capture completed');
        } catch (error) {
            console.error(`Error capturing debug info: ${error}`);
        }
    }
    
    /**
     * Perform a robust click operation with multiple strategies
     * @param selector - The selector to click
     * @param options - Click options
     */
    public static async robustClick(
        selector: string, 
        options: { 
            timeout?: number, 
            force?: boolean,
            description?: string,
            fallbackSelectors?: string[]
        } = {}
    ): Promise<boolean> {
        const {
            timeout = 10000,
            force = false,
            description = selector,
            fallbackSelectors = []
        } = options;
        
        try {
            console.log(`Attempting to click: ${description}`);
            
            // First try with standard click
            await fixture.page.click(selector, { timeout });
            console.log(`Successfully clicked: ${description}`);
            return true;
        } catch (initialError) {
            console.log(`Initial click failed: ${initialError}. Trying alternative approaches...`);
            
            // Try fallback selectors if provided
            if (fallbackSelectors.length > 0) {
                for (const fallbackSelector of fallbackSelectors) {
                    try {
                        console.log(`Trying fallback selector: ${fallbackSelector}`);
                        await fixture.page.click(fallbackSelector, { timeout: timeout / 2 });
                        console.log(`Successfully clicked fallback: ${fallbackSelector}`);
                        return true;
                    } catch (fallbackError) {
                        console.log(`Fallback selector ${fallbackSelector} failed: ${fallbackError}`);
                    }
                }
            }
            
            // Try force click option
            try {
                console.log('Trying force click option');
                await fixture.page.click(selector, { force: true, timeout: timeout / 2 });
                console.log(`Successfully force-clicked: ${description}`);
                return true;
            } catch (forceError) {
                console.log(`Force click failed: ${forceError}`);
            }
            
            // Try JavaScript click as last resort
            try {
                console.log('Trying JavaScript click');
                await fixture.page.$eval(selector, (element) => {
                    if (element instanceof HTMLElement) {
                        element.click();
                        return true;
                    }
                    return false;
                });
                console.log(`Successfully JavaScript-clicked: ${description}`);
                return true;
            } catch (jsError) {
                console.log(`JavaScript click failed: ${jsError}`);
                
                // Take a debug screenshot
                await ScreenshotHelper.takeScreenshot(`click-failed-${description.replace(/[^a-zA-Z0-9]/g, '-')}`, true, true);
                
                // Log failure but don't throw
                console.error(`All click strategies failed for: ${description}`);
                return false;
            }
        }
    }
}
