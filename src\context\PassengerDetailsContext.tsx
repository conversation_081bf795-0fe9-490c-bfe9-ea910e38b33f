'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

import { PassengerFormData } from "@/constants/models";

interface PassengerDetailsContextType {
    passengerDetails: PassengerFormData[];
    updatePassengerDetail: (index: number, data: Partial<PassengerFormData>) => void;
    initializePassengers: (count: number) => void;
}

const PassengerDetailsContext = createContext<PassengerDetailsContextType | undefined>(undefined);

export const PassengerDetailsProvider = ({ children }: { children: ReactNode }) => {
    const [passengerDetails, setPassengerDetails] = useState<PassengerFormData[]>([]);

    const updatePassengerDetail = (index: number, data: Partial<PassengerFormData>) => {
        setPassengerDetails((prev) =>
          prev.map((p, i) => (i === index ? { ...p, ...data } : p))
        );
    };

    const initializePassengers = (count: number) => {
        const empty: PassengerFormData = {
            title: '',
            firstName: '',
            middleName: '',
            lastName: '',
            dob: '',
            gender: '',
            passportNumber: '',
            passportCountry: '',
            specialService: '',
        };
        setPassengerDetails(Array.from({ length: count }, () => ({ ...empty })));
    };

    return (
        <PassengerDetailsContext.Provider
          value={{ passengerDetails, updatePassengerDetail, initializePassengers }}
        >
          {children}
        </PassengerDetailsContext.Provider>
    );
};

export const usePassengerDetails = () => {
    const context = useContext(PassengerDetailsContext);
    if (!context) {
      throw new Error('usePassengerDetails must be used within a PassengerDetailsProvider');
    }
    return context;
};