// store/slices/hotelBookingContext.ts
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Hotel, Room, RateOption } from "@/lib/types";

interface HotelBookingContext {
  // Search parameters
  location: string;
  checkIn: string;
  checkOut: string;
  adults: number;
  children: number;
  rooms: number;
  
  // Calculated values
  nights: number;
  
  // Selected booking data
  selectedRoom?: {
    hotel: Hotel;
    room: Room;
    selectedRate: RateOption;
    selectedRateIndex: number;
  };
  
  // Additional booking info
  totalPrice?: number;
  currency?: string;
}

interface HotelBookingContextState {
  hotelBookingContext: HotelBookingContext | null;
}

// Load initial state from localStorage if available (matching hotelDetails pattern)
const loadFromLocalStorage = (): HotelBookingContext | null => {
  if (typeof window !== 'undefined') {
    try {
      const savedContext = localStorage.getItem('hotelBookingContext');
      return savedContext ? JSON.parse(savedContext) : null;
    } catch (error) {
      console.error('Error loading booking context from localStorage:', error);
      return null;
    }
  }
  return null;
};

// Save to localStorage (matching hotelDetails pattern)
const saveToLocalStorage = (context: HotelBookingContext | null) => {
  if (typeof window !== 'undefined') {
    try {
      if (context) {
        localStorage.setItem('hotelBookingContext', JSON.stringify(context));
      } else {
        localStorage.removeItem('hotelBookingContext');
      }
    } catch (error) {
      console.error('Error saving booking context to localStorage:', error);
    }
  }
};

const calculateNights = (checkIn: string, checkOut: string): number => {
  try {
    const start = new Date(checkIn);
    const end = new Date(checkOut);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays || 1;
  } catch {
    return 1;
  }
};

const initialState: HotelBookingContextState = {
  hotelBookingContext: loadFromLocalStorage(),
};

const hotelBookingContextSlice = createSlice({
  name: "hotelBookingContext",
  initialState,
  reducers: {
    setSearchContext: (state, action: PayloadAction<{
      location: string;
      checkIn: string;
      checkOut: string;
      adults: number;
      children: number;
      rooms: number;
    }>) => {
      const { checkIn, checkOut, ...rest } = action.payload;
      const nights = calculateNights(checkIn, checkOut);
      
      state.hotelBookingContext = {
        ...rest,
        checkIn,
        checkOut,
        nights,
      };
      
      saveToLocalStorage(state.hotelBookingContext);
    },
    
    setSelectedRoom: (state, action: PayloadAction<{
      hotel: Hotel;
      room: Room;
      selectedRate: RateOption;
      selectedRateIndex: number;
    }>) => {
      if (state.hotelBookingContext) {
        state.hotelBookingContext.selectedRoom = action.payload;
        
        // Calculate total price
        const rate = action.payload.selectedRate;
        const nights = state.hotelBookingContext.nights;
        const pricePerNight = parseFloat(rate.net_price || '0');
        
        state.hotelBookingContext.totalPrice = pricePerNight * nights;
        state.hotelBookingContext.currency = action.payload.hotel.currency;
        
        saveToLocalStorage(state.hotelBookingContext);
      }
    },
    
    updateBookingDates: (state, action: PayloadAction<{
      checkIn: string;
      checkOut: string;
    }>) => {
      if (state.hotelBookingContext) {
        state.hotelBookingContext.checkIn = action.payload.checkIn;
        state.hotelBookingContext.checkOut = action.payload.checkOut;
        state.hotelBookingContext.nights = calculateNights(action.payload.checkIn, action.payload.checkOut);
        
        // Recalculate total price if room is selected
        if (state.hotelBookingContext.selectedRoom) {
          const rate = state.hotelBookingContext.selectedRoom.selectedRate;
          const pricePerNight = parseFloat(rate.net_price || '0');
          state.hotelBookingContext.totalPrice = pricePerNight * state.hotelBookingContext.nights;
        }
        
        saveToLocalStorage(state.hotelBookingContext);
      }
    },
    
    updateGuestInfo: (state, action: PayloadAction<{
      adults: number;
      children: number;
      rooms: number;
    }>) => {
      if (state.hotelBookingContext) {
        state.hotelBookingContext.adults = action.payload.adults;
        state.hotelBookingContext.children = action.payload.children;
        state.hotelBookingContext.rooms = action.payload.rooms;
        
        saveToLocalStorage(state.hotelBookingContext);
      }
    },
    
    clearHotelBookingContext: (state) => {
      state.hotelBookingContext = null;
      saveToLocalStorage(null);
    },
  },
});

export const { 
  setSearchContext, 
  setSelectedRoom, 
  updateBookingDates, 
  updateGuestInfo, 
  clearHotelBookingContext 
} = hotelBookingContextSlice.actions;

export default hotelBookingContextSlice.reducer;