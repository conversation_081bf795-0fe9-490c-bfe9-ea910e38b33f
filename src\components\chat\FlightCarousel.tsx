import React, { useState, useEffect } from "react";
import NewFlightCard from "./NewFlightCard";
import { CHAT_CONSTANTS } from "../../constants/chat";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";


function getFlightDateRange(flight:any) {
  const startRaw = flight?.outward_flight?.departure_date || "";
  const endRaw = flight?.return_flight?.departure_date || "";

  const getFormatted = (dateStr:any) => {
    const parts = dateStr.split(", ");
    return parts[1] || ""; // returns "Jul 04"
  };

  const start = getFormatted(startRaw);
  const end = endRaw ? getFormatted(endRaw) : null;

  return end ? `${start} – ${end}` : start;
}

const getBadgeConfig = (rank: number) => {
  switch (rank) {
    case 1:
      return { text: "Best Value", bgColor: "bg-[#4B4BC3]" };
    case 2:
      return { text: "More Flex", bgColor: "bg-[#4DC34B]" };
    case 3:
      return { text: "Fully Flexible", bgColor: "bg-[#1E1E76]" };
    default:
      return { text: "Best Value", bgColor: "bg-[#4B4BC3]" };
  }
};

interface FlightCarouselProps {
  showMoreFlightsOption: any;
  onMoreFlights: any;
  flights: any[];
  routingId?: string;
}

export const FlightCarousel: React.FC<FlightCarouselProps> = ({
  showMoreFlightsOption,
  onMoreFlights,
  flights,
  routingId,
}) => {
  // Limit flights to display
  const displayFlights =
    flights.length > CHAT_CONSTANTS.UI_CONFIG.MAX_FLIGHTS_DISPLAYED
      ? flights.slice(0, CHAT_CONSTANTS.UI_CONFIG.MAX_FLIGHTS_DISPLAYED)
      : flights;

  if (!flights || flights.length === 0) {
    return null;
  }

  return (
    <div className="flex justify-center no-scrollbar mt-4">
      <div className="w-full max-w-[800px]">
        <Tabs defaultValue="1" className="w-full">
          <div className="flex justify-between">
            <TabsList className="rounded-xl bg-white border">
              {displayFlights.map((flight: any, fIdx: number) => (
                <TabsTrigger
                  key={fIdx}
                  value={String(flight.rank)}
                  className={`rounded-xl bg-white text-black data-[state=active]:py-1 data-[state=active]:bg-[#1E1E76] data-[state=active]:text-white`}
                >
                  {getBadgeConfig(flight.rank).text}
                </TabsTrigger>
              ))}
            </TabsList>
            <div className="md:pb-0 md:pt-0 md:pb-0 relative pb-4 pt-12">
              <div className="md:relative md:w-auto absolute w-[130px] right-1 inline-flex items-center rounded-full border border-gray-300 px-4 py-1 bg-white shadow-sm text-sm font-medium text-black space-x-2">
                <span>{getFlightDateRange(displayFlights[0])}</span>
                {/* <span className="text-gray-400">|</span> */}
                {/* <span>1 traveler</span> */}
              </div>
            </div>
          </div>

          {displayFlights.map((flight: any, fIdx: number) => (
            <TabsContent
              value={String(flight.rank)}
              key={fIdx}
              className="w-full"
            >
              <NewFlightCard
                key={fIdx}
                flight={flight}
                isRecommended={fIdx === 0}
                showMoreFlightsOption={showMoreFlightsOption}
                onMoreFlights={onMoreFlights}
                // routing_id={routingId}
              />
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
};
