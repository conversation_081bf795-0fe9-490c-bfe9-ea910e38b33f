import { useState, useEffect } from 'react';

// Custom hook to detect device type
export const useDeviceDetect = () => {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    // Function to check if the device is mobile
    const checkMobile = () => {
      // Option 1: Check viewport width (most reliable for responsive design)
      const isMobileViewport = window.innerWidth < 768; // Adjust breakpoint as needed
      
      // Option 2: Check user agent (less reliable but catches some edge cases)
      const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
      
      // You can use either approach or combine them based on your needs
      setIsMobile(isMobileViewport || isMobileUserAgent);
    };
    
    // Run the check immediately
    checkMobile();
    
    // Set up event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Clean up event listener
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  return { isMobile };
};