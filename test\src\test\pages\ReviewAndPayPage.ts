import { BasePage } from './BasePage';
import { fixture } from '../fixtures/Fixture';

/**
 * Page object for the Review & Pay screen
 */
export class ReviewAndPayPage extends BasePage {
  // Elements
  private static creditCardInfoSelector = 'div.flex.text-2xl.text-\\[\\#1E1E76\\].font-semibold.font-proxima-nova:has-text("Credit Card Information")';
  private static billingTitleSelector = 'div.flex.text-2xl.text-\\[\\#1E1E76\\].font-semibold.font-proxima-nova:has-text("Billing")';
  private static contactDetailsTitleSelector = 'div.flex.text-2xl.text-\\[\\#1E1E76\\].font-semibold.font-proxima-nova:has-text("Contact Details")';
  private static confirmPayButtonSelector = 'button:has-text("Confirm & Pay")';
  private static cardNumberInputSelector = 'input[placeholder="Card Number"]';
    // Credit card related selectors
  private static existingCardSelector = 'div:has-text("Payment will be charged with") span.font-bold:has-text("****")';
  private static changeCardButtonSelector = 'button:has-text("Change Card")';
  
  // Updated credit card field selectors with multiple options to improve detection
  private static cardNumberFieldSelector = [
    'input[name="cardnumber"][data-elements-stable-field-name="cardNumber"]',
    'input[placeholder="Card number"]', 
    'input[placeholder="Card Number"]',
    'input.InputElement[autocomplete="cc-number"]',
    'input[aria-label="Credit or debit card number"]'
  ];
  
  private static expDateFieldSelector = [
    'input[name="exp-date"][data-elements-stable-field-name="cardExpiry"]',
    'input[placeholder="MM / YY"]',
    'input.InputElement[autocomplete="cc-exp"]',
    'input[aria-label="Credit or debit card expiry date"]'
  ];
  
  private static cvvFieldSelector = [
    'input[name="cvc"][data-elements-stable-field-name="cardCvc"]',
    'input[placeholder="CVC"]',
    'input.InputElement[autocomplete="cc-csc"]',
    'input[aria-label="Credit or debit card CVC/CVV"]'
  ];
  
  // Payment-related selectors
  private static termsCheckboxSelector = 'input[type="checkbox"]';
  private static payNowButtonSelector = 'button.px-4.py-2.text-\\[\\#F2F3FA\\].w-1\\/3.xs\\:w-2\\/3, button:has-text("Pay now")';
  private static alternativePayNowButtonSelector = 'button[style*="background: linear-gradient"][style*="border-radius: 100px"]:has-text("Pay now")';
  
  /**
   * Checks if the user is on the Review & Pay screen by verifying if key elements are visible
   * @returns {Promise<boolean>} True if user is on Review & Pay screen, false otherwise
   */  static async isUserOnReviewAndPayScreen(): Promise<boolean> {
    try {
      console.log('Checking if user is on Review & Pay screen...');
      
      // Wait for the page to be stable
      await fixture.page.waitForTimeout(2000);
      
      // First try with modern Playwright locators
      try {
        // Check for headings using getByRole
        const headingIndicators = [
          { name: 'Credit Card Information', description: 'Credit Card Information heading' },
          { name: 'Billing', description: 'Billing heading' },
          { name: 'Contact Details', description: 'Contact Details heading' }
        ];
        
        for (const heading of headingIndicators) {
          try {
            const headingElement = fixture.page.getByRole('heading', { 
              name: heading.name, 
              exact: false
            });
            
            if (await headingElement.isVisible({ timeout: 3000 })) {
              console.log(`Found Review & Pay page indicator: ${heading.description} using role locator`);
              return true;
            }
          } catch (headingError) {
            console.log(`Heading indicator not found: ${heading.description}`);
          }
        }
        
        // Check for button using getByRole
        const confirmButton = fixture.page.getByRole('button', { name: /confirm & pay/i });
        if (await confirmButton.isVisible({ timeout: 3000 })) {
          console.log('Found Review & Pay page indicator: Confirm & Pay button using role locator');
          return true;
        }
        
        // Check for text fields/labels using getByText
        const textIndicators = [
          'Credit Card Information',
          'Billing',
          'Contact Details',
          'Card Number',
          'Exp. date',
          'Enter your address details for Billing'
        ];
        
        for (const text of textIndicators) {
          try {
            const textElement = fixture.page.getByText(text, { exact: true });
            if (await textElement.isVisible({ timeout: 2000 })) {
              console.log(`Found Review & Pay page indicator: ${text} text using text locator`);
              return true;
            }
          } catch (textError) {
            console.log(`Text indicator not found: ${text}`);
          }
        }
        
        // Check for input fields using getByPlaceholder
        const cardNumberInput = fixture.page.getByPlaceholder('Card Number', { exact: true });
        if (await cardNumberInput.isVisible({ timeout: 2000 })) {
          console.log('Found Review & Pay page indicator: Card Number input field using placeholder locator');
          return true;
        }
      } catch (locatorError) {
        console.log('Error using modern locators:', locatorError.message);
      }
      
      // Fall back to legacy selectors
      // Define multiple indicators of the Review & Pay page
      const pageIndicators = [
        { selector: this.creditCardInfoSelector, name: 'Credit Card Information heading' },
        { selector: this.billingTitleSelector, name: 'Billing heading' },
        { selector: this.contactDetailsTitleSelector, name: 'Contact Details heading' },
        { selector: this.confirmPayButtonSelector, name: 'Confirm & Pay button' },
        { selector: this.cardNumberInputSelector, name: 'Card Number input field' }
      ];
      
      // Check for each indicator's visibility using legacy selectors
      for (const indicator of pageIndicators) {
        try {
          const isVisible = await fixture.page.isVisible(indicator.selector, { timeout: 3000 });
          if (isVisible) {
            console.log(`Found Review & Pay page indicator: ${indicator.name} using legacy selector`);
            return true;
          }
        } catch (error) {
          console.log(`Legacy indicator not found: ${indicator.name}`);
        }
      }
      
      // Try with text-based selectors (more reliable for localization)
      const textSelectors = [
        'text="Credit Card Information"',
        'text="Billing"',
        'text="Contact Details"',
        'text="Confirm & Pay"',
        'text="Card Number"',
        'text="Exp. date"',
        'text="Enter your address details for Billing"',
        'text="Enter your address details for Contact"'
      ];
      
      for (const selector of textSelectors) {
        try {
          const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
          if (isVisible) {
            console.log(`Found Review & Pay page using text selector: ${selector}`);
            return true;
          }
        } catch (e) {
          console.log(`Text selector not found: ${selector}`);
        }
      }
      
      // Check for payment form elements
      try {
        const cardNumberInput = await fixture.page.locator('input[placeholder="Card Number"]').count();
        const monthInput = await fixture.page.locator('input[placeholder="Month"]').count();
        const yearInput = await fixture.page.locator('input[placeholder="Year"]').count();
        const cvvInput = await fixture.page.locator('input[placeholder="CVV"]').count();
        
        if (cardNumberInput > 0 && monthInput > 0 && yearInput > 0 && cvvInput > 0) {
          console.log('Found payment form fields');
          return true;
        }
      } catch (error) {
        console.log('Payment form fields not found');
      }
      
      // Check URL as a fallback
      const url = fixture.page.url();
      if (url.includes('payment') || url.includes('review') || url.includes('checkout')) {
        console.log(`Found Review & Pay page based on URL: ${url}`);
        return true;
      }
      
      console.log('User is not on Review & Pay screen');
      return false;
    } catch (error) {
      console.error('Error while checking if user is on Review & Pay screen:', error);
      return false;
    }
  }
  
  /**
   * Checks if credit card information already exists
   * @returns {Promise<boolean>} True if credit card info already exists, false otherwise
   */
  static async isCreditCardInfoExisting(): Promise<boolean> {
    try {
      console.log('Checking if credit card information already exists...');
      
      // Wait for the page to be stable
      await fixture.page.waitForTimeout(1000);
      
      // Check if the existing card element is visible
      const isExistingCardVisible = await fixture.page.isVisible(this.existingCardSelector)
        .catch(() => false);
        
      if (isExistingCardVisible) {
        console.log('Existing credit card information found');
        return true;
      }
      
      // Alternative check for saved card text
      const hasSavedCardText = await fixture.page.locator('text=**** **** ****').count()
        .then(count => count > 0)
        .catch(() => false);
        
      if (hasSavedCardText) {
        console.log('Saved card found via text pattern');
        return true;
      }
      
      console.log('No existing credit card information found');
      return false;
    } catch (error) {
      console.error('Error while checking for existing credit card information:', error);
      return false;
    }
  }
    /**
   * Fills in credit card information if required
   * @param {string} cardNumber - The credit card number
   * @param {string} expDate - The expiration date in MM/YY format
   * @param {string} cvv - The security code
   * @returns {Promise<boolean>} True if successful, false otherwise
   */
  static async fillCreditCardInfoIfRequired(cardNumber: string, expDate: string, cvv: string): Promise<boolean> {
    try {
      console.log('Checking if credit card information needs to be filled...');
      
      // Check if credit card info already exists
      const isExisting = await this.isCreditCardInfoExisting();
      
      if (isExisting) {
        console.log('Credit card information already exists, skipping fill');
        return true;
      }
      
      console.log('Credit card information needs to be filled');
      
      // Wait for page to be stable and any iframe to load (payment forms often use iframes)
      await fixture.page.waitForTimeout(2000);
      
      // 1. First, fill the card number field
      console.log('Attempting to fill card number...');
      let cardNumberFilled = false;
      
      // Try with modern Playwright locators first
      try {
        // Try to find card number field by placeholder
        const cardNumberInput = fixture.page.getByPlaceholder('Card Number', { exact: true });
        if (await cardNumberInput.isVisible({ timeout: 3000 })) {
          console.log('Found card number field using placeholder locator');
          await cardNumberInput.click();
          await cardNumberInput.clear();
          await cardNumberInput.fill(cardNumber);
          await fixture.page.waitForTimeout(500);
          cardNumberFilled = true;
        }
      } catch (placeholderError) {
        console.log('Failed to find card number field by placeholder:', placeholderError.message);
      }
      
      // Try by label if placeholder didn't work
      if (!cardNumberFilled) {
        try {
          const cardNumberByLabel = fixture.page.getByLabel('Card number', { exact: false });
          if (await cardNumberByLabel.isVisible({ timeout: 2000 })) {
            console.log('Found card number field using label locator');
            await cardNumberByLabel.click();
            await cardNumberByLabel.clear();
            await cardNumberByLabel.fill(cardNumber);
            await fixture.page.waitForTimeout(500);
            cardNumberFilled = true;
          }
        } catch (labelError) {
          console.log('Failed to find card number field by label:', labelError.message);
        }
      }
      
      // Try by role if other methods didn't work
      if (!cardNumberFilled) {
        try {
          // Try to find any textbox with a card number related aria-label
          const cardInputByRole = fixture.page.getByRole('textbox', { 
            name: /card number|credit card/i,
            exact: false
          });
          
          if (await cardInputByRole.isVisible({ timeout: 2000 })) {
            console.log('Found card number field using role locator');
            await cardInputByRole.click();
            await cardInputByRole.clear();
            await cardInputByRole.fill(cardNumber);
            await fixture.page.waitForTimeout(500);
            cardNumberFilled = true;
          }
        } catch (roleError) {
          console.log('Failed to find card number field by role:', roleError.message);
        }
      }
      
      // Fall back to CSS selectors if modern locators fail
      if (!cardNumberFilled) {
        // Try each selector in the array until one works
        for (const selector of this.cardNumberFieldSelector) {
          try {
            const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
              .catch(() => false);
            
            if (isVisible) {
              console.log(`Found card number field with selector: ${selector}`);
              
              // Click and fill with small delay between characters to avoid anti-fraud detection
              await fixture.page.locator(selector).click();
              await fixture.page.locator(selector).clear();
              await fixture.page.locator(selector).fill(cardNumber);
              await fixture.page.waitForTimeout(500);
              
              cardNumberFilled = true;
              break;
            }
          } catch (error) {
            console.log(`Failed to interact with card number selector ${selector}: ${error}`);
          }
        }
      }
      
      // If none of the selectors worked directly, try with iframe handling
      if (!cardNumberFilled) {
        console.log('Trying to find card number field in iframes...');
        
        // Check if there are any iframes that might contain the payment form
        const iframeCount = await fixture.page.locator('iframe').count();
        console.log(`Found ${iframeCount} iframes on the page`);
        
        // Try each iframe
        for (let i = 0; i < iframeCount; i++) {
          try {
            const frame = await fixture.page.frameLocator('iframe').nth(i);
            
            // Try each selector in the iframe
            for (const selector of this.cardNumberFieldSelector) {
              const isVisible = await frame.locator(selector).isVisible()
                .catch(() => false);
              
              if (isVisible) {
                console.log(`Found card number field in iframe ${i} with selector: ${selector}`);
                await frame.locator(selector).click();
                await frame.locator(selector).fill(cardNumber);
                await fixture.page.waitForTimeout(500);
                cardNumberFilled = true;
                break;
              }
            }
            
            if (cardNumberFilled) break;
          } catch (error) {
            console.log(`Error working with iframe ${i}: ${error}`);
          }
        }
      }
      
      if (!cardNumberFilled) {
        console.error('Failed to find or fill the card number field');
        return false;
      }
      
      // 2. Now fill the expiration date
      console.log('Attempting to fill expiration date...');
      let expiryFilled = false;
      
      // Try with modern Playwright locators first
      try {
        // Try using getByPlaceholder
        const expiryByPlaceholder = fixture.page.getByPlaceholder('MM / YY', { exact: true });
        if (await expiryByPlaceholder.isVisible({ timeout: 3000 })) {
          console.log('Found expiry date field using placeholder locator');
          await expiryByPlaceholder.click();
          await expiryByPlaceholder.clear();
          await expiryByPlaceholder.fill(expDate);
          await fixture.page.waitForTimeout(500);
          expiryFilled = true;
        }
      } catch (placeholderError) {
        console.log('Failed to find expiry date field by placeholder:', placeholderError.message);
      }
      
      // Try by label if placeholder didn't work
      if (!expiryFilled) {
        try {
          const expiryByLabel = fixture.page.getByLabel(/expir|expiry|exp/i, { exact: false });
          if (await expiryByLabel.isVisible({ timeout: 2000 })) {
            console.log('Found expiry date field using label locator');
            await expiryByLabel.click();
            await expiryByLabel.clear();
            await expiryByLabel.fill(expDate);
            await fixture.page.waitForTimeout(500);
            expiryFilled = true;
          }
        } catch (labelError) {
          console.log('Failed to find expiry date field by label:', labelError.message);
        }
      }
      
      // Try by role if other methods didn't work
      if (!expiryFilled) {
        try {
          const expiryInputByRole = fixture.page.getByRole('textbox', { 
            name: /expir|expiry|exp|date/i,
            exact: false
          });
          
          if (await expiryInputByRole.isVisible({ timeout: 2000 })) {
            console.log('Found expiry date field using role locator');
            await expiryInputByRole.click();
            await expiryInputByRole.clear();
            await expiryInputByRole.fill(expDate);
            await fixture.page.waitForTimeout(500);
            expiryFilled = true;
          }
        } catch (roleError) {
          console.log('Failed to find expiry date field by role:', roleError.message);
        }
      }
      
      // Fall back to CSS selectors if modern locators fail
      if (!expiryFilled) {
        // Try each selector in the array until one works
        for (const selector of this.expDateFieldSelector) {
          try {
            const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
              .catch(() => false);
            
            if (isVisible) {
              console.log(`Found expiry date field with selector: ${selector}`);
              await fixture.page.locator(selector).click();
              await fixture.page.locator(selector).clear();
              await fixture.page.locator(selector).fill(expDate);
              await fixture.page.waitForTimeout(500);
              expiryFilled = true;
              break;
            }
          } catch (error) {
            console.log(`Failed to interact with expiry selector ${selector}: ${error}`);
          }
        }
      }
      
      // If direct selectors failed, try iframes
      if (!expiryFilled) {
        const iframeCount = await fixture.page.locator('iframe').count();
        for (let i = 0; i < iframeCount; i++) {
          try {
            const frame = await fixture.page.frameLocator('iframe').nth(i);
            for (const selector of this.expDateFieldSelector) {
              const isVisible = await frame.locator(selector).isVisible()
                .catch(() => false);
              
              if (isVisible) {
                console.log(`Found expiry field in iframe ${i} with selector: ${selector}`);
                await frame.locator(selector).click();
                await frame.locator(selector).fill(expDate);
                await fixture.page.waitForTimeout(500);
                expiryFilled = true;
                break;
              }
            }
            if (expiryFilled) break;
          } catch (error) {
            console.log(`Error working with iframe ${i} for expiry: ${error}`);
          }
        }
      }
      
      if (!expiryFilled) {
        console.warn('Could not fill expiration date field');
      }
      
      // 3. Finally fill the CVV field
      console.log('Attempting to fill CVV...');
      let cvvFilled = false;
      
      // Try with modern Playwright locators first
      try {
        // Try using getByPlaceholder
        const cvvByPlaceholder = fixture.page.getByPlaceholder('CVC', { exact: true });
        if (await cvvByPlaceholder.isVisible({ timeout: 3000 })) {
          console.log('Found CVV field using placeholder locator');
          await cvvByPlaceholder.click();
          await cvvByPlaceholder.clear();
          await cvvByPlaceholder.fill(cvv);
          await fixture.page.waitForTimeout(500);
          cvvFilled = true;
        }
      } catch (placeholderError) {
        console.log('Failed to find CVV field by placeholder:', placeholderError.message);
      }
      
      // Try by label if placeholder didn't work
      if (!cvvFilled) {
        try {
          // Look for common label texts used for CVV/CVC fields
          const cvvByLabel = fixture.page.getByLabel(/cvc|cvv|security code|verification/i, { exact: false });
          if (await cvvByLabel.isVisible({ timeout: 2000 })) {
            console.log('Found CVV field using label locator');
            await cvvByLabel.click();
            await cvvByLabel.clear();
            await cvvByLabel.fill(cvv);
            await fixture.page.waitForTimeout(500);
            cvvFilled = true;
          }
        } catch (labelError) {
          console.log('Failed to find CVV field by label:', labelError.message);
        }
      }
      
      // Try by role if other methods didn't work
      if (!cvvFilled) {
        try {
          const cvvInputByRole = fixture.page.getByRole('textbox', { 
            name: /cvc|cvv|security|verification/i,
            exact: false
          });
          
          if (await cvvInputByRole.isVisible({ timeout: 2000 })) {
            console.log('Found CVV field using role locator');
            await cvvInputByRole.click();
            await cvvInputByRole.clear();
            await cvvInputByRole.fill(cvv);
            await fixture.page.waitForTimeout(500);
            cvvFilled = true;
          }
        } catch (roleError) {
          console.log('Failed to find CVV field by role:', roleError.message);
        }
      }
      
      // Fall back to CSS selectors if modern locators fail
      if (!cvvFilled) {
        // Try each selector in the array until one works
        for (const selector of this.cvvFieldSelector) {
          try {
            const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
              .catch(() => false);
            
            if (isVisible) {
              console.log(`Found CVV field with selector: ${selector}`);
              await fixture.page.locator(selector).click();
              await fixture.page.locator(selector).clear();
              await fixture.page.locator(selector).fill(cvv);
              await fixture.page.waitForTimeout(500);
              cvvFilled = true;
              break;
            }
          } catch (error) {
            console.log(`Failed to interact with CVV selector ${selector}: ${error}`);
          }
        }
      }
      
      // If direct selectors failed, try iframes
      if (!cvvFilled) {
        const iframeCount = await fixture.page.locator('iframe').count();
        for (let i = 0; i < iframeCount; i++) {
          try {
            const frame = await fixture.page.frameLocator('iframe').nth(i);
            for (const selector of this.cvvFieldSelector) {
              const isVisible = await frame.locator(selector).isVisible()
                .catch(() => false);
              
              if (isVisible) {
                console.log(`Found CVV field in iframe ${i} with selector: ${selector}`);
                await frame.locator(selector).click();
                await frame.locator(selector).fill(cvv);
                await fixture.page.waitForTimeout(500);
                cvvFilled = true;
                break;
              }
            }
            if (cvvFilled) break;
          } catch (error) {
            console.log(`Error working with iframe ${i} for CVV: ${error}`);
          }
        }
      }
      
      if (!cvvFilled) {
        console.warn('Could not fill CVV field');
      }
      
      // Verify if at least the credit card number was filled
      if (cardNumberFilled) {
        console.log('Credit card information has been filled successfully');
        return true;
      } else {
        console.error('Failed to fill mandatory credit card information');
        return false;
      }    } catch (error) {
      console.error('Error filling credit card information:', error);
      return false;
    }
  }
  
  /**
   * Gets the iframe containing credit card fields if present
   * @returns {Promise<any>} The frame containing credit card fields, or null if not found
   */
  private static async getPaymentIframe(): Promise<any> {
    try {
      const iframeCount = await fixture.page.locator('iframe').count();
      console.log(`Looking for payment iframe among ${iframeCount} iframes`);
      
      for (let i = 0; i < iframeCount; i++) {
        const frame = fixture.page.frameLocator('iframe').nth(i);
        
        // Try to find a credit card field to identify the payment iframe
        for (const selector of [
          'input[name="cardnumber"]', 
          'input[autocomplete="cc-number"]',
          'input[placeholder="Card number"]'
        ]) {
          const hasField = await frame.locator(selector).count()
            .then(count => count > 0)
            .catch(() => false);
          
          if (hasField) {
            console.log(`Found payment iframe at index ${i}`);
            return frame;
          }
        }
      }
      
      console.log('No payment iframe found');
      return null;
    } catch (error) {
      console.error('Error finding payment iframe:', error);
      return null;
    }
  }
  
  /**
   * Clicks the terms checkbox and the Pay Now button
   * @returns {Promise<boolean>} True if successful, false otherwise
   */
  static async clickCheckboxAndPayNow(): Promise<boolean> {
    try {
      console.log('Attempting to click the terms checkbox and Pay Now button...');
      
      // Wait for the page to be stable
      await fixture.page.waitForTimeout(2000);
      
      // First, try to find and click the terms checkbox
      let isTermsCheckboxFound = false;
      
      try {
        // Try with modern Playwright locators first
        try {
          // Look for checkbox by its role with common label text
          const checkboxByRole = fixture.page.getByRole('checkbox', {
            name: /terms|conditions|agree|accept/i, 
            exact: false
          });
          
          if (await checkboxByRole.isVisible({ timeout: 3000 })) {
            console.log('Found terms checkbox using role locator');
            await checkboxByRole.check();
            isTermsCheckboxFound = true;
          }
        } catch (roleError) {
          console.log('Failed to find checkbox by role:', roleError.message);
        }
        
        // Try by label if role didn't work
        if (!isTermsCheckboxFound) {
          try {
            const checkboxByLabel = fixture.page.getByLabel(/terms|conditions|agree|accept/i, { exact: false });
            
            if (await checkboxByLabel.isVisible({ timeout: 2000 })) {
              console.log('Found terms checkbox using label locator');
              await checkboxByLabel.check();
              isTermsCheckboxFound = true;
            }
          } catch (labelError) {
            console.log('Failed to find checkbox by label:', labelError.message);
          }
        }
        
        // Fall back to direct selector if modern locators fail
        if (!isTermsCheckboxFound) {
          const isCheckboxVisible = await fixture.page.isVisible(this.termsCheckboxSelector, { timeout: 3000 })
            .catch(() => false);
          
          if (isCheckboxVisible) {
            console.log('Found terms checkbox using direct selector');
            await fixture.page.check(this.termsCheckboxSelector);
            isTermsCheckboxFound = true;
          } else {
            // Try alternative approaches to find the checkbox
            console.log('Direct selector for checkbox failed, trying alternative approaches');
            
            // Try to find by checking all checkboxes on the page
            const checkboxCount = await fixture.page.locator('input[type="checkbox"]').count();
            console.log(`Found ${checkboxCount} checkboxes on the page`);
            
            if (checkboxCount > 0) {
              // If there's only one checkbox, use it
              if (checkboxCount === 1) {
                await fixture.page.locator('input[type="checkbox"]').first().check();
                isTermsCheckboxFound = true;
                console.log('Checked the only checkbox on the page');
              } else {
                // If there are multiple checkboxes, look for one near terms text
                const termsCheckbox = await fixture.page.locator('input[type="checkbox"]:near(:text("terms"), :text("Terms"), :text("conditions"), :text("Conditions"), :text("agree"))').first();
                const termsCheckboxCount = await termsCheckbox.count();
                
                if (termsCheckboxCount > 0) {
                  await termsCheckbox.check();
                  isTermsCheckboxFound = true;
                  console.log('Checked checkbox near terms text');
                } else {
                  // Last resort: try to check all checkboxes
                  console.log('Checking all checkboxes as a last resort');
                  for (let i = 0; i < checkboxCount; i++) {
                    await fixture.page.locator('input[type="checkbox"]').nth(i).check().catch(() => {});
                  }
                  isTermsCheckboxFound = true;
                }
              }
            }
          }
        }
        
        // If still not found, try JavaScript approach
        if (!isTermsCheckboxFound) {
          const jsResult = await fixture.page.evaluate(() => {
            const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));
            if (checkboxes.length > 0) {
              for (const checkbox of checkboxes) {
                // @ts-ignore
                checkbox.checked = true;
              }
              return true;
            }
            return false;
          });
          
          isTermsCheckboxFound = jsResult;
          console.log(`JavaScript checkbox selection result: ${jsResult}`);
        }
      } catch (error) {
        console.error('Error finding or clicking the terms checkbox:', error);
      }
      
      if (!isTermsCheckboxFound) {
        console.warn('Could not find or click any terms checkbox. Attempting to proceed anyway.');
      }
      
      // Now, try to click the Pay Now button
      console.log('Looking for Pay Now button...');
      let payNowButtonClicked = false;
      
      try {
        // Try with modern Playwright locators first
        try {
          // Try to find the button by role and name
          const payNowButton = fixture.page.getByRole('button', { 
            name: /pay now|pay|complete purchase|submit payment/i,
            exact: false
          });
          
          if (await payNowButton.isVisible({ timeout: 3000 })) {
            console.log('Found Pay Now button using role locator');
            await payNowButton.click();
            payNowButtonClicked = true;
          }
        } catch (roleError) {
          console.log('Failed to find Pay Now button by role:', roleError.message);
        }
        
        // Try by text if role didn't work
        if (!payNowButtonClicked) {
          try {
            const payNowByText = fixture.page.getByText(/pay now|pay|complete payment/i, { exact: false });
            
            if (await payNowByText.isVisible({ timeout: 2000 })) {
              console.log('Found Pay Now button using text locator');
              await payNowByText.click();
              payNowButtonClicked = true;
            }
          } catch (textError) {
            console.log('Failed to find Pay Now button by text:', textError.message);
          }
        }
        
        // Fall back to direct selectors if modern locators fail
        if (!payNowButtonClicked) {
          // Try the direct selector first
          const isPayNowButtonVisible = await fixture.page.isVisible(this.payNowButtonSelector, { timeout: 3000 })
            .catch(() => false);
          
          if (isPayNowButtonVisible) {
            console.log('Found Pay Now button using direct selector');
            await fixture.page.click(this.payNowButtonSelector);
            payNowButtonClicked = true;
          } else {
            // Try the alternative selector
            const isAlternativeButtonVisible = await fixture.page.isVisible(this.alternativePayNowButtonSelector, { timeout: 3000 })
              .catch(() => false);
            
            if (isAlternativeButtonVisible) {
              console.log('Found Pay Now button using alternative selector');
              await fixture.page.click(this.alternativePayNowButtonSelector);
              payNowButtonClicked = true;
            } else {
              // Try various text-based selectors
              const textSelectors = [
                'button:has-text("Pay now")',
                'button:has-text("Pay Now")',
                'button:has-text("PAY NOW")',
                'button:has-text("pay now")',
                'button[style*="linear-gradient"]'
              ];
              
              for (const selector of textSelectors) {
                const isButtonVisible = await fixture.page.isVisible(selector, { timeout: 2000 })
                  .catch(() => false);
                
                if (isButtonVisible) {
                  console.log(`Found Pay Now button using selector: ${selector}`);
                  await fixture.page.click(selector);
                  payNowButtonClicked = true;
                  break;
                }
              }
              
              // If still not found, try JavaScript approach
              if (!payNowButtonClicked) {
                const jsResult = await fixture.page.evaluate(() => {
                  // First, try to find by exact text match
                  const buttons = Array.from(document.querySelectorAll('button'));
                  const payButton = buttons.find(button => 
                    button.textContent?.trim().toLowerCase() === 'pay now' ||
                    button.textContent?.trim().toLowerCase() === 'pay' ||
                    button.textContent?.includes('Pay now') ||
                    button.textContent?.includes('Pay Now') ||
                    (button.style.background && button.style.background.includes('linear-gradient') && 
                     button.style.borderRadius === '100px')
                  );
                  
                  if (payButton) {
                    payButton.click();
                    return true;
                  }
                  return false;
                });
                
                payNowButtonClicked = jsResult;
                console.log(`JavaScript button click result: ${jsResult}`);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error finding or clicking the Pay Now button:', error);
      }
      
      if (!payNowButtonClicked) {
        console.error('Could not find or click the Pay Now button');
        return false;
      }
      
      // Wait for navigation or page update after clicking Pay Now
      await fixture.page.waitForTimeout(3000);
      
      console.log('Successfully clicked checkbox and Pay Now button');
      return true;
    } catch (error) {
      console.error('Error in clickCheckboxAndPayNow method:', error);
      return false;
    }
  }
}
