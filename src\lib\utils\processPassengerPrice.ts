import { GroupedPassengerData, GroupKey, PassengerPriceWrapper, TravelerDisplayInfo } from "@/constants/models";

export const groupPassengerPrices = (
    passenger_prices: PassengerPriceWrapper[]
  ): GroupedPassengerData => {
    const groups: Record<GroupKey, PassengerPriceWrapper[]> = {
      adult: [],
      child: [],
      infant: [],
    };

    for (const { PassengerPrice } of passenger_prices) {
      const age = parseInt(PassengerPrice.Age, 10);
      let group: GroupKey = age >= 12 ? 'adult' : age >= 2 ? 'child' : 'infant';
      groups[group].push({ PassengerPrice });
    }
  
    const examplePerGroup: Partial<Record<GroupKey, TravelerDisplayInfo>> = {};
    const expandedTravelers: TravelerDisplayInfo[] = [];
    let totalAmount = 0;
  
    for (const group in groups) {
      const passengers = groups[group as GroupKey];
      if (passengers.length === 0) continue;
  
      // Get representative PassengerPrice
      const sample = passengers[0].PassengerPrice;
      const currency = sample.Currency;
      const amount = parseFloat(sample.Amount);
      const taxes = sample.TaxItemList?.[0]?.TaxItem?.reduce((sum, tax) => {
        return sum + parseFloat(tax.Amount);
      }, 0) ?? 0;
      const ticketPrice = amount - taxes;
  
      const displayInfo: TravelerDisplayInfo = {
        travelerType: group as GroupKey,
        amount,
        currency,
        taxes,
        ticketPrice,
      };
  
      examplePerGroup[group as GroupKey] = displayInfo;
  
      // Repeat info for all passengers in group
      for (let i = 0; i < passengers.length; i++) {
        expandedTravelers.push({ ...displayInfo });
        totalAmount += amount;
      }
    }

    const groupCounts: Record<GroupKey, number> = {
        adult: groups.adult.length,
        child: groups.child.length,
        infant: groups.infant.length,
    };
  
    return { examplePerGroup, expandedTravelers, totalAmount, groupCounts };
  };