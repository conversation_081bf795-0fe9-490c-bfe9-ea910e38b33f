import { Given, Then } from '@cucumber/cucumber';
import { BasePage } from '../../pages/BasePage';
import { Properties } from '../../properties/Properties';
import Assert from '../../utils/AssertionsHelper';

Given('The user accepts all cookies', async function () {
  const { ConsentHelper } = require('../../utils/ConsentHelper');
  await ConsentHelper.handleConsentDialog('accept');
});

Given('The user enters {string} in the chat', async function (query: string) {
  await BasePage.fillTextboxByName('Ask me anything...', query);
});

Given('The user solves the reCAPTCHA manually', async function () {
  // Wait for the reCAPTCHA iframe to disappear (manual intervention required)
  await this.page.waitForSelector('iframe[title="reCAPTCHA"]', { state: 'detached', timeout: 120000 });
});

Given('The user sends the chat message', async function () {
  await BasePage.waitAndClick('button[aria-label="Send"]');
});

Then('The user should see available flight options', async function () {
  // Check for the presence of 'Flight Options' text on the page
  await this.page.waitForSelector('text=Flight Options', { timeout: 15000 });
}); 