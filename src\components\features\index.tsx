"use client"

import Image from 'next/image';

export const Features = () => {
  const features = [
    {
      title: "AI-Powered Itinerary Planning",
      description: "Personalized travel suggestions in seconds with Shasa",
      icon: "images/AI-Power.png"
    },
    {
      title: "One-Click Booking",
      description: "Flights, hotels, transport & activities, all in one place",
      icon: "images/one-click.png"
    },
    {
      title: "Live Travel Updates",
      description: "Flight changes, weather updates, & real-time assistance",
      icon: "images/travel-update.png"
    },
    {
      title: "Smart Budgeting",
      description: "Stay on top of your travel costs effortlessly",
      icon: "images/smart.png"
    },
    {
        title: "Multi-Language Assistant",
        description: "Plan and navigate seamlessly in any language with Shasa",
        icon: "images/Multi-lang.png"
      },
      {
        title: "WhatsApp Notifications",
        description: "Get instant updates & trip reminders from Shasa",
        icon: "images/whatsup.png"
      },
      {
        title: "AR & VR Previews",
        description: "Explore destinations before you book",
        icon: "images/AR.png"
      },
      {
        title: "Complete Travel Suite",
        description: "Everything you need for perfect trips in one AI-powered platform",
        icon: "images/TravelSuite.png"
      }
  ]

  return (
    <section className="mt-4 md:mt-6 lg:mt-10 mb-20 relative">
      {/* 🌟 Marquee Text Effect */}
      <div className="overflow-hidden whitespace-nowrap">
        <div className="flex animate-marquee space-x-10">
          {[...Array(2)].map((_, index) => (
            <div key={index} className="flex items-center space-x-8">
              {/* Marquee Heading */}
              <h1
                className="font-bold uppercase text-[40px] leading-[50px] sm:text-[60px] sm:leading-[75px]
                  md:text-[80px] md:leading-[100px] lg:text-[114px] lg:leading-[138.85px] m-0"
                style={{
                  background:
                    "linear-gradient(180.97deg, #F2A1F2 -49.04%, #A195F9 -4.67%, #707FF5 39.7%, #4B4BC3 84.07%, #1E1E76 128.44%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Coming Soon!
              </h1>
              {/* Marquee Ellipse Icon */}
              <span className="inline-block relative w-[14px] h-[14px] sm:w-[20px] sm:h-[20px] md:w-[28px] md:h-[28px] mx-2 sm:mx-4 md:mx-6">
                <img
                  src="/images/Ellipse13.png"
                  alt="Ellipse Icon"
                  className="absolute top-0 left-0 w-full h-full m-0"
                />
              </span>
              {/* Repeat Marquee Heading */}
              <h1
                className="font-bold uppercase text-[40px] leading-[50px] sm:text-[60px] sm:leading-[75px]
                  md:text-[80px] md:leading-[100px] lg:text-[114px] lg:leading-[138.85px]"
                style={{
                  background:
                    "linear-gradient(180.97deg, #F2A1F2 -49.04%, #A195F9 -4.67%, #707FF5 39.7%, #4B4BC3 84.07%, #1E1E76 128.44%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Coming Soon!
              </h1>
              {/* Marquee Ellipse Icon */}
              <span className="inline-block relative w-[14px] h-[14px] sm:w-[20px] sm:h-[20px] md:w-[28px] md:h-[28px] mx-2 sm:mx-4 md:mx-6">
                <img
                  src="/images/Ellipse13.png"
                  alt="Ellipse Icon"
                  className="absolute top-0 left-0 w-full h-full m-0"
                />
              </span>
            </div>
          ))}
        </div>
      </div>
    
      {/* 🌟 Section Heading */}
      <div className="px-[10px] sm:px-[10px] md:px-[10px] ">
        <h2 className="font-proxima-nova font-bold text-[#1E1E76] text-center mt-6 md:mt-4 mb-7 md:mb-7
          text-[22px] leading-[40px]
          sm:text-[42px] sm:leading-[50px]
          md:text-[52px] md:leading-[63.34px]"
        >
          What to Expect from NxVoy?
        </h2>
    
        {/* 🌟 Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-2 justify-items-center">
          {features.map((feature, index) => (
            <div
              key={index}
              className="w-full max-w-[350px] h-[113px] sm:h-[138px] md:h-[138px] lg:h-[138px] rounded-[18px] p-6 transition-all duration-300 group relative"
              style={{
                background: 'white',
                borderRadius: '18px',
                border: '1.5px solid transparent',
                backgroundImage: 'linear-gradient(white, white), linear-gradient(180deg, #1E1E76 0%, #4B4BC3 25%, #707FF5 50%, #A195F9 75%, #F2A1F2 100%)',
                backgroundOrigin: 'border-box',
                backgroundClip: 'padding-box, border-box',
              }}
            >
              <div className="absolute inset-[1px] rounded-[17px] transition-colors duration-300 hover:bg-[#E6E3FF] p-4 flex flex-col items-center justify-center text-center">
                <div className="text-2xl pt-2">
                  <Image
                    src={feature.icon}
                    alt={feature.title}
                    width={0}
                    height={0}
                    className="h-[38px] w-auto object-contain transition-all duration-300 group-hover:brightness-0"
                    style={{ height: '38px', width: 'auto' }}
                    unoptimized
                  />
                </div>
                <h3 className="text-lg font-semibold text-[#1E1E76] transition-colors duration-300 group-hover:text-black"
                  style={{
                    fontFamily: 'Proxima Nova, sans-serif',
                    fontSize: '18px',
                    lineHeight: '24.36px',
                    letterSpacing: '0%',
                    fontWeight: 700
                  }}>
                  {feature.title}
                </h3>
                <p className="text-sm text-[#080236]"
                  style={{
                    fontFamily: 'Proxima Nova, sans-serif',
                    fontSize: '12px',
                    lineHeight: '21.92px',
                    letterSpacing: '0%',
                    fontWeight: 500
                  }}>
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
