import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface UserDetails {
  title: string;
  firstName: string;
  middleName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  nationality: string;
  email: string;
  phone: string | null;
  alternatePhone: string;
  profile_picture: string;
  last_login?: string;
  addresses: any[];
  paymentCards: any[];
  passengers: any[];
}

interface UserDetailsState {
  currentUserDetails: UserDetails | null;
}

const initialState: UserDetailsState = {
  currentUserDetails: null,
};

const userDetailsSlice = createSlice({
  name: "currentUserDetails",
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<UserDetails>) => {
      state.currentUserDetails = action.payload;
    },
    clearCurrentUser: (state) => {
      state.currentUserDetails = null;
    },
  },
});

export const { setCurrentUser, clearCurrentUser } = userDetailsSlice.actions;

export default userDetailsSlice.reducer;
