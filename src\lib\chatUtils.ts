import axios from "axios";
import { format } from "date-fns";
import { CHAT_CONSTANTS } from "@/constants/chat";
import { TripOptions } from "@/constants/flight";


export const wait = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const generateTimestamp = (): string => {
  const now = new Date();

  const pad = (n: number) => n.toString().padStart(2, "0");

  const year = now.getFullYear();
  const month = pad(now.getMonth() + 1); // Months are 0-based
  const day = pad(now.getDate());
  const hours = pad(now.getHours());
  const minutes = pad(now.getMinutes());
  const seconds = pad(now.getSeconds());
  const microseconds = (now.getMilliseconds() * 1000).toString().padStart(6, "0");

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}`;
};

export const formatMessageTime = (timestamp: string): string => {
  return format(new Date(timestamp), "hh:mma");
};

export const generateThreadId = async (token: string): Promise<string> => {
  const res = await axios.post(
    `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}${CHAT_CONSTANTS.API_ENDPOINTS.THREAD_GENERATE}`,
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      },
    }
  );

  return res?.data?.detail?.data?.thread_id || "";
};

export const generateThreadIdWithRetry = async (
  token: string,
  retries = CHAT_CONSTANTS.RETRY_CONFIG.MAX_RETRIES
): Promise<string> => {
  let attempt = 0;

  while (attempt < retries) {
    try {
      return await generateThreadId(token);
    } catch (err: any) {
      const message = err?.response?.data?.detail?.message;
      if (
        message?.includes("You've reached the maximum number of conversations (10) for a free account. Please upgrade your account for unlimited conversations")
      ) {
        throw new Error(`You've reached the maximum number of conversations (10) for a free account. Please upgrade your account for unlimited conversations`);
      }
      
      if (err.response?.status === 429) {
        const waitTime =
          CHAT_CONSTANTS.RETRY_CONFIG.BASE_WAIT_TIME * Math.pow(2, attempt);
        await wait(waitTime);
        attempt++;
      } else {
        throw err;
      }
    }
  }

  throw new Error("Failed to generate thread ID after retries");
};



export const updateMessageFeedback = async (
  feedback: string,
  messageId: string,
  threadId: string | null,
  token: string
): Promise<void> => {
  await axios.put(
    `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}${CHAT_CONSTANTS.API_ENDPOINTS.MESSAGE_FEEDBACK}`,
    {
      feedback,
      message_id: messageId,
      thread_id: threadId,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
      },
    }
  );
};

export const formatChatMessages = (rawMessages: RawMessage[]): ChatMessage[] => {
  const formatted: ChatMessage[] = rawMessages.map((msg) => ({
    sender: msg.role,
    text: msg.content || "",
    timestamp: msg.timestamp,
    messageId: msg.id,
  }));

  // Check if any message has flights in cards
  const flightMsg = rawMessages.find(
    (msg) =>
      msg.role === "ai" &&
      msg.card?.data?.flights &&
      Array.isArray(msg.card.data.flights) &&
      msg.card.data.flights.length > 0
  );

  if (flightMsg && flightMsg.card?.data?.flights) {
    formatted.push({
      sender: "ai",
      type: "flights",
      flights: flightMsg.card.data.flights,
      timestamp: flightMsg.timestamp,
      routingId: flightMsg.card.data.routing_id,
    });
  }

  return formatted;
};

export const createFlightSearchPayload = (
  chatResult: any,
  tripSummaryDetails: any
): FlightSearchPayload | null => {
  if (!chatResult) return null;

  const trip_type =
    chatResult?.output?._return &&
    Object.keys(chatResult.output._return).length === 0
      ? TripOptions[0]
      : TripOptions[1];

  let deptValue = chatResult?.output?.airport_data[chatResult?.input?.origin] || {};
  if (deptValue && Object.keys(deptValue).length > 0) {
    deptValue = `${deptValue?.airport_name}, ${deptValue?.city_name_original}, ${deptValue?.country_name}`;
  }

  let arrValue = chatResult?.output?.airport_data[chatResult?.input?.destination] || {};
  if (arrValue && Object.keys(arrValue).length > 0) {
    arrValue = `${arrValue?.airport_name}, ${arrValue?.city_name_original}, ${arrValue?.country_name}`;
  }

  const startDate = new Date(chatResult?.input?.departure_date) || new Date();
  const endDate = new Date(chatResult?.input?.return_date) || new Date();
  const flightData = chatResult?.output || {};

  return {
    flightSearch: {
      ...tripSummaryDetails?.flightSearch,
      ...tripSummaryDetails?.filterOptions,
      trip_type,
      departureLocation: chatResult?.input?.origin,
      departureValue: deptValue,
      destinationLocation: chatResult?.input?.destination,
      destinationValue: arrValue,
      dateRange: [
        {
          startDate: startDate.toISOString(),
          endDate:
            endDate instanceof Date && !isNaN(endDate.getTime())
              ? endDate.toISOString()
              : "",
        },
      ],
      travel_class: chatResult?.input?.travel_class,
      dept_airport_name: deptValue?.airport_name,
      arr_airport_name: arrValue?.airport_name,
      adults: chatResult?.input?.adults || 0,
      children: chatResult?.input?.children || 0,
      infants: chatResult?.input?.infants || 0,
    },
    flightSearchRespnse: flightData,
    sharedFlightResults: flightData,
  };
};