import React, { useState } from "react";
import { UserIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import ConfirmDeleteModal from "./ConfirmDeleteModal";
import { Address } from "./AddressDetails";

interface SavedAddressesProps {
  addresses: Address[];
  onAddNew: () => void;
  onEdit: (address: Address, idx: string) => void;
  onDelete: (idx: string) => void;
  status: string | null;
}

const SavedAddresses: React.FC<SavedAddressesProps> = ({
  addresses,
  onAddNew,
  onEdit,
  onDelete,
  status,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteIdx, setDeleteIdx] = useState<string | null>(null);

  const handleDeleteClick = (idx: string) => {
    setDeleteIdx(idx);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteIdx !== null) {
      onDelete(deleteIdx);
    }
    setModalOpen(false);
    setDeleteIdx(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteIdx(null);
  };

  if (!addresses || addresses.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 min-h-screen">
        <div className="text-xl font-semibold text-center text-neutral-dark w-full">
          No address to show
        </div>
        <div className="text-xl font-bold text-brand-black text-center">
          You Can Create Address Here.
        </div>
        <Button
          icon="edit"
          className="text-sm text-brand-white bg-brand px-6 py-4 rounded-[8px] hover:bg-brand hover:text-brand-white"
          onClick={onAddNew}
        >
          Add New Address
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full p-2 md:p-6 space-y-2 md:space-y-2">
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are Sure you want to delete?"
      />
      <div className="flex flex-row justify-between items-center gap-2 mb-4 md:mb-8">
        <h2 className="text-sm md:text-3xl font-bold text-brand-black truncate max-w-[60%]">
          Saved Address
        </h2>
        <Button
          icon="edit"
          className="text-xs md:text-sm text-brand-white bg-brand hover:brand hover:text-brand-white hover:bg-brand px-3 md:px-6 py-1 md:py-4 rounded-[8px] whitespace-nowrap flex-shrink-0"
          onClick={onAddNew}
          disabled={addresses.length >= 3}
        >
          Add New Address
        </Button>
      </div>

      {/* Grid with up to 3 columns */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
        {addresses.map((address, i) => (
          <div
            key={i}
            className="bg-brand-white border-[1px] border-neutral rounded-2xl p-4 md:p-6 w-full text-sm md:text-xl"
          >
            <div className="flex items-center gap-2 mb-2 md:mb-4">
              <UserIcon
                fill="#999999"
                color="#999999"
                className="w-5 h-5 md:w-6 md:h-6"
              />
              <span className="font-bold text-base md:text-lg text-neutral-dark truncate">
                {address.memorableName}
              </span>
            </div>
            <div className="text-brand-grey mb-4 md:mb-6 text-sm md:text-base">
              <div className="truncate">{address.userName}</div>
              <div className="truncate">{address.street}</div>
              <div className="truncate">{address.city}</div>
              <div className="truncate">{address.state}</div>
              <div className="truncate">{address.country}</div>
              <div className="truncate">{address.postalCode}</div>
            </div>
            <div className="flex gap-2 md:gap-4 mt-2 md:mt-4">
              <button
                className="border border-brand text-brand bg-white px-2 md:px-3 py-1 text-xs md:text-sm rounded-[8px] font-semibold"
                onClick={() => handleDeleteClick(address.address_id)}
              >
                Delete
              </button>
              <button
                className="border border-brand text-brand-white bg-brand px-2 md:px-3 py-1 text-xs md:text-sm rounded-[8px] font-semibold"
                onClick={() => onEdit(address, address.address_id)}
              >
                Edit
              </button>
            </div>
          </div>
        ))}
      </div>

      {status == "success" ? (
        <div className="mb-6 flex justify-center">
          <span className="text-green-600">Changes Updated Successfully</span>
        </div>
      ) : (
        <div className="mb-6 h-6"></div>
      )}

      <p className="text-sm text-neutral-dark italic ml-1">
        * You can store a maximum of 3 addresses.
      </p>
    </div>
  );
};

export default SavedAddresses;
