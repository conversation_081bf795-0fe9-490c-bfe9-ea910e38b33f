import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { LocaleHelper } from '../properties/LocaleHelper';
import { Properties } from '../properties/Properties';
import { ConfigHandler } from '../properties/ConfigHandler';
import { getCucumberStepsArgument } from '../utils/CucumberTsNodeConfig';

async function runScript(command: string) {
    try {
        console.log(`Running command: ${command}`);
        execSync(command, { stdio: 'inherit' });
        return true;
    } catch (error) {
        console.error(`Command failed: ${command}`);
        console.error(error);
        return false;
    }
}

// Ensure a directory exists only when needed
function ensureDirectoryExists(dirPath: string, createNow: boolean = false) {
    // Only create the directory if explicitly requested
    if (createNow && !fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
    return dirPath;
}

export async function runFeeVerificationTest() {
    let hasError = false;
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const screenshotsDir = path.join('test-reports', 'screenshots', timestamp);
    
    // Don't create the screenshots directory yet - it will be created only when needed
    // Just store the path for later use
    const screenshotsDirPath = ensureDirectoryExists(screenshotsDir, false);
    
    try {
        ConfigHandler.loadConfig();
        console.log('Starting Fee Verification Tests...');
        console.log(`Environment: ${Properties.ENVIRONMENT}, Locale: ${LocaleHelper.LOCALE}`);
        
        // Set environment variables for screenshot path
        process.env.SCREENSHOT_DIR = screenshotsDir;
        
        // Set CI flag to ensure headless mode in GitHub Actions
        if (process.env.GITHUB_ACTIONS === 'true') {
            process.env.CI = 'true';
        }
        
        // Use the getCucumberStepsArgument function to get the proper steps argument
        const stepsArg = getCucumberStepsArgument();
          // Run the Cucumber tests specifically for FeeVerification.feature
        const success = await runScript(`cucumber-js src/resources/features/ui/FeeVerification.feature --require-module ts-node/register ${stepsArg} --format progress-bar --format json:./test-reports/cucumber-json-reports/report.json --format junit:./test-reports/cucumber-json-reports/report.xml --format @cucumber/pretty-formatter --tags "@filghtsverify and @ui and not (@ignore or @bug or @wip)" env_${Properties.ENVIRONMENT} locale_${LocaleHelper.LOCALE}`);
        if (!success) {
            hasError = true;
            console.error('❌ Fee verification tests failed');
        } else {
            console.log('✅ Fee verification tests completed successfully');
        }
    } catch (error) {
        hasError = true;
        console.error('❌ An unexpected error occurred during test execution:', error);
    } finally {
        console.log('Generating HTML report...');
        await runScript(`npx ts-node src/test/reporting/HtmlReporter.ts env_${Properties.ENVIRONMENT} locale_${LocaleHelper.LOCALE}`);
        
        console.log(`Test screenshots available at: ${screenshotsDir}`);
        
        if (hasError) {
            console.log('❌ Tests completed with errors');
            process.exit(1);
        } else {
            console.log('✅ All tests passed successfully');
        }
    }
}

(async () => { await runFeeVerificationTest(); })();
