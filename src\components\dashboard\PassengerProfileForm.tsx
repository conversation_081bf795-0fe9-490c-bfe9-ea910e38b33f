import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { User } from "@/constants/user";
import InputField from "@/components/input/InputField";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/input/Select";

interface PassengerProfileFormProps {
  user: User | null;
  onUpdate: () => void;
  onCancel: () => void;
}

const PassengerProfileForm: React.FC<PassengerProfileFormProps> = ({ user, onUpdate, onCancel }) => {
  // State for form fields (for demonstration, not fully implemented)
  const [title, setTitle] = useState("Mr.");
  const [gender, setGender] = useState("");
  const [travelerType, setTravelerType] = useState("");
  const [preferredSeat, setPreferredSeat] = useState("");
  const [mealPreference, setMealPreference] = useState("");
  const [cabinClass, setCabinClass] = useState("");

  // Document form toggles and values
  const [showFrequentFlyerForm, setShowFrequentFlyerForm] = useState(false);
  const [showPassportForm, setShowPassportForm] = useState(false);
  const [showAadharForm, setShowAadharForm] = useState(false);
  const [frequentFlyerNumber, setFrequentFlyerNumber] = useState("");
  const [passportNumber, setPassportNumber] = useState("");
  const [passportExpiry, setPassportExpiry] = useState("");
  const [passportCountry, setPassportCountry] = useState("");
  const [aadharNumber, setAadharNumber] = useState("");

  // Handlers for Frequent Flyer
  const handleFrequentFlyerSave = () => {
    setShowFrequentFlyerForm(false);
    // Save logic here
  };
  const handleFrequentFlyerCancel = () => {
    setShowFrequentFlyerForm(false);
    setFrequentFlyerNumber("");
  };

  // Handlers for Passport
  const handlePassportSave = () => {
    setShowPassportForm(false);
    // Save logic here
  };
  const handlePassportCancel = () => {
    setShowPassportForm(false);
    setPassportNumber("");
    setPassportExpiry("");
    setPassportCountry("");
  };

  // Handlers for Aadhar
  const handleAadharSave = () => {
    setShowAadharForm(false);
    // Save logic here
  };
  const handleAadharCancel = () => {
    setShowAadharForm(false);
    setAadharNumber("");
  };

  const handleUpdate = () => {
    onUpdate();
  }

  const handleCancel = () => {
    onCancel();
  }

  return (
    <div className="p-4 md:p-6 space-y-6 md:space-y-10">
      {/* Add New Passenger Profiles */}
      <div>
        <h2 className="text-xl md:text-3xl font-semibold text-[#4B4BC3] mb-4 md:mb-6">Add New Passenger Profiles</h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-6 mb-4">
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Title</label>
              <Select value={title} onValueChange={setTitle}>
                <SelectTrigger>
                  <SelectValue placeholder="Title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Mr.">Mr.</SelectItem>
                  <SelectItem value="Ms.">Ms.</SelectItem>
                  <SelectItem value="Mrs.">Mrs.</SelectItem>
                  <SelectItem value="Dr.">Dr.</SelectItem>
                </SelectContent>
              </Select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
          <InputField label="First Name" placeholder="User First Name" />
          <InputField label="Middle Name" placeholder="User Middle Name" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
          <InputField label="Last Name" placeholder="User Last Name" />
          <InputField label="Nationality" placeholder="India" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 md:gap-6 mb-4 items-start">
          <div>
            <label className="font-semibold text-[#1E1E76] block">Gender</label>
              <Select value={gender} onValueChange={setGender}>
                <SelectTrigger>
                  <SelectValue placeholder="Gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Male">Male</SelectItem>
                  <SelectItem value="Female">Female</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
          </div>
          <InputField label="Date of Birth" placeholder="12/12/1996" type="date" />
        </div>
      </div>

      {/* Contact Info */}
      <div>
        <h2 className="text-lg md:text-3xl font-semibold text-[#4B4BC3] mb-4">Contact Info</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          <InputField label="Primary Email" placeholder="User Email id" />
          <InputField label="Mobile Number" placeholder="+91 12345 67890" />
        </div>
      </div>

      {/* Travel Preferences */}
      <div>
        <h2 className="text-xl md:text-3xl font-semibold text-[#4B4BC3] mb-4 md:mb-6">Travel preferences</h2>
        <div className="space-y-4 md:space-y-6">
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Traveler Type</label>
              <Select value={travelerType} onValueChange={setTravelerType}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose Traveler Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Adult">Adult</SelectItem>
                  <SelectItem value="Child">Child</SelectItem>
                  <SelectItem value="Infant">Infant</SelectItem>
                </SelectContent>
              </Select>
          </div>
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Preferred Seat</label>
              <Select value={preferredSeat} onValueChange={setPreferredSeat}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose Your Preferred Seat" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Aisle">Aisle</SelectItem>
                  <SelectItem value="Window">Window</SelectItem>
                  <SelectItem value="Middle">Middle</SelectItem>
                </SelectContent>
              </Select>
          </div>
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Meal Preference</label>
              <Select value={mealPreference} onValueChange={setMealPreference}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose Your Meal Preference" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Veg">Veg</SelectItem>
                  <SelectItem value="Non-Veg">Non-Veg</SelectItem>
                  <SelectItem value="Vegan">No preference</SelectItem>
                </SelectContent>
              </Select>
          </div>
          {showFrequentFlyerForm ? 
          (
            <>
              <div className="flex-1">
                <InputField 
                  label="Frequent Flyer Program & Number" 
                  placeholder="Enter Your Frequent Flyer Program & Number"
                  value={frequentFlyerNumber}
                  onChange={e => setFrequentFlyerNumber(e.target.value)}
                />
              </div>
              <div className="flex flex-col md:flex-row gap-4 mb-4 w-full mt-4">
                <Button
                  variant="outline"
                  className="rounded-full border-2 border-[#4B4BC3] bg-transparent text-[#1E1E76] px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handleFrequentFlyerCancel}
                >
                  Cancel
                </Button>
                <Button
                  className="rounded-full bg-[#4B4BC3] text-white px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handleFrequentFlyerSave}
                >
                  Save
                </Button>
              </div>
            </>
          ) : 
          (
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="flex-1">
                <InputField label="Frequent Flyer Program & Number" placeholder="Enter Your Frequent Flyer Program & Number" />
              </div>
              <div className="md:mt-6">
                <Button className="rounded-full bg-[#4B4BC3] text-white px-6 w-full md:w-auto" onClick={() => setShowFrequentFlyerForm(true)}>+ Add</Button>
              </div>
            </div>
          )}
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Default Cabin Class</label>
              <Select value={cabinClass} onValueChange={setCabinClass}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose Your Default Cabin Class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Economy">Economy</SelectItem>
                  <SelectItem value="Premium Economy">Premium Economy</SelectItem>
                  <SelectItem value="Business">Business</SelectItem>
                  <SelectItem value="First">First</SelectItem>
                </SelectContent>
              </Select>
          </div>
        </div>
      </div>

      {/* Documents */}
      <div>
        <h2 className="text-xl md:text-3xl font-semibold text-[#4B4BC3] mb-4 md:mb-6">Documents</h2>
        {/* Passport Section */}
        <div className="mb-6 md:mb-8">
          {showPassportForm ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-6 mb-4">
                <div>
                  <InputField
                    label=""
                    placeholder="Enter Your Passport Number"
                    value={passportNumber}
                    onChange={e => setPassportNumber(e.target.value)}
                  />
                </div>
                <div>
                  <InputField
                    label=""
                    placeholder="Passport Expiry Date"
                    value={passportExpiry}
                    onChange={e => setPassportExpiry(e.target.value)}
                  />
                </div>
                <div>
                  <Select value={passportCountry} onValueChange={setPassportCountry}>
                    <SelectTrigger>
                      <SelectValue placeholder="Country Name" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="India">India</SelectItem>
                      <SelectItem value="USA">USA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <Button
                  variant="outline"
                  className="rounded-full border-2 border-[#4B4BC3] bg-transparent text-[#1E1E76] px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handlePassportCancel}
                >
                  Cancel
                </Button>
                <Button
                  className="rounded-full bg-[#4B4BC3] text-white px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handlePassportSave}
                >
                  Save
                </Button>
              </div>
            </>
          ) : (
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="flex-1">
                <InputField label="Passport" placeholder="User Passport Details" />
              </div>
              <div className="md:mt-6">
                <Button className="rounded-full bg-[#4B4BC3] text-white px-6 w-full md:w-auto" onClick={() => setShowPassportForm(true)}>+ Add</Button>
              </div>
            </div>
          )}
        </div>

        {/* Aadhar Section */}
        <div>
          {showAadharForm ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4">
                <InputField
                  label="Aadhar / PAN"
                  placeholder="Enter Your Aadhar / PAN Number"
                  value={aadharNumber}
                  onChange={e => setAadharNumber(e.target.value)}
                />
              </div>
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <Button
                  variant="outline"
                  className="rounded-full border-2 border-[#4B4BC3] bg-transparent text-[#1E1E76] px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handleAadharCancel}
                >
                  Cancel
                </Button>
                <Button
                  className="rounded-full bg-[#4B4BC3] text-white px-8 py-2 font-semibold w-full md:w-auto"
                  onClick={handleAadharSave}
                >
                  Save
                </Button>
              </div>
            </>
          ) : (
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="flex-1">
                <InputField label="Aadhar / PAN" placeholder="User Aadhar / PAN Details" />
              </div>
              <div className="md:mt-6">
                <Button className="rounded-full bg-[#4B4BC3] text-white px-6 w-full md:w-auto" onClick={() => setShowAadharForm(true)}>+ Add</Button>
              </div>
            </div>
          )}
        </div>
        <p className="text-green-600 text-sm w-full p-2 text-center">Details Added Successfully</p>
        
        <div className="flex w-full justify-center mt-10 gap-4">
          <Button
            variant="outline"
            className="rounded-full bg-[#4B4BC3] text-white px-8 py-2 font-semibold w-full md:w-auto"
            onClick={handleUpdate}>
              Update
          </Button>
          <Button
            variant="outline"
            className="px-8 py-2 rounded-full bg-[#E6E3FF] text-[#4B4BC3] text-lg font-semibold border border-[#4B4BC3]"
            onClick={handleCancel}>
              Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PassengerProfileForm;
