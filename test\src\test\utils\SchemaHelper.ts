import Ajv from "ajv";
import fse from 'fs-extra';

export class SchemaHelper {
    public static async validateSchema(schema: any, data: string): Promise<void> {
        const ajv = new Ajv();
        const validate = ajv.compile(schema);
        const valid = validate(data);
        if (!valid) {
            throw new Error(`Schema validation failed: ${JSON.stringify(validate.errors, null, 2)}\nActual value: ${data}`);
        }
    }

    static getSchema(file: string) {
        const schemaLocation: string = `src/resources/data/schemas/${file}.json`;
        return fse.readJson(schemaLocation);
    }
}