type Segment = {
    operator?: string;
    travel_class?: string;
    operator_code?: string;
    flight_number?: string;
    aircraft_name?: string;
};

function capitalizeWords(str?: string): string {
    if (typeof str !== 'string') return '';
    return str
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');
}

export function getFlightDescription(segment: Segment): string {
    const supplier = capitalizeWords(segment.operator);
    let travelClass
    const travelClasses = 
    typeof segment.travel_class === 'object' && segment.travel_class !== null && 'class' in segment.travel_class
      ? capitalizeWords((segment.travel_class as { class: string }).class)
      : '';

      if(travelClasses === "EconomyWithRestrictions"){
        travelClass = "Economy With Restrictions"
      } else if(travelClasses === "EconomyWithoutRestrictions"){
        travelClass = "Economy Without Restrictions"
      } else if(travelClasses === "EconomyPremium"){
        travelClass = "Economy Premium"
      } else if(travelClasses === "Buisness"){
        travelClass = "Buisness Class"
      } else if(travelClasses === "First"){
        travelClass = "First Class"
      }
    const flightCode = segment.operator_code?.toUpperCase();
    const flightNumber = segment.flight_number;

    const aircraft = segment.aircraft_name ? capitalizeWords(segment.aircraft_name) : "";
  
    let parts = [
      supplier,
      travelClass,
      flightCode && flightNumber ? `${flightCode} ${flightNumber}` : null,
      aircraft || null,
    ];
  
    return parts.filter(Boolean).join(" · ");
}