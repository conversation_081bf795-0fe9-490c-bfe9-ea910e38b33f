import { motion } from 'framer-motion';
import React from 'react'

interface CardProps {
    imageUrl: string;
    title: string;
    desc: string;
    rotate?: number;
    animateState?: {
        x?: number;
        y?: number;
        rotate?: number;
        rotateY?: number;
        opacity?: number;
        scale?: number;
    }
}

const TravellerCard = ({imageUrl, title, desc, animateState, rotate = 0}: CardProps) => {
  return (
    <motion.div 
        initial={{ rotate: 0, x: 0, y: 0, scale: 0.8, opacity: 0 }}
        transition={{ duration: 1, ease: 'easeInOut'}}
        animate={{ 
            ...(animateState || {}),
            rotate: typeof rotate === 'number' ? rotate : animateState?.rotate ?? 0,
            rotateY: animateState?.rotateY ?? 0,
        }}
        className='sm:w-[160px] sm:h-[192px] lg:w-[262px] lg:h-[240.47px] rounded-lg
        flex flex-col overflow-hidden [transform-style:preserve-3d]'
        style={{
            border: '1px solid transparent',
            backgroundImage: 'linear-gradient(white, white), linear-gradient(315deg, #1E1E76, #4B4BC3, #707FF5, #A195F9, #F2A1F2)',
            backgroundOrigin: 'border-box',
            backgroundClip: 'content-box, border-box'
        }}>
        <img src={`https://storage.googleapis.com/nxvoytrips-img/Homepage/plan-my-trip/${imageUrl}.png`}
            className='w-full object-cover' alt='weekend getaway'/>
        <h2 className='font-proxima-nova font-bold
            sm:mt-2 lg:mt-5 text-lucky-blue sm:text-[16px] text-center p-0
            md:leading-none'>{title}</h2> 
        <span className='font-proxima-nova font-medium text-[#080236] sm:text-[14px]
        sm:px-2 sm:pb-2 text-center md:leading-none md:pt-1'>{desc}</span>                        
    </motion.div>
  )
}

export default TravellerCard