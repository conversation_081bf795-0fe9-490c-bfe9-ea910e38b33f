import React from 'react';
import dynamic from 'next/dynamic';
import animation from '../../../public/PaymentConfirmation.json'

const Lottie = dynamic(() => import("react-lottie"), { ssr: false });

const defaultOptions = {
  loop: true,
  autoplay: true,
  animationData: animation,
  rendererSettings: {
    preserveAspectRatio: "xMidYMid slice",
  },
};

interface LoadingOverlayProps {
  loadingText?: string;
}

const LoadingOverlay = ({ loadingText }: LoadingOverlayProps) => {
  return (
    <div className="mt-24 inset-0 z-50  flex flex-col font-proxima-nova justify-center items-center" data-testid="loading-overlay">
      <Lottie speed={0.5} options={defaultOptions} width={350} height={300} />
      <div className='flex flex-col gap-4 justify-center items-center bg-white'>
        <div className="text-2xl text-[#4B4BC3] font-medium mt-6 text-center">
          {loadingText ? loadingText : 'Putting the final touches on your flight Itinerary, Almost there'}
        </div>
        <img className='w-40 h-14' src='https://storage.googleapis.com/nxvoytrips-img/TripSummary/TravelFusionLogo.png' alt='' />
      </div>
    </div>
  );
};

export default LoadingOverlay;
