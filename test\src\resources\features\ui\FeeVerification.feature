
@filghtsverify @ui
Feature: Flight Booking End-to-End with Luggage Fee Verification

Scenario: Book a return flight for 1 adult, 1 child, and 1 infant and verify all charges

Given The user starts from the "ui.nxvoy.appUrl" page
And The user clicks on the Sign in button
Given The user types the "nxvoy.users.standard" username on the login page
And The user types the "nxvoy.users.standard" password on the login page
When The user clicks on the login button
Then Assert user is logged in home page
Given User is on the flights search "ui.nxvoy.filghtspage" page directly
When user select the "ui.nxvoy.flight_search_testdata.validinput.triptype" trip type
And user select "ui.nxvoy.flight_search_testdata.validinput.travelers" passengers
And user select the class type "ui.nxvoy.flight_search_testdata.validinput.classtype"

# When I perform a flight search
# And I select a flight from the search results
# Then I should be taken to the flight summary page
# When I select luggage options for all passengers
# Then I should see the correct luggage fees, service fees, taxes, and total price displayed on the summary (right-hand side)
# When I proceed to the booking confirmation page
# Then I should see all the details (flight, passenger, luggage, service fees, and total) accurately reflected