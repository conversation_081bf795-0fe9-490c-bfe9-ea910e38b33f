import { createContext, Dispatch, SetStateAction, useContext } from "react";

interface AccordionContextType {
    selected: string | null;
    setSelected: Dispatch<SetStateAction<string | null>>;
}

export const AccordionContext = createContext<AccordionContextType | undefined>(undefined);

export const useAccordionContext = () => {
    const context = useContext(AccordionContext);
    if(!context) {
        throw new Error("useAccordionContext must be used within an AccordionProvider");
    }
    return context;
}