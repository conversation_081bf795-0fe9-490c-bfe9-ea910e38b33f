@api
@sample
@regression
Feature: API: Sample tests (delete this once you have tests up and running)

	Background:
		Given I set the base URL to the "api.sample.baseUrl" URL

	Scenario: API: Get users - GET
		Given The request query parameters are the following:
			| key  | value |
			| page | 2     |
		When I make a "GET" request to the "sample.getUsers" endpoint
		Then The status code is 200
		And The response matches the "sample/users-success" schema
		And The "page" response field value is 2

	Scenario: API: Get single user - GET
		Given The request path parameters are the following:
			| key    | value |
			| userId | 2     |
		When I make a "GET" request to the "sample.getUser" endpoint
		Then The status code is 200
		And The response matches the "sample/user-single-success" schema
		And  The "data.id" response field value is 2

	Scenario: API: Get single user (not found) - GET
		Given The request path parameters are the following:
			| key    | value |
			| userId | 23    |
		When I make a "GET" request to the "sample.getUser" endpoint
		Then The status code is 404
		And The response matches the "sample/user-single-not-found" schema

	Scenario: API: Create new user - POST
		Given The request body is the "sample/create-user" payload
		And The request headers are the following:
			| key          | value            |
			| Content-Type | application/json |
		When I make a "POST" request to the "sample.createUser" endpoint
		Then The status code is 201
		And The response matches the "sample/user-create-success" schema

	Scenario: API: Update existing user - PUT
		Given The request body is the "sample/update-user" payload
		And The request path parameters are the following:
			| key    | value |
			| userId | 2     |
		And The request headers are the following:
			| key          | value            |
			| Content-Type | application/json |
		When I make a "PUT" request to the "sample.updateUser" endpoint
		Then The status code is 200
		And The response matches the "sample/user-update-success" schema

	Scenario: API: Delete existing user - DELETE
		Given The request path parameters are the following:
			| key    | value |
			| userId | 2     |
		When I make a "DELETE" request to the "sample.deleteUser" endpoint
		Then The status code is 204
		And The response is empty