import { Star, HandHeart } from "lucide-react";

const HotelConfirmationCard = () => {
  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        {/* Hotel Image */}
        <div className="md:w-[342px] h-[240px] bg-gray-200 rounded-xl overflow-hidden flex-shrink-0">
          <img
            src="https://plus.unsplash.com/premium_photo-1661964071015-d97428970584?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8aG90ZWx8ZW58MHx8MHx8fDA%3D"
            alt="Hotel room"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Hotel Details */}
        <div className="flex-1 ">
          <h2 className="text-[30px] font-bold text-[#1E1E76] mb-2 xs:text-center">
            ITC Grand Chola, a Luxury Collection Hotel, Chennai
          </h2>

          <div className="flex items-center gap-2 mb-3 xs:justify-center">
            <p className="text-[#080236] text-[24px] font-semibold">
              One-Bedroom Apartment, 1 Queen – South Wing
            </p>
          </div>

          <div className="flex justify-around gap-4 mb-3 xs:justify-center"></div>

          <div className="grid grid-cols-2 gap-6 mt-3">
            <div>
              <p className="text-[#080236] text-[18px] font-semibold">
                Address
              </p>
              <p className="text-[14px] text-[#080236]">
                #63 Mount Road Guindy Chennai India
              </p>
            </div>
            <div>
              <p className="text-[#080236] text-[18px] font-semibold">Phone</p>
              <p className="text-[14px] text-[#080236]">+91 44 2220 0000</p>
            </div>
            <div>
              <p className="text-[#080236] text-[18px] font-semibold">Email</p>
              <p className="text-[14px] text-[#080236]">
                <EMAIL>
              </p>
            </div>
            <div>
              <p className="text-[#080236] text-[18px] font-semibold">
                Website
              </p>
              <p className="text-[14px] text-[#080236]">www.itchotels.in</p>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Details */}
      <div className="flex justify-center">
        <div className=" font-proxima-nova w-5/6 p-px rounded-lg h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 px-4 py-2 bg-[#E6E3FF] rounded-lg">
            <div className="text-center text-[18px] font-semibold text-[#707FF5]">
              <p className="mb-1">CHECK-IN</p>
              <p className="text-[#1E1E76]">Mon, Jun 23, 2025</p>
            </div>
            <div className="text-center text-[18px] font-semibold text-[#707FF5]">
              <p className="mb-1">CHECK-OUT</p>
              <p className="text-[#1E1E76]">Wed, Jun 25, 2025</p>
            </div>
            <div className="text-center text-[18px] font-semibold text-[#707FF5]">
              <p className="mb-1">NIGHTS</p>
              <p className="text-[#1E1E76]">2</p>
            </div>
            <div className="text-center text-[18px] font-semibold text-[#707FF5]">
              <p className="mb-1">ROOMS</p>
              <p className="text-[#1E1E76]">1</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelConfirmationCard;
