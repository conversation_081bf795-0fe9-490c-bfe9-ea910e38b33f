export interface Review {
  title: string;
  author: string;
  date: string;
  rating: number;
  comment: string;
  hotelResponse?: string;
}

export interface HotelData {
  name: string;
  rating: number;
  reviewCount: number;
  location: string;
  price: number;
  description: string;
  fullDescription: string;
  aboutHotel: string;
  aboutLuxuryCollection: string;
  address: string;
  website: string;
  phone: string;
  email: string;
  images: string[];
  allImages: string[];
  mapImage: string;
  topAmenities: string[];
  amenities: {
    category: string;
    items: string[];
  }[];
  policies: {
    name: string;
    icon: string;
    details: string[];
  }[];
  reviews: Review[];
}

// Enhanced Hotel Content Data
export interface HotelPhone {
  label: string;
  number: string;
}

export interface HotelFacility {
  label: string;
  group: string;
  amount?: number;
  currency?: string;
  paid_facility?: boolean;
}

export interface HotelImage {
  title: string;
  path: string;
  order: number;
  url?: string;
  thumbnail?: string;
}

export interface InterestPoint {
  name: string;
  distance: string;
  label: string;
  group: string;
}

export interface RoomDetail {
  description: string;
  type: string;
  character: string;
  minPax: number;
  maxPax: number;
  maxAdults: number;
  maxChildren: number;
  minAdults: number;
  facilities: HotelFacility[];
}

export interface HotelContent {
  full_description: string;
  address_full: string;
  email: string;
  phones: HotelPhone[];
  website: string | null;
  check_in?: string;
  check_out?: string;
  all_images: HotelImage[];
  facilities_grouped: Record<string, HotelFacility[]>;
  interest_points: InterestPoint[];
  nearest_terminals: string[];
}

export interface SearchParams {
  adults: number;
  check_in: string;
  check_out: string;
  children: number;
  hotel_codes: any[];
  items_per_page: number;
  location: string;
  page: number;
  rooms: number;
  sort: string;
  sort_order: string;
}

interface Offer {
  code: string;
  name: string;
  amount: string; // Changed to string to match API response
}

interface CancellationPolicy {
  amount: string; // Changed to string to match API response
  from: string;
}

export interface RateOption {
  rate_key: string;
  rate_type: string;
  rate_class: string;
  net_price: string; // Changed to string to match API response
  allotment: number;
  selling_price: string | null; // Changed to string to match API response
  board_code: string;
  board_name: string;
  payment_type: string;
  offers: Offer[] | null;
  cancellation_policies: CancellationPolicy[];
  // Enhanced fields
  amenities?: string[];
  room_facilities?: string[];
}

export interface Room {
  room_code: string;
  room_name: string;
  rate_options: RateOption[];
  available_options: number;
  // Enhanced fields from hotel content
  room_images?: string[];
  room_description?: string;
  room_size?: {
    min_pax: number;
    max_pax: number;
    max_adults: number;
    max_children: number;
    min_adults: number;
  };
  room_facilities?: string[];
}

export interface Hotel {
  id: string;
  name: string;
  description: string;
  imageSrc: string;
  rating: number;
  currency: string;
  provider?: string;
  rooms: Room[];
  code?: number;
  city: string;
  address: string;
  category_name: string;
  images?: any;
  img_base_url: string;
  min_rate: string; // Changed to string to match API response
  available_rooms: number;
  // Enhanced field for hotel content data
  hotel_content?: HotelContent;
}

// Room interface for room selection page
export interface TransformedRoom {
  id: string;
  roomType: string;
  bedType: string;
  sleeps: number;
  price: number;
  totalPrice: number;
  nights: number;
  discount: number;
  refundable: boolean;
  refundableUntil?: string;
  images: string[];
  amenities: string[];
  description: string;
  reviews: {
    count: number;
    overall: number;
    categories: Record<string, number>;
  };
  facilities: string[];
  boardName: string;
  cancellationPolicies: CancellationPolicy[];
  offers: Offer[];
  rateKey: string;
  allotment: number;
}

type FilterOption = {
    id: string;
    label: string;
    count?: number;
    checked?: boolean;
    filter?: string | { facilityCode: number; facilityGroupCode: number };
}

export type FilterSection = {
    id: string;
    title: string;
    options: FilterOption[];
    expandable?: boolean;
    expanded?: boolean;
    showCount?: number;
}