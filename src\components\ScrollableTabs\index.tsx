import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

// Main Reusable ScrollableTabs Component
const ScrollableTabs = ({ 
  tabs = [], 
  defaultActiveTab = null,
  onTabChange = () => {},
  className = '',
  tabClassName = '',
  activeTabClassName = '',
  arrowClassName = '',
  showContent = true,
  contentClassName = '',
  scrollAmount = 200,
  autoCenter = true
}:any) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id || '');
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const scrollContainerRef = useRef(null);
  const tabRefs = useRef({});

  // Check if arrows should be shown
  const checkArrowVisibility = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollLeft, scrollWidth, clientWidth } = container;
    setShowLeftArrow(scrollLeft > 5);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 5);
  };

  // Scroll active tab into view
  const scrollToActiveTab = (tabId) => {
    if (!autoCenter) return;
    
    const container = scrollContainerRef.current;
    const tabElement = tabRefs.current[tabId];
    
    if (container && tabElement) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();
      
      const tabLeft = tabRect.left - containerRect.left + container.scrollLeft;
      const tabRight = tabLeft + tabRect.width;
      const visibleLeft = container.scrollLeft;
      const visibleRight = container.scrollLeft + container.clientWidth;
      
      if (tabLeft < visibleLeft || tabRight > visibleRight) {
        const scrollTo = tabLeft - (container.clientWidth / 2) + (tabRect.width / 2);
        container.scrollTo({ left: scrollTo, behavior: 'smooth' });
      }
    }
  };

  // Manual scroll functions
  const scrollLeft = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // Handle tab click
  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
    onTabChange(tabId);
    if (autoCenter) {
      scrollToActiveTab(tabId);
    }
  };

  // Check arrow visibility on mount and resize
  useEffect(() => {
    const checkAndUpdate = () => {
      setTimeout(checkArrowVisibility, 100);
    };

    checkAndUpdate();
    
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkArrowVisibility);
      
      const resizeObserver = new ResizeObserver(checkAndUpdate);
      resizeObserver.observe(container);
      
      return () => {
        container.removeEventListener('scroll', checkArrowVisibility);
        resizeObserver.disconnect();
      };
    }
  }, []);

  // Scroll to active tab on mount
  useEffect(() => {
    if (autoCenter && activeTab) {
      setTimeout(() => scrollToActiveTab(activeTab), 100);
    }
  }, []);

  // Update active tab when defaultActiveTab changes
  useEffect(() => {
    if (defaultActiveTab && defaultActiveTab !== activeTab) {
      setActiveTab(defaultActiveTab);
      if (autoCenter) {
        setTimeout(() => scrollToActiveTab(defaultActiveTab), 100);
      }
    }
  }, [defaultActiveTab]);

  return (
    <div className={`w-full ${className}`}>
      {/* Tabs Container */}
      <div className="relative flex items-center">
        {/* Left Arrow */}
        {showLeftArrow && (
          <button
            onClick={scrollLeft}
            className={`flex-shrink-0 p-2 hover:bg-gray-100 rounded-full transition-colors mr-2 ${arrowClassName}`}
            aria-label="Scroll left"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600" />
          </button>
        )}

        {/* Scrollable Tabs */}
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-x-auto scrollbar-hide"
          style={{ 
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
          onScroll={checkArrowVisibility}
        >
          <div className="flex items-center min-w-max px-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                ref={el => tabRefs.current[tab.id] = el}
                onClick={() => handleTabClick(tab.id)}
                className={`relative px-4 pt-4 pb-2 text-sm font-medium whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? `text-[#1E1E76] ${activeTabClassName}`
                    : `text-gray-500 hover:text-gray-700 ${tabClassName}`
                }`}
              >
                {tab.label}
                {/* Active tab underline */}
                {activeTab === tab.id && (
                  <div className="absolute bottom-0 left-3 right-3 h-0.5 bg-[#1E1E76] rounded-full" />
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Right Arrow */}
        {showRightArrow && (
          <button
            onClick={scrollRight}
            className={`flex-shrink-0 p-2 hover:bg-gray-100 rounded-full transition-colors ml-2 ${arrowClassName}`}
            aria-label="Scroll right"
          >
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>

      {/* Tab Content */}
      {showContent && (
        <div className={`mt-6 p-6 bg-gray-50 rounded-lg ${contentClassName}`}>
          {tabs.find(tab => tab.id === activeTab)?.content || (
            <div>
              <h2 className="text-xl font-semibold mb-4 capitalize">
                {tabs.find(tab => tab.id === activeTab)?.label}
              </h2>
              <p className="text-gray-600">
                Content for {tabs.find(tab => tab.id === activeTab)?.label}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};


export default ScrollableTabs;