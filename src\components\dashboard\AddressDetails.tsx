import React, { useEffect, useState } from "react";
import SavedAddresses from "./SavedAddresses";
import AddressForm from "./AddressForm";
import { User, Address as UserAddress, UserDetailDelete } from "@/constants/user";

export type Address = {
  address_id: string;
  memorableName: string;
  userName: string;
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

interface AddressDetailsProps {
  user: User | null;
  updateUser: (data: User) => Promise<void>;
  updatedStatus: string | null;
  deleteApi: (data: UserDetailDelete) => Promise<void>;
}

const AddressDetails: React.FC<AddressDetailsProps> = ({ user, updateUser, updatedStatus, deleteApi }) => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editIdx, setEditIdx] = useState<string | null>(null);
  const [editData, setEditData] = useState<Address | undefined>(undefined);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    const addArr: Address[] = [];
    if (user && user?.addresses?.length > 0) {
      user.addresses.forEach((add) => {
        const newAdd: Address = {
          address_id: add.address_id,
          memorableName: add.memorableName,
          userName: add.userName,
          street: add.streetAddress,
          city: add.city,
          state: add.state,
          country: add.country,
          postalCode: add.postalCode
        };

        addArr.push(newAdd)
      })
      setAddresses(addArr);
      setShowForm(false);
    }
  }, [user])


  const handleAddNew = () => {
    setIsEdit(false)
    setEditIdx(null);
    setEditData(undefined);
    setShowForm(true);
  };

  const handleEdit = (address: Address, idx: string) => {
    setIsEdit(true);
    setEditIdx(idx);
    setEditData(address);
    setShowForm(true);
  };

  // const handleDelete = (idx: string) => {
  //   //setAddresses(addresses.filter((address) => address.address_id !== idx));
  //   deleteApi({ address_id: idx });

  // };

  const handleDelete = async (idx: string) => {
    try {
      await deleteApi({ address_id: idx }); // Wait for API call to succeed
      setAddresses((prev) =>
        prev.filter((address) => address.address_id !== idx)
      ); // Remove from UI
    } catch (error) {
      console.error("Delete failed:", error);
      // Optionally show a toast or error message here
    }
  };


  const handleSave = (data: Address) => {
    let updatedAddresses;

    if (editIdx !== null) {
      // Edit existing
      updatedAddresses = addresses.map((addr) =>
        addr.address_id === editIdx ? data : addr
      );
    } else {
      // Add new
      updatedAddresses = [...addresses, data];
    }

    setAddresses(updatedAddresses); // update local UI
    setShowForm(false);
    setEditIdx(null);
    setEditData(undefined);

    //clone user, avoid mutation
    if (user) {
      const userCopy = { ...user };
      const newAddArr: UserAddress[] = updatedAddresses.map((add) => ({
        address_id: add.address_id,
        memorableName: add.memorableName,
        city: add.city,
        country: add.country,
        state: add.state,
        postalCode: add.postalCode,
        streetAddress: add.street,
        userName: add.userName
      }));
      userCopy.addresses = newAddArr;
      updateUser(userCopy);
    }
  };


  const handleCancel = () => {
    setShowForm(false);
    setEditIdx(null);
    setEditData(undefined);
  };


  return (
    <div className="w-full">
      {showForm ? (
        <AddressForm onCancel={handleCancel} onSave={handleSave} initialData={editData} isEdit={isEdit} />
      ) : (
        <SavedAddresses
          addresses={addresses}
          onAddNew={handleAddNew}
          onEdit={handleEdit}
          onDelete={handleDelete}
          status={updatedStatus}
        />
      )}
    </div>
  );
};

export default AddressDetails;
