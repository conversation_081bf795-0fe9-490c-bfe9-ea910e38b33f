"use client";

import axios from "axios";
import type { User, UserDetailDelete } from "@/constants/user";
import { useAuth } from "@/components/AuthProvider/auth-Provider";

export const useUserOperations = (fetchUserDetails: () => Promise<void>) => {
  const { token } = useAuth();

  const updateUserDetails = async (data: User) => {
    try {
      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/update-details`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const status = response.data?.detail?.status;
      fetchUserDetails();
      // Return the status for the success message handling
      return status;
    } catch (error: any) {
      console.error(
        "Error updating user details:",
        error.response?.data || error.message
      );
      throw error;
    }
  };

  const updateUserDetailsVoid = async (data: User): Promise<void> => {
    await updateUserDetails(data);
  };

  const deleteApi = async (data: UserDetailDelete) => {
    try {
      const response = await axios.delete(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/delete-details`,
        {
          data,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      console.log("User details updated:", response.data?.detail?.status);
      fetchUserDetails();
    } catch (error: any) {
      console.error(
        "Error deleting user details:",
        error.response?.data || error.message
      );
      throw error;
    }
  };

  return {
    updateUserDetails,
    updateUserDetailsVoid,
    deleteApi,
  };
};
