import React, { useState } from "react";
import { Penci<PERSON><PERSON><PERSON>, Save } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { User } from "@/constants/user";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/input/Select";
import InputField from "@/components/input/InputField";

interface EditProfileFormProps {
  user: User | null;
  onSave: () => void;
}

const EditProfileForm: React.FC<EditProfileFormProps> = ({ user, onSave }) => {
  const [selectedTitle, setSelectedTitle] = useState<string>("");
  const [selectedGender, setSelectedGender] = useState<string>("");

  const handleTitleChange = (value: string) => {
    setSelectedTitle(value);
  };

  const handleGenderChange = (value: string) => {
    setSelectedGender(value);
  };

  return (
    <>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 md:gap-0">
        <h2 className="text-lg md:text-3xl font-semibold text-[#4B4BC3]">
          Edit Personal Information
        </h2>
        <Button className="text-sm text-[#F2F3FA] bg-[#4B4BC3] px-6 py-4 rounded-full" onClick={onSave}>
            <Save /> Save Changes
        </Button>
      </div>
      <div className="flex flex-col gap-1">
        <label className="text-[#1E1E76] text-base md:text-lg font-semibold">Title</label>
        <div className="grid grid-cols-1 md:grid-cols-4">
          <Select value={selectedTitle} onValueChange={handleTitleChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select Title" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Mr.">Mr.</SelectItem>
              <SelectItem value="Mrs.">Mrs.</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5">
        <InputField
          label="First Name"
          placeholder="User First Name"
          name="firstName"
        />
        <InputField
          label="Middle Name"
          placeholder="User Middle Name"
          name="middleName"
        />
        <InputField
          label="Last Name"
          placeholder="User Last Name"
          name="lastName"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-5 md:gap-8">
        <div>
          <label className="text-[#1E1E76] font-semibold">Gender</label>
          <Select value={selectedGender} onValueChange={handleGenderChange}>
            <SelectTrigger>
              <SelectValue placeholder="Gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Male">Male</SelectItem>
              <SelectItem value="Female">Female</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <InputField
          label="Date of Birth"
          placeholder="DOB"
          type="date"
          name="dob"
        />
        <InputField
          label="Nationality"
          placeholder="Nationality"
          name="nationality"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5">
        <InputField
          label="Primary Email"
          placeholder="Primary Name"
          type="email"
          name="primaryEmail"
        />
        <InputField
          label="Mobile Number"
          placeholder="+91 1234567890"
          type="tel"
          name="mobileNumber"
        />
        <InputField
          label="Alternate Phone Number(optional)"
          placeholder="+91 1234567890"
          type="tel"
          name="alternateNumber"
        />
      </div>
      <p className="text-green-600 text-sm w-full p-2 text-center">Changes Updated Successfully</p>
      <div className="flex w-full h-[0.5px] bg-[#B4BBE8]"></div>
    </>
  );
};

export default EditProfileForm;
