import React, { useState } from "react";
import FormInput from "./FormInput";
import Button from "../ui/LoginButton";
import { authPostMethod } from "@/utils/auth";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button as DefaultButton } from "../ui/button";
interface SignupFormProps {
  onVerificationRequest: (email: string, type: "email" | "phone") => void;
}

interface Credentials {
  firstName: string;
  lastName: string;
  username: string;
  password: string;
  confirmPassword: string;
}

const SignupForm: React.FC<SignupFormProps> = ({ onVerificationRequest }) => {
  const [credentials, setCredentials] = useState<Credentials>({
    firstName: "",
    lastName: "",
    username: "",
    password: "",
    confirmPassword: "",
  });
  const [isHovered, setIsHovered] = useState(false);
  const [errors, setErrors] = useState<Partial<Credentials>>({});
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showPasswordTooltip, setShowPasswordTooltip] =
    useState<boolean>(false);
  const [passwordValidation, setPasswordValidation] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });
  const { executeRecaptcha } = useGoogleReCaptcha();
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials({
      ...credentials,
      [name]: value,
    });

    // Clear error when user starts typing
    if (errors[name as keyof Credentials]) {
      setErrors({
        ...errors,
        [name]: undefined,
      });
    }
    if (name === "password") {
      const validation = {
        length: value.length >= 8,
        lowercase: /[a-z]/.test(value),
        uppercase: /[A-Z]/.test(value),
        number: /[0-9]/.test(value),
        specialChar: /[!@#$%^&*]/.test(value),
      };
      setPasswordValidation(validation);
      const allValid = Object.values(validation).every(Boolean);
      if (value.length > 0 && !allValid) {
        setShowPasswordTooltip(true);
      } else {
        setShowPasswordTooltip(false);
      }
    }
  };

  const getInputType = (input: string): "email" | "phone" => {
    return /^\d{10}$/.test(input) ? "phone" : "email";
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Credentials> = {};

    if (!credentials.firstName) {
      newErrors.firstName = "First Name is required";
    }
    if (!credentials.lastName) {
      newErrors.lastName = "Last Name is required";
    }

    // Email validation
    if (!credentials.username) {
      newErrors.username = "Email/phone is required";
    } else if (
      !/\S+@\S+\.\S+/.test(credentials.username) &&
      !/^\d{10}$/.test(credentials.username)
    ) {
      newErrors.username = "Please enter a valid email or phone number";
    }

    // Password validation
    if (!credentials.password) {
      newErrors.password = "Password is required";
    } else {
      const { length, lowercase, uppercase, number, specialChar } =
        passwordValidation;
      if (!(length && lowercase && uppercase && number && specialChar)) {
        newErrors.password = "Password does not meet requirements";
      }
    }

    // Confirm password validation
    if (credentials.password !== credentials.confirmPassword) {
      newErrors.confirmPassword =
        "Those passwords don’t match. Try retyping them?";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;
    setGeneralError(null);
    if (!executeRecaptcha) {
      return;
    }
    try {
      const recaptcha = await executeRecaptcha("signup");
      const inputType = getInputType(credentials.username);
      const params = {
        firstName: credentials.firstName,
        lastName: credentials.lastName,
        password: credentials.password,
        confirm_password: credentials.confirmPassword,
        sessionid: recaptcha,
        ...(inputType === "email"
          ? { email: credentials.username }
          : { phone: `+${credentials.username}` }),
      };
      const response = await authPostMethod("auth/signup", params);
      if (!response.success) {
        console.warn("Signup error:", response.data?.detail.message);
        setGeneralError(
          response.data?.detail.message ||
          "Oops! Signup failed. Please try again."
        );
        return;
      }
      onVerificationRequest(credentials.username, inputType);
    } catch (error) {
      console.error("Signup exception:", error);
      setGeneralError("Something went wrong during signup");
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex flex-row w-full gap-4">
        <div className="lg:mb-3 md:mb-2 xs:mb-2 w-1/2 font-proxima-nova">
          <label
            className="block text-brand-black mb-1 text-sm md:text-lg"
            style={styles.label}
          >
            First Name
          </label>
          <FormInput
            type="text"
            name="firstName"
            placeholder="Enter your First Name"
            value={credentials.firstName}
            onChange={handleChange}
          />
          {errors.firstName && (
            <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
          )}
        </div>
        <div className="lg:mb-3 md:mb-2 xs:mb-2 w-1/2 font-proxima-nova">
          <label
            className="block text-brand-black mb-1 text-sm md:text-lg"
            style={styles.label}
          >
            Last Name
          </label>
          <FormInput
            type="text"
            name="lastName"
            placeholder="Enter your Last Name"
            value={credentials.lastName}
            onChange={handleChange}
          />
          {errors.lastName && (
            <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
          )}
        </div>
      </div>
      <div className="lg:mb-3 md:mb-2 xs:mb-2 font-proxima-nova">
        <label
          className="block text-brand-black mb-1 text-sm md:text-lg"
          style={styles.label}
        >
          Email / Phone Number
        </label>
        <FormInput
          type="text"
          name="username"
          placeholder="Enter your email ID / Phone Number"
          value={credentials.username}
          onChange={handleChange}
        />
        {errors.username && (
          <p className="text-red-500 text-sm mt-1">{errors.username}</p>
        )}
      </div>

      <div className="lg:mb-3 md:mb-2 xs:mb-2 font-proxima-nova">
        <div className="flex justify-between items-center mb-1">
          <label
            className="block text-brand-black text-sm md:text-lg"
            style={styles.label}
          >
            Create a Password
          </label>
        </div>
        <div className="relative">
          <FormInput
            type={(!showPassword && "password") || "text"}
            name="password"
            placeholder="Enter your password"
            value={credentials.password}
            onChange={handleChange}
            onFocus={() => setShowPasswordTooltip(false)}
            onBlur={() => setShowPasswordTooltip(false)}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-2">
            {(!showPassword && (
              <EyeIcon
                onClick={() => setShowPassword(!showPassword)}
                style={{ color: "#999999" }}
              />
            )) || (
                <EyeOffIcon
                  onClick={() => setShowPassword(!showPassword)}
                  style={{ color: "#999999" }}
                />
              )}
          </div>
        </div>
        {errors.password && (
          <p className="text-red-500 text-sm mt-1">{errors.password}</p>
        )}
        {showPasswordTooltip && (
          <div className="absolute z-10 mt-1 w-full max-w-xs bg-white border border-gray-200 rounded-lg shadow-lg p-3">
            <p className="font-medium text-neutral-dark mb-1">
              Password Requirements:
            </p>
            <ul className="space-y-1">
              <li
                className={`flex items-center ${passwordValidation.length ? "text-green-500" : "text-red-500"}`}
              >
                {passwordValidation.length ? (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                )}
                Must be at least 8 characters
              </li>
              <li
                className={`flex items-center ${passwordValidation.lowercase ? "text-green-500" : "text-neutral-grey"}`}
              >
                {passwordValidation.lowercase ? (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                )}
                At least 1 lowercase letter
              </li>
              <li
                className={`flex items-center ${passwordValidation.uppercase ? "text-green-500" : "text-neutral-grey"}`}
              >
                {passwordValidation.uppercase ? (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                )}
                At least 1 uppercase letter
              </li>
              <li
                className={`flex items-center ${passwordValidation.specialChar ? "text-green-500" : "text-neutral-grey"}`}
              >
                {passwordValidation.specialChar ? (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                )}
                At least 1 special char (!@#$%^&*)
              </li>
              <li
                className={`flex items-center ${passwordValidation.number ? "text-green-500" : "text-neutral-grey"}`}
              >
                {passwordValidation.number ? (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                )}
                At least 1 number
              </li>
            </ul>
          </div>
        )}
      </div>

      <div className="lg:mb-4 md:mb-3 xs:mb-3 font-proxima-nova">
        <div className="flex justify-between items-center mb-1">
          <label
            className="block text-brand-black text-sm md:text-lg"
            style={styles.label}
          >
            Confirm Password
          </label>
        </div>
        <FormInput
          type="password"
          name="confirmPassword"
          placeholder="Enter Confirm Password"
          value={credentials.confirmPassword}
          onChange={handleChange}
        />
        {errors.confirmPassword && (
          <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>
        )}
      </div>
      {generalError && (
        <p
          className={`text-sm mt-2 mb-4 text-center ${generalError === "Email has been sent successfully"
            ? "text-green-600"
            : "text-red-600"
            }`}
        >
          {generalError}
        </p>
      )}
      <DefaultButton
        type="submit"
        variant="primary"
        className={`font-proxima-nova font-semibold w-full text-center py-2 px-4 xs:text-sm xs:py-1 rounded-[8px] transition-all duration-200 bg-brand text-white
          
           
          `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        Send Verification Code
      </DefaultButton>
    </form>
  );
};

const styles = {
  label: {
    fontFamily: "Proxima Nova, sans-serif",
    fontWeight: 500,
    lineHeight: "24.36px",
    letterSpacing: "0%",
  },
  button: {
    background: "#1E1E76",
    color: "white",
    borderRadius: "8px",
  },
};

export default SignupForm;
