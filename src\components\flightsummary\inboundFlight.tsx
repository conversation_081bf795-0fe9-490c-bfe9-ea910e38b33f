import React, { useState } from "react";
import {
  PlaneLanding,
  PlaneTakeoff,
  ChevronDown,
  ChevronUp,
  Plane,
  InfoIcon
} from "lucide-react";
import { useSelector } from 'react-redux';
import { useFlightContext } from "@/context/FlightContext";
import { formatFlightTime } from "@/lib/utils/flightTime";
import { formatAirportDisplay } from "@/lib/utils/formatAirport";
import { getFormattedLayoverTime } from "@/lib/utils/layover";
import { getFlightDescription } from "@/lib/utils/flightDescription";
import { AppState } from '@/store/store';

const InboundFlight = () => {
  const {
    selectedOutboundFlight,
    selectedInboundFlight,
    sharedFlightResults
  } = useFlightContext();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const tripSummaryDetails = useSelector((state: AppState) => state.tripSummary);
  const airportData = tripSummaryDetails?.sharedFlightResults?.airport_data;
  const dept = tripSummaryDetails?.selectedInboundFlight && formatFlightTime(tripSummaryDetails?.selectedInboundFlight.departure, tripSummaryDetails?.selectedInboundFlight.origin);
  const arr = tripSummaryDetails?.selectedInboundFlight && formatFlightTime(tripSummaryDetails?.selectedInboundFlight.arrival, tripSummaryDetails?.selectedInboundFlight.destination);

  const firstSegment = tripSummaryDetails?.selectedInboundFlight?.segments?.[0];
  const lastSegment = tripSummaryDetails?.selectedInboundFlight?.segments?.[tripSummaryDetails?.selectedInboundFlight.segments.length - 1];
  const isConnecting = tripSummaryDetails?.selectedInboundFlight?.segments && tripSummaryDetails?.selectedInboundFlight.segments.length > 1;
  const dep1 = firstSegment && formatFlightTime(firstSegment.depart_date, firstSegment.origin);
  const arr1 = firstSegment && formatFlightTime(firstSegment.arrive_date, firstSegment.destination);
  const dep2 = lastSegment && formatFlightTime(lastSegment.depart_date, lastSegment.origin);
  const arr2 = lastSegment && formatFlightTime(lastSegment.arrive_date, lastSegment.destination);

  const getAirportDisplayName = (
    airportCode: any,
    airportOptions: any
  ): string => {
    // console.log("getAirportDisplayName=======", airportCode, airportOptions);
    if (!airportOptions || typeof airportOptions !== "object") {
      return airportCode;
    }

    const airport = airportOptions[airportCode];

    if (airport && isOpen) {
      return `${airportCode}, ${airport.airport_name}, ${airport.city_name_original}`;
    }

    return airportCode; // fallback if not found
  };

  const firstSegmentOriginDisplay = firstSegment && getAirportDisplayName(firstSegment.origin, airportData);
  const firstSegmentDestDisplay = firstSegment && getAirportDisplayName(firstSegment.destination, airportData);
  const lastSegmentOriginDisplay = lastSegment && getAirportDisplayName(lastSegment.origin, airportData);
  const lastSegmentDestDisplay = lastSegment && getAirportDisplayName(lastSegment.destination, airportData);
  const layoverStr = tripSummaryDetails?.selectedInboundFlight && getFormattedLayoverTime(tripSummaryDetails?.selectedInboundFlight);
  const flightDescription2 = lastSegment && getFlightDescription(lastSegment);
  const flightDescription1 = firstSegment && getFlightDescription(firstSegment);


  const styles = {
    button: {
      background:
        "linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)",
      color: "white",
      borderRadius: "100px",
    },
  };
  return (
    <div className="relative font-proxima-nova w-full p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
      <div className="flex flex-col  justify-between w-full 2xl:p-4 xl:p-3 lg:p-2 md:p-3 sm:p-3 xs:p-3 border rounded-2xl shadow-md bg-[#F8F9FF] relative">
        {!isOpen ? (
          <div className="md:flex w-full justify-around items-center xs:hidden sm:hidden">
            <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">

              <img
                src={tripSummaryDetails?.selectedInboundFlight?.supplier_logo}
                alt={tripSummaryDetails?.selectedInboundFlight?.airline}
                className="lg:w-28 md:w-12 w-28 h-16 rounded-full object-contain"
              />
            </div>
            <div className="flex flex-col">
              <div className="text-[#080236] lg:text-base md:text-sm font-semibold">{dept && dept.dayLabel}</div>
              <div className="text-[#080236] lg:text-base md:text-sm font-semibold">Departure</div>
            </div>
            <div className="flex flex-col">
              {
                tripSummaryDetails?.selectedInboundFlight && tripSummaryDetails?.sharedFlightResults && (
                  <>
                    <div className="text-[#080236] lg:text-base md:text-sm font-semibold">
                      {tripSummaryDetails?.selectedInboundFlight.departure_time_ampm} - {tripSummaryDetails?.selectedInboundFlight.arrival_time_ampm}
                    </div>
                    <div className="text-[#B4BBE8] lg:text-base md:text-sm">
                      {tripSummaryDetails?.sharedFlightResults.airport_data[tripSummaryDetails?.selectedInboundFlight.airline_code]}
                    </div>
                    <>&nbsp;</>
                  </>
                )
              }
            </div>
            <div className="flex flex-col">
              <div className="text-[#080236] lg:text-base md:text-sm font-semibold">{tripSummaryDetails?.selectedInboundFlight?.duration}</div>
              <div className="text-[#B4BBE8] lg:text-base md:text-sm">{tripSummaryDetails?.selectedInboundFlight?.origin} - {tripSummaryDetails?.selectedInboundFlight?.destination}</div>
            </div>
            {tripSummaryDetails?.selectedInboundFlight.segments.length > 1 && <div className="flex flex-col">
              <div className="text-[#080236] lg:text-base md:text-sm font-semibold">{tripSummaryDetails?.selectedInboundFlight && tripSummaryDetails?.selectedInboundFlight.segments.length - 1} Stop</div>
              <div className="text-[#B4BBE8] lg:text-base md:text-sm">{tripSummaryDetails?.selectedOutboundFlight && tripSummaryDetails?.selectedOutboundFlight.segments[0].destination}</div>
            </div>}
            <div className="flex justify-end xs:hidden">
              {/* Dropdown Icon */}
              <ChevronDown
                onClick={() => setIsOpen((prev) => !prev)}
                className="text-[#070708] w-5 h-5 cursor-pointer"
              />
            </div>
          </div>
        ) : (
          <>
            <div className="md:flex flex-row lg:w-[90%] md:w-full mx-auto justify-between xs:hidden sm:hidden">
              <div className="flex lg:gap-5 md:gap-2 items-center">
                <img
                  src={tripSummaryDetails?.selectedInboundFlight?.supplier_logo}
                  alt={tripSummaryDetails?.selectedInboundFlight?.airline}
                  className="lg:w-28 md:w-12 w-28 h-16 rounded-full object-contain"
                />
                <div className="flex flex-col">
                  <div>Departure {dep1 && dep1.dayLabel}</div>
                  <div className="lg:text-sm md:text-xs text-[#FF3B3F]">
                    *Free Cancellation within 24 hours of booking
                  </div>
                </div>
              </div>
              <div className="flex flex-row lg:gap-5 md:gap-2 items-center">
                {/* <button className="flex lg:px-4 lg:py-2 md:px-2 md:py-1" style={styles.button}>
                  Change Flight
                </button> */}
                <div className="flex justify-end xs:hidden">
                  {/* Dropdown Icon */}
                  <ChevronUp
                    onClick={() => setIsOpen((prev) => !prev)}
                    className="text-[#070708] w-5 h-5 cursor-pointer"
                  />
                </div>
              </div>
            </div>
            <div className=" py-3 w-full h-full md:flex flex-col gap-2 justify-center items-center xs:hidden sm:hidden">
              <div className="flex flex-row lg:w-[90%] md:w-full mx-auto justify-center xl:gap-2 lg:gap-1 sm:gap-2 h-auto xl:p-2 lg:p-0">
                {/* First Leg: MAA → DEL */}
                <div className="flex flex-col xl:w-1/5 sm:w-1/4 justify-between text-left xl:py-2 lg:py-1 md:py-0">
                  {dep1 && (
                    <div className="flex flex-col">
                      <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                        {dep1.timeLabel}
                      </p>
                      <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                        Departure - {dep1.dayLabel}
                      </p>
                    </div>
                  )}
                  {
                    arr1 && (
                      <div className="flex flex-col">
                        <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                          {arr1.timeLabel}
                        </p>
                        <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                          Departure - {arr1.dayLabel}
                        </p>
                      </div>
                    )
                  }
                </div>
                <div className="flex flex-col items-center justify-center p-4">
                  <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

                </div>
                <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
                  <div className="flex flex-col">
                    <p className="font-bold flex-wrap 2xl:text-lg xl:text-lg sm:text-base text-[#080236]">
                      {firstSegmentOriginDisplay}
                    </p>
                    <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#B4BBE8]">
                      {flightDescription1}
                    </p>
                    <p className="xl:text-sm lg:text-xs sm:text-sm text-[#080236] mt-1">
                      {firstSegment && firstSegment.duration}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#080236] 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                      {firstSegmentDestDisplay}
                    </p>
                  </div>
                </div>
              </div>
              {
                isConnecting && (
                  <>
                    <div className="flex w-1/2">
                      <div className="flex items-center gap-3 p-2 rounded-lg border border-[#B4BBE8] w-max">
                        <InfoIcon color="#4B4BC3" />
                        <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#4B4BC3] font-medium">
                          {`${layoverStr} ${lastSegmentOriginDisplay}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-row justify-center xl:gap-2 md:gap-1 sm:gap-2 lg:w-[90%] md:w-full mx-auto h-auto xl:p-2 lg:p-0">
                      {/* Second Leg: MAA → DEL */}
                      <div className="flex flex-col xl:w-1/5 sm:w-1/4 text-left justify-between xl:py-2 lg:py-1 md:py-0">
                        {dep2 && (
                          <div className="flex flex-col">
                            <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                              {dep2.timeLabel}
                            </p>
                            <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                              Departure - {dep2.dayLabel}
                            </p>
                          </div>
                        )}
                        {
                          arr2 && (
                            <div className="flex flex-col">
                              <p className="2xl:text-lg xl:text-lg sm:text-base font-bold text-[#080236]">
                                {arr2.timeLabel}
                              </p>
                              <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#707FF5]">
                                Departure - {arr2.dayLabel}
                              </p>
                            </div>
                          )
                        }
                      </div>
                      <div className="flex flex-col-reverse items-center justify-center p-4">
                        <img
                          src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                          alt="Landing"
                        />
                      </div>
                      <div className="flex flex-col xl:w-4/5 sm:w-3/4 gap-1 justify-between xl:py-3 lg:py-1">
                        <div>
                          <p className="font-bold 2xl:text-lg xl:text-lg sm:text-base text-[#080236]">
                            {lastSegmentOriginDisplay}
                          </p>
                          <p className="2xl:text-sm xl:text-sm lg:text-xs sm:text-sm text-[#B4BBE8]">
                            {flightDescription2}
                          </p>
                        </div>
                        <p className="xl:text-sm lg:text-xs sm:text-sm text-[#080236] mt-1">
                          {lastSegment && lastSegment.duration}
                        </p>
                        <div>
                          <p className="text-[#080236] 2xl:text-lg xl:text-lg sm:text-base font-semibold">
                            {lastSegmentDestDisplay}
                          </p>
                        </div>
                      </div>
                    </div>
                  </>
                )
              }
            </div>
          </>
        )}
        {!isOpen && (
          <div className="text-sm text-[#FF3B3F] xs:hidden">
            *Free Cancellation within 24 hours of booking
          </div>
        )}
        {/* // ----------------------Mobile--------------------------// */}
        {!isOpen ? (
          <div
            className={`flex flex-col gap-4 xs:gap-2 xs:py-1 xs:justify-center xs:items-center w-[95%] mx-auto md:hidden`}
          >
            <div className="flex w-1/6">
              <img
                src={tripSummaryDetails?.selectedInboundFlight?.supplier_logo}
                alt={tripSummaryDetails?.selectedInboundFlight?.airline}
                className="w-full h-full rounded-full"
              />
            </div>
            <div className="flex flex-row justify-around gap-2 w-full">
              <div className="w-full">
                <h2 className="font-bold 2xl:text-lg xl:text-base lg:text-sm md:text-lg sm:text-base xs:text-sm text-[#1E1E76]">
                  {dept && dept.dayLabel}
                </h2>
                {
                  tripSummaryDetails?.selectedInboundFlight && (
                    <p className="xl:text-sm lg:text-sm md:text-sm sm:text-sm xs:text-sm text-[#1E1E76]">
                      {tripSummaryDetails?.selectedInboundFlight?.departure_time_ampm} | <span className="text-[#707FF5]">{tripSummaryDetails?.selectedInboundFlight?.origin}</span>
                    </p>
                  )
                }
              </div>
              {/* Flight Duration & Connection */}
              <div className="text-center flex w-full items-center">
                <div className="flex flex-col">
                  <p className="xl:text-sm lg:text-xs sm:text-sm xs:text-sm tracking-wide text-[#707FF5] font-medium">
                    {tripSummaryDetails?.selectedInboundFlight?.duration}
                  </p>
                  <div className="h-0.5 xs:w-20 bg-gradient-to-r from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76]"></div>
                  <p className="text-[#3CBC5C] tracking-wide xl:text-sm lg:text-xs sm:text-sm xs:text-sm font-medium">
                    Connect
                  </p>
                </div>
                <Plane
                  fill="#1E1E76"
                  className="text-[#161B49] xl:w-5 xl:h-5 lg:w-4 lg:h-4 xs:w-4 xs:h-4 rotate-45"
                />
              </div>
              {/* Destination */}
              <div className="flex w-full items-center gap-3">
                <div>
                  <h2 className="font-bold 2xl:text-lg xl:text-base lg:text-sm md:text-lg sm:text-base xs:text-sm text-[#1E1E76]">
                    {arr && arr.dayLabel}
                  </h2>
                  {
                    tripSummaryDetails?.selectedInboundFlight && (
                      <p className="xl:text-sm lg:text-sm sm:text-sm xs:text-sm text-[#1E1E76]">
                        {tripSummaryDetails?.selectedInboundFlight.arrival_time_ampm} | <span className="text-blue-600">{tripSummaryDetails?.selectedInboundFlight.destination}</span>
                      </p>
                    )
                  }
                </div>
              </div>
            </div>
            <p
              className={`2xl:text-sm xl:text-xs lg:text-xs md:text-sm sm:text-sm ${isOpen ? "xs:hidden" : "xs:text-sm"} text-[#FF3B3F]`}
            >
              *Free Cancellation within 24 hours of booking
            </p>
          </div>
        ) : (
          <div className="md:hidden xs:flex sm:flex w-full h-full justify-center items-center flex flex-col gap-2">
            <div className="flex w-1/6">
              <img
                src={tripSummaryDetails?.selectedInboundFlight?.supplier_logo}
                alt={tripSummaryDetails?.selectedInboundFlight?.airline}
                className="w-full h-full rounded-full"
              />
            </div>
            <div className="flex flex-row text-xl">
              <div className="text-[#FF3B3F] font-semibold">Departure&nbsp;</div>
              <div className="text-[#FF3B3F] font-semibold">{dep1 && dep1.dayLabel}</div>
            </div>
            <div className="flex flex-row gap-2 w-full p-2">
              <div className="flex flex-col w-3/6 justify-center items-center gap-1">
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-[#080236] font-semibold mx-auto w-full text-start text-base">
                    {dep1 && dep1.dayLabel}
                  </div>
                  <div className="text-[#707FF5] mx-auto w-full text-start text-sm">
                    Departure - {dep1 && dep1.dayLabel}
                  </div>
                </div>
                <div className="flex items-center w-4/6">
                  <div className="flex flex-col justify-center items-center gap-0.5">
                    <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />

                  </div>
                  <div className="flex text-sm">{firstSegment?.duration}</div>
                </div>
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-[#080236] font-semibold mx-auto w-full text-start text-base">
                    {arr1 && arr1.dayLabel}
                  </div>
                  <div className="text-[#707FF5] mx-auto w-full text-start text-sm">
                    Departure - {arr1 && arr1.dayLabel}
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-1 justify-between w-4/6">
                <div className="flex flex-col w-full gap-0.5">
                  <div className="text-sm font-semibold">
                    {firstSegmentOriginDisplay}
                  </div>
                  <div className="text-xs text-[#B4BBE8]">
                    {flightDescription1}
                  </div>
                </div>
                <div className="flex flex-col w-full gap-0.5">
                  <div className="text-sm font-semibold">
                    {lastSegmentOriginDisplay}
                  </div>
                  <div className="text-xs text-[#B4BBE8]">
                    {flightDescription2}
                  </div>
                </div>
              </div>
            </div>
            {
              isConnecting && (
                <>
                  <div className="flex w-full">
                    <div className="flex items-center gap-3 bg-blue-50 p-2 rounded-lg border border-[#B4BBE8] w-full">
                      <svg
                        className="w-4 h-4 text-blue-500"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M13 16h-1v-4h-1m0-4h.01M21 12c0-4.418-3.582-8-8-8s-8 3.582-8 8 3.582 8 8 8 8-3.582 8-8z"
                        ></path>
                      </svg>
                      <p className="text-xs text-[#4B4BC3] font-medium">
                        {`${layoverStr} ${lastSegmentOriginDisplay}`}
                      </p>
                    </div>
                  </div>
                </>
              )
            }

            <div className="flex flex-row gap-2 w-full p-2">
              <div className="flex flex-col w-3/6 justify-center items-center gap-1">
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-[#080236] font-semibold mx-auto w-full text-start text-base">
                    {dep2 && dep2.dayLabel}
                  </div>
                  <div className="text-[#707FF5] mx-auto w-full text-start text-sm">
                    Departure - {dep2 && dep2.dayLabel}
                  </div>
                </div>
                <div className="flex items-center w-4/6">
                  <div className="flex flex-col-reverse justify-center items-center gap-0.5">
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/Destinations/connecting.svg"
                      alt="Landing"
                    />
                  </div>
                  <div className="flex text-sm">{lastSegment?.duration}</div>
                </div>
                <div className="flex flex-col w-full mx-auto">
                  <div className="text-[#080236] font-semibold mx-auto w-full text-start text-base">
                    {arr2 && arr2.dayLabel}
                  </div>
                  <div className="text-[#707FF5] mx-auto w-full text-start text-sm">
                    Arrival - {arr2 && arr2.dayLabel}
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-1 justify-between w-4/6">
                <div className="flex flex-col gap-0.5">
                  <div className="text-sm font-semibold">
                    {lastSegmentOriginDisplay}
                  </div>
                  <div className="text-xs text-[#B4BBE8]">
                    {flightDescription2}
                  </div>
                </div>
                <div className="flex flex-col gap-0.5">
                  <div className="text-sm font-semibold">
                    {lastSegmentDestDisplay}
                  </div>
                  <div className="text-xs text-[#B4BBE8]">
                    {flightDescription2}
                  </div>
                </div>
              </div>
            </div>
            <div className="text-[#FF3B3F] text-sm">
              *Free Cancellation within 24 hours of booking
            </div>
          </div>
        )}
        <div className="flex flex-row w-full">
          <div className="flex xs:w-1/2 sm:w-1/2 justify-end items-center md:hidden">
            <ChevronDown
              onClick={() => setIsOpen((prev) => !prev)}
              className="text-[#161B49] w-5 h-5 cursor-pointer"
            />
          </div>
          <div className="sm:w-full xs:w-1/2 flex flex-col justify-end text-right md:hidden">
            <p className="text-[#4B4BC3] 2xl:text-xl xl:text-lg sm:text-base xs:text-base font-bold">
              $ 235
            </p>
            <p className="text-[#B4BBE8] xl:text-sm sm:text-xs xs:text-xs">
              Trip Total
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InboundFlight;
