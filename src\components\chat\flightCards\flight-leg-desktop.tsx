"use client";

import type React from "react";
import { ChevronDown } from "lucide-react";
import FlightSegmentDetails from "./flight-segment-details";
import FlightPricing from "./flight-pricing";

import { AppState } from "@/store/store";
import { useSelector } from "react-redux";
import Image from "next/image";

interface Flight {
  id: string;
  supplier_logo: string;
  departure_date: string;
  departure_time_ampm: string;
  arrival_date: string;
  arrival_time_ampm: string;
  duration: string;
  origin: string;
  destination: string;
  segments: any[];
  price: { amount: number };
}

interface FlightLegDesktopProps {
  label: string;
  flightData: Flight;
  type: string;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  getAirportDisplayName: (code: string, options: any) => string;
  airportOptions: any;
  onSelectFlight: () => void;
  isSelected: boolean;
}

const FlightLegDesktop: React.FC<FlightLegDesktopProps> = ({
  label,
  flightData,
  type,
  isOpen,
  setIsOpen,
  getAirportDisplayName,
  airportOptions,
  onSelectFlight,
  isSelected,
}) => {
  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);

  const isDirect = flightData.segments.length === 1;
  const amount = flightData.price;

  return (
    <div className="relative">
      {/* Header with chevron */}
      {/* <div className="flex justify-end items-start px-4 pt-4">
        
      </div> */}

    
        <div className="px-4 py-4 relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center md:gap-8">
              <div className="grid">
                <p className="text-[#999999] font-medium text-sm md:text-base w-24 uppercase">
                  {type}
                </p>
                <div className="grid grid-cols-2 gap-0">
                  <Image
                    alt="airline logo"
                    className=""
                    src={flightData.supplier_logo}
                    height={30}
                    width={30}
                  />
                </div>
              </div>
              {/* <div className="flex items-center gap-24">
                                <div>
                                    <h2 className="font-bold text-xl text-[#1E1E76]">
                                        {flightData.departure_date}
                                    </h2>
                                    <p className="text-base font-medium text-[#1E1E76]">
                                        {flightData.departure_time_ampm} |{" "}
                                        <span className="text-[#707FF5] font-medium">
                                            {getAirportDisplayName(flightData.origin, airportOptions)}
                                        </span>
                                    </p>
                                </div>

                                <div className="flex flex-col items-center relative">
                                    <div className="text-[#707FF5] font-medium text-base relative top-2 ">
                                        {flightData.duration}
                                    </div>
                                    <div className="flex items-center">
                                        <img
                                            src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/flight-travel.svg"
                                            alt="air travel"
                                        />
                                    </div>
                                    <p className="text-[#24C72F] text- relative bottom-2 ">
                                        {isDirect
                                            ? "Connect"
                                            : `${flightData.segments.length - 1} Stop${flightData.segments.length - 1 > 1 ? "s" : ""}`}
                                    </p>
                                </div>

                                <div>
                                    <h2 className="font-bold text-xl text-[#1E1E76]">
                                        {flightData.arrival_date}
                                    </h2>
                                    <p className="text-base text-[#1E1E76]">
                                        {flightData.arrival_time_ampm} |{" "}
                                        <span className="text-[#707FF5]">
                                            {getAirportDisplayName(flightData.destination, airportOptions)}
                                        </span>
                                    </p>
                                </div>
                            </div> */}

              <div className="flex  gap-2">
                <div className="grid">
                  <p className="font-semibold">
                    {flightData.departure_time_ampm}
                  </p>
                  <p className="text-[#999999] text-center">
                    {getAirportDisplayName(flightData.origin, airportOptions)}
                  </p>
                </div>
                <div className="grid md:min-w-[20rem]">
                  <p className="text-[#999999] text-center">{flightData.duration}</p>
                  <div className="flex items-center justify-center relative">
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <div className="flex-1 border-t border-dashed border-gray-400 relative">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="15"
                          viewBox="0 0 16 15"
                          fill="none"
                        >
                          <path
                            d="M10.2898 8.5L6.5 14.5L5 14.5L6.8945 8.5L2.8745 8.5L1.625 10.75L0.500001 10.75L1.25 7.375L0.500002 4L1.625 4L2.87525 6.25L6.89525 6.25L5 0.25L6.5 0.25L10.2898 6.25L14.375 6.25C14.6734 6.25 14.9595 6.36853 15.1705 6.57951C15.3815 6.79048 15.5 7.07663 15.5 7.375C15.5 7.67337 15.3815 7.95952 15.1705 8.1705C14.9595 8.38148 14.6734 8.5 14.375 8.5L10.2898 8.5Z"
                            fill="#1E1E76"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  </div>
                  <p className="text-[#999999] text-center">
                    {isDirect
                      ? "Connect"
                      : `${flightData.segments.length - 1} Stop${flightData.segments.length - 1 > 1 ? "s" : ""}`}
                  </p>
                </div>
                <div className="grid">
                  <p className="font-semibold">
                    {flightData.arrival_time_ampm}
                  </p>
                  <p className="text-[#999999] text-center">
                    {getAirportDisplayName(flightData.destination,airportOptions)}
                  </p>
                </div>
              </div>
            </div>
            <div
          className="p-2 border border-gray-300 rounded-full cursor-pointer"
          onClick={() => setIsOpen(!isOpen)}
        >
          <ChevronDown
            className={`w-4 h-4 text-brand transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
          />
        </div>
          </div>
          {/* <FlightPricing amount={amount} /> */}
        </div>
      {isOpen && (
        /* Expanded Desktop View */
        <div className="px-4 pb-4 border-t py-4 bg-gray-50">

          <FlightSegmentDetails
            segments={flightData.segments}
            getAirportDisplayName={getAirportDisplayName}
            airportOptions={airportOptions}
          />

          {/* <FlightPricing
            amount={amount}
            isExpanded={true}
            showCancellation={false}
          /> */}
        </div>
      )}
    </div>
  );
};

export default FlightLegDesktop;
