import React, { useState } from "react";
import InputField from "@/components/input/InputField";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/input/Select";

interface PaymentCardFormProps {
  onCancel: () => void;
}

const PaymentCardForm: React.FC<PaymentCardFormProps> = ({ onCancel }) => {
  // Form state (optional, can be added for real functionality)
  const [form, setForm] = useState({
    cardholderName: "",
    cardNumber: "",
    expiryDate: "",
    userName: "",
    billingAddress: ""
  });

  const [billingAddress, setBillingAddress] = useState([
    {
      address: "Address 1"
    },
    {
      address: "Address 2"
    }
  ]);
  const [selectedBillingAddress, setSelectedBillingAddress] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  return (
    <div className="w-full mx-auto py-8">
      <h2 className="text-md md:text-2xl font-bold text-[#4B4BC3] mb-8">Add New Payment Card</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        <InputField
          label="Cardholder Name"
          placeholder="User Cardholder Name"
          name="cardholderName"
          value={form.cardholderName}
          onChange={handleChange}
        />
        <InputField
          label="Card Number"
          placeholder="User Card Number"
          name="cardNumber"
          value={form.cardNumber}
          onChange={handleChange}
        />
        <InputField
          label="Expiry Date"
          placeholder="MM/YYYY"
          name="expiryDate"
          value={form.expiryDate}
          onChange={handleChange}
        />
      </div>
      <h2 className="text-md md:text-2xl font-bold text-[#4B4BC3] mb-8">Choose Billing Address</h2>
      <div className="flex flex-row gap-4 mb-10">
      <Select value={selectedBillingAddress} onValueChange={setSelectedBillingAddress}>
        <SelectTrigger>
          <SelectValue placeholder="Choose Billing Address" />
        </SelectTrigger>
        <SelectContent>
          {billingAddress.map(address => (
            <SelectItem value={address.address}>{address.address}</SelectItem>
          ))}
        </SelectContent>
      </Select>
      </div>
      <div className="flex gap-4 mt-8">
        <Button className="px-8 py-2 rounded-full bg-[#4B4BC3] text-white text-lg font-semibold">Update</Button>
        <Button
          type="button"
          className="px-8 py-2 rounded-full bg-[#E6E3FF] text-[#4B4BC3] text-lg font-semibold border border-[#4B4BC3]"
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default PaymentCardForm;
