import { format } from "date-fns";
import Markdown from "react-markdown";
import { motion } from "framer-motion";
import { imageFadeIn } from "@/utils/motion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import NewFlightCard from "./NewFlightCard";
import { ChatReactionBar } from "./ChatReactionBar";
import { useState, useEffect } from "react";

export const ChatMessages = ({
  chatMessages,
  streamingMessage,
  isStreaming,
  handleReactionSelect,
  messagesEndRef,
}: {
  chatMessages: any[];
  streamingMessage: string;
  isStreaming: boolean;
  handleReactionSelect: (reaction: string, messageId: string) => void;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}) => {
  const [current, setCurrent] = useState(0);
  const [api, setApi] = useState<any>();

  // Setup carousel navigation
  useEffect(() => {
    if (!api) return;
    setCurrent(api.selectedScrollSnap());
    api.on("select", () => setCurrent(api.selectedScrollSnap()));
  }, [api]);

  // Scroll to bottom whenever new messages or streaming tokens appear
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages, streamingMessage]);

  return (
    <div className="flex-1 overflow-y-auto md:px-6 md:pt-4 pb-[120px]">
      <div className="h-full overflow-y-auto px-6 py-4 ">
        <div className="max-w-4xl mx-auto">
          {/* Placeholder when no messages */}
          {chatMessages.length === 0 && (
            <div className="mt-2">
              <div className="absolute bottom-80 left-1/2 -translate-x-1/2 text-center">
                <motion.div
                  initial="hidden"
                  whileInView="show"
                  variants={imageFadeIn(0, 0)}
                  className="flex"
                >
                  <video
                    autoPlay
                    muted
                    loop
                    className="w-full md:h-[207px] h-[120px]"
                  >
                    <source
                      className="w-full h-full"
                      src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/SHASA%20face%20AI%20640x640.mp4"
                      type="video/mp4"
                    />
                  </video>
                </motion.div>
                <h2 className="text-[14px] md:text-[30px] font-bold text-[#2a1a6e]">
                  Ready to Explore?
                </h2>
              </div>
            </div>
          )}

          {/* Render each chat message */}
          {chatMessages.map((message, index) => {
            const timestamp =
              message.timestamp &&
              format(new Date(message.timestamp), "hh:mma");

            // If this message contains flight cards
            if (message.type === "flights" && message.flights?.length > 0) {
              return (
                <div key={index} className="mt-6">
                  <div className="flex items-start">
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA-face-AI.svg"
                      alt="AI"
                      className="w-[26px] h-[26px] rounded-full mr-2"
                    />
                    <div className="bg-[#E6E3FF] rounded-lg px-5 py-2">
                      <p className="text-sm font-medium">Best Flights for you..</p>
                      <div className="text-[10px] text-right text-[#707ff5] mt-1">
                        {timestamp}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Carousel setApi={setApi}>
                      <CarouselContent>
                        {message.flights.slice(0, 3).map((flight: any, idx: number) => (
                          <CarouselItem key={idx} className="basis-full mt-4">
                            <NewFlightCard
                              flight={flight}
                              routing_id={message.routingId}
                              isRecommended={idx === 0}
                            />
                          </CarouselItem>
                        ))}
                      </CarouselContent>
                      <div className="flex justify-between items-center gap-4">
                        <CarouselPrevious className="text-[#4B4BC3] border-none shadow-none" />
                        <div className="flex justify-center gap-2">
                          {message.flights.slice(0, 3).map((_: any, dotIdx: number) => (
                            <button
                              key={dotIdx}
                              className={`w-2 h-2 rounded-full ${
                                current === dotIdx ? "bg-[#5C55FE] w-6" : "bg-gray-300"
                              }`}
                              onClick={() => setCurrent(dotIdx)}
                            />
                          ))}
                        </div>
                        <CarouselNext className="text-[#4B4BC3] border-none shadow-none" />
                      </div>
                    </Carousel>
                  </div>
                </div>
              );
            }

            // Standard “AI” message
            if (message.sender === "ai") {
              return (
                <div
                  key={index + message.timestamp}
                  className="mb-4 flex items-start"
                >
                  <img
                    src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA-face-AI.svg"
                    alt="AI"
                    className="w-[26px] h-[26px] rounded-full mr-2"
                  />
                  <div className="relative group">
                    <div className="bg-[#E6E3FF] rounded-lg px-5 py-2 text-sm max-w-[700px]">
                      <Markdown>{message.text}</Markdown>
                      <div className="text-[10px] text-right text-[#707ff5] mt-1">
                        {timestamp}
                      </div>
                    </div>
                    <div className="absolute bottom-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                      <ChatReactionBar
                        onSelect={handleReactionSelect}
                        messageId={message?.messageId}
                        copyText={message.text}
                      />
                    </div>
                  </div>
                </div>
              );
            }

            // Standard “human” message
            return (
              <div
                key={index + message.timestamp}
                className="mb-4 flex justify-end"
              >
                <div className="bg-gradient-to-r from-[#4B4BC3] via-[#707FF5] to-[#A195F9] text-white rounded-lg px-5 py-2 max-w-[700px]">
                  <Markdown>{message.text}</Markdown>
                  <div className="text-[10px] text-right text-[#e9e8fc] mt-1">
                    {timestamp}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Streaming token display */}
          {isStreaming && streamingMessage && (
            <div className="flex items-start mt-4">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/SHASA-face-AI.svg"
                alt="AI"
                className="w-[26px] h-[26px] rounded-full mr-2"
              />
              <div className="bg-[#E6E3FF] rounded-lg px-5 py-2 text-sm max-w-[700px]">
                <Markdown>{streamingMessage}</Markdown>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>
    </div>
  );
};
