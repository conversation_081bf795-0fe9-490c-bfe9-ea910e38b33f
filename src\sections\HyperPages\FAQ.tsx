"use client";

import React, { useState } from "react";
import Footer from "./Footer";
import { faqData } from "@/constants/faqData";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { ChevronDown, ChevronUp } from "lucide-react";
import Navbar from "./Navbar";

const FAQPage: React.FC = () => {
  const [openItem, setOpenItem] = useState("faq-0");

  const toggleItem = (item: string) => {
    setOpenItem((prev) => (prev === item ? "" : item));
  };

  return (
    <div className="flex flex-col min-h-screen bg-white text-[#0e0b2b] font-proxima-nova">
      <div
        className="w-full min-h-screen bg-cover bg-center relative flex flex-col"
        style={{
          backgroundImage:
            "url('https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png')",
        }}
      >
        <Navbar />
        <div className="flex-grow flex w-[85%] mx-auto justify-between items-center">
          <h1 className="text-3xl md:text-5xl font-bold leading-snug bg-gradient-to-r from-[#707FF5] via-[#A195F9] to-[#F2A1F2] text-transparent bg-clip-text text-center md:text-left max-w-full md:max-w-[60%]">
            Got a Travel Question?
          </h1>
          <img
            src="https://storage.googleapis.com/nxvoytrips-img/HyperPages/FAQ.png"
            alt="FAQ Letters"
            className="max-w-[46%] h-auto"
          />
        </div>
      </div>
      <section className="w-[85%] mx-auto py-16">
        <h2 className="text-3xl font-bold mb-4 text-[#080236]">
          Your Travel Questions, Answered
        </h2>
        <p className="text-lg text-gray-400 mb-10">
          Browse our FAQs or connect with us directly through{" "}
          <a href="/contact" className="underline">
            Contact Us
          </a>{" "}
          or WhatsApp — we're here to help!
        </p>
        <Accordion
          type="single"
          collapsible
          value={openItem}
          onValueChange={setOpenItem}
          className="space-y-4"
        >
          {faqData.map((faq, idx) => (
            <AccordionItem
              key={idx}
              value={`faq-${idx}`}
              className="border border-gray-200 rounded-lg px-6 py-4"
            >
              <button
                onClick={() => toggleItem(`faq-${idx}`)}
                className="flex justify-between items-center w-full focus:outline-none"
              >
                <span className="text-2xl font-semibold text-left text-brand-black">
                  {idx + 1}. {faq.question}
                </span>
                <div className="ml-auto w-9 h-9 border border-[#0e0b2b] rounded-full flex items-center justify-center">
                  {openItem === `faq-${idx}` ? (
                    <ChevronUp size={24} className="text-[#0e0b2b]" />
                  ) : (
                    <ChevronDown size={24} className="text-[#0e0b2b]" />
                  )}
                </div>
              </button>
              {openItem === `faq-${idx}` && (
                <AccordionContent className="text-lg leading-relaxed mt-4 text-brand-black">
                  {faq.answer}
                </AccordionContent>
              )}
            </AccordionItem>
          ))}
        </Accordion>
      </section>
      <Footer />
    </div>
  );
};

export default FAQPage;
