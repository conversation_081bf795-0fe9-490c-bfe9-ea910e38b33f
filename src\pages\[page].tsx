import { useRouter } from "next/router";
import About from "@/sections/HyperPages/AboutUs";
import ContactUs from "@/sections/HyperPages/ContactUs";
import FAQ from "@/sections/HyperPages/FAQ";
import TermsAndConditions from "@/sections/HyperPages/TermsOfService";
import PrivacyPolicy from "@/sections/HyperPages/PrivacyPolicy";
import MemberShip from "@/sections/HyperPages/MemberShip";
import ComingSoon from "@/sections/HyperPages/ComingSoon";

const InfoPage = () => {
  const { query } = useRouter();
  const page = query.page as string;

  if (!page) return null;

  switch (page.toLowerCase()) {
    case "about-us":
      return <About />;
    case "contact-us":
      return <ContactUs />;
    case "membership":
      return <MemberShip />;
    case "faq":
      return <FAQ />;
    case "terms-of-service":
      return <TermsAndConditions />;
    case "privacy-policy":
      return <PrivacyPolicy />;
    case "coming-soon":
      return <ComingSoon />;
    default:
      return (
        <div className="p-8 text-red-500 text-xl font-semibold">
          404 - Page Not Found
        </div>
      );
  }
};

export default InfoPage;
