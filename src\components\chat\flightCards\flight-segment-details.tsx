import React from "react"
import { InfoIcon, PlaneTakeoff, PlaneLanding } from "lucide-react"

interface Segment {
    origin: string
    destination: string
    departure_time_ampm: string
    departure_date: string
    arrival_time_ampm: string
    arrival_date: string
    duration: string
    operator: string
    operator_code: string
    flight_number: string
    travel_class: { class: string }
    wait_time?: string
}

interface FlightSegmentDetailsProps {
    segments: Segment[]
    getAirportDisplayName: (code: string, options: any) => string
    airportOptions: any
    isMobile?: boolean
}

const FlightSegmentDetails: React.FC<FlightSegmentDetailsProps> = ({
    segments,
    getAirportDisplayName,
    airportOptions,
    isMobile = false,
}) => {
    const timeTextSize = isMobile ? "text-lg" : "text-xl"
    const detailTextSize = isMobile ? "text-xs" : "text-sm"
    const minWidth = isMobile ? "min-w-[80px]" : "min-w-[100px]"
    const layoverMargin = isMobile ? "ml-[100px]" : "ml-[120px]"

    return (
        <div className="space-y-4">
            {segments.map((segment, i) => (
                <React.Fragment key={i}>
                    <div className="flex gap-4">
                        <div className={`flex flex-col ${minWidth}`}>
                            <div className="mb-4">
                                <div className={`${timeTextSize} font-semibold`}>{segment.departure_time_ampm}</div>
                                <div className={`${detailTextSize} text-[#999999]`}>Departure - {segment.departure_date}</div>
                            </div>
                            <div>
                                <div className={`${timeTextSize} font-semibold`}>{segment.arrival_time_ampm}</div>
                                <div className={`${detailTextSize} text-[#999999]`}>Arrival - {segment.arrival_date}</div>
                            </div>
                        </div>

                        {/* <div className="flex flex-col items-center justify-center px-3">
                            <img src="https://storage.googleapis.com/nxvoytrips-img/Destinations/takeOff2.svg" alt="takeoff" />
                        </div> */}
                        <div className="flex flex-col items-center">
                        <PlaneTakeoff className="w-5 h-5 text-black" />
                        <div className="border-l-2 border-[#0000001A] flex-1 my-2"></div>
                        <PlaneLanding className="w-5 h-5 text-black mb-2" />
                    </div>

                        <div className="flex flex-col justify-between flex-1">
                            <div>
                                <div className={`${timeTextSize} font-bold `}>
                                    {getAirportDisplayName(segment.origin, airportOptions)}
                                </div>
                                <div className={`${detailTextSize} text-[#999999]`}>
                                    {segment.operator} · {segment.travel_class.class} · {segment.operator_code} {segment.flight_number}
                                </div>
                                <div className={`text-base text-[#999999] mt-1`}>{segment.duration}</div>
                            </div>
                            <div className="mt-4">
                                <div className={`${timeTextSize} font-semibold text-[#161B49]`}>
                                    {getAirportDisplayName(segment.destination, airportOptions)}
                                </div>
                            </div>
                        </div>
                    </div>

                    {i < segments.length - 1 && (
                        <div className={`flex items-center gap-2 p-2 border border-[#B4BBE8] rounded-md w-fit ${layoverMargin}`}>
                            <InfoIcon className="h-4 w-4 text-[#4B4BC3]" />
                            <span className={`${detailTextSize} text-[#4B4BC3] font-medium`}>
                                {segments[i + 1].wait_time || "Layover"} at {segment.destination}
                            </span>
                        </div>
                    )}
                </React.Fragment>
            ))}
        </div>
    )
}

export default FlightSegmentDetails
