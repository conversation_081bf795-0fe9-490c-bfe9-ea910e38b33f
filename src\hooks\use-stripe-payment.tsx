"use client"

import { useState, useEffect } from "react"
import { useStripe, useElements, CardElement } from "@stripe/react-stripe-js"
import { useAuth } from "@/components/AuthProvider/auth-Provider"
import { useDispatch } from "react-redux"
import axios from "axios"
import type { Stripe, StripeElements } from "@stripe/stripe-js"
import { updateTripSummary } from "@/store/slices/tripSummary"

interface StripePaymentParams {
    stripe: Stripe | null
    elements: StripeElements | null
    token: string
    amount: number
    currency: string
    customerEmail: string
    customerName: string
}

interface PaymentResult {
    success: boolean
    message: string
    paymentIntentId?: string
    orderId?: string
}

export const useStripePayment = () => {
    const stripe = useStripe()
    const elements = useElements()
    const { token } = useAuth()
    const dispatch = useDispatch() // Use Redux dispatch directly in the hook

    const [message, setMessage] = useState("")
    const [success_message, setSuccessMessage] = useState("")
    const [loading, setLoading] = useState(false)
    const [isCardExpired, setIsCardExpired] = useState(false)
    const [hasActiveCard, setHasActiveCard] = useState(false)
    const [cardLast4, setCardLast4] = useState("")
    const [showCardForm, setShowCardForm] = useState(true)
    const [paymentMethodId, setPaymentMethodId] = useState("")
    const [cardTouched, setCardTouched] = useState(false)
    const [cardError, setCardError] = useState("")
    const [cardComplete, setCardComplete] = useState(false)
    const [isCardEmpty, setIsCardEmpty] = useState(true)

    const getStripePaymentMethods = async () => {
        try {
            console.log("Fetching Stripe payment methods...")
            const paymentMethods = await axios.post(
                `${process.env.NEXT_PUBLIC_PAYMENT_API_ENDPOINT}/api/v1/payment/list-stripe-payment-methods`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                },
            )
            console.log("Payment methods response:", paymentMethods.data)
            return paymentMethods?.data?.detail?.data?.payment_methods
        } catch (error) {
            console.error("Stripe payment methods error:", error)
            if (axios.isAxiosError(error)) {
                console.error("Response data:", error.response?.data)
                console.error("Response status:", error.response?.status)
            }
            return null
        }
    }

    const processStripePayment = ({
        stripe,
        elements,
        token,
        amount,
        currency,
        customerEmail,
        customerName,
    }: StripePaymentParams): Promise<PaymentResult> => {
        console.log("Starting Stripe payment process...")
        console.log("Payment details:", { amount, currency, customerEmail, customerName })

        if (!stripe || !elements) {
            console.error("Stripe not loaded properly")
            return Promise.resolve({
                success: false,
                message: "Stripe has not loaded properly. Please try again.",
            })
        }

        const cardElement = elements.getElement(CardElement)
        if (!cardElement) {
            console.error("Card element not found")
            return Promise.resolve({
                success: false,
                message: "Card element not found. Please try again.",
            })
        }

        if (showCardForm && !cardComplete) {
            console.error("Card details incomplete")
            return Promise.resolve({
                success: false,
                message: "Card details are incomplete. Please check and try again.",
            })
        }

        // Step 1: Create/Get Stripe customer
        console.log("Creating Stripe customer...")
        return axios
            .post(
                `${process.env.NEXT_PUBLIC_PAYMENT_API_ENDPOINT}/api/v1/payment/stripe-customer`,
                {
                    email: customerEmail,
                    name: customerName,
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                },
            )
            .then((customerResponse) => {
                console.log("Customer response:", customerResponse.data)
                const customerId = customerResponse?.data?.detail?.data?.customerId

                // Step 2: Create payment intent
                console.log("Creating payment intent...")
                return axios.post(
                    `${process.env.NEXT_PUBLIC_PAYMENT_API_ENDPOINT}/api/v1/payment/stripe-intent`,
                    {
                        customer_id: customerId,
                        amount,
                        currency,
                        payment_source: "flight",
                    },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `Bearer ${token}`,
                        },
                    },
                )
            })
            .then((intentResponse) => {
                console.log("Intent response:", intentResponse.data)
                const { clientSecret, paymentIntentId, orderId } = intentResponse?.data?.detail?.data

                console.log("Order ID from API:", orderId)
                console.log("Payment Intent ID:", paymentIntentId)

                if (showCardForm) {
                    // Step 3: Confirm card payment with new card
                    console.log("Confirming payment with new card...")
                    return stripe!
                        .confirmCardPayment(clientSecret, {
                            payment_method: {
                                card: cardElement,
                                billing_details: {
                                    name: customerName,
                                    email: customerEmail,
                                },
                            },
                            setup_future_usage: "off_session",
                        })
                        .then(({ paymentIntent, error }) => {
                            if (error) {
                                console.error("Payment confirmation error:", error)
                                return {
                                    success: false,
                                    message: error.message || "Payment failed",
                                }
                            }
                            console.log("Payment successful:", paymentIntent)
                            return {
                                success: true,
                                message: "Payment Authorized!",
                                paymentIntentId,
                                orderId,
                            }
                        })
                } else {
                    // Step 3: Confirm payment with existing card
                    console.log("Confirming payment with existing card...")
                    return stripe!
                        .confirmCardPayment(clientSecret, {
                            payment_method: paymentMethodId,
                            setup_future_usage: "off_session",
                        })
                        .then(({ paymentIntent, error }) => {
                            if (error) {
                                console.error("Payment confirmation error:", error)
                                return {
                                    success: false,
                                    message: error.message || "Payment failed",
                                }
                            }
                            console.log("Payment successful:", paymentIntent)
                            return {
                                success: true,
                                message: "Payment Authorized!",
                                paymentIntentId,
                                orderId,
                            }
                        })
                }
            })
            .catch((err) => {
                console.error("Payment process error:", err)
                if (axios.isAxiosError(err)) {
                    console.error("API Error Response:", err.response?.data)
                    console.error("API Error Status:", err.response?.status)
                }
                return {
                    success: false,
                    message: `Payment failed: ${err.response?.data?.message || err.message}`,
                }
            })
    }

    const validateStripePayment = async (tripSummaryDetails: any) => {
        console.log("=== STARTING PAYMENT VALIDATION ===")
        console.log("Current trip summary details:", tripSummaryDetails)

        const paymentErrors = []
        let validPayment = false

        setLoading(true)
        setMessage("")
        setSuccessMessage("")

        try {
            let totalAmount =
                tripSummaryDetails?.outboundTotal +
                tripSummaryDetails?.inboundTotal +
                (tripSummaryDetails?.selectedLuggageInfo?.totalPrice || 0)

            totalAmount = totalAmount + (totalAmount * (tripSummaryDetails?.serviceFee / 100))
            console.log("Payment validation details:", {
                amount: totalAmount,
                currency: tripSummaryDetails?.selectedOutboundFlight?.price?.currency,
                email: tripSummaryDetails?.paymentDetails?.email,
                name: `${tripSummaryDetails?.paymentDetails?.firstName || ""} ${tripSummaryDetails?.paymentDetails?.lastName || ""}`.trim(),
            })

            const result = await processStripePayment({
                stripe,
                elements,
                token: token ?? "",
                amount: totalAmount,
                currency: tripSummaryDetails?.selectedOutboundFlight?.price?.currency || "usd",
                customerEmail: tripSummaryDetails?.paymentDetails?.email || "<EMAIL>",
                customerName:
                    `${tripSummaryDetails?.paymentDetails?.firstName || ""} ${tripSummaryDetails?.paymentDetails?.lastName || ""}`.trim() ||
                    "Test User",
            })

            console.log("=== PAYMENT RESULT ===", result)

            if (result.success && result.orderId) {
                validPayment = true
                setSuccessMessage(result.message)

                console.log("=== UPDATING REDUX STATE ===")
                console.log("Order ID to dispatch:", result.orderId)
                console.log("Payment Intent ID to dispatch:", result.paymentIntentId)

                const updatedTripSummary = {
                    ...tripSummaryDetails,
                    paymentDetails: {
                        ...tripSummaryDetails?.paymentDetails,
                        orderId: result.orderId,
                        paymentIntentId: result.paymentIntentId,
                    },
                }

                console.log("Updated trip summary object:", updatedTripSummary)

                // Dispatch the update
                dispatch(updateTripSummary(updatedTripSummary))

                console.log("=== REDUX DISPATCH COMPLETED ===")
            } else {
                console.error("Payment failed or no order ID received")
                setMessage(result.message)
                paymentErrors.push(result.message)
            }
        } catch (error) {
            console.error("Payment validation error:", error)
            setMessage("An unexpected error occurred during payment processing.")
            paymentErrors.push("Payment validation failed")
        } finally {
            setLoading(false)
        }

        return { paymentErrors, validPayment }
    }

    useEffect(() => {
        const fetchCustomerId = async () => {
            if (!token) {
                console.log("No token available, skipping payment methods fetch")
                return
            }

            console.log("Fetching customer payment methods...")
            const paymentMethods = await getStripePaymentMethods()

            if (paymentMethods?.length > 0) {
                const card = paymentMethods.find((method: any) => method.type === "card")
                if (card) {
                    console.log("Found existing card:", card)
                    const expMonth = card?.card?.exp_month
                    const expYear = card?.card?.exp_year

                    const now = new Date()
                    const currentMonth = now.getMonth() + 1
                    const currentYear = now.getFullYear()
                    const isExpired = expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)

                    if (isExpired) {
                        console.log("Card is expired")
                        setIsCardExpired(true)
                        setHasActiveCard(true)
                        setShowCardForm(false)
                        setCardLast4(card?.card?.last4)
                        setPaymentMethodId(card?.id)
                        return
                    }

                    console.log("Card is valid, setting up existing card")
                    setPaymentMethodId(card?.id)
                    setHasActiveCard(true)
                    setCardLast4(card?.card?.last4)
                    setShowCardForm(false)
                } else {
                    console.log("No card found in payment methods")
                    setHasActiveCard(false)
                    setShowCardForm(true)
                }
            } else {
                console.log("No payment methods found")
                setHasActiveCard(false)
                setShowCardForm(true)
            }
        }
        fetchCustomerId()
    }, [token])

    return {
        stripe,
        elements,
        message,
        success_message,
        loading,
        isCardExpired,
        hasActiveCard,
        cardLast4,
        showCardForm,
        setShowCardForm,
        paymentMethodId,
        cardTouched,
        setCardTouched,
        cardError,
        setCardError,
        cardComplete,
        setCardComplete,
        isCardEmpty,
        setIsCardEmpty,
        validateStripePayment,
    }
}
