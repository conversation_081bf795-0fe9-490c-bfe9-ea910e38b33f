"use client"

import { useState, useEffect } from "react"
import { useSelector, useDispatch } from "react-redux"
import { useRouter } from "next/router"
import type { AppState } from "@/store/store"
import { updateTripSummary } from "@/store/slices/tripSummary"
import { initialFlightState } from "@/lib/utils/initialValues"

export const useTripSummaryState = () => {
    const router = useRouter()
    const [currentStep, setCurrentStep] = useState(1)
    const [baggageModal, setBaggageModal] = useState<boolean>(false)
    const [flightSeatModal, setFlightSeatModal] = useState<boolean>(false)
    const [isTransitioning, setIsTransitioning] = useState(false)

    const tripSummaryDetails = useSelector((state: AppState) => state.tripSummary)
    const dispatch = useDispatch()

    useEffect(() => {
        if (tripSummaryDetails?.passengerDetailsUpdated) {
            setCurrentStep(2)
        }

        if (
            (tripSummaryDetails?.bookingDetails && Object.keys(tripSummaryDetails?.bookingDetails).length > 0) ||
            !Object.keys(tripSummaryDetails?.flightSearchRespnse).length
        ) {
            dispatch(updateTripSummary(initialFlightState))
            router.push("/flights")
        }
    }, [])

    useEffect(() => {
        if (tripSummaryDetails?.outboundTravelers.length && !tripSummaryDetails?.passengerDetailsUpdated) {
            const initializePassengersState = () => {
                const empty: any = {
                    title: "",
                    firstName: "",
                    middleName: "",
                    lastName: "",
                    dob: "",
                    gender: "",
                    passportNumber: "",
                    passportCountry: "",
                    countryText: "",
                    selectedDob: "",
                    specialService: "",
                }
                dispatch(
                    updateTripSummary({
                        passengerDetails: Array.from({ length: tripSummaryDetails?.outboundTravelers.length }, () => ({
                            ...empty,
                        })),
                    }),
                )
            }
            initializePassengersState()
        }
    }, [tripSummaryDetails?.outboundTravelers])

    return {
        currentStep,
        setCurrentStep,
        baggageModal,
        setBaggageModal,
        flightSeatModal,
        setFlightSeatModal,
        isTransitioning,
        setIsTransitioning,
        tripSummaryDetails,
        dispatch,
        router,
    }
}
