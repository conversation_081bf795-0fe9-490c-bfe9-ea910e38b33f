"use client";

import * as React from "react";
import { useState } from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  ChevronsUpDown,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  ClipboardListIcon,
  FileIcon,
  DatabaseIcon,
  SquarePen,
  Loader,
  MessageCircle,
  Plane,
  X,
} from "lucide-react";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import { CHAT_CONSTANTS } from "@/constants/chat";
import { ChatMessage, ChatScreenProps, InitialMessage } from "@/types/chat";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

import { NavMain } from "./NavMain";
import { ChatHistory } from "./ChatHistory";
import { NavUser } from "./NavUser";
import { useIsMobile } from "@/hooks/use-mobile";
import { useRouter } from "next/router";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import Image from "next/image";
import { useIsCollapsed } from "@/hooks/sidebar/useIsCollapsed";
import { useCustomSession } from "@/hooks/use-custom-session";
import { updateCurrentThreadInfo } from "@/store/slices/chatThread";
import { NEW_THREAD_DEFAULT_NAME } from "@/constants/chat";
import { toast } from "@/hooks/use-toast";
import { AppState } from "@/store/store";
import { usePathname } from "next/navigation";
import { ErrorModal } from "../chat/errorModal/error-modal";

// For now, we'll handle these through a simple state or create a UI slice later
// You can create a UI slice for loginModal if needed

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const isMobile = useIsMobile();
  const dispatch = useDispatch();
  const router = useRouter();
  const { data: session } = useCustomSession();
  const token = session?.accessToken;
  const isCollapsed = useIsCollapsed();
  const { toggleSidebar } = useSidebar();

  const [isCreating, setIsCreating] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);

  // Get any needed state from Redux instead of context
  const chatThreadDetails = useSelector((state: AppState) => state.chatThread);

  const handleNewChat = async () => {
    if (isCreating) return;
    setIsCreating(true);

    try {
      if (!token) {
        setLoginModalOpen(true);
        return;
      }

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/thread/generate?_new=true`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to generate thread");
      }

      console.log("new thread Id created=======", response);
      const newThreadId = response.data.detail.data.thread_id;

      dispatch(
        updateCurrentThreadInfo({
          currentChatPageThreadId: newThreadId,
          currentThreadName: NEW_THREAD_DEFAULT_NAME,
          history: false,
          newThread: true,
          showMoreFlightsOption: false,
          chatResult: {},
          allFlights: {},
          tripType: "",
        })
      );

      // Navigate to the new chat thread
      router.push(`/chat/${newThreadId}`);
    } catch (err: any) {
      console.error("Failed to start new chat:", err?.response?.data?.detail?.message.includes("You've reached the maximum number of conversations (10) for a free account. Please upgrade your account for unlimited conversations"));
      if(err?.response?.data?.detail?.message.includes("You've reached the maximum number of conversations (10) for a free account. Please upgrade your account for unlimited conversations")){
        console.log("10-chat-reached");
        setChatErrorType('10-chat-reached')
        setIsErrorModalOpen(true);
        setCurrentErrorImage('https://storage.googleapis.com/nxvoytrips-img/ChatPage/ChatError/thread-limit-reached.svg');
        setCurrentErrorMessage(`But don’t worry - we can still plan more adventures together. Just upgrade your membership to unlock unlimited conversations with me. `);
      } else {
        setIsErrorModalOpen(true);
      }
      // if (axios.isAxiosError(err) && err.response?.status === 429) {
      //   toast({
      //     title: "Too many requests",
      //     description:
      //       "You're creating threads too quickly. Please wait a second and try again.",
      //   });
      // } else {
      //   toast({
      //     title: "Unable to start a new chat",
      //     description: "Something went wrong. Please retry in a moment.",
      //     variant: "destructive",
      //   });
      // }
    } finally {
      setIsCreating(false);
    }
  };

  const pathname = usePathname();
  console.log("app pathname", pathname);
  const [message, setMessage] = useState("");
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);

  const [chatErrorType, setChatErrorType] = useState("");
  const [currentErrorImage, setCurrentErrorImage] = useState(
    CHAT_CONSTANTS.DEFAULT_ERROR_IMAGE
  );
  const [currentErrorMessage, setCurrentErrorMessage] = useState(
    CHAT_CONSTANTS.DEFAULT_MESSAGES.ERROR_GENERAL
  );

  const [initialMessage, setInitialMessage] = useState<InitialMessage>({
    message: "",
    time: "",
  });

  return (
    <Sidebar
    
      collapsible="icon"
      {...props}
      className={`
        bg-[#080236] text-white
        ${props.className || ""}
        ${isMobile ? "bg-[#080236] data-[state=open]:w-full data-[state=open]:h-full data-[state=open]:fixed data-[state=open]:inset-0 data-[state=open]:z-50" : ""}
      `}
    >
      {/* Mobile backdrop overlay */}
      {isMobile && (
        <div className="absolute inset-0 data-[state=closed]:hidden bg-[#080236] border border-none" />
      )}

      {/* Sidebar content container */}
      <div
        className={`
        relative flex h-full w-full flex-col 
        ${isMobile ? "w-80 max-w-[100vw] ml-0 pr-0" : ""}
      `}
      >
        {isCollapsed ? (
          <div
            className="mb-4 justify-center flex pt-2 cursor-pointer mr-2"
            onClick={() => router.push("/chat")}
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="40" height="40" rx="20" fill="#1E1E76" />
              <path
                d="M31 10.0368V25.8757C31 28.1435 29.5562 30.1134 27.4066 30.7748C26.9112 30.9273 26.4088 31 25.912 31C24.2578 31 22.6827 30.1804 21.7187 28.7308L19.5678 25.498C19.5381 25.4538 19.5692 25.3954 19.6214 25.3954H21.7145C21.9615 25.3954 22.0843 25.0932 21.9093 24.9179L19.6849 22.6985C19.4535 22.4676 19.3236 22.1541 19.3236 21.8248V20.6845C19.3236 20.0616 18.8395 19.5328 18.2227 19.5171C17.5848 19.5 17.0626 20.0188 17.0626 20.6588V21.7663C17.0626 21.7663 17.0626 21.7692 17.0626 21.772V21.9032C17.0513 22.2168 16.92 22.5147 16.697 22.7356L14.484 24.9165C14.3061 25.0918 14.4289 25.3954 14.6773 25.3954H16.7648C16.9214 25.3954 17.0513 25.518 17.0626 25.6733V30.9629C17.0626 30.9701 17.0569 30.9758 17.0499 30.9758H10.0127C10.0056 30.9758 10 30.9701 10 30.9629V15.1925C10 12.7123 11.7289 10.55 14.1424 10.0896C16.1226 9.71185 18.1338 10.5457 19.2799 12.269L21.4717 15.5631C21.5225 15.64 21.4689 15.7427 21.3772 15.7427H19.2841C19.0371 15.7427 18.9143 16.0448 19.0893 16.2202L21.3137 18.4395C21.5451 18.6704 21.675 18.984 21.675 19.3133V20.4536C21.675 21.0779 22.1591 21.6053 22.7759 21.621C23.4138 21.6381 23.9374 21.1206 23.9374 20.4792V19.2819C23.9374 18.9512 24.0687 18.6348 24.303 18.4039L26.516 16.223C26.6939 16.0477 26.5711 15.7441 26.3227 15.7441H24.2352C24.0701 15.7441 23.9374 15.6087 23.9374 15.4433V14.772V10.0368C23.9374 10.0368 23.9431 10.024 23.9501 10.024H30.9873C30.9944 10.024 31 10.0297 31 10.0368Z"
                fill="white"
              />
            </svg>
          </div>
        ) : (
          <div className="flex pl-4 items-center justify-between">
            <div
            className="mb-6 justify-center flex pt-4 cursor-pointer"
            onClick={() => router.push("/")}
          >
            <Image
              alt="logo"
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Footer/Logo%20White.png"
              height={120}
              width={120}
            />
          </div>
          <div className="pr-4">
            <X className="text-white h-5" onClick={() => toggleSidebar()} />
          </div>
          </div>
        )}
        <SidebarHeader className="pt-2 bg-[#080236]">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                onClick={handleNewChat}
                disabled={isCreating}
                tooltip={"Start a New chat"}
                className={`h-10 rounded-xl data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed ${pathname?.includes("/chat") ? "bg-white text-[#1E1E76]" : "text-white"}`}
              >
                <div className="flex items-center justify-center rounded-lg bg-sidebar-primary">
                  {isCreating ? (
                    <Loader className="size-5  animate-spin" />
                  ) : (
                    <MessageCircle className="size-5" />
                  )}
                </div>
                <div className="grid flex-1 text-left text-sm font-medium leading-tight">
                  <p>Start a New chat</p>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                onClick={() => router.push("/flights")}
                tooltip="Quick Flight Search"
                className={`${pathname?.includes("/flights") ? "bg-white text-[#1E1E76]" : "text-white"} h-10 rounded-xl data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground`}
              >
                <div className="flex items-center justify-center rounded-lg bg-sidebar-primary">
                  <Plane className="size-5" />
                </div>
                <div className="grid flex-1 text-left text-sm font-medium leading-tight">
                  <p>Quick Flight Search</p>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent className="flex-1 overflow-y-auto">
          <ChatHistory />
        </SidebarContent>

        <SidebarFooter>
          <NavUser />
        </SidebarFooter>
      </div>

      <ErrorModal
        isOpen={isErrorModalOpen}
        onClose={() => {
          setIsErrorModalOpen(false);
          setInitialMessage({ message: "", time: "" });
          setMessage("");
        }}
        errorMessage={currentErrorMessage}
        errorImage={currentErrorImage}
        errorType={chatErrorType}
      />

      <SidebarRail />
    </Sidebar>
  );
}
