import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import NewFlightCard from "./NewFlightCard";

// src/components/chat/NewFlightCard.test.tsx

// --- Extracted getAirportDisplayName for unit testing ---
function getAirportDisplayName(airportCode: any, airportOptions: any): string {
  if (!airportOptions || typeof airportOptions !== "object") {
    return airportCode;
  }
  const airport = airportOptions[airportCode];
  if (airport) {
    return `${airportCode}, ${airport.airport_name}, ${airport.city_name_original}`;
  }
  return airportCode;
}

describe("getAirportDisplayName", () => {
  it("returns airportCode if airportOptions is undefined", () => {
    expect(getAirportDisplayName("JFK", undefined)).toBe("JFK");
  });

  it("returns airportCode if airportOptions is null", () => {
    expect(getAirportDisplayName("JFK", null)).toBe("JFK");
  });

  it("returns airportCode if airportOptions is not an object", () => {
    expect(getAirportDisplayName("JFK", "not-an-object")).toBe("JFK");
  });

  it("returns airportCode if airportOptions does not contain the code", () => {
    expect(getAirportDisplayName("JFK", { LAX: { airport_name: "Los Angeles", city_name_original: "Los Angeles" } })).toBe("JFK");
  });

  it("returns formatted string if airportOptions contains the code", () => {
    const airportOptions = {
      JFK: { airport_name: "John F Kennedy Intl", city_name_original: "New York" }
    };
    expect(getAirportDisplayName("JFK", airportOptions)).toBe("JFK, John F Kennedy Intl, New York");
  });
});

// --- Component test ---
const mockStore = configureStore([]);
const initialState = {
  chatThread: { chatResult: { output: { airport_data: { JFK: { airport_name: "John F Kennedy Intl", city_name_original: "New York" } } } } },
  tripSummary: { selectedOutboundFlight: null, selectedInboundFlight: null }
};

const flight = {
  rank: 1,
  outward_flight: {
    id: "1",
    airline: "Delta",
    airline_code: "DL",
    origin: "JFK",
    destination: "LAX",
    departure: "2024-06-01T10:00:00",
    arrival: "2024-06-01T13:00:00",
    duration: "5h",
    departure_time_ampm: "10:00 AM",
    arrival_time_ampm: "1:00 PM",
    segments: [],
    supplier_logo: "",
    price: { amount: 200, currency: "USD" },
    departure_date: "2024-06-01",
    arrival_date: "2024-06-01",
    cabin_class: "", // ensure this is always a string
    fare_basis: "",  // ensure this is always a string
    stops: 0         // add fallback for possible undefined number
  },
  return_flight: null
};

describe("NewFlightCard", () => {
  it("renders outbound flight label", () => {
    const store = mockStore(initialState);
    render(
      <Provider store={store}>
        <NewFlightCard
          flight={flight}
          routing_id="route1"
          showMoreFlightsOption={false}
          onMoreFlights={() => {}}
        />
      </Provider>
    );
    expect(screen.getAllByText(/outbound/i)).toHaveLength(1);
    expect(screen.getAllByText(/JFK/)).toHaveLength(1);
    expect(screen.getAllByText(/LAX/)).toHaveLength(1);
    expect(screen.getByText(/For all Passengers/)).toBeInTheDocument();
    
  });
});