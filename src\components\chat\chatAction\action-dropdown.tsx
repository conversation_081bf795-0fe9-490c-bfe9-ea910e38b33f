"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface ActionDropdownProps {
    onEdit: () => void
    onDelete: () => void
}

export function ActionDropdown({ onEdit, onDelete }: ActionDropdownProps) {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <div className="cursor-pointer">
                    <Ellipsis size={20} />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[160px]">
                <DropdownMenuItem onClick={onEdit}>
                    <Pencil className="mr-2 h-4 w-4" /> Rename Trip
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDelete}>
                    <Trash2 className="mr-2 h-4 w-4" /> Delete Chat
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
