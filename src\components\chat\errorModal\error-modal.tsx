"use client";

import { useEffect } from "react";
import { X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/router";

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  errorMessage?: string;
  errorImage?: string;
  spiralImage?: string;
  className?: string;
  errorType?: string;
}

export function ErrorModal({
  isOpen,
  onClose,
  errorMessage = "Oops! Something went wrong on my end. Try again in a moment - I'm fixing it!",
  errorImage = "https://storage.googleapis.com/nxvoytrips-img/HomeImgs/ErrorModal/shash-I.png",
  spiralImage = "https://storage.googleapis.com/nxvoytrips-img/HomeImgs/ErrorModal/Spiral%203.png",
  className = "",
  errorType = "",
}: ErrorModalProps) {
  const router = useRouter();

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div className="absolute inset-0 backdrop-blur-sm" onClick={onClose} />

      {/* Modal */}
      <div
        style={
          errorType === "50-message-limit"
            ? {
                backgroundImage:
                  "url('https://storage.googleapis.com/nxvoytrips-img/ChatPage/ChatError/Sasha-Style%20Error%20Message%20(Pop-up%20or%20Chat%20Bubble).svg')",
              }
            : {}
        }
        className={` relative w-full max-w-xl h-[290px]  mx-auto bg-white dark:bg-slate-900 rounded-3xl shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden transform transition-all duration-300 ease-out ${className} `}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className={`${errorType === "50-message-limit" ? "border-[#ffffff] border" : "border-[#1E1E76] border-2 "} absolute top-3 right-3 z-20 w-5 h-5 rounded-full  dark:border-slate-600 flex items-center justify-center  transition-colors duration-200 group`}
          aria-label="Close modal"
        >
          <X
            className={`${errorType === "50-message-limit" ? "text-white" : "text-[#1E1E76] group-hover:text-gray-800 dark:group-hover:text-gray-200"} w-5 h-5  dark:text-gray-400 `}
          />
        </button>

        {/* Content Container */}
        <div className="relative p-4">
          {/* Main Content - Horizontal Layout */}
          <div className="md:flex block items-center gap-6 relative">
            {/* Robot Image Section */}
            <div className="flex-shrink-0 relative">
              {/* Circular Container for Robot */}
              <div className="relative w-40 h-full">
                {/* Purple Circle Border */}
                <div className="" />

                {/* Robot Image */}
                <div className={`${errorType === "10-chat-reached" ? 'mt-2 mx-2' : ''} w-full h-full md:block hidden`}>
                  <Image
                    src={errorImage || "/placeholder.svg"}
                    alt="Error illustration"
                    width={200}
                    height={260}
                    className="w-full h-full object-cover scale-110"
                  />
                </div>
              </div>

              {/* Spiral positioned behind and extending from the robot */}

              {errorType !== "10-chat-reached" && (
                <div className="absolute -left-[100px] top-[0px] -z-10 w-[250%]">
                  <Image
                    src={spiralImage || "/placeholder.svg"}
                    alt=""
                    width={128}
                    height={260}
                    className="w-full h-full object-contain"
                  />
                </div>
              )}
            </div>

            {/* Error Message Section */}
            <div className="flex-1 pl-4">
              {errorType === "50-message-limit" && (
                <h1 className="text-white md:text-lg text-md font-bold mb-2">
                  Oh hey, quick heads up!
                </h1>
              )}
              {errorType === "10-chat-reached" && (
                <h1 className="text-lg font-bold mb-2 md:mr-0 mr-4">
                  Looks like you've used up all 10 free chats!
                </h1>
              )}
              <p
                className={`${errorType === "50-message-limit" ? "text-white md:text-lg" : "text-[#1E1E76] md:text-xl"} dark:text-[#F2F3FA] leading-relaxed font-medium text-md`}
              >
                {errorMessage}
              </p>

              {errorType === "50-message-limit" && (
                <div className="mt-4">
                  <div
                    onClick={() => router.push("/chat")}
                    style={{
                      background: `linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)`,
                    }}
                    className="rounded-full py-2 w-[200px] text-white text-center hover:cursor-pointer"
                  >
                    Start a New Chat
                  </div>
                </div>
              )}
              {errorType === "10-chat-reached" && (
                <div className="mt-4">
                  <div
                    onClick={() => router.push("/membership")}
                    style={{
                      background: `linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)`,
                    }}
                    className="rounded-full py-2 w-[200px] text-white text-center hover:cursor-pointer"
                  >
                    Upgrade Now
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
