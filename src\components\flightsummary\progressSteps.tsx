import React from "react";

type ProgressStepProps = {
  currentStep: number;
  steps: {
    id: number;
    label: string;
  }[];
};

const ProgressStepper = ({ currentStep, steps }: ProgressStepProps) => {
  return (
    <div className="flex flex-col">
      <div className="flex gap-8 justify-center items-center mb-6 text-sm">
        <div className="flex items-center space-x-2">
          <img src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Time.png" className="w-5 h-5" alt=""/>
          <span className="text-base">24-hour service</span>
        </div>
        <div className="flex items-center space-x-2">
          <img src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/Security.png" className="w-5 h-5" alt=""/>
          <span className="text-base">Secure payment</span>
        </div>
      </div>

      <div className="flex h-max justify-center items-center relative">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            {/* Step Circle and Label */}
            <div className="flex flex-col xs:w-16 md:w-20 lg:w-48 h-full gap-2 items-center">
              <div
                className={`w-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-10 lg:h-10 h-6 rounded-full flex items-center justify-center font-medium ${
                  currentStep >= step.id
                    ? "bg-[#24C72F] text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {step.id}
              </div>
              <div
                className={`sm:text-sm md:text-base lg:text-lg h-full xs:text-sm text-center font-medium ${
                  currentStep >= step.id ? "text-[#080236]" : "text-gray-600"
                }`}
              >
                {step.label}
              </div>
            </div>

            {/* Connector Line (except after last step) */}
            {index < steps.length - 1 && (
              <div
                className={`w-28 xs:w-20 h-0.5 relative -top-5 sm:-top-6 md:-top-6 lg:-top-3 ${
                  currentStep > step.id ? "bg-[#4B4BC3]" : "bg-gray-200"
                }`}
              ></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};
export default ProgressStepper;
