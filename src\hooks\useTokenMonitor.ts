import { useSession, signOut } from "next-auth/react";
import { useEffect, useRef, useCallback } from "react";

export function useTokenMonitor() {
  const { data: session, update, status } = useSession();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const tokenStartTimeRef = useRef<number | null>(null);
  const isMonitoringRef = useRef<boolean>(false);

  // Stable update function to avoid dependency issues
  const stableUpdate = useCallback(async () => {
    try {
      await update();
      // Reset start time after successful update
      tokenStartTimeRef.current = Date.now();
      console.log("✅ Token updated successfully, reset timer");
    } catch (error) {
      console.error("Failed to update token:", error);
    }
  }, [update]);

  useEffect(() => {
    // Only start monitoring if we have a valid session and aren't already monitoring
    if (
      status === "authenticated" &&
      session?.accessToken &&
      !isMonitoringRef.current
    ) {
      // Set the start time when we begin monitoring
      const startTime = Date.now();
      tokenStartTimeRef.current = startTime;
      isMonitoringRef.current = true;

      console.log("🎯 Starting token monitor...", {
        tokenDuration: session?.accessTokenExpireOn,
        startTime: new Date(startTime).toISOString(),
      });

      // Check token every 2 minutes
      intervalRef.current = setInterval(async () => {
        if (session?.accessTokenExpireOn && tokenStartTimeRef.current) {
          const tokenDurationMs = parseInt(session.accessTokenExpireOn) * 500;
          const expiryTime = tokenStartTimeRef.current + tokenDurationMs;
          const currentTime = Date.now();
          const timeUntilExpiry = expiryTime - currentTime;

          console.log("🔍 Token monitor check:", {
            tokenDuration: session.accessTokenExpireOn + "s",
            expiryTime: new Date(expiryTime).toISOString(),
            timeUntilExpiry: Math.floor(timeUntilExpiry / 1000) + "s",
            willRefreshSoon: timeUntilExpiry < 300000 && timeUntilExpiry > 0, // 5 minutes
            currentTime: new Date(currentTime).toISOString(),
          });

          // If token expires in less than 5 minutes, force an update
          if (timeUntilExpiry < 300000 && timeUntilExpiry > 0) {
            console.log("⏰ Token expiring soon, forcing update...");
            await stableUpdate();
          }

          // If token is already expired, sign out
          if (timeUntilExpiry <= 0) {
            console.log("💀 Token expired, signing out...");
            signOut({ callbackUrl: "/login" });
          }
        }
      }, 120000); // Check every 2 minutes (120 seconds)
    }

    // Clean up when session becomes invalid
    if (status !== "authenticated" || !session?.accessToken) {
      if (intervalRef.current) {
        console.log("🛑 Stopping token monitor...");
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        isMonitoringRef.current = false;
        tokenStartTimeRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        console.log("🛑 Stopping token monitor...");
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        isMonitoringRef.current = false;
      }
    };
  }, [status, session?.accessToken, stableUpdate]);

  // Handle logout on token error
  useEffect(() => {
    if (session?.error === "RefreshAccessTokenError") {
      console.log("❌ Token refresh failed, signing out...");
      signOut({ callbackUrl: "/login" });
    }
  }, [session?.error]);

  // Calculate time until expiry
  const getTimeUntilExpiry = () => {
    if (!session?.accessTokenExpireOn || !tokenStartTimeRef.current)
      return null;

    const tokenDurationMs = parseInt(session.accessTokenExpireOn) * 1000;
    const expiryTime = tokenStartTimeRef.current + tokenDurationMs;
    const timeUntilExpiry = expiryTime - Date.now();

    return Math.floor(timeUntilExpiry / 1000);
  };

  return {
    isTokenExpired: session?.error === "RefreshAccessTokenError",
    tokenExpiresIn: getTimeUntilExpiry(),
    hasValidToken: !!session?.accessToken && !session?.error,
    tokenStartTime: tokenStartTimeRef.current,
  };
}