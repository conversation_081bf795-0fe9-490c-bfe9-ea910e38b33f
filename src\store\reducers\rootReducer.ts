import { combineReducers } from "@reduxjs/toolkit";
import tripSummaryReducer from "../slices/tripSummary";
import loggedInUserReducer from "../slices/loggedInUser";
import chatThreadReducer from "../slices/chatThread";
import showMoreFlightsReducer from "../slices/showMoreFlights";
import authReducer from "../slices/authSlice";
import userDetailsReducer from "../slices/userDetails";
import hotelDetailsReducer from "../slices/hotelDetails";
import hotelBookingContextReducer from "../slices/hotelBookingContext";
import flightSearchFormReducer from "../slices/flightSearchSlice";
import flightResponseReducer from "../slices/flightResponse"
import flightJourneyReducer from "../slices/flightJourney"

import { actionTypes } from "../actionTypes";
import { REHYDRATE } from 'redux-persist';

const appReducer = combineReducers({
  tripSummary: tripSummaryReducer,
  auth: authReducer,
  loggedInUser: loggedInUserReducer,
  chatThread: chatThreadReducer,
  showMoreFlights: showMoreFlightsReducer,
  userDetails: userDetailsReducer,
  hotelDetails: hotelDetailsReducer,
  hotelBookingContext: hotelBookingContextReducer,
  flightSearchForm: flightSearchFormReducer,
  flightResponse: flightResponseReducer,
  flightJourney: flightJourneyReducer
});


// Track if we're in logout process
let isClearing = false;

const rootReducer = (state: any, action: any) => {
  if (action.type === actionTypes.RESET_STORE) {
    isClearing = true;
    state = undefined;
  }

  // Block rehydration if we just cleared the store
  if (action.type === REHYDRATE && isClearing) {
    console.log('Blocking rehydration after logout');
    isClearing = false;
    return appReducer(undefined, action);
  }

  // Reset the flag on any other action after clearing
  if (isClearing && action.type !== REHYDRATE) {
    isClearing = false;
  }

  return appReducer(state, action);
};

export default rootReducer;