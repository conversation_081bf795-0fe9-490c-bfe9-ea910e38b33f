import React from 'react'
import { AccordionProvider } from './AccordionProvider';

interface AccordionProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    value?: string;
}

const Accordion = ({
    value,
    children,
    ...props
}: AccordionProps) => {
    return (
        <ul {...props}>
            <AccordionProvider>
                {children}
            </AccordionProvider>
        </ul>
    )
}

export default Accordion