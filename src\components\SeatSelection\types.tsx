// types.ts
export type SeatType = 'economy' | 'premium' | 'business' | 'first';
export type SeatStatus = 'available' | 'selected' | 'booked' | 'blocked';

export interface Seat {
  id: string;
  row: number;
  letter: string;
  type: SeatType;
  status: SeatStatus;
  price?: number;
  features?: string[]; // e.g., 'window', 'aisle', 'extra-legroom'
}

export interface AircraftSeatMap {
  aircraftType: string;
  totalRows: number;
  seatsPerRow: number;
  cabinLayout: Cabin[];
}

export interface Cabin {
  class: SeatType;
  startRow: number;
  endRow: number;
  seatLetters: string[];
  configuration: string; // e.g., "3-3" for standard economy
}

export interface FlightSegment {
  id: string;
  departure: string;
  arrival: string;
  aircraftType: string;
  bookedSeats: string[]; // Array of seat IDs like "12A", "15B"
}