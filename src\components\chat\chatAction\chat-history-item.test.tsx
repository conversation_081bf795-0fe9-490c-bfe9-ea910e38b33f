import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { ChatHistoryItem } from "./chat-history-item";

// Mock ActionDropdown to test prop passing and handler calls
jest.mock("./action-dropdown", () => ({
    ActionDropdown: ({ onEdit, onDelete }: any) => (
        <div>
            <button onClick={onEdit}>Edit</button>
            <button onClick={onDelete}>Delete</button>
        </div>
    ),
}));

describe("ChatHistoryItem", () => {
    const threadName = "Test Thread";
    let onThreadClick: jest.Mock, onEdit: jest.Mock, onDelete: jest.Mock;

    beforeEach(() => {
        onThreadClick = jest.fn();
        onEdit = jest.fn();
        onDelete = jest.fn();
    });

    it("renders the thread name", () => {
        render(
            <ChatHistoryItem
                threadName={threadName}
                onThreadClick={onThreadClick}
                onEdit={onEdit}
                onDelete={onDelete}
            />
        );
        expect(screen.getByText(threadName)).toBeInTheDocument();
    });

    it("calls onThreadClick when thread name is clicked", () => {
        render(
            <ChatHistoryItem
                threadName={threadName}
                onThreadClick={onThreadClick}
                onEdit={onEdit}
                onDelete={onDelete}
            />
        );
        fireEvent.click(screen.getByText(threadName));
        expect(onThreadClick).toHaveBeenCalledTimes(1);
    });

    it("calls onEdit when Edit button is clicked", () => {
        render(
            <ChatHistoryItem
                threadName={threadName}
                onThreadClick={onThreadClick}
                onEdit={onEdit}
                onDelete={onDelete}
            />
        );
        fireEvent.click(screen.getByText("Edit"));
        expect(onEdit).toHaveBeenCalledTimes(1);
    });

    it("calls onDelete when Delete button is clicked", () => {
        render(
            <ChatHistoryItem
                threadName={threadName}
                onThreadClick={onThreadClick}
                onEdit={onEdit}
                onDelete={onDelete}
            />
        );
        fireEvent.click(screen.getByText("Delete"));
        expect(onDelete).toHaveBeenCalledTimes(1);
    });
});