"use client"
import { ActionDropdown } from "./action-dropdown"

interface ChatHistoryItemProps {
    threadName: string
    onThreadClick: () => void
    onEdit: () => void
    onDelete: () => void
}

export function ChatHistoryItem({ threadName, onThreadClick, onEdit, onDelete }: ChatHistoryItemProps) {
    return (
        <div className="flex items-center justify-between p-2 text-sm cursor-pointer hover:bg-gray-50 rounded">
            <div onClick={onThreadClick} className="flex-1 truncate">
                {threadName}
            </div>
            <ActionDropdown onEdit={onEdit} onDelete={onDelete} />
        </div>
    )
}
