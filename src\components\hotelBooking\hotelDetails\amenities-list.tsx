"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronUp, ChevronDown } from "lucide-react";
import { forwardRef } from "react";

interface AmenitiesListProps {
    amenities: Record<string, string[]>;
    showAllAmenities: boolean;
    onToggleAmenities: () => void;
}

const AmenitiesList = forwardRef<HTMLDivElement, AmenitiesListProps>(
    ({ amenities, showAllAmenities, onToggleAmenities }, ref) => {
        const amenitiesArray = Object.entries(amenities)
            .map(([category, items]) => ({
                category,
                items
            }))
            .sort((a, b) => b.items.length - a.items.length);

        const displayedAmenities = showAllAmenities
            ? amenitiesArray
            : amenitiesArray.slice(0, 4);

        return (
            <div
                className="border-t pt-6 text-[#080236]"
                ref={ref}
                id="amenities-section"
            >
                <h2 className="text-xl font-bold mb-4">Amenities</h2>

                {/* Amenities grid with conditional display */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {displayedAmenities.map((category, index) => (
                        <div key={index} className="mb-6">
                            <h3 className="font-bold text-sm mb-3 flex items-center gap-2 text-[#1E1E76]">
                                {category.category}
                                {index < 2 && (
                                    <img src="https://storage.googleapis.com/nxvoytrips-img/HotelDeatils/PhotoCard.png" />
                                )}

                            </h3>
                            <ul className="space-y-2">
                                {(showAllAmenities
                                    ? category.items
                                    : category.items.slice(0, 5)
                                ).map((item, itemIndex) => (
                                    <li
                                        key={itemIndex}
                                        className="flex items-center gap-2 text-sm"
                                    >
                                        <div className="h-1.5 w-1.5 rounded-full bg-[#080236]"></div>
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                {/* Toggle button */}
                <div className="flex justify-center">
                    <Button
                        className="mt-6 text-[#1E1E76] bg-[#E9E8FC] hover:bg-[#E9E8FC] rounded-xl items-center flex gap-2"
                        onClick={onToggleAmenities}
                    >
                        {showAllAmenities ? (
                            <>
                                Show Less <ChevronUp className="h-4 w-4" />
                            </>
                        ) : (
                            <>
                                Show All Amenities <ChevronDown className="h-4 w-4" />
                            </>
                        )}
                    </Button>
                </div>
            </div>
        );
    }
);

AmenitiesList.displayName = "AmenitiesList";

export default AmenitiesList;