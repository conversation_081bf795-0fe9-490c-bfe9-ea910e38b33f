import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ChatMobileNav from "./ChatMobileNav";
import axios from "axios";

// src/components/chat/ChatMobileNav.test.tsx

// Mocks
jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockPush = jest.fn();
jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockDispatch = jest.fn();
const mockUser = {
  user: {
    firstName: "Alice",
    lastName: "Smith",
    email: "<EMAIL>",
    picture: "pic.jpg",
  },
};

const mockSelector = jest.fn((fn: any) => {
  // Always return an object with currentUserDetails and loggedInUser
  const state = {
    loggedInUser: mockUser,
    currentUserDetails: mockUser,
    userDetails: {}
  };
  return fn ? fn(state) : state;
});
jest.mock("react-redux", () => ({
  useDispatch: () => mockDispatch,
  useSelector: (fn: any) => mockSelector(fn),
}));

const mockSetChatThreadId = jest.fn();
const mockSetNewChatThreadId = jest.fn();
const mockSetCurrentThreadTitle = jest.fn();
const mockSetRefreshChatHistory = jest.fn();
const mockSetLoginModal = jest.fn();
const mockUseChatContext = () => ({
  setChatThreadId: mockSetChatThreadId,
  setNewChatThreadId: mockSetNewChatThreadId,
  refreshChatHistory: false,
  setRefreshChatHistory: mockSetRefreshChatHistory,
  setCurrentThreadTitle: mockSetCurrentThreadTitle,
  setLoginModal: mockSetLoginModal,
});
jest.mock("@/context/ChatContext", () => ({
  useChatContext: () => mockUseChatContext(),
}));

let mockSession: any = null;
jest.mock("next-auth/react", () => ({
  useSession: () => ({ data: mockSession, status: mockSession ? "authenticated" : "unauthenticated" }),
}));


function openMobileSheet() {
  // Find hamburger icon and click it
  const hamburger = screen.getAllByRole("img").find(img => (img as HTMLImageElement).src.includes("Expansion.png"));
  if (hamburger) fireEvent.click(hamburger);
}

describe("ChatMobileNav", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSession = { accessToken: "token" };
    mockSelector.mockImplementation((fn: any) =>
      fn({
        loggedInUser: mockUser,
        currentUserDetails: mockUser,
        userDetails: {
          currentUserDetails: mockUser,
        }
      })
    );
  });

  // it("renders logo and hamburger", () => {
  //   render(<ChatMobileNav />);
  //   expect(screen.getByAltText("logo")).toBeInTheDocument();
  //   expect(screen.getAllByRole("img").some(img => (img as HTMLImageElement).src.includes("Expansion.png"))).toBe(true);
  // });

  // it("shows user info if logged in", async () => {
  //   // Mock the chat history API to resolve immediately so loading state is skipped
  //   mockedAxios.post.mockResolvedValueOnce({
  //     status: 200,
  //     data: {
  //       detail: {
  //         data: {
  //           data: [],
  //         },
  //       },
  //     },
  //   });
  //   render(<ChatMobileNav />);
  //   openMobileSheet();
  //   // Wait for the loading state to finish and for user info to appear
  //   await waitFor(() => {
  //     expect(screen.queryByText("Loading...")).toBeInTheDocument();
  //   });
  //   // Check for initials or email, since name is not rendered in the current loading state
  //   expect(screen.getByText("My Account")).toBeInTheDocument();
  //   expect(screen.getByText("Back to home")).toBeInTheDocument();
  // });

  // it("calls loadChatHistory and displays threads", async () => {
  //   mockedAxios.post.mockResolvedValueOnce({
  //     status: 200,
  //     data: {
  //       detail: {
  //         data: {
  //           data: [
  //             {
  //               thread_id: "1",
  //               thread_name: "Trip to Rome",
  //               updated_at: "2024-06-01T12:00:00Z",
  //               created_at: "2024-06-01T12:00:00Z",
  //               message_count: 2,
  //             },
  //           ],
  //         },
  //       },
  //     },
  //   });
  //   render(<ChatMobileNav />);
  //   openMobileSheet();
  //   // Now check for the sign out button
  //   expect(screen.getByText("Sign out")).toBeInTheDocument();
  // });

  it("EditThreadDialog input resets on open", async () => {
    // This test is a placeholder and should be implemented or removed if not needed.
    expect(true).toBe(true);
  });
});