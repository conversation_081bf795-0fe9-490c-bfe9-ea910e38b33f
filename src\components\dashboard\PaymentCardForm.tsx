import React, { useState, useEffect } from "react";
import Input<PERSON>ield from "@/components/input/InputField";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/input/Select";
import { PaymentCard, Address } from "@/constants/user";

interface PaymentCardFormProps {
  onCancel: () => void;
  initialData?: PaymentCard;
  addresses?: Address[];
  onSave: (card: PaymentCard) => void;
  showAdd: () => void;
}

const PaymentCardForm: React.FC<PaymentCardFormProps> = ({
  onCancel,
  initialData,
  addresses,
  onSave,
  showAdd
}) => {
  const [form, setForm] = useState<PaymentCard>({
    cardHolderName: initialData?.cardHolderName || "",
    cardNumber: initialData?.cardNumber || "",
    expiryDate: initialData?.expiryDate || "",
    nickName: initialData?.nickName || "",
    billingAddress: initialData?.billingAddress || "",
    card_id: initialData?.card_id || "",
    cardCVV: initialData?.cardCVV || "",
    cardType: initialData?.cardType || "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedBillingAddress, setSelectedBillingAddress] = useState(
    initialData?.billingAddress || ""
  );

  // Update form.billingAddress whenever selectedBillingAddress changes
  useEffect(() => {
    setForm((prev) => ({ ...prev, billingAddress: selectedBillingAddress }));
  }, [selectedBillingAddress]);

  const validateExpiryDate = (value: string): boolean => {
    // Check for MM/YYYY format
    if (!/^\d{2}\/\d{4}$/.test(value)) {
      setErrors(prev => ({ ...prev, expiryDate: "Invalid format. Use MM/YYYY" }));
      return false;
    }

    const [monthStr, yearStr] = value.split('/');
    const month = parseInt(monthStr, 10);
    const year = parseInt(yearStr, 10);
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    // Check month validity
    if (month < 1 || month > 12) {
      setErrors(prev => ({ ...prev, expiryDate: "Month must be between 1 and 12" }));
      return false;
    }

    // Check year validity
    if (year < currentYear) {
      setErrors(prev => ({ ...prev, expiryDate: "Card has expired" }));
      return false;
    }

    // If same year, check month
    if (year === currentYear && month < currentMonth) {
      setErrors(prev => ({ ...prev, expiryDate: "Card has expired" }));
      return false;
    }

    // Clear error if valid
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.expiryDate;
      return newErrors;
    });
    return true;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === "cardNumber") {
      // Format card number with dashes after every 4 digits
      const digits = value.replace(/\D/g, '');

      // Limit to 16 digits
      if (digits.length > 16) return;

      let formattedValue = '';

      for (let i = 0; i < digits.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += '-';
        }
        formattedValue += digits[i];
      }

      setForm({ ...form, [name]: formattedValue });
    }
    else if (name === "expiryDate") {
      // Format expiry date as MM/YYYY
      const digits = value.replace(/\D/g, '');

      // Limit to 6 digits (MMYYYY)
      if (digits.length > 6) return;

      let formattedValue = '';

      if (digits.length <= 2) {
        // For the month part, constrain to valid month values (1-12)
        let monthPart = digits;
        if (digits.length === 1 && parseInt(digits, 10) > 1) {
          // If first digit is > 1, prepend 0 (e.g., 5 becomes 05)
          monthPart = `0${digits}`;
        } else if (digits.length === 2) {
          // If two digits, ensure it's a valid month
          const month = parseInt(digits, 10);
          if (month === 0) {
            monthPart = "01"; // 00 is not valid, change to 01
          } else if (month > 12) {
            monthPart = "12"; // Constrain to max 12
          }
        }
        formattedValue = monthPart;
      } else {
        // Handle the month part
        let monthPart = digits.substring(0, 2);
        const month = parseInt(monthPart, 10);
        if (month === 0) {
          monthPart = "01"; // 00 is not valid, change to 01
        } else if (month > 12) {
          monthPart = "12"; // Constrain to max 12
        }

        formattedValue = `${monthPart}/${digits.substring(2)}`;
      }

      setForm({ ...form, [name]: formattedValue });

      // Validate complete date if in correct format
      if (formattedValue.length === 7) {
        validateExpiryDate(formattedValue);
      }
    }
    else {
      setForm({ ...form, [name]: value });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!form.cardHolderName) newErrors.cardHolderName = "Cardholder name is required";
    if (!form.cardNumber) newErrors.cardNumber = "Card number is required";
    if (!form.expiryDate) newErrors.expiryDate = "Expiry date is required";
    else if (!validateExpiryDate(form.expiryDate)) {
      // The validation itself will set any errors
    }
    if (!form.billingAddress) newErrors.billingAddress = "Billing address is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(form);
    }
  };

  return (
    <div className="w-full mx-auto py-8">
      <h2 className="text-md md:text-2xl font-bold text-brand-black mb-8">Add New Payment Card</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div>
          <InputField
            label="Cardholder Name"
            placeholder="Cardholder Name"
            name="cardHolderName"
            value={form.cardHolderName}
            onChange={handleChange}
          />
          {errors.cardHolderName && <div className="text-red-500 text-sm mt-1">{errors.cardHolderName}</div>}
        </div>
        <div>
          <InputField
            label="Card Number"
            placeholder="XXXX-XXXX-XXXX-XXXX"
            name="cardNumber"
            value={form.cardNumber}
            onChange={handleChange}
          />
          {errors.cardNumber && <div className="text-red-500 text-sm mt-1">{errors.cardNumber}</div>}
        </div>
        <div>
          <InputField
            label="Expiry Date"
            placeholder="MM/YYYY"
            name="expiryDate"
            value={form.expiryDate}
            onChange={handleChange}
          />
          {errors.expiryDate && <div className="text-red-500 text-sm mt-1">{errors.expiryDate}</div>}
        </div>
      </div>

      <h2 className="text-md md:text-2xl font-bold text-brand-black mb-4">Billing Address</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div>
          <label className="font-semibold text-brand-black block mb-2">Choose Billing Address</label>
          <Select
            value={selectedBillingAddress}
            onValueChange={(value) => {
              if (value === "add-new") {
                showAdd();
              } else {
                setSelectedBillingAddress(value);
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose Billing Address" />
            </SelectTrigger>
            <SelectContent className="[&_[data-state=active]]:text-brand-black [&_[data-state=active]]:bg-neutral [&_[data-highlighted]]:bg-neutral [&_[data-highlighted]]:text-brand-black">
              {addresses?.map((address) => (
                <SelectItem key={address.address_id} value={address.address_id}>
                  {address.memorableName}
                </SelectItem>
              ))}
              <SelectItem value="add-new" className="text-brand-black font-semibold hover:text-brand-black hover:bg-neutral data-[state=active]:text-white data-[state=active]:bg-neutral">
                + Add New Address
              </SelectItem>
            </SelectContent>
          </Select>
          {errors.billingAddress && <div className="text-red-500 text-sm mt-1">{errors.billingAddress}</div>}
        </div>

        {/* Empty column placeholders for consistent grid layout */}
        <div className="hidden md:block"></div>
        <div className="hidden md:block"></div>
      </div>

      <div className="flex gap-4 mt-8">
        <Button

          icon="save"
          className="px-8 py-2 rounded-[8px] bg-brand text-white  hover:bg-brand hover:text-white text-lg font-semibold"
          onClick={handleSubmit}
        >
          Update
        </Button>
        <Button
          type="button"
          variant="outline"
          className="px-8 py-2 rounded-[8px] bg-brand text-white  hover:bg-brand hover:text-white text-lg font-semibold"
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default PaymentCardForm;