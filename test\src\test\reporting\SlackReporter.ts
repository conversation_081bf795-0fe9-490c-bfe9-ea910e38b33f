import { WebClient } from '@slack/web-api';
import fs from 'fs';
import { Properties } from '../properties/Properties';
import { LocaleHelper } from '../properties/LocaleHelper';
import Utils from '../utils/Utils';
import { FileUtils } from '../utils/FileUtils';

export async function sendSlackReport(suite: string) {
  const token: string = Properties.getProperty("slack.token");
  const slackChannelId: string = Properties.getProperty("slack.channelId");
  const web = new WebClient(token);
  const report = fs.readFileSync(`test-reports/cucumber-json=reports/report.json`, 'utf8');
  const jsonReport = JSON.parse(report);
  let passedNumber = 0;
  let failedNumber = 0;

  jsonReport.forEach((feature) => {
    feature.elements.forEach((scenario) => {
      const scenarioStatus = scenario.steps.some(
        (step) => step.result.status === 'failed'
      )
        ? 'failed'
        : 'passed';
      if (scenarioStatus === 'passed') {
        passedNumber++;
      } else {
        failedNumber++;
      }
    });
  });
  const totalNumber = passedNumber + failedNumber;
  const passRate = `${((passedNumber / totalNumber) * 100).toFixed(2)}%`;

  const summary = `*${suite} on ${Properties.ENVIRONMENT} ${LocaleHelper.LOCALE}*
*Total:* ${totalNumber}\t*Pass rate:* ${passRate}
*Passed:* ${passedNumber}\t*Failed:* ${failedNumber}`;

  try {
    const sourceDir = `test-reports/cucumber-html-reports`;
    const outFileName = `test-report-${Properties.ENVIRONMENT}-${LocaleHelper.LOCALE}-${Utils.getCurrentDateTime()}.zip`;
    const outPath = `test-reports/${outFileName}`;
    await FileUtils.zipFolder(sourceDir, outPath);
    const fileStream = fs.createReadStream(outPath);

    await web.files.uploadV2({
      channel_id: slackChannelId,
      initial_comment: summary,
      file: fileStream,
      filename: outFileName,
    });
    console.log('\nReport sent to Slack successfully!');
  } catch (error) {
    console.error('\nError sending report to Slack:', error);
  }
}
