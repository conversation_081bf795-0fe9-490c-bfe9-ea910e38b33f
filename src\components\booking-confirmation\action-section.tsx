"use client"

import { Download, QrCode, Phone, Mail } from "lucide-react"
import type { BookingStatus } from "@/types/booking"
import { downloadPDFFromURL } from "@/lib/utils/helper"

interface ActionSectionProps {
    bookingStatus: BookingStatus
    bookingDetails: any
}

export const ActionSection = ({ bookingStatus, bookingDetails }: ActionSectionProps) => {
    const handleDownload = () => {

        if (bookingDetails?.pdf_url && bookingDetails?.pdf_url !== "") {
            const fileName = "Flight_ticket.pdf"
            downloadPDFFromURL(bookingDetails?.pdf_url, fileName)
        } else {
            alert("PDF download would start here")
        }
    }

    const handleContactSupport = () => {
        alert("Redirecting to support page...")
    }

    if (bookingStatus === "CONFIRMED") {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Download PDF */}
                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <Download className="w-12 h-12 text-brnad-black mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-brand-black mb-2">Download Your Ticket</h3>
                    <p className="text-brand-black mb-4">
                        All your travel details bundled into a single PDF. Easy to save, print, or carry offline.
                    </p>
                    <button
                        onClick={handleDownload}
                        className="w-full bg-brand  text-white py-2 px-4 rounded-lg font-medium  transition-colors"
                    >
                        Download as PDF
                    </button>
                </div>

                {/* Mobile App */}
                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <QrCode className="w-12 h-12 text-brand-black mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-brand-black mb-2">Get Our Mobile App</h3>
                    <p className="text-brand-black mb-4">Track flights, receive updates, and manage all your trips in one place.</p>
                    <div className="bg-[#EBEBEB] p-4 rounded-lg mb-4">
                        <img
                            src="https://storage.googleapis.com/nxvoytrips-img/TripSummary/QR%20Code.png"
                            alt="QR Code"
                            className="w-32 h-32 mx-auto"
                            onError={(e) => {
                                const target = e.target as HTMLImageElement
                                target.src = "/placeholder.svg?height=128&width=128"
                            }}
                        />
                    </div>
                    <p className="text-sm text-brand-black">Scan to download our app</p>
                </div>
            </div>
        )
    }

    return (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h3 className="text-lg font-semibold text-brand-black mb-4">Need Help?</h3>
            <p className="text-brand-black mb-6">Our customer support team is available 24/7 to assist you with your booking.</p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <a
                    href="tel:+44 20 4572 1222"
                    className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-brand-grey rounded-lg text-brand-700 font-medium hover:bg-brand-grey transition-colors"
                >
                    <Phone className="w-4 h-4" />
                    Call Support
                </a>
                <a
                    href="mailto:<EMAIL>?subject=Help%20with%20Booking"
                    className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-brand-greyrounded-lg text-brand-700 font-medium hover:bg-brand-grey transition-colors"
                >
                    <Mail className="w-4 h-4" />
                    Email Us
                </a>
            </div>
        </div>
    )
}
