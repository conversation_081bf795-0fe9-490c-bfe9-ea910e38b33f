// Imports required for the passenger info functions
import { fixture } from '../../fixtures/Fixture';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';

// Optimized version of the passenger info filling function

/**
 * Opens the passenger accordion to reveal the input fields
 * This is required for the new UI where passenger information fields are hidden
 * until the accordion is clicked
 */
async function openPassengerAccordion(): Promise<void> {
    try {
        console.log('Opening passenger accordion to reveal input fields...');
        
        // First try with modern Playwright locators
        try {
            // Look for accordion button using role
            const accordionButton = fixture.page.getByRole('button', { name: /Adult 1/i });
            if (await accordionButton.isVisible({ timeout: 3000 })) {
                await accordionButton.click();
                console.log('Clicked on passenger accordion successfully using button role locator');
                return;
            }
        } catch (error) {
            console.log('Could not find accordion using button role locator:', error.message);
        }
        
        // Fallback to CSS selectors
        const accordionSelectors = [
            // Primary selector for the accordion button
            'button[data-radix-collection-item=""]:has(span:text("Adult 1"))',
            // Fallback selectors
            'span.font-semibold.text-\\[\\#1E1E76\\].text-left:has-text("Adult 1")',
            'button:has-text("Adult 1")',
            'div:has-text("Adult 1"):has(svg)'
        ];
        
        // Try each selector until one works
        let clickSuccess = false;
        
        for (const selector of accordionSelectors) {
            try {
                // Check if element is visible
                const isVisible = await fixture.page.isVisible(selector, { timeout: 5000 });
                
                if (isVisible) {
                    console.log(`Found accordion with selector: ${selector}`);
                    
                    // Click the accordion
                    await fixture.page.click(selector);
                    console.log('Clicked on passenger accordion successfully');
                    
                    // Wait a moment for the accordion to open
                    await fixture.page.waitForTimeout(1000);
                    
                    clickSuccess = true;
                    break;
                }
            } catch (error) {
                console.log(`Selector ${selector} not found or not clickable: ${error}`);
            }
        }
        
        // If none of the selectors worked, try a JavaScript approach
        if (!clickSuccess) {
            try {
                console.log('Trying JavaScript approach to find and click accordion');
                
                clickSuccess = await fixture.page.evaluate(() => {
                    // Find button with Adult 1 text
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const adultButton = buttons.find(button => 
                        button.textContent?.includes('Adult 1') && 
                        button.querySelector('svg.lucide-chevron-down')
                    );
                    
                    if (adultButton) {
                        adultButton.click();
                        return true;
                    }
                    return false;
                });
                
                if (clickSuccess) {
                    console.log('Successfully clicked accordion using JavaScript');
                    await fixture.page.waitForTimeout(1000);
                }
            } catch (jsError) {
                console.error('JavaScript evaluation failed:', jsError);
            }
        }
        
        // If we still couldn't open the accordion, take a screenshot and log a warning
        if (!clickSuccess) {
            console.warn('Could not open passenger accordion. Fields may not be visible.');
            await ScreenshotHelper.takeScreenshot('accordion-open-failed', true, true);
            // We'll continue anyway and hope the fields are accessible
        }
    } catch (error) {
        console.error(`Error opening passenger accordion: ${error}`);
        await ScreenshotHelper.takeScreenshot('accordion-open-error', true, true);
        // Don't throw the error, try to continue with filling the form
    }
}

/**
 * Optimized function to fill passenger information 
 * This reduces execution time by:
 * 1. Using simplified selector strategies
 * 2. Minimizing waitForTimeout calls
 * 3. Using more efficient Playwright actions
 * 4. Consolidating error handling
 * 
 * Note: As of the UI change, this function only fills title, first name, last name,
 * date of birth, and gender fields.
 */
export async function fillPassengerInfo(passengerInfo: any): Promise<void> {
    try {
        console.log('Filling passenger information...');
        
        // Use a single timeout value consistently
        const defaultTimeout = 5000;
        
        // Open the passenger accordion first to reveal input fields
        await openPassengerAccordion();
        
        // 1. Fill title if provided
        if (passengerInfo.tittle) {
            console.log(`Setting title to: "Mr" for consistency (original value: ${passengerInfo.tittle})`);
            
            // Use our specialized helper to ensure title is always set to "Mr"
            await ensureTitleIsMr();
            
            // Take a screenshot after title selection
            await ScreenshotHelper.takeScreenshot('after-title-selection', true);
        }
        
        // 2. Fill first name if provided
        if (passengerInfo.first_Name) {
            await fillTextField({
                fieldSelector: '#firstName, input[placeholder="First Name"]',
                fieldValue: passengerInfo.first_Name,
                fieldName: 'first name'
            });
        }
        
        // 3. Fill last name if provided
        if (passengerInfo.last_Name) {
            await fillTextField({
                fieldSelector: '#lastName, input[placeholder="Last Name"]',
                fieldValue: passengerInfo.last_Name,
                fieldName: 'last name'
            });
        }
        
        // 4. Fill date of birth if provided
        if (passengerInfo.date_of_birth) {
            await fillDateOfBirth(passengerInfo.date_of_birth);
        }
        
        // 5. Select gender if provided
        if (passengerInfo.gender) {
            await fillDropdownField({
                fieldIdentifier: {
                    buttonSelector: 'button#gender, button:has-text("Select Gender")', 
                    optionSelector: `div[role="option"]:has-text("${passengerInfo.gender}")`
                },
                fieldValue: passengerInfo.gender,
                fieldName: 'gender'
            });
        }
        
        // NOTE: Passport country of issue filling has been removed as per UI changes
        
        // 7. Fill passport number if provided
        if (passengerInfo.passportNumber) {
            await fillTextField({
                fieldSelector: '#passportNumber, input[placeholder="Passport Number"]',
                fieldValue: passengerInfo.passportNumber,
                fieldName: 'passport number'
            });
        }
        
        // 8. Fill passport expiry date if provided
        if (passengerInfo.passportExpiryDate) {
            await fillPassportExpiryDate(passengerInfo.passportExpiryDate);
        }
        
        console.log('Passenger information filled successfully');
    } catch (error) {
        console.error('Error filling passenger information:', error);
        await takeScreenshot('passenger-info-error');
        throw error;
    }
}

/**
 * Helper function to fill text fields with better error handling
 */
async function fillTextField({ 
    fieldSelector, 
    fieldValue, 
    fieldName 
}: { 
    fieldSelector: string, 
    fieldValue: string, 
    fieldName: string 
}): Promise<void> {
    try {
        // Try first with modern Playwright locators
        if (fieldName === 'first name') {
            try {
                // Try using getByPlaceholder
                const firstNameInput = fixture.page.getByPlaceholder('First Name', { exact: true });
                if (await firstNameInput.isVisible({ timeout: 3000 })) {
                    await firstNameInput.fill(fieldValue);
                    await fixture.page.keyboard.press('Tab');
                    console.log(`Filled ${fieldName} with value: ${fieldValue} using placeholder locator`);
                    return;
                }
            } catch (placeholderError) {
                console.log(`Failed to use placeholder locator for ${fieldName}:`, placeholderError.message);
            }
            
            // Try with getByLabel if available
            try {
                const labelInput = fixture.page.getByLabel('First Name', { exact: false });
                if (await labelInput.isVisible({ timeout: 2000 })) {
                    await labelInput.fill(fieldValue);
                    await fixture.page.keyboard.press('Tab');
                    console.log(`Filled ${fieldName} with value: ${fieldValue} using label locator`);
                    return;
                }
            } catch (labelError) {
                console.log(`Failed to use label locator for ${fieldName}:`, labelError.message);
            }
        } else if (fieldName === 'last name') {
            try {
                // Try using getByPlaceholder
                const lastNameInput = fixture.page.getByPlaceholder('Last Name', { exact: true });
                if (await lastNameInput.isVisible({ timeout: 3000 })) {
                    await lastNameInput.fill(fieldValue);
                    await fixture.page.keyboard.press('Tab');
                    console.log(`Filled ${fieldName} with value: ${fieldValue} using placeholder locator`);
                    return;
                }
            } catch (placeholderError) {
                console.log(`Failed to use placeholder locator for ${fieldName}:`, placeholderError.message);
            }
        }
        
        // Fallback to standard approach with CSS selector
        const element = await fixture.page.locator(fieldSelector).first();
        await element.waitFor({ state: 'visible', timeout: 5000 });
        await element.fill(fieldValue);
        
        // Press Tab to move to next field and trigger validation
        await fixture.page.keyboard.press('Tab');
        console.log(`Filled ${fieldName} with value: ${fieldValue} using CSS selector`);
    } catch (error) {
        console.warn(`Error filling ${fieldName} with standard approach: ${error}`);
        
        // Fallback to JavaScript approach
        await fixture.page.evaluate(
            ({ selector, value }) => {
                // Find the input element
                const inputs = Array.from(document.querySelectorAll(selector));
                const input = inputs[0] as HTMLInputElement;
                
                if (input) {
                    // Set the value and trigger events
                    input.value = value;
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            { selector: fieldSelector, value: fieldValue }
        );
        console.log(`Filled ${fieldName} with value: ${fieldValue} using JavaScript fallback`);
    }
}

/**
 * Helper function to fill dropdown fields with better error handling
 */
async function fillDropdownField({ 
    fieldIdentifier, 
    fieldValue, 
    fieldName 
}: { 
    fieldIdentifier: { buttonSelector: string, optionSelector: string }, 
    fieldValue: string, 
    fieldName: string 
}): Promise<void> {
    try {
        // First try with modern Playwright locators
        if (fieldName === 'gender') {
            try {
                // Try to find the gender dropdown by role
                const genderButton = fixture.page.getByRole('button', { name: /select gender/i });
                if (await genderButton.isVisible({ timeout: 3000 })) {
                    await genderButton.click();
                    
                    // Wait for options to appear
                    await fixture.page.waitForTimeout(300);
                    
                    // Select option by text
                    const genderOption = fixture.page.getByRole('option', { name: fieldValue, exact: true });
                    await genderOption.click();
                    
                    console.log(`Selected ${fieldName}: ${fieldValue} using role locators`);
                    return;
                }
            } catch (roleError) {
                console.log(`Failed to use role locators for ${fieldName}:`, roleError.message);
            }
        }
        
        // Fallback to CSS selectors
        // Click dropdown button
        const button = await fixture.page.locator(fieldIdentifier.buttonSelector).first();
        await button.waitFor({ state: 'visible', timeout: 5000 });
        await button.click();
        
        // Wait a small amount for options to appear
        await fixture.page.waitForTimeout(300);
        
        // Select option
        const option = await fixture.page.locator(fieldIdentifier.optionSelector).first();
        await option.waitFor({ state: 'visible', timeout: 5000 });
        await option.click();
        
        console.log(`Selected ${fieldName}: ${fieldValue} using CSS selectors`);
    } catch (error) {
        console.warn(`Error selecting ${fieldName} with standard approach: ${error}`);
        
        // Fallback to JavaScript approach
        await fixture.page.evaluate(
            ({ buttonSelector, optionValue }) => {
                // Find and click dropdown button
                const buttons = Array.from(document.querySelectorAll(buttonSelector));
                const button = buttons[0] as HTMLElement;
                if (button) button.click();
                
                // Wait a moment for dropdown to open (handled externally)
                setTimeout(() => {
                    // Find and click option with matching text
                    const options = Array.from(document.querySelectorAll('div[role="option"]'));
                    const option = options.find(o => o.textContent?.includes(optionValue)) as HTMLElement;
                    if (option) option.click();
                }, 300);
            },
            { buttonSelector: fieldIdentifier.buttonSelector, optionValue: fieldValue }
        );
        
        // Give time for the click to take effect
        await fixture.page.waitForTimeout(500);
        console.log(`Selected ${fieldName}: ${fieldValue} using JavaScript fallback`);
    }
}

/**
 * Helper function specifically for handling date of birth field
 */
async function fillDateOfBirth(dateString: string): Promise<void> {
    try {
        // Parse the date
        const parts = dateString.split('-');
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]);
        const day = parseInt(parts[2]);
        
        // Format for display
        const displayDate = `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
        
        // Click date field to open calendar
        const dateField = await fixture.page.locator('div.peer.w-full.px-3.cursor-pointer').first();
        await dateField.waitFor({ state: 'visible', timeout: 5000 });
        await dateField.click();
        
        // Consolidated approach using JavaScript to set date
        await fixture.page.evaluate(
            ({ y, m, d }) => {
                // Select year
                const yearSelect = document.querySelector('span.rdrYearPicker select') as HTMLSelectElement;
                if (yearSelect) {
                    yearSelect.value = y.toString();
                    yearSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                // Select month (0-based index)
                const monthSelect = document.querySelector('span.rdrMonthPicker select') as HTMLSelectElement;
                if (monthSelect) {
                    monthSelect.value = (m - 1).toString();
                    monthSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            { y: year, m: month, d: day }
        );
        
        // Short wait for calendar to update
        await fixture.page.waitForTimeout(300);
        
        // Click day - first try with locator
        const dayButton = await fixture.page.locator(`button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled) span.rdrDayNumber > span:text-is("${day}")`);
        if (await dayButton.count() > 0) {
            await dayButton.first().click();
        } else {
            // Fallback to JavaScript approach
            await fixture.page.evaluate(
                (d) => {
                    const dayButtons = Array.from(document.querySelectorAll('button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled)'));
                    for (const button of dayButtons) {
                        const daySpan = button.querySelector('span.rdrDayNumber > span');
                        if (daySpan && daySpan.textContent === d.toString()) {
                            (button as HTMLElement).click();
                            return true;
                        }
                    }
                    return false;
                },
                day
            );
        }
        
        console.log(`Set date of birth to: ${displayDate}`);
    } catch (error) {
        console.warn(`Error setting date of birth: ${error}`);
        
        // Last resort: try to set the value directly
        await fixture.page.evaluate(
            (dateString) => {
                const dateElements = document.querySelectorAll('div.peer.w-full.px-3.cursor-pointer');
                if (dateElements.length > 0) {
                    dateElements[0].textContent = dateString;
                    dateElements[0].dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            dateString
        );
        console.log(`Set date of birth to: ${dateString} using direct value approach`);
    }
}

/**
 * Helper function for handling country field with autocomplete
 * Enhanced with more robust country selection logic
 */
async function fillCountryField(country: string): Promise<void> {
    try {
        console.log(`Filling passport country field with value: ${country}`);
        
        // Get the country code mapping for common countries
        const countryCodeMapping: Record<string, string> = {
            'IN': 'India',
            'US': 'United States',
            'UK': 'United Kingdom',
            'ID': 'Indonesia',
            'CN': 'China',
            'AU': 'Australia',
            'CA': 'Canada',
            'FR': 'France',
            'DE': 'Germany',
            'JP': 'Japan',
            'SG': 'Singapore',
            'MY': 'Malaysia'
        };
        
        // If a 2-letter code was provided, map it to the full country name
        const countryToUse = countryCodeMapping[country] || country;
        console.log(`Using country value: ${countryToUse} (original input: ${country})`);
        
        // Find country input field with multiple possible selectors
        const countryInputSelectors = [
            'input[placeholder="Select Country"]', 
            'input[placeholder="Passport Country of Issue"]',
            'input[placeholder*="Country"]',
            'input[id*="country" i]',
            'input[aria-label*="country" i]'
        ];
        
        let countryInput;
        for (const selector of countryInputSelectors) {
            const element = fixture.page.locator(selector).first();
            if (await element.isVisible({ timeout: 1000 }).catch(() => false)) {
                countryInput = element;
                console.log(`Found country input field with selector: ${selector}`);
                break;
            }
        }
        
        if (!countryInput) {
            throw new Error('Could not find passport country input field');
        }
        
        await countryInput.waitFor({ state: 'visible', timeout: 5000 });
        
        // Clear existing value with multiple strategies
        await countryInput.fill('');
        await fixture.page.waitForTimeout(300);
        
        // Type the country name to trigger suggestions - use slower typing for stability
        await countryInput.type(countryToUse, { delay: 150 });
        
        // Wait for suggestions to load - increased timeout
        await fixture.page.waitForTimeout(1500);
        
        // Take screenshot for debugging
        await ScreenshotHelper.takeScreenshot('passport-country-dropdown', true);
        
        // Look for dropdown containers with multiple possible selectors
        const dropdownSelectors = [
            'div.absolute.z-10.mt-2.w-full.bg-\\[\\#E6E3FF\\].text-black',
            'div.absolute.z-10.mt-2',
            'div[class*="dropdown"]',
            'div[class*="suggestion"]',
            'div[class*="autocomplete"]'
        ];
        
        let dropdownContainer;
        let isDropdownVisible = false;
        
        for (const selector of dropdownSelectors) {
            const element = fixture.page.locator(selector);
            if (await element.isVisible({ timeout: 1000 }).catch(() => false)) {
                dropdownContainer = element;
                isDropdownVisible = true;
                console.log(`Found dropdown with selector: ${selector}`);
                break;
            }
        }
        
        if (isDropdownVisible && dropdownContainer) {
            console.log('Suggestions dropdown is visible');
            
            // Try multiple selectors for dropdown items
            const dropdownItemSelectors = [
                'div.px-4.py-2',
                'div[role="option"]',
                'div[class*="item"]',
                'li',
                'div'
            ];
            
            let dropdownItems = [];
            for (const selector of dropdownItemSelectors) {
                try {
                    const items = await dropdownContainer.locator(selector).all();
                    if (items.length > 0) {
                        dropdownItems = items;
                        console.log(`Found ${items.length} dropdown items with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    console.log(`Could not find dropdown items with selector: ${selector}`);
                }
            }
            
            console.log(`Found total of ${dropdownItems.length} suggestions`);
            
            // Try to find exact match or similar match
            let exactMatch = null;
            let partialMatch = null;
            let firstItem = dropdownItems.length > 0 ? dropdownItems[0] : null;
            
            for (const item of dropdownItems) {
                const text = await item.textContent();
                console.log(`Suggestion option: ${text}`);
                
                // Check for exact or partial matches
                if (text) {
                    const normalizedText = text.toLowerCase().trim();
                    const normalizedCountry = countryToUse.toLowerCase().trim();
                    
                    if (normalizedText === normalizedCountry) {
                        // Found exact match
                        exactMatch = item;
                        break;
                    } else if (normalizedText.includes(normalizedCountry) || 
                               normalizedCountry.includes(normalizedText)) {
                        // Found partial match
                        partialMatch = item;
                    }
                }
            }
            
            // Click in order of preference: exact match, partial match, or first item
            if (exactMatch) {
                await exactMatch.click();
                console.log(`Selected exact match: ${await exactMatch.textContent()} from suggestions`);
            } else if (partialMatch) {
                await partialMatch.click();
                console.log(`Selected partial match: ${await partialMatch.textContent()} from suggestions`);
            } else if (firstItem) {
                await firstItem.click();
                console.log(`Selected first item: ${await firstItem.textContent()} from suggestions`);
            } else {
                console.log('No suitable dropdown items found to click');
                await fixture.page.keyboard.press('Enter');
                await fixture.page.keyboard.press('Tab');
            }
            
            // Wait for dropdown to disappear
            await fixture.page.waitForTimeout(800);
            
            // Verify the selection was applied
            const valueAfter = await countryInput.inputValue();
            console.log(`Country input value after selection: "${valueAfter}"`);
            
            // If the field is still empty, try using Enter
            if (!valueAfter) {
                console.log('Country field still empty, trying Enter key');
                await countryInput.press('Enter');
                await fixture.page.waitForTimeout(500);
            }
            
        } else {
            console.log('No suggestions dropdown found, trying alternative approaches');
            
            // If no suggestions dropdown, try combination of Enter and Tab
            await fixture.page.keyboard.press('Enter');
            await fixture.page.waitForTimeout(500);
            await fixture.page.keyboard.press('Tab');
            console.log('Pressed Enter and Tab to submit country');
            
            // Try clicking elsewhere on the page to commit the value
            try {
                await fixture.page.click('label:has-text("Passport Country of Issue")');
                console.log('Clicked on label to commit the value');
            } catch (e) {
                console.log('Could not click on label');
            }
        }
        
        // Final verification
        const finalValue = await countryInput.inputValue();
        console.log(`Final passport country input value: "${finalValue}"`);
        
    } catch (error) {
        console.warn(`Error setting passport country: ${error}`);
        
        // Fallback to JavaScript approach
        await fixture.page.evaluate(
            (countryValue) => {
                // Find input field
                const inputs = Array.from(document.querySelectorAll('input')).filter(
                    input => input.placeholder?.includes('Country') || 
                            input.placeholder?.includes('Passport')
                );
                
                if (inputs.length > 0) {
                    // Set value and trigger events
                    inputs[0].value = countryValue;
                    inputs[0].dispatchEvent(new Event('input', { bubbles: true }));
                    inputs[0].dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            country
        );
        console.log(`Set passport country to: ${country} using JavaScript fallback`);
    }
}

/**
 * Helper function for passport expiry date
 */
async function fillPassportExpiryDate(dateString: string): Promise<void> {
    try {
        // Parse the date
        const parts = dateString.split('-');
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]);
        const day = parseInt(parts[2]);
        
        // Format for display
        const displayDate = `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
        
        // Use similar approach as date of birth but with different selectors
        const dateFields = await fixture.page.locator('div.peer.w-full.px-3.cursor-pointer').all();
        
        // The second date field is usually passport expiry
        if (dateFields.length > 1) {
            await dateFields[1].click();
        } else {
            // Try to find by label proximity
            const expiryDateField = await fixture.page.locator('div:has-text("Passport Expiry Date") + div div.peer.w-full.px-3.cursor-pointer').first();
            await expiryDateField.click();
        }
        
        // Similar approach as for date of birth
        await fixture.page.evaluate(
            ({ y, m, d }) => {
                // Select year and month
                const yearSelect = document.querySelector('span.rdrYearPicker select') as HTMLSelectElement;
                const monthSelect = document.querySelector('span.rdrMonthPicker select') as HTMLSelectElement;
                
                if (yearSelect) {
                    yearSelect.value = y.toString();
                    yearSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }
                
                if (monthSelect) {
                    monthSelect.value = (m - 1).toString();
                    monthSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            { y: year, m: month, d: day }
        );
        
        // Short wait for calendar to update
        await fixture.page.waitForTimeout(300);
        
        // Click day using similar approach as date of birth
        await fixture.page.evaluate(
            (d) => {
                const dayButtons = Array.from(document.querySelectorAll('button.rdrDay:not(.rdrDayPassive):not(.rdrDayDisabled)'));
                for (const button of dayButtons) {
                    const daySpan = button.querySelector('span.rdrDayNumber > span');
                    if (daySpan && daySpan.textContent === d.toString()) {
                        (button as HTMLElement).click();
                        return true;
                    }
                }
                return false;
            },
            day
        );
        
        console.log(`Set passport expiry date to: ${displayDate}`);
    } catch (error) {
        console.warn(`Error setting passport expiry date: ${error}`);
        
        // Last resort fallback
        await fixture.page.evaluate(
            (dateStr) => {
                const dateElements = Array.from(document.querySelectorAll('div.peer.w-full.px-3.cursor-pointer'));
                if (dateElements.length > 1) {
                    const expiryElement = dateElements[1] as HTMLElement;
                    expiryElement.textContent = dateStr;
                    expiryElement.dispatchEvent(new Event('change', { bubbles: true }));
                }
            },
            dateString
        );
    }
}

/**
 * Helper function to take screenshots
 */
async function takeScreenshot(name: string): Promise<void> {
    try {
        await ScreenshotHelper.takeScreenshot(name, true);
    } catch (error) {
        console.error(`Failed to take screenshot ${name}:`, error);
    }
}

/**
 * Special helper function to ensure title always gets set to "Mr"
 * This provides an extra layer of reliability for the critical title field
 */
async function ensureTitleIsMr(): Promise<void> {
    console.log('Ensuring title is set to "Mr" using specialized function');
    
    try {
        // First try the normal dropdown approach
        await fillDropdownField({
            fieldIdentifier: {
                buttonSelector: 'button#title, button:has-text("Title"), button:has-text("Select Title")', 
                optionSelector: 'div[role="option"]:has-text("Mr")'
            },
            fieldValue: "Mr",
            fieldName: 'title'
        });
        
        // Verify if title was set correctly
        const titleSet = await fixture.page.evaluate(() => {
            // Look for elements that might indicate the title is set
            const elements = Array.from(document.querySelectorAll(
                'button:has-text("Title"), [id="title"], [aria-labelledby*="title" i]'
            ));
            
            for (const el of elements) {
                if (el.textContent?.includes('Mr')) {
                    return true;
                }
            }
            return false;
        });
        
        // If title wasn't set, try direct JavaScript approach
        if (!titleSet) {
            console.log('Title may not be set, using direct JavaScript approach');
            
            await fixture.page.evaluate(() => {
                // Try multiple strategies to set the title
                
                // 1. Try select element
                const select = document.querySelector('select#title') as HTMLSelectElement;
                if (select) {
                    select.value = 'Mr';
                    select.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                }
                
                // 2. Try setting the value on a hidden input
                const input = document.querySelector('input[name="title"], input#title') as HTMLInputElement;
                if (input) {
                    input.value = 'Mr';
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    return true;
                }
                
                // 3. Try clicking options directly
                const options = Array.from(document.querySelectorAll('[role="option"]'));
                const mrOption = options.find(opt => opt.textContent?.includes('Mr'));
                if (mrOption) {
                    (mrOption as HTMLElement).click();
                    return true;
                }
                
                return false;
            });
        }
        
        // Take a screenshot after trying to ensure title is Mr
        await ScreenshotHelper.takeScreenshot('title-selection-ensured', true);
        
        console.log('Title selection to "Mr" completed');
    } catch (error) {
        console.warn(`Error in ensureTitleIsMr: ${error}`);
    }
}
