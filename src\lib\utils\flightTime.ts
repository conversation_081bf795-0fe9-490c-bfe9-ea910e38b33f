import { format, parseISO } from "date-fns";

/**
 * Format ISO date-time string into:
 * { dayLabel: "Tue, Feb 11", timeLabel: "1:35 am", airport: "MAA" }
 */
export function formatFlightTime(dateTime: string, airportCode: string) {
  if (!dateTime) return { dayLabel: "", timeLabel: "", airport: airportCode };

  const parsed = parseISO(dateTime);

  return {
    dayLabel: format(parsed, "EEE, MMM d"),
    timeLabel: format(parsed, "h:mm a").toLowerCase(),
    airport: airportCode,
  };
}