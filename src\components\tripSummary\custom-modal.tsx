"use client"

import type React from "react"
import { useEffect } from "react"
import { X } from "lucide-react"

interface CustomModalProps {
    isOpen: boolean
    onClose: () => void
    children: React.ReactNode
}

const CustomModal = ({ isOpen, onClose, children }: CustomModalProps) => {
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = "hidden"
        } else {
            document.body.style.overflow = "unset"
        }

        return () => {
            document.body.style.overflow = "unset"
        }
    }, [isOpen])

    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === "Escape") {
                onClose()
            }
        }

        if (isOpen) {
            document.addEventListener("keydown", handleEscape)
        }

        return () => {
            document.removeEventListener("keydown", handleEscape)
        }
    }, [isOpen, onClose])

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* Backdrop */}
            <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />

            {/* Modal Content */}
            <div className="relative bg-[#E8E6F7] rounded-2xl w-full max-w-lg mx-auto h-[85vh] max-h-[700px] min-h-[600px] overflow-hidden shadow-2xl">
                {/* Close Button */}
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 z-10 text-[#4B4BC3] hover:text-[#3A3A9F] transition-colors bg-opacity-80 rounded-full p-1"
                >
                    <X size={24} />
                </button>

                {/* Content */}
                <div className="h-full overflow-hidden">{children}</div>
            </div>
        </div>
    )
}

export default CustomModal
