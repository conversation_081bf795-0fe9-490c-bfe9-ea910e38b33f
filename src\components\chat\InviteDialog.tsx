import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronDown, CircleX, Link2, Share2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";




type InviteDialogProps = {};

export const InviteDialog: React.FC<InviteDialogProps> = () => {
  const [note, setNote] = useState("");
  const [openDialog, setOpenDialog] = useState(false);

  return (
    <Dialog open={openDialog}>
      <DialogTrigger asChild className="">
        <Button
          className="text-[12px] px-4 h-[30px] border-[#B4BBE8] font-[600] text-[#080236]"
          variant={"outline"}
          onClick={() => setOpenDialog(true)}
        >
        Share
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl p-6 [&>button:last-child]:hidden">
        <div className="flex justify-between items-start">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-[#080236]">
              Invite people
            </DialogTitle>
            <p className="text-sm mt-1 text-[#B4BBE8]">
              Create a group chat with friends or family and plan together
            </p>
          </DialogHeader>
          <button
            onClick={() => setOpenDialog(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <CircleX color="#080236" size={20} />
          </button>
        </div>

        <div className="flex items-center gap-4 justify-between">
          {/* <Input
            placeholder="Email, comma separated..."
            className="rounded-full border border-purple-300 text-sm text-[#B4BBE8] placeholder-[#B4BBE8]"
          /> */}
          <div className="p-[1px] w-full  rounded-full bg-[linear-gradient(238.16deg,_#1E1E76_0.56%,_#4B4BC3_25.53%,_#707FF5_50.5%,_#A195F9_75.47%,_#F2A1F2_100.44%)]">
            <Input
              placeholder="Email, comma separated..."
              className="rounded-full bg-white text-sm text-[#B4BBE8] placeholder-[#B4BBE8] border-none px-4 py-2 focus:outline-none focus:ring-0 focus-visible:ring-0"
            />
          </div>
          <Button className="rounded-full border border-[#B4BBE8] bg-white text-[#B4BBE8] hover:bg-purple-100">
            Invite
          </Button>
        </div>

        <div className="mt-2">
          <Textarea
            placeholder="Add a note (optional)..."
            value={note}
            onChange={(e) => setNote(e.target.value)}
            maxLength={300}
            className="h-28 border border-[#B4BBE8] text-sm rounded-xl"
          />
          <div className="text-xs text-right text-muted-foreground mt-1">
            {note.length}/300
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            className="rounded-full flex gap-2 items-center text-sm"
          >
            <Link2 /> Copy link
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="rounded-full flex gap-2 items-center text-sm"
              >
                <Share2 /> Anyone <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[300px] gap-y-4 flex flex-col p-0">
              <div className="hover:bg-[#4B4BC3] group cursor-pointer hover:text-[#F2F3FA] text-[#080236] py-2 mt-2">
                <div className="flex items-center gap-6 px-4">
                  <div>
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/invite-only.svg"
                      className="group-hover:hidden"
                    />
                    <img
                      src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/invite-only-white.svg"
                      className="hidden group-hover:block"
                    />
                  </div>
                  <div className="flex flex-col">
                    <p className="font-bold text-[14px]">Invite only</p>
                    <p className="text-[10px]">Only you and people invited can access</p>
                  </div>
                </div>
              </div>
              <div className="hover:bg-[#4B4BC3] group cursor-pointer hover:text-[#F2F3FA] text-[#080236] mb-2 py-2">
                <div className="flex item-center gap-6 px-4">
                  <div>
                    <img src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/link-only.svg" className="group-hover:hidden" />
                    <img src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/link-only-white.svg" className="hidden group-hover:block" />
                  </div>
                  <div className="flex flex-col">
                    <p className="font-bold text-[14px]">Anyone with link</p>
                    <p className="text-[10px]">People invited and people with link access</p>
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </DialogContent>
    </Dialog>
  );
};
