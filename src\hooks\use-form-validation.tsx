"use client"

import { useState } from "react"
import { validatePassenger, validatePaymentForm } from "@/lib/utils/formValidation"

interface Passenger {
    title: string
    firstName: string
    middleName?: string
    lastName: string
    dob: string
    gender: "Male" | "Female" | "Other"
    specialService?: string
}

export const useFormValidation = () => {
    const [formErrors, setFormErrors] = useState<{
        passengers: Partial<Record<keyof Passenger, string>>[]
        billing: Record<string, string>
        contact_details: Record<string, string>
        card_details: Record<string, string>
    }>({ passengers: [], billing: {}, contact_details: {}, card_details: {} })

    const initialPaymentFormErrors = {
        cc_number: "",
        cc_expiry_date: "",
        cc_security_code: "",
        name: "",
        street: "",
        city: "",
        state: "",
        postcode: "",
        country_code: "",
        title: "",
        phone_int_code: "",
        contact_details_phone_number: "",
        email: "",
    }

    const [paymenFormErrors, setPaymentFormErrors] = useState(initialPaymentFormErrors)

    const validatePassegerForm = (tripSummaryDetails: any) => {
        const allErrors = []
        let validForm = true
        for (const [index, passengerInfo] of tripSummaryDetails?.passengerDetails.entries()) {
            const errors = validatePassenger(
                {
                    ...passengerInfo,
                    gender: passengerInfo.gender as Passenger["gender"],
                },
                tripSummaryDetails?.outboundTravelers[index]?.travelerType,
            )
            if (Object.keys(errors).length > 0) {
                validForm = false
            }
            allErrors.push(errors)
        }
        return { allErrors, validForm }
    }

    const validatePaymentFormData = (paymentDetails: any) => {
        setPaymentFormErrors(initialPaymentFormErrors)
        const validateFormData = validatePaymentForm(paymentDetails)

        if (Object.keys(validateFormData).length > 0) {
            setPaymentFormErrors({
                ...initialPaymentFormErrors,
                ...validateFormData,
            })
            return false
        }
        return true
    }

    return {
        formErrors,
        setFormErrors,
        paymenFormErrors,
        setPaymentFormErrors,
        initialPaymentFormErrors,
        validatePassegerForm,
        validatePaymentFormData,
    }
}
