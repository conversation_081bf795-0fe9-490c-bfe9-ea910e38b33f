import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import InputField from "@/components/input/InputField";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/input/Select";

interface EditPassengerProfileFormProps {
  profile: any;
  onSave: (profile: any) => void;
  onCancel: () => void;
}

const titles = ["Mr.", "Ms.", "Mrs.", "Dr."];
const genders = ["Male", "Female", "Other"];

const EditPassengerProfileForm: React.FC<EditPassengerProfileFormProps> = ({ profile, onSave, onCancel }) => {
  const [title, setTitle] = useState(profile.title || "");
  const [firstName, setFirstName] = useState(profile.firstName || "");
  const [middleName, setMiddleName] = useState(profile.middleName || "");
  const [lastName, setLastName] = useState(profile.lastName || "");
  const [nationality, setNationality] = useState(profile.nationality || "");
  const [gender, setGender] = useState(profile.gender || "");
  const [dob, setDob] = useState(profile.dob || "");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...profile,
      title,
      firstName,
      middleName,
      lastName,
      nationality,
      gender,
      dob,
    });
  };

  return (
    <div className="p-6 space-y-10">
      <h2 className="text-2xl font-semibold text-[#4B4BC3] mb-6">Edit Passenger Profiles</h2>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-2 gap-6 mb-4">
          <div>
            <label className="font-semibold text-[#1E1E76] mb-2 block">Title</label>
            <Select value={title} onValueChange={setTitle}>
              <SelectTrigger>
                <SelectValue placeholder="Title" />
              </SelectTrigger>
              <SelectContent>
                {titles.map(t => <SelectItem key={t} value={t}>{t}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-6 mb-4">
          <InputField label="First Name" placeholder="User First Name" value={firstName} onChange={e => setFirstName(e.target.value)} />
          <InputField label="Middle Name" placeholder="User Middle Name" value={middleName} onChange={e => setMiddleName(e.target.value)} />
          <InputField label="Last Name" placeholder="User Last Name" value={lastName} onChange={e => setLastName(e.target.value)} />
          <InputField label="Nationality" placeholder="India" value={nationality} onChange={e => setNationality(e.target.value)} />
        </div>
        <div className="grid grid-cols-2 gap-6 mb-4">
          <div>
            <label className="font-semibold text-[#1E1E76] block">Gender</label>
            <Select value={gender} onValueChange={setGender}>
              <SelectTrigger>
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                {genders.map(g => <SelectItem key={g} value={g}>{g}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>
          <InputField label="Date of Birth" placeholder="12/12/1996" type="date" value={dob} onChange={e => setDob(e.target.value)} />
        </div>
        <div className="flex gap-4 mt-6">
          <Button type="button" variant="outline" className="px-8 py-2 rounded-full bg-[#E6E3FF] text-[#4B4BC3] text-lg font-semibold border border-[#4B4BC3]" onClick={onCancel}>Cancel</Button>
          <Button type="submit" variant="outline" className="rounded-full bg-[#4B4BC3] text-white px-8 py-2 font-semibold">Save</Button>
        </div>
      </form>
    </div>
  );
};

export default EditPassengerProfileForm; 