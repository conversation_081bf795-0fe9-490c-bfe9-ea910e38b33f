import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import PassengerDetails from "./passengerDetails";

// Mocks
const mockDispatch = jest.fn();
const mockUpdateTripSummary = jest.fn((payload) => ({ type: "updateTripSummary", payload }));

jest.mock("react-redux", () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
}));

jest.mock("@/context/FlightContext", () => ({
  useFlightContext: () => ({
    updateGlobalPopup: jest.fn(),
  }),
}));

jest.mock("@/hooks/use-custom-session", () => ({
  useCustomSession: () => ({
    data: { accessToken: "token" },
    status: "authenticated",
  }),
}));

jest.mock("@/store/slices/tripSummary", () => ({
  updateTripSummary: (payload: any) => mockUpdateTripSummary(payload),
}));

jest.mock("@/utils/api", () => ({
  agentPostMethod: jest.fn().mockResolvedValue({
    detail: { data: [{ code: "IN", name: "India" }, { code: "US", name: "United States" }] },
  }),
}));

// Helper to setup Redux state
const getMockState = (overrides = {}) => ({
  tripSummary: {
    passengerDetails: [
      {
        title: "",
        firstName: "",
        middleName: "",
        lastName: "",
        dob: "",
        selectedDob: null,
        gender: "",
        passportCountry: "",
        countryText: "",
        ...overrides,
      },
    ],
    currentUserDetails: {}, // Always present to avoid destructuring errors
  },
});

const mockFormError = [
  {
    title: undefined,
    firstName: undefined,
    lastName: undefined,
    dob: undefined,
    gender: undefined,
    passportCountry: undefined,
  },
];

beforeAll(() => {
  // Mock ResizeObserver for test environment
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

describe("PassengerDetails component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // @ts-ignore
    require("react-redux").useSelector.mockImplementation((fn) =>
      fn(getMockState()) || { tripSummary: { currentUserDetails: {} } }
    );
  });

  it("renders all fields with correct initial values", () => {
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={false}
      />
    );
    expect(screen.getByText((content) => /Adult\s*1/i.test(content))).toBeInTheDocument();
    // Open the accordion to reveal the fields
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    expect(screen.getByPlaceholderText("First Name")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Middle Name")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Last Name")).toBeInTheDocument();
    expect(screen.getByText("Date of Birth")).toBeInTheDocument();
    expect(screen.getByText("Select Title")).toBeInTheDocument();
    expect(screen.getByText("Select Gender")).toBeInTheDocument();
  });

  it("dispatches updateTripSummary when firstName is changed", () => {
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={false}
      />
    );
    // Open the accordion to reveal the fields
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    const input = screen.getByPlaceholderText("First Name");
    fireEvent.change(input, { target: { value: "John" } });
    expect(mockDispatch).toHaveBeenCalled();
    expect(mockUpdateTripSummary).toHaveBeenCalledWith(
      expect.objectContaining({
        passengerDetails: expect.any(Array),
      })
    );
  });

  it("dispatches updateTripSummary when lastName is changed", () => {
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={false}
      />
    );
    // Open the accordion to reveal the fields
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    const input = screen.getByPlaceholderText("Last Name");
    fireEvent.change(input, { target: { value: "Doe" } });
    expect(mockDispatch).toHaveBeenCalled();
  });

  it("dispatches updateTripSummary when middleName is changed", () => {
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={false}
      />
    );
    // Open the accordion to reveal the fields
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    const input = screen.getByPlaceholderText("Middle Name");
    fireEvent.change(input, { target: { value: "M" } });
    expect(mockDispatch).toHaveBeenCalled();
  });

  it("shows error messages when formerror is set", () => {
    const errorFormError = [
      {
        title: "Title required",
        firstName: "First name required",
        lastName: "Last name required",
        dob: "DOB required",
        gender: "Gender required",
        passportCountry: "Country required",
      },
    ];
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={errorFormError}
        disabled={false}
      />
    );
    // Open the accordion to reveal the error messages
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    expect(screen.getByText("Title required")).toBeInTheDocument();
    expect(screen.getByText("First name required")).toBeInTheDocument();
    expect(screen.getByText("Last name required")).toBeInTheDocument();
    expect(screen.getByText("DOB required")).toBeInTheDocument();
    expect(screen.getByText("Gender required")).toBeInTheDocument();
  });

  it("fields are disabled when disabled prop is true", () => {
    render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={true}
      />
    );
    // Open the accordion to reveal the fields
    const accordionButton = screen.getByRole("button", { name: /Adult\s*1/i });
    fireEvent.click(accordionButton);
    expect(screen.getByPlaceholderText("First Name")).toBeDisabled();
    expect(screen.getByPlaceholderText("Middle Name")).toBeDisabled();
    expect(screen.getByPlaceholderText("Last Name")).toBeDisabled();
  });

  it("returns null if form is missing", () => {
    // @ts-ignore
    require("react-redux").useSelector.mockImplementation((fn) => {
      const state = {
        tripSummary: {
          passengerDetails: [],
          currentUserDetails: {}, // Always present to avoid destructuring errors
        },
      };
      // Ensure the selector always returns an object with tripSummary and currentUserDetails
      return fn ? fn(state) || state : state;
    });
    const { container } = render(
      <PassengerDetails
        index={0}
        travelerType="adult"
        formerror={mockFormError}
        disabled={false}
      />
    );
    expect(container.firstChild).toBeNull();
  });
});