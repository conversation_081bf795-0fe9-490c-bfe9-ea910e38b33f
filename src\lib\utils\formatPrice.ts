import { Price } from "@/constants/models";

export function formatFlightPrice(price: Price): string {
  if (!price || !price.currency) return "";

  try {
    return new Intl.NumberFormat("en-GB", {
      style: "currency",
      currency: price.currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price.amount);
  } catch (error) {
    return `${price.currency} ${price.amount.toFixed(2)}`;
  }
}

export function getCurrencySymbol(currencyCode: string): string {
  switch (currencyCode.toUpperCase()) {
    case 'USD':
      return '$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    case 'INR':
      return '₹';
    case 'JPY':
      return '¥';
    case 'CAD':
      return 'C$';
    case 'AUD':
      return 'A$';
    case 'CHF':
      return 'CHF';
    // Add more cases for other currencies as needed
    default:
      return currencyCode; // Fallback: return the code itself if symbol is not mapped
  }
}