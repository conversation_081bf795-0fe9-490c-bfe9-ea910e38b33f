"use client";

import { useState, useEffect } from "react";

export const useSuccessMessage = () => {
  const [updateSuccessMsg, setUpdateSuccessMsg] = useState<string | null>(null);

  useEffect(() => {
    if (updateSuccessMsg) {
      const timer = setTimeout(() => {
        setUpdateSuccessMsg(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [updateSuccessMsg]);

  return {
    updateSuccessMsg,
    setUpdateSuccessMsg,
  };
};
