"use client"
import { useEffect, useState } from 'react'
import <PERSON>ript from 'next/script'
import { useGoogleReCaptcha } from "react-google-recaptcha-v3"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import Image from "next/image";
import { postMethod } from "@/utils/api";

export const EmailSubscription = () => {
    const [isLoading, setIsLoading] = useState(false)
    const { executeRecaptcha } = useGoogleReCaptcha()
    const { toast } = useToast()
    const [email, setEmail] = useState('')
    const [errors, setErrors] = useState<string[]>([]);
    const [isSubscribed, setIsSubscribed] = useState(false)
    const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')
    const [isValidEmail, setIsValidEmail] = useState(false)
    const [buttonSize, setButtonSize] = useState({ width: 99, height: 52 });

    const validateEmail = (email: string) => {
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return regex.test(email)
    }

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setEmail(value);
      setStatus('idle');
      let newErrors: string[] = [];
      if (value == '') {
        newErrors.push('Email is required');
      } else if (!validateEmail(value)) {
        newErrors.push('Please enter a valid email address');
      }
      setErrors(newErrors);
      setIsValidEmail(newErrors.length === 0);
    };

    const submitEmail = async (email: string,token: string) => {
      let newErrors : string[] = [];
      const method = 'join-now'
      const param = {'email':email,'sessionid':token}
      const response = await postMethod(method,param);
      if (response?.detail?.status === 'success') {
        setStatus('success');
      } else {
        setStatus('error');
        if (response?.detail?.message) {
          newErrors.push(response?.detail?.message);
        } else if (response?.errors) {
          newErrors = [...response.errors]; // Assuming API returns an array of errors
        } else {
          newErrors.push("An unexpected error occurred. Please try again.");
        }
      }
      // Update errors state
      setErrors(newErrors);
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setIsLoading(true);
      let newErrors: string[] = [];

      if (email.trim() === '') {
          newErrors.push('Oops! Please enter a valid email to stay updated.');
      }

      setErrors(newErrors);

      if (newErrors.length > 0) {
        setIsLoading(false);
        return;
      }
  
      if (!executeRecaptcha) {
        setIsLoading(false);
        return
      }
  
      try {
        setIsLoading(true)
        // setStatus('success')
        const token = await executeRecaptcha("search_submit")
        await submitEmail(email, token)
        setIsSubscribed(true)
        setEmail('')
      } catch (error) {
          setStatus('error')
        toast({
          title: "Error",
          description: "Something went wrong. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    useEffect(() => {
      const updateSize = () => {
        if (window.innerWidth >= 1024) {
          setButtonSize({ width: 99, height: 52 }); // Desktop
        } else if (window.innerWidth >= 768) {
          setButtonSize({ width: 99, height: 52 }); // Tablet
        } else {
          setButtonSize({ width: 99, height: 52 }); // Mobile
        }
      };
    
      updateSize();
      window.addEventListener("resize", updateSize);
      return () => window.removeEventListener("resize", updateSize);
    }, []);

  return (
    <div className="max-w-[50%]  mx-auto">
      <div className="relative">
        <form onSubmit={handleSubmit} className="w-full mx-auto flex flex-col items-center">
          <div className="flex flex-col sm:flex-row items-center gap-2 w-[358px] h-[32px] sm:w-[205px] md:w-[365px] sm:gap-4 relative">
            <input
              type="text"
              value={email}
              onChange={handleEmailChange}
              placeholder="Enter Your e-mail id"
              className="w-[214px] sm:w-[205px] md:w-[365px] lg:w-[365px] placeholder:text-[#A195F9] placeholder:text-xs  h-full px-4 text-[#A195F9] text-base rounded-full border-transparent focus:outline-none"
            />
              <button className="g-recaptcha absolute sm:right-[-0.75rem] md:-right-[8px] top-0 right-[65px] cursor-pointer hover:brightness-125 transition duration-300 ease-in-out">
              <Image
                src="images/Button-Primary.png"
                alt="Join Now"
                style={{ height: '51px', fontSize: '16px' }}
                width={buttonSize.width}
                height={buttonSize.height}
                className=" left-[5px] w-[99px] h-[52px] sm:w-[99px] sm:h-[52px] md:w-[99px] md:h-[52px] lg:w-[99px] lg:h-[52px] transition-opacity duration-300 ease-in-out"
                onMouseEnter={(e) => e.currentTarget.src =
                  window.innerWidth >= 1024 ? "images/Button-Primary-w.png" :
                  window.innerWidth >= 640 ? "images/Button-Primary-tab-w.png" : "images/Button-Primary-tab-w.png"}
                onMouseLeave={(e) => e.currentTarget.src =
                  window.innerWidth >= 1024 ? "images/Button-Primary.png" :
                  window.innerWidth >= 640 ? "images/Button-Primary-tab.png" : "images/Button-Primary-tab.png"}
              />

              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <Loader2 className="h-5 w-5 sm:h-6 sm:w-6 animate-spin text-white" />
                </div>
              )}
            </button>
          </div>
        </form>

        {status === 'success' && 
        (
          <div className="w-full  mx-auto text-green-400 text-center py-8 px-4 sm:px-6 ">
            <p className="text-sm sm:text-base"> You're all set! Thanks for subscribing. Get ready for travel tips, deals, and updates! </p>
          </div>
        )}
        {errors.length > 0 && (
          <div className="w-full mx-auto text-red-400 text-center py-8 px-4 sm:px-6">
            {errors.map((err, index) => (
              <p key={index} className="text-sm sm:text-base">{err}</p>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}