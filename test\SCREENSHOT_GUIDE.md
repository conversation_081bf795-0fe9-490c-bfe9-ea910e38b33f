
## 📸 Screenshot Management

### Default Behavior
Screenshots are now only captured when:
- A test fails
- When an error is detected
- When explicitly forced in critical steps for debugging

### Key Improvements
- No empty screenshot folders are created for passing tests
- Screenshot directories are only created when needed
- Failed tests automatically capture detailed screenshots
- Empty screenshot directories are automatically cleaned up
- All screenshots for a test run are organized in a single timestamped folder
- Consistent screenshot naming with timestamps for better traceability
- Each test run creates one timestamped folder instead of multiple directories

### Debugging with Screenshots
For debugging specific test scenarios, you can enable additional screenshots:

#### Enable All Screenshots
```powershell
# Windows PowerShell
$env:DEBUG_SCREENSHOTS="true"; npm run test:ui
```

```bash
# Linux/Mac
DEBUG_SCREENSHOTS=true npm run test:ui
```

#### Debug Specific Features
Debug flight search result issues:
```powershell
# Windows PowerShell
$env:DEBUG_FLIGHT_SEARCH="true"; npm run test:ui:flight
```

Debug flight summary screen issues:
```powershell
# Windows PowerShell
$env:DEBUG_FLIGHT_SUMMARY="true"; npm run test:ui:flight
```

Debug trip summary screen issues:
```powershell
# Windows PowerShell
$env:DEBUG_TRIP_SUMMARY="true"; npm run test:ui:flight
```

Debug passenger info form issues:
```powershell
# Windows PowerShell
$env:DEBUG_PASSENGER_INFO="true"; npm run test:ui:flight
```

Debug checkout process issues:
```powershell
# Windows PowerShell
$env:DEBUG_CHECKOUT="true"; npm run test:ui:flight
```

Debug payment process issues:
```powershell
# Windows PowerShell
$env:DEBUG_PAYMENT="true"; npm run test:ui:flight
```

### HTML Source Capturing
HTML source is also captured only when:
- Tests fail
- Specific error contexts are encountered
- Debugging is enabled

To enable HTML source capturing for debugging:
```powershell
# Windows PowerShell
$env:DEBUG_HTML="true"; npm run test:ui
```

### Where to Find Screenshots
Screenshots are saved in the `test-reports/screenshots` directory, organized by timestamp.
Each test run creates a single timestamped folder (format: `YYYY-MM-DDThh-mm-ss.sssZ`) that contains all screenshots 
from that run. This ensures better organization and makes it easier to correlate screenshots with specific test runs.

For passing tests with no debugging enabled and no errors, no screenshot directories will be created.
The system automatically cleans up empty screenshot directories to keep the test reports folder tidy.
