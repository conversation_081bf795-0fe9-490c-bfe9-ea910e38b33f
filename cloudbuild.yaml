steps:
  # 1. SSH into the Dev Server and Execute `deploy_container.sh`
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud compute ssh madhavan@dev-server --project=dev-nxvoytrips --zone=europe-west2-a --command='
        sudo /bin/bash /home/<USER>/deploy_container.sh'
timeout: 1800s  # Set timeout to 30 minutes (adjust if needed)
options:
  logging: CLOUD_LOGGING_ONLY 
  

