import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface LoggedInUserState {
  user : {
    userId?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  }
}

const initialState: any = {};

const loggedInUserSlice = createSlice({
  name: 'loggedInUser',
  initialState,
  reducers: {
    setLoggedInUser: (state, action: PayloadAction<any>) => {
      return { ...state, ...action.payload };
    },
    clearLoggedInUser: () => {
      return {};
    }
  }
});

export const { setLoggedInUser, clearLoggedInUser } = loggedInUserSlice.actions;
export default loggedInUserSlice.reducer;
