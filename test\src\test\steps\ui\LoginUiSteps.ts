import { Given, Then } from '@cucumber/cucumber';
import { Properties } from '../../properties/Properties';
import { LoginPage } from '../../pages/LoginPage';
import { HomePage } from '../../pages/HomePage';
import { fixture } from '../../fixtures/Fixture';

import Assert from '../../utils/AssertionsHelper';
import { LocaleHelper } from '../../properties/LocaleHelper';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';

Given('The user types the {string} username on the login page', async function (user: string) {
    await LoginPage.typeUsername(Properties.getProperty(`ui.${user}.username`));
});

Given('The user types the {string} password on the login page', async function (user: string) {
    await LoginPage.typePassword(Properties.getProperty(`ui.${user}.password`));
});

Given('The user clicks on the login button', async function () {
    console.log('Clicking the login button...');
    
    // Take screenshot before clicking login button
    await ScreenshotHelper.takeScreenshot('before-login-button-click');
    
    // Click the login button using the enhanced method in LoginPage
    await LoginPage.clickLoginButton();
    
    // Increased wait for login processing - critical for stability
    console.log('Waiting for login to process...');
    await fixture.page.waitForTimeout(5000); // Increased from 2000ms
    
    // After clicking login, wait for navigation to complete
    try {
        console.log('Waiting for navigation after login...');
        
        // Create a reliable way to detect when login is complete
        // Based on the exact HTML element you shared for the user account icon
            // Create proper Playwright locators for the user account icon with initials
        // Based on: <span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full cursor-pointer" type="button" id="radix-:r1t:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">CD</span></span>
        
        console.log('Setting up locators for login detection...');

        // Enhanced waiting strategy with specific indicators for this app
        const navigationPromises = [
            // Wait for user avatar with aria-haspopup attribute (most reliable)
            fixture.page
                .getByRole('button')
                .filter({ has: fixture.page.locator('[aria-haspopup="menu"]') })
                .waitFor({ 
                    timeout: 25000,
                    state: 'visible' 
                })
                .then(() => console.log('Found user avatar button with aria-haspopup!')),
                
            // Wait for any user initials (two uppercase letters) in a rounded container
            fixture.page
                .getByRole('generic')
                .filter({ 
                    has: fixture.page.locator('.rounded-full'),
                    hasText: /^[A-Z]{1,2}$/ // Matches 1-2 uppercase letters for user initials
                })
                .waitFor({ 
                    timeout: 25000, 
                    state: 'visible' 
                })
                .then(() => console.log('Found user initials in avatar!')),
                
            // Use the more specific button for "Chat with Shasa" using class to differentiate
            fixture.page
                .getByRole('button')
                .filter({ hasText: 'Chat with Shasa' })
                .nth(1) // Taking the second one based on the error message
                .waitFor({ 
                    timeout: 25000, 
                    state: 'visible' 
                })
                .then(() => console.log('Found Chat with Shasa button (second instance)!')),
                
            // Wait for a reasonable amount of time as fallback (increased)
            fixture.page.waitForTimeout(20000)
                .then(() => console.log('Timed out waiting for login indicators, continuing test'))
        ];
        
        // Wait for any of the above conditions
        await Promise.race(navigationPromises);
        
        console.log('Login navigation appears to have completed');
        
        // Take screenshot after login
        await ScreenshotHelper.takeScreenshot('after-login-navigation');
        
        // Additional wait for UI to fully stabilize and render all elements
        await fixture.page.waitForTimeout(5000); // Increased from 3000ms
        
        // Additional screenshot to verify login state
        await ScreenshotHelper.takeScreenshot('login-completed-state');
        
    } catch (error) {
        console.log('Warning: Navigation after login may not have completed properly');
        console.error(error);
        await ScreenshotHelper.takeScreenshot('login-navigation-error', true);
        
        // Continue anyway - let the next step (Assert user is logged in home page) verify login state
        console.log('Continuing to next step despite navigation issues');
    }
    
    // Final verification screenshot
    await ScreenshotHelper.takeScreenshot('login-final-state');
});

Then('Assert user is logged in home page', async function () {
    console.log('Verifying user is logged in on home page...');
    
    try {
        // Take a screenshot of current state
        await ScreenshotHelper.takeScreenshot('login-verification-start');
        
        // Give the UI more time to fully render
        await fixture.page.waitForTimeout(5000); // Increased from 3000ms
        
        // Use the exact account icon element structure you provided
        // '<span class="relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full cursor-pointer" type="button" id="radix-:r12:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">CD</span></span>'
        
        // Define precise selectors based on the HTML you provided
        const accountIconSelectors = [
            // Most specific selector matching the exact structure
            'span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer',
            
            // Match the inner span containing user initials
            '.rounded-full span.flex:has-text("CD")',
            
            // Match by aria attributes
            '[aria-haspopup="menu"][data-state="closed"]',
            
            // Match by gradient background colors (very specific to your UI)
            '.bg-gradient-to-r.from-\\[\\#4B4BC3\\].to-\\[\\#707FF5\\]'
        ];
        
        // Try each selector
        let isAccountIconVisible = false;
        for (let i = 0; i < accountIconSelectors.length; i++) {
            const selector = accountIconSelectors[i];
            console.log(`Checking account icon with selector: ${selector}`);
            
            try {
                // Use a longer timeout for better reliability
                isAccountIconVisible = await fixture.page.locator(selector).isVisible({ timeout: 5000 });
                
                if (isAccountIconVisible) {
                    console.log(`Found account icon with selector: ${selector}`);
                    // Highlight the element in the UI for screenshot
                    await fixture.page.evaluate((sel) => {
                        const el = document.querySelector(sel);
                        if (el) {
                            (el as HTMLElement).style.border = '3px solid red';
                            (el as HTMLElement).style.boxShadow = '0 0 10px red';
                        }
                    }, selector);
                    
                    await ScreenshotHelper.takeScreenshot('account-icon-found');
                    break;
                }
            } catch (e) {
                console.log(`Selector ${selector} failed: ${e.message}`);
            }
        }
        
        // If account icon not found, try other login indicators
        if (!isAccountIconVisible) {
            console.log('Account icon not found with specific selectors, trying alternative indicators...');
            
            // Additional login indicators
            const alternativeIndicators = [
                // Chat with Shasa button
                async () => {
                    const chatButton = fixture.page.getByRole('button', { name: /chat with shasa/i });
                    return await chatButton.isVisible({ timeout: 5000 }).catch(() => false);
                },
                
                // Any rounded-full element (common for user avatars)
                async () => {
                    return await fixture.page.locator('.rounded-full').isVisible({ timeout: 5000 }).catch(() => false);
                },
                
                // Any element with user initials (typically 2 uppercase letters)
                async () => {
                    const initials = await fixture.page.$$eval('*', elements => {
                        for (const el of elements) {
                            const text = el.textContent?.trim();
                            if (text && text.length === 2 && text === text.toUpperCase() && /^[A-Z]{2}$/.test(text)) {
                                return true;
                            }
                        }
                        return false;
                    }).catch(() => false);
                    return initials;
                }
            ];
            
            // Try each alternative indicator
            for (let i = 0; i < alternativeIndicators.length; i++) {
                console.log(`Trying alternative login indicator #${i + 1}...`);
                isAccountIconVisible = await alternativeIndicators[i]();
                if (isAccountIconVisible) {
                    console.log(`Login confirmed with alternative indicator #${i + 1}`);
                    break;
                }
            }
        }
        
        if (!isAccountIconVisible) {
            // Take screenshot of failed state
            await ScreenshotHelper.takeScreenshot('login-indicators-not-found', true);
            throw new Error('No login success indicators found. User might not be logged in.');
        }
        
        console.log('Successfully verified user is logged in on home page');
    } catch (error) {
        console.error('Failed to verify logged in state:', error);
        await ScreenshotHelper.takeScreenshot('login-verification-failed', true);
        throw error;
    }
});

Given('The login failure message is visible', async function () {
    await Assert.assertTextEquals(
        await LoginPage.getLoginFailureMessage(),
        LocaleHelper.getMessage("messages.sample.login.failure")
    );
});