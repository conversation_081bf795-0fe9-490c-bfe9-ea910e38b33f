export interface ProcessedFareOptions {
  [fareType: string]: FareFeature[];
}

export interface FareFeature {
  type: string;
  label: string;
  icon: string;
  description: string;
  included: boolean;
  weight?: string;
  maxWeight?: string;
  dimensions?: string;
  quantity?: string;
  maxQuantity?: string;
  minTimeBeforeDeparture?: string;
  maxTimeAfterBooking?: string;
  value?: string;
  currency?: string;
  order: number;
}

// Enhanced time formatting function to handle minutes/hours/days
export const formatTime = (timeString: string): string => {
  if (!timeString) return timeString;

  // First, try to parse as a number (assuming minutes)
  const timeInMinutes = Number(timeString);
  if (!isNaN(timeInMinutes)) {
    // Convert minutes to appropriate format
    if (timeInMinutes >= 1440) {
      // 1440 minutes = 1 day
      const days = Math.floor(timeInMinutes / 1440);
      const remainingHours = Math.floor((timeInMinutes % 1440) / 60);
      if (remainingHours > 0) {
        return `${days} day${days > 1 ? "s" : ""} ${remainingHours}hr${remainingHours > 1 ? "s" : ""}`;
      }
      return `${days} day${days > 1 ? "s" : ""}`;
    } else if (timeInMinutes >= 60) {
      // Convert to hours
      const hours = Math.floor(timeInMinutes / 60);
      const remainingMinutes = timeInMinutes % 60;
      if (remainingMinutes > 0) {
        return `${hours}hr${hours > 1 ? "s" : ""} ${remainingMinutes}min${remainingMinutes > 1 ? "s" : ""}`;
      }
      return `${hours}hr${hours > 1 ? "s" : ""}`;
    } else {
      // Less than 60 minutes
      return `${timeInMinutes}min${timeInMinutes > 1 ? "s" : ""}`;
    }
  }

  // If not a number, try to parse as text
  const lowerTime = timeString.toLowerCase().trim();

  // Handle hours
  if (lowerTime.includes("hour")) {
    const match = lowerTime.match(/(\d+)\s*hours?/i);
    if (match) {
      const hours = match[1];
      return `${hours}hr${Number.parseInt(hours) > 1 ? "s" : ""}`;
    }
  }

  // Handle days
  if (lowerTime.includes("day")) {
    const match = lowerTime.match(/(\d+)\s*days?/i);
    if (match) {
      const days = match[1];
      return `${days} day${Number.parseInt(days) > 1 ? "s" : ""}`;
    }
  }

  // Handle minutes
  if (lowerTime.includes("minute") || lowerTime.includes("min")) {
    const match = lowerTime.match(/(\d+)\s*(?:minutes?|mins?)/i);
    if (match) {
      const minutes = match[1];
      return `${minutes}min${Number.parseInt(minutes) > 1 ? "s" : ""}`;
    }
  }

  return timeString;
};

export const processFareFeatures = (
  supplierData: any
): ProcessedFareOptions => {
  const processedOptions: ProcessedFareOptions = {};

  if (!supplierData || typeof supplierData !== "object") {
    return processedOptions;
  }

  Object.entries(supplierData).forEach(
    ([fareType, fareOptions]: [string, any]) => {
      if (!Array.isArray(fareOptions)) return;

      const featuresMap = new Map<string, FareFeature>();

      // First, collect all features by type
      fareOptions.forEach((option: any) => {
        if (!option.options || !Array.isArray(option.options)) return;

        option.options.forEach((optionDetail: any) => {
          if (optionDetail.usable !== "Y") return;

          const conditions = optionDetail.conditions || [];
          const isBundled = conditions.some(
            (c: any) => c.type === "Provision" && c.value === "Bundled"
          );
          const isFree = optionDetail.value === "0.00";

          // For HoldBag (check-in baggage), only show the free/bundled one
          if (option.type === "HoldBag" && !isFree && !isBundled) {
            console.log(`Skipping paid HoldBag option:`, optionDetail);
            return; // Skip paid check-in baggage options
          }

          // Extract all condition values
          let weight = "";
          let maxWeight = "";
          let dimensions = "";
          let quantity = "";
          let maxQuantity = "";
          let minTimeBeforeDeparture = "";
          let maxTimeAfterBooking = "";

          conditions.forEach((condition: any) => {
            switch (condition.type) {
              case "Weight":
                weight = condition.value;
                break;
              case "MaxWeight":
                maxWeight = condition.value;
                break;
              case "Dimensions":
                dimensions = condition.value;
                break;
              case "Quantity":
                quantity = condition.value;
                break;
              case "MaxQuantity":
                maxQuantity = condition.value;
                break;
              case "MinTimeBeforeDeparture":
                minTimeBeforeDeparture = formatTime(condition.value);
                break;
              case "MaxTimeAfterBooking":
                maxTimeAfterBooking = formatTime(condition.value);
                break;
            }
          });

          const feature: FareFeature = {
            type: option.type,
            label: getFeatureLabel(option.type),
            icon: getFeatureIcon(option.type),
            description: buildFeatureDescription(
              option.type,
              weight,
              maxWeight,
              dimensions,
              quantity,
              maxQuantity,
              minTimeBeforeDeparture,
              maxTimeAfterBooking,
              optionDetail.value,
              optionDetail.currency
            ),
            included: isBundled || isFree,
            weight,
            maxWeight,
            dimensions,
            quantity,
            maxQuantity,
            minTimeBeforeDeparture,
            maxTimeAfterBooking,
            value: optionDetail.value,
            currency: optionDetail.currency,
            order: getFeatureOrder(option.type),
          };

          // Use a unique key to avoid duplicates
          const uniqueKey = getUniqueFeatureKey(feature);

          // For HoldBag, only keep one (the free one)
          if (option.type === "HoldBag") {
            featuresMap.set("HoldBag", feature);
          } else {
            // For other features, prevent duplicates
            if (!featuresMap.has(uniqueKey)) {
              featuresMap.set(uniqueKey, feature);
            } else {
              console.log(`Duplicate feature skipped for key: ${uniqueKey}`);
            }
          }
        });
      });

      // Convert to array and sort by order
      const features = Array.from(featuresMap.values()).sort(
        (a, b) => a.order - b.order
      );
      processedOptions[fareType] = features;
    }
  );
  return processedOptions;
};

const getUniqueFeatureKey = (feature: FareFeature): string => {
  // For baggage items, include weight in key to allow multiple weights if needed
  if (feature.type === "LargeCabinBag" || feature.type === "SmallCabinBag") {
    const weight = feature.maxWeight || feature.weight || "default";
    return `${feature.type}-${weight}`;
  }
  // For other features, use type only to prevent duplicates
  return feature.type;
};

const getFeatureOrder = (type: string): number => {
  const order: { [key: string]: number } = {
    // Baggage first
    SmallCabinBag: 1,
    LargeCabinBag: 2,
    HoldBag: 3,
    // Policies in middle
    FlightChange: 4,
    Cancellation: 5,
    // Services at bottom
    Meal: 6,
    Seat: 7,
    Product: 8,
    NameChange: 9,
  };
  return order[type] || 10;
};

const buildFeatureDescription = (
  type: string,
  weight: string,
  maxWeight: string,
  dimensions: string,
  quantity: string,
  maxQuantity: string,
  minTimeBeforeDeparture: string,
  maxTimeAfterBooking: string,
  value: string,
  currency: string
): string => {
  const weightInfo = maxWeight || weight;
  const pieceCount = maxQuantity || quantity;

  switch (type) {
    case "SmallCabinBag":
      if (weightInfo && pieceCount) {
        return `upto ${weightInfo} (${pieceCount} pc${Number.parseInt(pieceCount) > 1 ? "s" : ""})`;
      } else if (weightInfo) {
        return `upto ${weightInfo}`;
      }
      return "Small Cabin Baggage";

    case "LargeCabinBag":
      if (weightInfo && pieceCount) {
        return `upto ${weightInfo} (${pieceCount} pc${Number.parseInt(pieceCount) > 1 ? "s" : ""})`;
      } else if (weightInfo) {
        return `upto ${weightInfo}`;
      }
      return "Cabin Baggage";

    case "HoldBag":
      if (weightInfo && pieceCount) {
        return `upto ${weightInfo} (${pieceCount} pc${Number.parseInt(pieceCount) > 1 ? "s" : ""})`;
      } else if (weightInfo) {
        return `upto ${weightInfo}`;
      }
      return "Check-in Baggage";

    case "FlightChange":
      let changeDesc = "Change charges";
      if (minTimeBeforeDeparture) {
        changeDesc += ` upto ${minTimeBeforeDeparture} and beyond`;
      }
      if (value && value !== "0.00") {
        changeDesc += ` - ${currency} ${value}`;
      } else {
        changeDesc += " - NIL";
      }
      return changeDesc;

    case "Cancellation":
      let cancelDesc = "Cancellation charges";
      if (minTimeBeforeDeparture) {
        cancelDesc += ` upto ${minTimeBeforeDeparture} and beyond`;
      }
      if (value && value !== "0.00") {
        cancelDesc += ` - ${currency} ${value}`;
      } else {
        cancelDesc += " - NIL";
      }
      return cancelDesc;

    case "Meal":
      if (value === "0.00") {
        return "Complimentary - Standard veg meal box";
      }
      return `Meal Service - ${currency} ${value}`;

    case "Seat":
      if (value === "0.00") {
        return "Premium seat with charging outlet";
      }
      return `Seat Selection - ${currency} ${value}`;

    case "Product":
      return "In-Flight Entertainment";

    default:
      return getFeatureLabel(type);
  }
};

const getFeatureLabel = (type: string): string => {
  const labels: { [key: string]: string } = {
    SmallCabinBag: "Small Cabin Baggage",
    LargeCabinBag: "Cabin Baggage",
    HoldBag: "Check-in Baggage",
    FlightChange: "Flight Change",
    Cancellation: "Cancellation",
    Seat: "Seat Selection",
    Meal: "Meal Service",
    Product: "In-Flight Entertainment",
    NameChange: "Name Change",
  };
  return labels[type] || type;
};

const getFeatureIcon = (type: string): string => {
  const icons: { [key: string]: string } = {
    SmallCabinBag: "Briefcase",
    LargeCabinBag: "Briefcase",
    HoldBag: "Luggage",
    FlightChange: "RefreshCw",
    Cancellation: "IndianRupee",
    Seat: "Armchair",
    Meal: "UtensilsCrossed",
    Product: "FastForward",
    NameChange: "Edit",
  };
  return icons[type] || "FileText";
};
