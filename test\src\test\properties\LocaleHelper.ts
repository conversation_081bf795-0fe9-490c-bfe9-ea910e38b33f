import { Config<PERSON><PERSON><PERSON> } from "./ConfigHandler";

export class LocaleHelper {
    public static LOCALE: string = "default";
    private static messages: object;
    private static backup = require(`../../resources/data/config/locale/default.json`);
    
    public static initialise(locale: string): void {
        this.LOCALE = locale;
        this.messages = require(`../../resources/data/config/locale/${this.LOCALE}.json`);
    }

    private static getMessages(): Object {
        if (this.messages === undefined) {
            ConfigHandler.loadConfig();
        }
        return this.messages;
    }

    public static getMessage(key: string) {
        const message = this.getMessageWithCompoundKey(this.getMessages(), key.split("."));
        if (message === undefined) {
            return this.getMessageWithCompoundKey(this.backup, key.split("."));
        }
        return message;
    }

    public static getMessageWithCompoundKey(parent: object, keys: Array<string>): string {
        if (keys.length <= 1) {
            return parent[keys[0] as keyof typeof parent];
        }
        else {
            return this.getMessageWithCompoundKey(parent[keys[0] as keyof typeof parent], keys.slice(1, keys.length));
        }
    }
}