import { When, Then } from '@cucumber/cucumber';
import { PaymentPage } from '../../pages/PaymentPage';
import { Properties } from '../../properties/Properties';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';
import { fixture } from '../../fixtures/Fixture';
import { WaitHelper } from '../../waits/WaitHelper';

// Note: Creditcard info step definition moved to ReviewAndPayUiSteps.ts to avoid duplication

// Note: Billing address step definition moved to ReviewAndPayUiSteps.ts to avoid duplication

/**
 * DEPRECATED: This step definition has been moved to ReviewAndPayUiSteps.ts
 * DO NOT USE this version - it's kept here temporarily for reference only
 * Use the version in ReviewAndPayUiSteps.ts that includes both contact info and billing address
 * 
 * NOTE: This step is commented out to avoid conflicts with the updated version in ReviewAndPayUiSteps.ts
 */
/* 
When('user fill the contact info using {string}', { timeout: 60000 }, async function (contactInfoKey: string) {
    // This step definition has been moved to ReviewAndPayUiSteps.ts
    // All functionality including contact info and billing address is now combined there
    throw new Error('This step definition has been moved to ReviewAndPayUiSteps.ts');
});
*/

// Add a placeholder non-conflicting step to document the deprecation
When('user fill the contact info on payment page using {string}', { timeout: 60000 }, async function (contactInfoKey: string) {
    try {
        console.warn('⚠️ DEPRECATED: Please use "user fill the contact info using {string}" from ReviewAndPayUiSteps.ts instead');
        console.log(`Filling contact information from property: ${contactInfoKey}`);
        
        // Get the contact info data from properties
        const rawContactData = Properties.getProperty(contactInfoKey);
        
        if (!rawContactData) {
            throw new Error(`Contact info not found for key: ${contactInfoKey}`);
        }
        
        // Parse the contact data if it's a string, or use as is if it's already an object
        let contactData: Record<string, any>;
        
        if (typeof rawContactData === 'string') {
            try {
                // Try to parse as JSON if it's a string representation of JSON
                contactData = JSON.parse(rawContactData);
            } catch (e) {
                console.warn('Could not parse contact data as JSON, trying to use as object');
                throw new Error(`Contact data from "${contactInfoKey}" is not in the expected format`);
            }
        } else if (typeof rawContactData === 'object' && rawContactData !== null) {
            // Use directly if it's already an object
            contactData = rawContactData as Record<string, any>;
        } else {
            // Fallback for unexpected data types
            console.warn(`Unexpected contact data type: ${typeof rawContactData}`);
            throw new Error(`Contact data from "${contactInfoKey}" has an unexpected format`);
        }
        
        // Log data for debugging
        console.log('Contact info data:', JSON.stringify(contactData, null, 2));
        
        // Wait for page to be stable before interacting with form
        await WaitHelper.waitForPageStable(3000);
        
        // Take screenshot before filling form
        await ScreenshotHelper.takeScreenshot('before-contact-info');
        
        // Check if contact form is visible (using title dropdown as reference)
        try {
            const isTitleDropdownVisible = await fixture.page.isVisible(PaymentPage.elements.titleDropdown, { timeout: 5000 });
            
            if (!isTitleDropdownVisible) {
                console.warn('Contact form not immediately visible, waiting longer...');
                
                // Wait a bit longer and try again
                await fixture.page.waitForTimeout(3000);
                
                const isTitleDropdownVisibleRetry = await fixture.page.isVisible(PaymentPage.elements.titleDropdown, { timeout: 5000 });
                
                if (!isTitleDropdownVisibleRetry) {
                    throw new Error('Contact form is not visible after waiting');
                }
            }
        } catch (error) {
            console.error('Error checking contact form visibility:', error);
            throw new Error('Could not verify contact form visibility: ' + error);
        }
        
        // Create a properly typed object for the payment page
        const contactInfo = {
            title: contactData.title || '',
            name: contactData.name || '',
            countrycode: contactData.countrycode || '',
            phoneNumber: contactData.phoneNumber || '',
            emailId: contactData.emailId || ''
        };
        
        console.log('Passing contact info to page object:', contactInfo);
        
        // Use the PaymentPage to fill contact information
        const fillSuccess = await PaymentPage.fillContactInfo(contactInfo);
        
        if (!fillSuccess) {
            throw new Error('Failed to fill contact information');
        }
        
        // Take screenshot after filling form
        await ScreenshotHelper.takeScreenshot('after-contact-info');
        
        console.log('Successfully filled contact information');
    } catch (error) {
        console.error(`Error filling contact information: ${error}`);
        await ScreenshotHelper.takeScreenshot('contact-info-error', true, true);
        throw error;
    }
});

// Note: The "user click the {string} button" step definition is moved to ReviewAndPayUiSteps.ts 
// to avoid duplication. This allows us to consolidate our tests and maintenance.
