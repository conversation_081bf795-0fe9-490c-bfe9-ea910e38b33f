import { ConfigHand<PERSON> } from "./ConfigHandler";

export class Properties {
    public static ENVIRONMENT: string = 'dev';
    private static properties: Object;

    public static initialise(env: string): void {
        this.ENVIRONMENT = env;
        this.properties = require(`../../resources/data/config/env/${Properties.ENVIRONMENT}.json`);
    }

    private static getProperties(): Object {
        if (this.properties === undefined) {
            ConfigHandler.loadConfig();
        }
        return this.properties;
    }

    public static getProperty(key: string): string {
        const value: string = Properties.getJsonValueWithCompoundKey(this.getProperties(), key.split('.'));
        if (value === undefined || value.length < 1) {
            return `PROPERTY_NOT_FOUND: [${key}]`;
        } else {
            return value;
        }
    }

    public static getJsonValueWithCompoundKey(parent: object, keys: Array<string>): string {
        if (keys.length < 2) {
            return parent[keys[0] as keyof typeof parent];
        } else {
            return Properties.getJsonValueWithCompoundKey(parent[keys[0] as keyof typeof parent], keys.slice(1, keys.length));
        }
    }
}
