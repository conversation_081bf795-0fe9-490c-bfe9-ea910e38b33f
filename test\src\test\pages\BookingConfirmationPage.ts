import { fixture } from "../fixtures/Fixture";
import { BasePage } from "./BasePage";
import { ScreenshotHelper } from "../utils/ScreenshotHelper";

/**
 * Page object for booking confirmation page interactions
 */
export class BookingConfirmationPage extends BasePage {    
    // Element selectors with enhanced options for better detection      
    static elements = {
        // Success message elements
        ticketBookingConfirmation: 'div.flex.flex-col.gap-4.pb-10.w-full.justify-center.items-center',
        successIcon: 'img[src*="TripSummary/Group%20427321396"]',
        successHeading: 'div.text-[#080236].font-semibold.text-xl',
        emailConfirmation: 'div.text-[#707FF5].text-lg',
        
        // Payment summary elements
        paymentSummarySection: 'div.flex.flex-col.gap-4:has(div:has-text("Payment Summary"))',
        paymentSummaryHeading: 'div.text-[#1E1E76].text-2xl.font-semibold',
        orderNumberLabel: 'div.font-semibold:has-text("Order Number")',
        orderNumberValue: 'div.font-semibold:has-text("Order Number") + div',
        dateLabel: 'div.font-semibold:has-text("Date")',
        dateValue: 'div.font-semibold:has-text("Date") + div',
        totalLabel: 'div.font-semibold:has-text("Total")',
        totalValue: 'div.font-semibold:has-text("Total") + div',
        paymentMethodLabel: 'div.font-semibold:has-text("Payment Method")',
        paymentMethodValue: 'div.font-semibold:has-text("Payment Method") + div',
        
        // PDF Download button - using the exact selector from the HTML
        downloadAsPdfButton: 'button.px-4.py-1.text-xl.xs\\:text-base.w-max',
        downloadAsPdfButtonAlt: 'button:has-text("Download as Pdf")',
        downloadAsPdfButtonSecondary: 'button[style*="background: linear-gradient"]:has-text("Download")',
        
        // New exact selector provided by the client for the Download as PDF button
        downloadAsPdfButtonExact: 'button.px-4.py-1.text-xl.xs\\:text-base.w-max[style*="background: linear-gradient(267.79deg, rgb(242, 161, 242) -60.95%, rgb(161, 149, 249) 6.26%, rgb(112, 127, 245) 73.47%, rgb(75, 75, 195) 140.67%, rgb(30, 30, 118) 207.88%)"]',
        
        // Alternative selectors (for better reliability)
        successMessageAlt: 'div:has-text("Your Ticket was Booked Successfully!")',
        paymentSummaryAlt: 'div:has-text("Payment Summary")',
        
        // Additional fallback selectors for different UI variations
        successFallback1: 'div:has-text("Booking Successful")',
        successFallback2: 'div:has-text("Ticket Confirmation")',
        successFallback3: 'div:has-text("Booking Confirmed")',
        paymentSummaryFallback1: 'div:has-text("Order Details")',
        paymentSummaryFallback2: 'div:has-text("Payment Details")',
        paymentSummaryFallback3: 'div:has-text("Order Number")',
        
        // New selectors from the provided HTML structure
        mainContainer: 'div.w-[80%].md\\:w-[95%].lg\\:w-[90%].xs\\:w-[90%].mx-auto.mt-10.xs\\:mt-5.mb-10',
        ticketSuccessful: 'div.text-[#080236].font-semibold.text-xl:has-text("Your Ticket was Booked Successfully!")',
        emailSentConfirmation: 'div.text-[#707FF5].text-lg:has-text("Booking details has been sent to:")',
        flightSummaryHeading: 'div.flex.text-[#1E1E76].text-2xl.font-semibold:has-text("Flight Summary")',
        travelerInfo: 'div.text-xl.font-semibold.text-[#1E1E76]:has-text("Travelers Information")',
        downloadPdfSection: 'div:has-text("Do you want to keep all your travel details in one place?")',
          // User account icon selectors
        userAccountIcon: 'span.flex.h-full.w-full.items-center.justify-center.rounded-full.bg-muted.text-white.bg-gradient-to-r.from-\\[\\#4B4BC3\\].to-\\[\\#707FF5\\]',
        userAccountIconContainer: 'span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer',
        userAccountIconAlt: 'div.avatar > span',
        userAccountIconAlt2: 'div.flex.items-center.gap-2 > div.avatar',
        
        // Account menu and logout button selectors
        accountMenu: 'div[data-radix-menu-content][role="menu"]',
        logoutButton: 'div[role="menuitem"]:has(img[alt="Logout"])',
        logoutButtonAlt: 'div[role="menuitem"]:has(span:text("Logout"))',
        logoutButtonExact: 'div[role="menuitem"].relative.flex.cursor-default.select-none.items-center.gap-2:has(img[src*="sign-out.svg"])'
    };

    /**
     * Check if booking confirmation page is displayed with retry mechanism
     * @returns {Promise<boolean>} True if booking confirmation page is displayed
     */
    static async isBookingConfirmationDisplayed(): Promise<boolean> {
        try {
            console.log('Checking if booking confirmation page is displayed');
            
            // Implement retry mechanism for more reliability
            for (let attempt = 1; attempt <= 3; attempt++) {
                console.log(`Attempt ${attempt} to find booking confirmation elements`);
                
                // Try to locate the success message
                const isSuccessMessageDisplayed = await fixture.page.locator(this.elements.successHeading)
                    .isVisible({ timeout: 5000 })
                    .catch(() => false);
                    
                if (!isSuccessMessageDisplayed) {
                    console.log('Success message not found with primary selector, trying alternative');
                    const isSuccessMessageAltDisplayed = await fixture.page.locator(this.elements.successMessageAlt)
                        .isVisible({ timeout: 5000 })
                        .catch(() => false);
                        
                    if (!isSuccessMessageAltDisplayed) {
                        console.log('Could not find booking confirmation message on attempt ' + attempt);
                        
                        // If not the last attempt, retry after waiting
                        if (attempt < 3) {
                            console.log('Waiting before next attempt...');
                            await fixture.page.waitForTimeout(2000);
                            continue;
                        }
                        return false;
                    }
                }
                
                // Try to locate the payment summary
                const isPaymentSummaryDisplayed = await fixture.page.locator(this.elements.paymentSummaryHeading)
                    .isVisible({ timeout: 5000 })
                    .catch(() => false);
                    
                if (!isPaymentSummaryDisplayed) {
                    console.log('Payment summary not found with primary selector, trying alternative');
                    const isPaymentSummaryAltDisplayed = await fixture.page.locator(this.elements.paymentSummaryAlt)
                        .isVisible({ timeout: 5000 })
                        .catch(() => false);
                        
                    if (!isPaymentSummaryAltDisplayed) {
                        console.log('Could not find payment summary section on attempt ' + attempt);
                        
                        // If not the last attempt, retry after waiting
                        if (attempt < 3) {
                            console.log('Waiting before next attempt...');
                            await fixture.page.waitForTimeout(2000);
                            continue;
                        }
                        return false;
                    }
                }
                
                // If we get here, we found both elements
                console.log(`Successfully found booking confirmation elements on attempt ${attempt}`);
                return true;
            }
              // If we've exhausted all retries
            return false;
        } catch (error) {
            console.error(`Error verifying booking confirmation page: ${error}`);
            await ScreenshotHelper.takeScreenshot('booking-confirmation-error', true);
            return false;
        }
    }

    /**
     * Get the order number from the booking confirmation page
     * @returns {Promise<string|null>} The order number or null if not found
     */
    static async getOrderNumber(): Promise<string|null> {
        try {
            const orderNumber = await fixture.page.locator(this.elements.orderNumberValue).textContent();
            return orderNumber ? orderNumber.trim() : null;
        } catch (error) {
            console.error(`Error getting order number: ${error}`);
            return null;
        }
    }

    /**
     * Get the total amount from the booking confirmation page
     * @returns {Promise<string|null>} The total amount or null if not found
     */
    static async getTotalAmount(): Promise<string|null> {
        try {
            const totalAmount = await fixture.page.locator(this.elements.totalValue).textContent();
            return totalAmount ? totalAmount.trim() : null;
        } catch (error) {
            console.error(`Error getting total amount: ${error}`);
            return null;
        }
    }
    
    /**
     * Get the payment method from the booking confirmation page
     * @returns {Promise<string|null>} The payment method or null if not found
     */
    static async getPaymentMethod(): Promise<string|null> {
        try {
            const paymentMethod = await fixture.page.locator(this.elements.paymentMethodValue).textContent();
            return paymentMethod ? paymentMethod.trim() : null;
        } catch (error) {
            console.error(`Error getting payment method: ${error}`);
            return null;
        }
    }
    
    /**
     * Clicks the "Download as PDF" button on the booking confirmation page
     * Uses multiple selectors and strategies for better reliability
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    static async clickDownloadAsPdfButton(): Promise<boolean> {
        try {
            console.log('Attempting to click the Download as PDF button');
            
            // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-download-pdf-click');
            
            // Try the exact selector provided by the client first
            if (await fixture.page.isVisible(this.elements.downloadAsPdfButtonExact, { timeout: 5000 })) {
                console.log('Found PDF button with exact selector provided by client');
                await fixture.page.click(this.elements.downloadAsPdfButtonExact);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-click-exact');
                return true;
            }
            
            // Try primary selector 
            console.log('Exact selector failed, trying primary selector');
            if (await fixture.page.isVisible(this.elements.downloadAsPdfButton, { timeout: 3000 })) {
                console.log('Found PDF button with primary selector');
                await fixture.page.click(this.elements.downloadAsPdfButton);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-click');
                return true;
            }
            
            // Try alternative selector
            console.log('Primary selector failed, trying alternative selector');
            if (await fixture.page.isVisible(this.elements.downloadAsPdfButtonAlt, { timeout: 2000 })) {
                console.log('Found PDF button with alternative selector');
                await fixture.page.click(this.elements.downloadAsPdfButtonAlt);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-click-alt');
                return true;
            }
            
            // Try secondary selector
            console.log('Alternative selector failed, trying secondary selector');
            if (await fixture.page.isVisible(this.elements.downloadAsPdfButtonSecondary, { timeout: 2000 })) {
                console.log('Found PDF button with secondary selector');
                await fixture.page.click(this.elements.downloadAsPdfButtonSecondary);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-click-secondary');
                return true;
            }
            
            // Last resort: try JavaScript approach
            console.log('All selectors failed, trying JavaScript approach');
            const buttonFound = await fixture.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                const pdfButton = buttons.find(button => 
                    button.textContent?.toLowerCase().includes('download as pdf') || 
                    button.textContent?.toLowerCase().includes('download as') ||
                    button.textContent?.toLowerCase().includes('download pdf') ||
                    button.onclick?.toString().includes('handleDownload') ||
                    (button.style && button.style.background && 
                     button.style.background.includes('linear-gradient') && 
                     button.textContent?.toLowerCase().includes('download'))
                );
                if (pdfButton) {
                    pdfButton.click();
                    return true;
                }
                return false;
            });
            
            if (buttonFound) {
                console.log('Found and clicked PDF button using JavaScript approach');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-download-pdf-click-js');
                return true;
            }
            
            console.error('Failed to find the Download as PDF button with all approaches');
            await ScreenshotHelper.takeScreenshot('download-pdf-button-not-found', true, true);
            return false;
        } catch (error) {
            console.error(`Error clicking Download as PDF button: ${error}`);
            await ScreenshotHelper.takeScreenshot('download-pdf-error', true, true);
            return false;
        }    }    
    
    /**
     * Wait for the booking confirmation page to be displayed with advanced detection strategies
     * @param {number} timeout Timeout in milliseconds (default: 60000ms / 60 seconds)
     * @returns {Promise<boolean>} True if booking confirmation page was detected within timeout
     */
    static async waitForBookingConfirmationPage(timeout: number = 60000): Promise<boolean> {
        try {
            console.log(`Waiting for booking confirmation page with timeout of ${timeout}ms`);
            
            // Take a screenshot before waiting
            await ScreenshotHelper.takeScreenshot('before-confirmation-wait');
              // First, try to ensure page is done loading (parallel wait for different load states)
            // with significantly increased timeouts for more stable page load handling
            try {
                console.log('Waiting for page load states with extended timeouts...');
                await Promise.race([
                    fixture.page.waitForLoadState('domcontentloaded', { timeout: 60000 })
                        .then(() => console.log('✓ DOMContentLoaded state reached'))
                        .catch(e => console.log(`⚠️ DOMContentLoaded timeout: ${e.message}`)),
                    fixture.page.waitForLoadState('networkidle', { timeout: 60000 })
                        .then(() => console.log('✓ Network idle state reached'))
                        .catch(e => console.log(`⚠️ Network idle timeout: ${e.message}`)),
                    fixture.page.waitForLoadState('load', { timeout: 60000 })
                        .then(() => console.log('✓ Load state reached'))
                        .catch(e => console.log(`⚠️ Load timeout: ${e.message}`))
                ]).catch(e => {
                    console.log(`⚠️ All initial load states timed out: ${e.message}, continuing with detection...`);
                });
            } catch (loadError) {
                // Non-fatal, just log and continue
                console.log(`Error waiting for page load: ${loadError.message}`);
            }
            
            // Wait longer to ensure the DOM has time to fully render and stabilize
            console.log('Waiting additional time for DOM rendering and stabilization (15 seconds)...');
            await fixture.page.waitForTimeout(15000).catch(() => {});
            
            // Start time for timeout tracking
            const startTime = Date.now();
            
            // Define comprehensive set of selectors for the confirmation page
            const keySelectors = [
                // Primary indicators - most reliable based on HTML structure
                'div.text-[#080236].font-semibold.text-xl:has-text("Your Ticket was Booked Successfully!")',
                'div.text-[#707FF5].text-lg:has-text("Booking details has been sent to:")',
                'div.flex.text-[#1E1E76].text-2xl.font-semibold:has-text("Payment Summary")',
                'div.font-semibold:has-text("Order Number")',
                
                // Secondary indicators from existing selectors
                this.elements.successHeading,
                this.elements.successMessageAlt,
                this.elements.paymentSummaryHeading,
                this.elements.ticketBookingConfirmation,
                this.elements.successIcon,
                this.elements.emailConfirmation,
                this.elements.paymentSummarySection,
                this.elements.orderNumberLabel,
                
                // New selectors for different UI parts
                this.elements.ticketSuccessful,
                this.elements.emailSentConfirmation,
                this.elements.flightSummaryHeading,
                this.elements.mainContainer,
                this.elements.travelerInfo,
                this.elements.downloadPdfSection,
                
                // Fallback indicators
                this.elements.successFallback1,
                this.elements.successFallback2,
                this.elements.successFallback3,
                this.elements.paymentSummaryFallback1,
                this.elements.paymentSummaryFallback2,
                this.elements.paymentSummaryFallback3,
                
                // Download PDF button indicators
                this.elements.downloadAsPdfButton,
                this.elements.downloadAsPdfButtonAlt,
                this.elements.downloadAsPdfButtonSecondary,
                'button:has-text("Download as Pdf")',
                'button.px-4.py-1.text-xl',
                'button[style*="background: linear-gradient"]:has-text("Download")',
                
                // Additional generic selectors that might be present
                'div:has-text(/confirmation/i)',
                'div:has-text(/booking.*success/i)',
                'div:has-text(/ticket.*booked/i)',
                'div:has-text(/payment.*summary/i)',
                'div.w-[80%].md\\:w-[95%].lg\\:w-[90%].xs\\:w-[90%].mx-auto',
                'div.flex.flex-col.gap-4'
            ];
            
            // Text patterns to look for in page content
            const confirmationTexts = [
                "Ticket was Booked Successfully",
                "Your Ticket was Booked Successfully",
                "Booking Successful",
                "Payment Summary",
                "Order Number",
                "Flight Summary",
                "Booking details has been sent to",
                "Do you want to keep all your travel details in one place",
                "Download as Pdf",
                "Travelers Information",
                "Order Details",
                "Payment Method",
                "Date",
                "Total",
                "Booking Confirmed",
                "Ticket Confirmation"
            ];
            
            // URL patterns that indicate we're on the confirmation page
            const urlPatterns = [
                'confirmation',
                'booking-success',
                'booking-confirmed',
                'ticket-confirmed',
                'success-summary',
                'checkout-success',
                'order-confirmation'
            ];
            
            // Enhanced detection loop with multiple strategies
            let attemptCount = 0;
            const maxAttempts = Math.ceil(timeout / 3000); // Calculate max attempts based on timeout
            
            while (Date.now() - startTime < timeout) {
                attemptCount++;
                console.log(`Confirmation page detection attempt ${attemptCount}/${maxAttempts}`);
                
                // STRATEGY 1: Check all selectors in parallel with Promise.allSettled
                // This is the fastest approach since it checks all selectors concurrently
                console.log('Strategy 1: Checking selectors concurrently...');
                let foundElement = false;
                let matchedSelector = '';
                
                const checkPromises = keySelectors.map(selector => {
                    return fixture.page.locator(selector).isVisible({ timeout: 2000 })
                        .then(visible => {
                            if (visible) {
                                foundElement = true;
                                matchedSelector = selector;
                            }
                            return { selector, visible };
                        })
                        .catch(() => ({ selector, visible: false }));
                });
                  const results = await Promise.allSettled(checkPromises);
                const visibleSelectors = results
                    .filter((result): result is PromiseFulfilledResult<{selector: string, visible: boolean}> => 
                        result.status === 'fulfilled' && 
                        result.value.visible === true
                    )
                    .map(result => result.value.selector);
                
                if (visibleSelectors.length > 0) {
                    console.log(`✅ Booking confirmation detected with selectors:`, visibleSelectors);
                    await ScreenshotHelper.takeScreenshot('booking-confirmation-detected');
                    return true;
                }
                
                // STRATEGY 2: Check page content for confirmation texts
                try {
                    console.log('Strategy 2: Checking page content...');
                    const pageContent = await fixture.page.content();
                    const matchedTexts = confirmationTexts.filter(text => pageContent.includes(text));
                    
                    if (matchedTexts.length >= 2) { // Require at least 2 matches for more confidence
                        console.log(`✅ Booking confirmation detected via content matches:`, matchedTexts);
                        await ScreenshotHelper.takeScreenshot('booking-confirmation-content-detected');
                        return true;
                    }
                } catch (error) {
                    console.log(`Content check error (non-critical): ${error.message}`);
                }
                
                // STRATEGY 3: Check URL for confirmation indicators
                try {
                    console.log('Strategy 3: Checking URL pattern...');
                    const currentUrl = await fixture.page.url();
                    const matchedUrlPattern = urlPatterns.find(pattern => currentUrl.includes(pattern));
                    
                    if (matchedUrlPattern) {
                        console.log(`✅ Booking confirmation detected via URL pattern: ${matchedUrlPattern}`);
                        await ScreenshotHelper.takeScreenshot('booking-confirmation-url-detected');
                        return true;
                    }
                } catch (urlError) {
                    console.log(`URL check error (non-critical): ${urlError.message}`);
                }
                
                // STRATEGY 4: Take a screenshot and check progress
                if (attemptCount % 5 === 0) {
                    await ScreenshotHelper.takeScreenshot(`confirmation-wait-attempt-${attemptCount}`);
                }
                  // Wait before next attempt (use increasing wait time for later attempts)
                // with longer base wait time and higher max wait time for better success chance
                const waitTime = Math.min(5000 + (attemptCount * 300), 10000);
                console.log(`Still waiting for booking confirmation page... (${Math.floor((Date.now() - startTime) / 1000)}s elapsed)`);
                console.log(`Waiting ${waitTime}ms before next attempt...`);
                await fixture.page.waitForTimeout(waitTime);
            }
              // If we got here, timeout occurred - but make one extensive final attempt
            console.log('Final extended attempt after timeout - checking if page has loaded...');
            await ScreenshotHelper.takeScreenshot('booking-confirmation-timeout', true);
            
            try {
                // Give page one more chance to finish loading anything that might be pending
                console.log('Giving page one final 20 second grace period to complete any pending operations...');
                await fixture.page.waitForTimeout(20000);
                await ScreenshotHelper.takeScreenshot('booking-confirmation-final-check');
                
                // One final comprehensive check for any obvious confirmation indicators
                // Including more potential indicators for better detection chances
                const finalCheckSelectors = [
                    'text="Your Ticket was Booked Successfully"',
                    'text="Booking Successful"',
                    'text="Payment Summary"',
                    'text="Order Number"',
                    'text="Booking details has been sent"',
                    'text="Flight Summary"',
                    'div.flex.flex-col.gap-4.pb-10.w-full.justify-center.items-center',
                    'div.text-[#1E1E76].text-2xl.font-semibold',
                    'div.w-[80%].md\\:w-[95%].lg\\:w-[90%].xs\\:w-[90%].mx-auto'
                ];
                
                const finalResults = await Promise.all(
                    finalCheckSelectors.map(selector => 
                        fixture.page.isVisible(selector, { timeout: 2000 })
                            .catch(() => false)
                    )
                );
                
                if (finalResults.some(result => result === true)) {
                    console.log('✅ Detected confirmation page in final check (after timeout)');
                    return true;
                }
            } catch (finalError) {
                // Ignore errors in final check
            }
            
            console.error(`❌ Timeout waiting for booking confirmation page after ${timeout}ms`);
            return false;
            
        } catch (error) {
            console.error(`Error waiting for booking confirmation page: ${error}`);
            await ScreenshotHelper.takeScreenshot('booking-confirmation-wait-error', true);
            return false;
        }
    }
    
    /**
     * Clicks the user account icon in the header
     * Uses multiple selectors and strategies for better reliability
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    static async clickUserAccountIcon(): Promise<boolean> {
        try {
            console.log('Attempting to click the user account icon');
            
            // Try with modern Playwright locators first
            try {
                // Look for user account icon by role (likely a button or img role)
                const userIconByRole = fixture.page.getByRole('button', { 
                    name: /account|profile|user|avatar/i, 
                    exact: false 
                });
                
                if (await userIconByRole.isVisible({ timeout: 3000 })) {
                    console.log('Found user account icon using role locator');
                    await userIconByRole.click();
                    await fixture.page.waitForTimeout(1000);
                    await ScreenshotHelper.takeScreenshot('after-click-user-account-icon-by-role');
                    return true;
                }
            } catch (roleError) {
                console.log('Failed to find user account icon by role:', roleError.message);
            }
            
            // Try by label if role didn't work
            try {
                const userIconByLabel = fixture.page.getByLabel(/account|profile|user|avatar/i, { exact: false });
                
                if (await userIconByLabel.isVisible({ timeout: 2000 })) {
                    console.log('Found user account icon using label locator');
                    await userIconByLabel.click();
                    await fixture.page.waitForTimeout(1000);
                    await ScreenshotHelper.takeScreenshot('after-click-user-account-icon-by-label');
                    return true;
                }
            } catch (labelError) {
                console.log('Failed to find user account icon by label:', labelError.message);
            }
            
            // If modern locators fail, fall back to CSS selectors
            // First try the exact selector provided in the requirement
            if (await fixture.page.isVisible(this.elements.userAccountIcon, { timeout: 5000 })) {
                console.log('Found user account icon with exact selector');
                await fixture.page.click(this.elements.userAccountIcon);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-user-account-icon');
                return true;
            }
            
            // Try container selector
            if (await fixture.page.isVisible(this.elements.userAccountIconContainer, { timeout: 3000 })) {
                console.log('Found user account icon container');
                await fixture.page.click(this.elements.userAccountIconContainer);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-user-account-icon-container');
                return true;
            }
            
            // Try alternative selectors
            if (await fixture.page.isVisible(this.elements.userAccountIconAlt, { timeout: 2000 })) {
                console.log('Found user account icon with alternative selector');
                await fixture.page.click(this.elements.userAccountIconAlt);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-user-account-icon-alt');
                return true;
            }
            
            if (await fixture.page.isVisible(this.elements.userAccountIconAlt2, { timeout: 2000 })) {
                console.log('Found user account icon with alternative selector 2');
                await fixture.page.click(this.elements.userAccountIconAlt2);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-user-account-icon-alt2');
                return true;
            }
            
            // Last resort: JavaScript approach
            console.log('All selectors failed, trying JavaScript approach');
            const buttonFound = await fixture.page.evaluate(() => {
                // Try to find by visual characteristics
                const possibleElements = [
                    // Look for gradient background
                    ...Array.from(document.querySelectorAll('span[class*="bg-gradient-to-r"]')),
                    // Look for rounded avatars
                    ...Array.from(document.querySelectorAll('span.rounded-full')),
                    ...Array.from(document.querySelectorAll('div.rounded-full')),
                    // Look by text content (could be user initials)
                    ...Array.from(document.querySelectorAll('span')).filter(el => 
                        el.textContent && el.textContent.length <= 2 && 
                        getComputedStyle(el).borderRadius.includes('50%')
                    )
                ];
                
                // Find the most likely account icon
                const accountIcon = possibleElements.find(el => {
                    const style = getComputedStyle(el);
                    // Check for avatar characteristics
                    const isRounded = style.borderRadius.includes('50%') || style.borderRadius === '9999px';
                    const hasGradient = el.className.includes('gradient') || style.background.includes('gradient');
                    const isClickable = style.cursor === 'pointer' || 
                                       (el.parentElement && getComputedStyle(el.parentElement).cursor === 'pointer');
                    const hasInitials = el.textContent && el.textContent.length <= 2;
                    
                    return (isRounded && (hasGradient || isClickable || hasInitials));
                });
                
                if (accountIcon) {
                    // Find closest clickable parent if needed
                    let elementToClick = accountIcon;
                    let currentEl = accountIcon;
                    while (currentEl && getComputedStyle(currentEl).cursor !== 'pointer') {
                        currentEl = currentEl.parentElement;
                        if (currentEl && getComputedStyle(currentEl).cursor === 'pointer') {
                            elementToClick = currentEl;
                            break;
                        }
                    }
                    
                    // Cast to HTMLElement to access click()
                    (elementToClick as HTMLElement).click();
                    return true;
                }
                
                return false;
            });
            
            if (buttonFound) {
                console.log('Found and clicked user account icon using JavaScript approach');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-js-click-user-account-icon');
                return true;
            }
            
            console.error('Failed to find and click the user account icon with all approaches');
            await ScreenshotHelper.takeScreenshot('user-account-icon-not-found', true);
            return false;
        } catch (error) {
            console.error(`Error clicking user account icon: ${error}`);
            await ScreenshotHelper.takeScreenshot('user-account-icon-error', true);
            return false;
        }
    }
    
    /**
     * Clicks the logout button in the account menu
     * Uses multiple selectors and strategies for better reliability
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    static async clickLogoutButton(): Promise<boolean> {
        try {
            console.log('Attempting to click the logout button');
            
            // Take a screenshot before clicking
            await ScreenshotHelper.takeScreenshot('before-click-logout');
            
            // First wait to make sure account menu is open
            console.log('Waiting for account menu to be fully visible');
            await fixture.page.waitForTimeout(1000);
            
            // Try to find menu using Playwright locators
            let isMenuVisible = false;
            
            try {
                // Check if menu is visible using role
                const menu = fixture.page.getByRole('menu');
                isMenuVisible = await menu.isVisible({ timeout: 3000 }).catch(() => false);
                
                if (!isMenuVisible) {
                    // Check if any element with menuitem role is visible
                    const menuItems = fixture.page.getByRole('menuitem');
                    isMenuVisible = await menuItems.first().isVisible({ timeout: 2000 }).catch(() => false);
                }
            } catch (menuError) {
                console.log('Error checking menu visibility with Playwright locators:', menuError.message);
            }
            
            // If Playwright locators didn't find menu, try CSS selectors
            if (!isMenuVisible) {
                isMenuVisible = await fixture.page.isVisible(this.elements.accountMenu, { timeout: 3000 })
                    .catch(() => false);
            }
            
            if (!isMenuVisible) {
                console.log('Account menu not visible, may need to click account icon first');
                // Try clicking account icon again as fallback
                const clicked = await this.clickUserAccountIcon();
                if (!clicked) {
                    console.error('Failed to open account menu');
                    await ScreenshotHelper.takeScreenshot('account-menu-not-visible', true);
                    return false;
                }
                // Wait for menu to appear after clicking
                await fixture.page.waitForTimeout(2000);
            }
            
            // Try with modern Playwright locators first
            try {
                // Try to find logout button by role
                const logoutButtonRole = fixture.page.getByRole('menuitem', { 
                    name: /logout|sign out|log out/i, 
                    exact: false 
                });
                
                if (await logoutButtonRole.isVisible({ timeout: 3000 })) {
                    console.log('Found logout button using role locator');
                    await logoutButtonRole.click();
                    await fixture.page.waitForTimeout(1000);
                    await ScreenshotHelper.takeScreenshot('after-click-logout-by-role');
                    return true;
                }
            } catch (roleError) {
                console.log('Failed to find logout button by role:', roleError.message);
            }
            
            // Try by text if role didn't work
            try {
                const logoutByText = fixture.page.getByText(/logout|sign out|log out/i, { exact: false });
                
                if (await logoutByText.isVisible({ timeout: 2000 })) {
                    console.log('Found logout button using text locator');
                    await logoutByText.click();
                    await fixture.page.waitForTimeout(1000);
                    await ScreenshotHelper.takeScreenshot('after-click-logout-by-text');
                    return true;
                }
            } catch (textError) {
                console.log('Failed to find logout button by text:', textError.message);
            }
            
            // Fall back to CSS selectors if modern locators fail
            
            // Try exact selector provided
            if (await fixture.page.isVisible(this.elements.logoutButtonExact, { timeout: 3000 })) {
                console.log('Found logout button with exact selector');
                await fixture.page.click(this.elements.logoutButtonExact);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-logout-exact');
                return true;
            }
            
            // Try primary selector
            if (await fixture.page.isVisible(this.elements.logoutButton, { timeout: 3000 })) {
                console.log('Found logout button with primary selector');
                await fixture.page.click(this.elements.logoutButton);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-logout');
                return true;
            }
            
            // Try alternative selector
            if (await fixture.page.isVisible(this.elements.logoutButtonAlt, { timeout: 2000 })) {
                console.log('Found logout button with alternative selector');
                await fixture.page.click(this.elements.logoutButtonAlt);
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-logout-alt');
                return true;
            }
            
            // Last resort: JavaScript approach
            console.log('All selectors failed, trying JavaScript approach');
            const buttonFound = await fixture.page.evaluate(() => {
                // First check for logout text
                const menuItems = Array.from(document.querySelectorAll('[role="menuitem"]'));
                
                // Find logout button by text or image
                const logoutButton = menuItems.find(item => {
                    const hasLogoutText = item.textContent?.toLowerCase().includes('logout');
                    const hasLogoutImage = item.querySelector('img[alt="Logout"]') || 
                                          item.querySelector('img[src*="sign-out"]');
                    return hasLogoutText || hasLogoutImage;
                });
                
                if (logoutButton) {
                    // Cast to HTMLElement to access click()
                    (logoutButton as HTMLElement).click();
                    return true;
                }
                
                return false;
            });
            
            if (buttonFound) {
                console.log('Found and clicked logout button using JavaScript approach');
                await fixture.page.waitForTimeout(1000);
                await ScreenshotHelper.takeScreenshot('after-click-logout-js');
                return true;
            }
            
            console.error('Failed to find and click the logout button with all approaches');
            await ScreenshotHelper.takeScreenshot('logout-button-not-found', true, true);
            return false;
        } catch (error) {
            console.error(`Error clicking logout button: ${error}`);
            await ScreenshotHelper.takeScreenshot('logout-click-error', true, true);
            return false;
        }
    }
}
