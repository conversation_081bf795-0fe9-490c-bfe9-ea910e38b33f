"use client";

import { useEffect, useState } from "react";
import Navbar from "../NavBar";
import Router, { useRouter } from "next/router";
import { motion } from "framer-motion";
import animation from "../../../public/Hero section banner.json";
import { textVariant, fadeIn } from "@/utils/motion";
import dynamic from "next/dynamic";
const Lottie = dynamic(() => import("react-lottie"), { ssr: false });
interface HomeBannerProps {
  handleSignIn: () => void;
}

export const HeroBanner: React.FC<HomeBannerProps> = ({ handleSignIn }) => {
  const router = useRouter();
  const [isLandscape, setIsLandscape] = useState(false);
  const [width, setWidth] = useState(350);
  const [height, setHeight] = useState(300);
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animation,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  useEffect(() => {
    const updateWidth = () => {
      if (window.innerWidth >= 1535) {
        setWidth(750);
        setHeight(700);
      } else if (window.innerWidth >= 1280) {
        setWidth(650);
        setHeight(650);
      } else if (window.innerWidth >= 1024) {
        setWidth(540);
        setHeight(500);
      } else if (window.innerWidth >= 768) {
        setWidth(700);
        setHeight(500);
      } else if (window.innerWidth >= 640) {
        setWidth(525);
        setHeight(400);
      } else setWidth(375);
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  useEffect(() => {
    const checkOrientation = () => {
      setIsLandscape(window.innerWidth > window.innerHeight);
    };

    checkOrientation();

    window.addEventListener("resize", checkOrientation);

    return () => window.removeEventListener("resize", checkOrientation);
  }, []);

  return (
    <>
      <section className={`relative w-full h-screen mx-auto `}>
        <div className="z-50 w-full absolute mx-auto xl:pt-16 lg:pt-16 md:pt-10 sm:pt-10 xs:pt-10 flex justify-center">
          <div className="w-[85%] mx-auto">
            <Navbar handleSignIn={handleSignIn} />
          </div>
        </div>
        <div
          className={`absolute inset-0 w-full h-full mx-auto xs:hidden sm:hidden lg:flex flex-row items-start gap-5`}
        >
          <div className="flex flex-row gap-4 w-full h-auto">
            <div className="absolute inset-0">
              <img
                src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png"
                alt="Background"
                className="w-full h-full object-cover"
              />
            </div>
            {/* <div className='absolute flex justify-end m-0 2xl:right-28 xl:right-24 lg:right-16 md:right-12 bottom-0 z-20'>
              <Lottie speed={0.5} options={defaultOptions} width={width}/>
            </div> */}
            <div className="lg:absolute w-full bottom-0 xl:top-[160px] lg:top-[120px]">
              <div className="flex relative lg:flex-row w-[85%] h-full mx-auto">
                <div className="flex flex-col xl:gap-4 z-20 xl:w-1/2 lg:w-2/4 lg:gap-4 text-[#F2F3FA] justify-center ">
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.25 }}
                    variants={textVariant(0.25)}
                    className="font-proxima-nova font-bold 2xl:text-[64px] xl:text-[60px] lg:text-[48px]"
                  >
                    Welcome to NxVoy!
                  </motion.div>
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.25 }}
                    variants={textVariant(0.5)}
                    className="font-proxima-nova font-bold 2xl:text-[64px] xl:text-[60px] lg:text-[48px] bg-[linear-gradient(to_right,rgba(242,161,242,1),rgba(161,149,249,0.6))] text-transparent bg-clip-text"
                  >
                    Meet SHASA, Your Travel Companion!
                  </motion.div>
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.25 }}
                    variants={textVariant(0.75)}
                    className="font-proxima-nova font-medium tracking-wider 2xl:text-[20px] xl:text-[20px] lg:text-[16px] lg:w-[80%]"
                  >
                    She’s an AI trip planner and a great itinerary master who
                    makes every journey effortless and joyful!
                  </motion.div>
                  <motion.div
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true, amount: 0.2 }}
                    variants={fadeIn("up", "spring", 1, 1)}
                    className="flex flex-row font-proxima-nova gap-6 xl:gap-6 lg:gap-6 py-8 xl:py-8 lg:py-3"
                  >
                    <button
                      onClick={() => router.push("/chat")}
                      className="flex w-max font-medium px-4 xl:px-4 py-1 cursor-pointer xl:text-base lg:text-base bg-brand-white border border-brand text-brand rounded-[8px]"
                    >
                      Chat with Shasa
                    </button>
                    <button
                      onClick={() => router.push("/destination")}
                      className="flex w-max font-medium px-4 xl:px-4 py-1 xl:text-base lg:text-base cursor-pointer bg-transparent text-white border border-brand-white rounded-[8px]"
                    >
                      Start Booking
                    </button>
                  </motion.div>
                </div>
                <div className="flex absolute right-0 bottom-0 xl:w-1/2 lg:w-2/4">
                  <div className="flex absolute justify-end m-0 2xl:right-5 xl:right-0 lg:right-0 bottom-0 z-10">
                    <Lottie
                      speed={0.5}
                      options={defaultOptions}
                      width={width}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* ---------------Tab----------------------- */}
        <div className="xs:hidden sm:hidden md:flex lg:hidden relative inset-0 w-full h-full mx-auto">
          {/* Background Image */}
          <div className="absolute w-full h-full inset-0">
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png"
              alt="Background"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute flex flex-col w-full h-full">
            <div className="flex flex-col gap-6 z-20 w-[85%] mx-auto h-3/5 pt-16 text-[#F2F3FA] justify-center ">
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(0.5)}
                className="font-proxima-nova font-bold md:text-5xl"
              >
                Welcome to NxVoy!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(1)}
                className="font-proxima-nova font-bold text-5xl bg-[linear-gradient(to_right,rgba(242,161,242,1),rgba(161,149,249,0.6))] text-transparent bg-clip-text"
              >
                Meet SHASA, Your Travel Companion!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(1.5)}
                className="font-proxima-nova font-medium tracking-wider text-xl"
              >
                She’s an AI trip planner and a great itinerary master who makes
                every journey effortless and joyful!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={fadeIn("up", "spring", 2, 2.5)}
                className="flex flex-col w-full justify-center items-center font-proxima-nova gap-6 py-4"
              >
                <button className="flex justify-center gap-2 w-max text-2xl font-medium px-4 py-2 cursor-pointer bg-brand-white border border-brand text-brand rounded-[8px]">
                  <img
                    className="w-5 h-5"
                    src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                  />
                  <div>Chat with Shasa</div>
                </button>
                <button
                  onClick={() => router.push("/destination")}
                  className="flex w-max font-medium text-2xl px-4 py-2 cursor-pointer bg-transparent text-white border border-brand-white  rounded-[8px]"
                >
                  Start Booking
                </button>
              </motion.div>
            </div>
            <div className="flex h-2/5 relative bottom-0">
              {/* Lottie Animation */}
              <div className="relative bottom-0 left-1/2 justify-end transform -translate-x-1/2 w-full flex">
                <Lottie options={defaultOptions} width={width} />
              </div>
            </div>
          </div>
        </div>
        {/* ---------------------Mobile---------------------- */}
        <div className="lg:hidden md:hidden xs:flex relative inset-0 w-full h-full mx-auto">
          {/* Background Image */}
          <div className="absolute w-full h-full inset-0">
            <img
              src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/HomePage%20Banner.png"
              alt="Background"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute flex flex-col w-full h-full">
            <div className="flex flex-col gap-4 z-20 w-[85%] mx-auto h-2/3 pt-24 text-[#F2F3FA] justify-center ">
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(0.5)}
                className="font-proxima-nova font-bold text-4xl"
              >
                Welcome to NxVoy!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(0.5)}
                className="font-proxima-nova font-bold text-4xl bg-[linear-gradient(to_right,rgba(242,161,242,1),rgba(161,149,249,0.6))] text-transparent bg-clip-text"
              >
                Meet SHASA, Your Travel Companion!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={textVariant(0.5)}
                className="font-proxima-nova font-medium tracking-wider text-base"
              >
                She’s an AI trip planner and a great itinerary master who makes
                every journey effortless and joyful!
              </motion.div>
              <motion.div
                initial="hidden"
                whileInView="show"
                viewport={{ once: true, amount: 0.25 }}
                variants={fadeIn("up", "spring", 2, 2.5)}
                className="flex flex-col sm:flex-row w-full justify-center items-center font-proxima-nova gap-6 py-2"
              >
                <button className="flex justify-center gap-2 w-max font-medium px-4 xl:px-4 py-1 cursor-pointer xl:text-base lg:text-base xs:text-lg bg-brand border border-brand text-white rounded-[8px]">
                  <img
                    className="w-5 h-5"
                    src="https://storage.googleapis.com/nxvoytrips-img/Homepage/Homepage%20Banner%20Section/Chat%20White.png"
                  />
                  <div>Chat with Shasa</div>
                </button>
                <button
                  onClick={() => router.push("/destination")}
                  className="flex w-max font-medium px-4 xl:px-4 py-1 xl:text-base lg:text-base xs:text-lg cursor-pointer bg-white text-brand  rounded-[8px]"
                >
                  Start Booking
                </button>
              </motion.div>
            </div>
            <div className="flex h-1/3 relative">
              <div className="relative bottom-0 justify-end left-1/2 transform -translate-x-1/2 w-full flex">
                <Lottie options={defaultOptions} width={width} />
              </div>
            </div>
          </div>
          {/* Lottie Animation */}
        </div>
      </section>
    </>
  );
};
