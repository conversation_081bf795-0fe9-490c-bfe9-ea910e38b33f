import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

interface SlideProps {
  id: string;
  name: string;
  imageUrl: string;
  autoPlay?: boolean;
}

const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 300 : -300,
    opacity: 0,
    rotate: direction > 0 ? 10 : -10,
    position: "absolute",
  }),
  center: {
    x: 0,
    opacity: 1,
    position: "relative",
    rotate: 0,
    transition: { ease: "easeInOut", duration: 0.5 },
  },
  exit: (direction: number) => ({
    x: direction > 0 ? -300 : 300,
    opacity: 0,
    rotate: direction > 0 ? -10 : 10,
    position: "absolute",
    transition: { ease: "easeInOut", duration: 0.5 },
  }),
};

const Carousel = ({ slides, autoPlay = false }: { slides: SlideProps[] }) => {
  const [[currentIndex, direction], setCurrentIndex] = useState([0, 0]);
  const userInteractedRef = useRef(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const paginate = (newDirection: number, isManual = false) => {
    if (isManual) userInteractedRef.current = true;

    const newIndex =
      (currentIndex + newDirection + slides.length) % slides.length;
    setCurrentIndex([newIndex, newDirection]);
  };

  const prevIndex = (currentIndex - 1 + slides.length) % slides.length;
  const nextIndex = (currentIndex + 1) % slides.length;

  useEffect(() => {
    if (autoPlay) {
      // Reset user interaction when carousel becomes visible again
      userInteractedRef.current = false;
    }
  }, [autoPlay]);

  useEffect(() => {
    if (autoPlay && !userInteractedRef.current) {
      intervalRef.current = setInterval(() => {
        paginate(1);
      }, 2000);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [autoPlay, currentIndex]);

  return (
    <div
      className="relative overflow-hidden flex items-center 
        justify-center w-full h-full -mt-[100px]"
    >
      <motion.div
        initial={{ opacity: 0.5, scale: 0.8, rotate: -10 }}
        animate={{ opacity: 0.5, scale: 0.8, rotate: -10 }}
        transition={{ duration: 0.5 }}
        className="absolute left-10 z-0"
      >
        <Image
          src={slides[prevIndex].imageUrl}
          alt="Previous"
          width={499}
          height={493}
        />
      </motion.div>
      <div className="relative flex flex-col gap-0 items-center mt-[150px]">
        <AnimatePresence custom={direction} initial={false}>
          <motion.div
            key={slides[currentIndex].id}
            custom={direction}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            className="w-full h-full flex z-10 items-center justify-center"
          >
            <Image
              src={slides[currentIndex].imageUrl}
              alt={slides[currentIndex].name}
              width={499}
              height={493}
              className="rounded-xl"
              priority
            />
          </motion.div>
        </AnimatePresence>
        <div className="flex justify-around w-full">
          <button
            onClick={() => paginate(-1, true)}
            className="p-2 rounded-full text-gray-800 bg-transparent"
          >
            <div className="w-[34px] h-[34px] flex items-center justify-center">
              <img width={18.8} height={17.59} src="https://storage.googleapis.com/nxvoytrips-img/Homepage/crousel/arrow-left.png" />
            </div>
          </button>
          <button
            onClick={() => paginate(1, true)}
            className="p-2 rounded-full text-gray-800 bg-transparent"
          >
            <div className="w-[34px] h-[34px] flex items-center justify-center">
              <img width={18.8} height={17.59} src="https://storage.googleapis.com/nxvoytrips-img/Homepage/crousel/arrow-right.png" />
            </div>
          </button>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0.5, scale: 0.8, rotate: 10 }}
        animate={{ opacity: 0.5, scale: 0.8, rotate: 10 }}
        transition={{ duration: 0.5 }}
        className="absolute right-10 z-0"
      >
        <Image
          src={slides[nextIndex].imageUrl}
          alt="Next"
          width={499}
          height={493}
        />
      </motion.div>
    </div>
  );
};

export default Carousel;
