import React, { useState } from "react";
import FormInput from "../../components/auth/FormInput";
// import Button from "../../components/ui/LoginButton";
import { authPostMethod } from "@/utils/auth";
import { Button } from "@/components/ui/button";

interface ForgotPasswordScreenProps {
  onEmailEnter: (email: string) => void;
}

const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({
  onEmailEnter,
}) => {
  const [email, setEmail] = useState("");
  const [errors, setErrors] = useState({
    email: "",
  });
  const [isHovered, setIsHovered] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);

  const isEmail = (value: string) => /\S+@\S+\.\S+/.test(value);
  const isPhone = (value: string) => /^\+?\d{10,15}$/.test(value);

  // Form validation
  const validateForm = (): boolean => {
    if (!email) {
      setErrors({ email: "Email or phone number is required" });
      return false;
    }

    if (!isEmail(email) && !isPhone(email)) {
      setErrors({ email: "Please enter a valid email or phone number" });
      return false;
    }

    setErrors({ email: "" });
    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;
    const payload = isPhone(email) ? { phone: email } : { email: email };

    try {
      const response = await authPostMethod("auth/forgot-password", payload);
      if (!response.success) {
        // setGeneralError(response.data?.detail.message || "Something went wrong");
        setGeneralError(
          "I couldn’t find an account with that email. Try again with a registered one?"
        );
        return;
      }
      console.log("✅ OTP sent:", response);
      onEmailEnter(email);
    } catch (err) {
      console.error("❌ Forgot password error:", err);
      setGeneralError("Failed to send verification code. Please try again.");
    }
  };

  return (
    <div className="p-8 xs:p-10 font-proxima-nova flex flex-col items-center text-center max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="w-full">
        <div className="mb-6">
          <div>
            <div className="flex lg:flex-col font-proxima-nova sm:flex-row xs:flex-col">
              <h1
                className="text-xl sm:text-2xl md:text-3xl font-proxima-nova lg:text-4xl mb-4 xs:mb-2 text-center md:text-left"
                style={styles.header}
              >
                Lost your way?&nbsp;
              </h1>

              <h1
                className="text-xl sm:text-2xl md:text-3xl font-proxima-nova lg:text-4xl mb-4 xs:mb-2 text-center md:text-left"
                style={styles.header}
              >
                No worries!
              </h1>
            </div>

            <p className="text-brand-black mb-6 xs:mb-3 font-proxima-nova text-sm md:text-base text-center md:text-left">
              Enter your email, and we'll help you reset your password in no
              time!
            </p>
          </div>
          <label className="block text-brand-black mb-2 font-proxima-nova text-sm md:text-lg text-left font-bold">
            Email ID / Phone Number
          </label>
          <FormInput
            type="email"
            name="email"
            placeholder="Enter your email ID / Phone Number"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              setErrors({ email: "" });
              setGeneralError(null);
            }}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1 text-left">
              {errors.email}
            </p>
          )}
        </div>
        {generalError && (
          <p className="text-red-600 text-sm mb-4 text-center">
            {generalError}
          </p>
        )}
        <Button
          type="submit"
          variant="primary"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className={`font-proxima-nova w-full text-center py-2 px-4 xs:text-sm xs:py-1 rounded-[8px] transition-all duration-200 bg-brand text-white`}
        >
          Continue
        </Button>
      </form>
    </div>
  );
};

const styles = {
  header: {
    fontFamily: "Proxima Nova, sans-serif",
    fontWeight: 700,
    letterSpacing: "0%",
    backgroundClip: "text",
    color: "#080236",
  },
};

export default ForgotPasswordScreen;
