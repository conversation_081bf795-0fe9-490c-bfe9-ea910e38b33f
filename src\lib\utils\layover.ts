type Segment = {
  duration_minutes?: number;
};

type Flight = {
  duration: string;
  segments: Segment[];
};

export function parseDurationToMinutes(duration: string): number {
  if(duration === undefined || duration === null) return 0;
  const hoursMatch = duration.match(/(\d+)h/);
  const minutesMatch = duration.match(/(\d+)m/);

  const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
  const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;

  return hours * 60 + minutes;
}

export function formatLayover(durationMinutes: number): string {
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;

  if (hours === 0) return `${minutes}m`;
  if (minutes === 0) return `${hours}h`;
  return `${hours}h ${minutes}m`;
}

export function getFormattedLayoverTime(flight: Flight): string {
    const totalDuration = parseDurationToMinutes(flight.duration);
    const segmentDurations = flight.segments?.reduce(
      (sum, seg) => sum + (seg.duration_minutes || 0),
      0
    ) ?? 0;
  
    const layoverMinutes = totalDuration - segmentDurations;
    return formatLayover(layoverMinutes);
}

export const formatMinutestoTime = (minutes: number) => {
  const hrs = String(Math.floor(minutes/60)).padStart(2, '0')
  const mins = String(minutes % 60).padStart(2, '0')
  return `${hrs}: ${mins}`
}

export const parse24HourTimeToMinutes = (timeStr: string): number => {
  console.log(timeStr);
  
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 60 + minutes;
};
