import { FareCardProps } from "@/constants/models";
import { useFlightContext } from "@/context/FlightContext";
import { getAirlineName } from "@/lib/utils/airline";
import { CheckCircle, XCircle } from "lucide-react";
import React from "react";

const FareCard: React.FC<FareCardProps> = ({
  title,
  price,
  discount,
  selected = false,
  buttonLabel = "Select",
  flight,
  options,
  onClick,
}) => {
  const styles = {
    button: {
      background:
        "linear-gradient(267.79deg, #F2A1F2 -60.95%, #A195F9 6.26%, #707FF5 73.47%, #4B4BC3 140.67%, #1E1E76 207.88%)",
      color: "white",
      borderRadius: "100px",
    },
  };

  const { 
    sharedFlightResults
  } = useFlightContext();

  const handleClick = () => {
    onClick && onClick(null);
  }
  const segment = flight?.segments[0] || flight?.segments[1]
  const travelClasses = 
    typeof segment?.travel_class === 'object' && segment.travel_class !== null && 'class' in segment.travel_class
      ? ((segment.travel_class as { class: string }).class)
      : '';
    
  return (
    <div className="relative font-proxima-nova w-96 p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
        <div
          className={`flex flex-col gap-2 justify-between rounded-2xl bg-[#F8F9FF] border p-4 shadow-sm transition-all h-[100%]`}
        >
        {/* Top row */}
        <div className="flex justify-between items-center mb-1">
          <div className="flex items-center gap-2">
            <img
              src={flight?.supplier_logo}
              alt={flight?.airline}
              className="w-6 h-6 object-contain"
            />
            <span className="text-sm text-[#A195F9] font-medium">
              {flight && sharedFlightResults && getAirlineName(flight?.airline_code, sharedFlightResults.airline_data)}
            </span>
          </div>
          {flight && (<span className="text-sm text-[#A195F9] font-medium">{flight?.origin} - {flight?.destination}</span>)}
        </div>

        {/* Title */}
        <div className="flex flex-col">
          <div className="text-lg font-bold text-[#080236]">{title}</div>
          <div className="text-sm">
            Cabin&nbsp;:&nbsp;<span className="underline">{travelClasses}</span>
          </div>
        </div>
        {/* Benefits */}
        <ul className="text-sm space-y-1 mb-4">
          {options && options.map((item, i) => {
            const isNegative = item.toLowerCase().includes("no");

            return (
              <li key={i} className="flex items-center gap-2 text-[#707FF5]">
              {!isNegative ? (
                <CheckCircle fill="#A195F9" size={20} className="text-white" />
              ) : (
                <XCircle
                  fill="oklch(80.8% 0.114 19.571)"
                  size={20}
                  className="text-white"
                />
              )}
              <span className={i === 0 ? "" : ""}>{item}</span>
            </li>
            )})}
        </ul>

        <hr className="my-2 border-[#B4BBE8]" />

        {/* Price & Action */}
        <div className="flex flex-col items-center mt-auto">
          <span className="text-[#24C72F] font-semibold">{discount}</span>
          <span className="text-xl font-bold text-[#080236]">{price}</span>
          <button
            style={!selected ? styles.button : undefined}
            className={`mt-2 px-6 py-1 rounded-full text-sm border-2 transition`}
            disabled={selected}
            onClick={handleClick}
          >
            {selected ? 'Selected' : buttonLabel}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FareCard;
