import React from "react";
import { render, screen } from "@testing-library/react";
import OverlayCard from "./OverlayCard";

describe("OverlayCard", () => {
  const defaultProps = {
    title: "Test Title",
    message: "Test message goes here.",
    overlayImageUrl: "https://example.com/image.png",
    classname: "custom-class",
  };

  it("renders the title and message", () => {
    render(<OverlayCard {...defaultProps} />);
    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test message goes here.")).toBeInTheDocument();
  });

  it("renders the image with correct src and alt", () => {
    render(<OverlayCard {...defaultProps} />);
    const img = screen.getByAltText("alert") as HTMLImageElement;
    expect(img).toBeInTheDocument();
    expect(img.src).toBe(defaultProps.overlayImageUrl);
  });

  it("applies the custom classname to the image container", () => {
    render(<OverlayCard {...defaultProps} />);
    const imgContainer = screen.getByAltText("alert").parentElement;
    expect(imgContainer).toHaveClass("absolute");
    expect(imgContainer).toHaveClass("custom-class");
  });

  it("renders without crashing if no props are provided", () => {
    render(<OverlayCard />);
    // Should render empty h2, p, and img with undefined src
    expect(screen.getByRole("heading")).toBeInTheDocument();
    expect(screen.getByRole("img")).toBeInTheDocument();
  });
});