{"name": "nxvoy-playwright-framework", "version": "1.0.0", "description": "NxVoy Playwright Master Framework", "main": "index.js", "scripts": {"setup": "npx playwright install chromium", "test:ui": "npx ts-node src/test/runners/UiTestRunner.ts env_test locale_uk", "test:api": "npm run setup && npx ts-node src/test/runners/ApiTestRunner.ts env_test locale_uk", "test:accessibility": "npm run setup && npx ts-node src/test/runners/AccessibilityTestRunner.ts env_test locale_uk", "generate-report": "npx ts-node src/test/reporting/HtmlReporter.ts", "test:flightbooking": "npm run setup && npx ts-node src/test/runners/FlightBookingTestRunner.ts env_test locale_uk", "test:flightbooking:qa": "npm run setup && npx ts-node src/test/runners/FlightBookingTestRunner.ts env_qa locale_uk", "test:feeverification": "npm run setup && npx ts-node src/test/runners/FeeVerificationTestRunner.ts env_test locale_uk", "test:feeverification:qa": "npm run setup && npx ts-node src/test/runners/FeeVerificationTestRunner.ts env_qa locale_uk"}, "author": "NxVoy", "devDependencies": {"@axe-core/playwright": "^4.10.0", "@cucumber/cucumber": "^10.0.0", "@cucumber/pretty-formatter": "1.0.0", "@faker-js/faker": "8.0.2", "@types/glob": "^8.1.0", "@types/node": "^18.18.3", "@types/node-fetch": "2.6.2", "ajv": "^8.17.1", "archiver": "^7.0.1", "glob": "^10.3.10", "cucumber-tsflow": "4.4.4", "fs-extra": "^11.1.1", "multiple-cucumber-html-reporter": "^3.7.0", "playwright-merge-html-reports": "^0.2.8", "playwright-slack-report": "^1.1.80", "prettier": "3.1.1", "ts-node": "^10.9.1"}, "dependencies": {"@axe-core/playwright": "^4.10.0", "@playwright/test": "^1.48.0", "playwright": "^1.48.0", "playwright-core": "^1.48.0", "typescript": "^5.2.2"}, "overrides": {"@cucumber/messages": {"reflect-metadata": "^0.2.2"}}}