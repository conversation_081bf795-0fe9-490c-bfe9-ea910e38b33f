import { When, Then } from '@cucumber/cucumber';
import { ReviewAndPayPage } from '../../pages/ReviewAndPayPage';
import { PaymentPage } from '../../pages/PaymentPage';
import { ScreenshotHelper } from '../../utils/ScreenshotHelper';
import { Properties } from '../../properties/Properties';
import { WaitHelper } from '../../waits/WaitHelper';
import assert from 'assert';
import { fixture } from '../../fixtures/Fixture';

/**
 * Assert that the user is on the Review & Pay screen
 */
Then('Assert user is on Review&Pay screen', async function() {
  try {
    console.log('Verifying user is on Review & Pay screen...');
    
    // Increased wait time to ensure the page is fully loaded
    await fixture.page.waitForTimeout(5000);
    
    // Take a screenshot for debugging purposes
    await ScreenshotHelper.takeScreenshot('review-pay-screen-check', true);
    
    // Multiple retry attempts to check if we're on the Review & Pay screen
    let isOnReviewAndPayScreen = false;
    const maxRetries = 3;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        isOnReviewAndPayScreen = await ReviewAndPayPage.isUserOnReviewAndPayScreen();
        if (isOnReviewAndPayScreen) {
          console.log(`Successfully verified user is on Review & Pay screen (attempt ${attempt + 1})`);
          break;
        } else {
          console.log(`Review & Pay screen not verified, attempt ${attempt + 1} of ${maxRetries}`);
          
          if (attempt < maxRetries - 1) {
            console.log('Waiting before next retry...');
            await fixture.page.waitForTimeout(3000);
          }
        }
      } catch (attemptError) {
        console.warn(`Error during attempt ${attempt + 1}:`, attemptError);
        if (attempt < maxRetries - 1) {
          await fixture.page.waitForTimeout(3000);
        }
      }
    }
    
    // Assert with a clear error message
    assert(isOnReviewAndPayScreen, 'User is not on Review & Pay screen - Required elements not found after multiple attempts');
    
    console.log('Successfully verified user is on Review & Pay screen');
  } catch (error) {
    console.error('Error while verifying Review & Pay screen:', error);
    await ScreenshotHelper.takeScreenshot('review-pay-screen-error', true, true);
    throw error;
  }
});

/**
 * Fill credit card information from test data on the Review & Pay page
 */
When('user fill the Creditcard info using {string}', { timeout: 30000 }, async function (creditCardInfoKey: string) {
  try {
    console.log(`Filling credit card information from property: ${creditCardInfoKey}`);
    
    // Get the credit card data from properties
    const rawCreditCardData = Properties.getProperty(creditCardInfoKey);
    
    if (!rawCreditCardData) {
      throw new Error(`Credit card info not found for key: ${creditCardInfoKey}`);
    }
    
    // Parse the credit card data if it's a string, or use as is if it's already an object
    let creditCardData: Record<string, any>;
    
    if (typeof rawCreditCardData === 'string') {
      try {
        // Try to parse as JSON if it's a string representation of JSON
        creditCardData = JSON.parse(rawCreditCardData);
      } catch (e) {
        console.warn('Could not parse credit card data as JSON, trying to use as object');
        creditCardData = { 
          cardnumber: rawCreditCardData 
        };
      }
    } else if (typeof rawCreditCardData === 'object' && rawCreditCardData !== null) {
      // Use directly if it's already an object
      creditCardData = rawCreditCardData as Record<string, any>;
    } else {
      // Fallback for unexpected data types
      console.warn(`Unexpected credit card data type: ${typeof rawCreditCardData}`);
      throw new Error(`Credit card data from "${creditCardInfoKey}" has an unexpected format`);
    }
    
    // Log data for debugging
    console.log('Credit card data:', JSON.stringify(creditCardData, null, 2));
    
    // Wait for page to be stable before interacting with form
    await WaitHelper.waitForPageStable(3000);
    
    // Take screenshot before filling form
    await ScreenshotHelper.takeScreenshot('before-credit-card-info');
    
    // Check if credit card form is visible
    const isFormVisible = await PaymentPage.isCreditCardFormVisible();
    
    if (!isFormVisible) {
      console.warn('Credit card form not immediately visible, waiting longer...');
      
      // Wait a bit longer and try again
      await fixture.page.waitForTimeout(3000);
      
      const isFormVisibleRetry = await PaymentPage.isCreditCardFormVisible();
      
      if (!isFormVisibleRetry) {
        throw new Error('Credit card form is not visible after waiting');
      }
    }
    
    // Create a properly typed object for the payment page
    const paymentInfo = {
      cardnumber: creditCardData.cardnumber || '',
      expMonth: creditCardData.expMonth || '',
      eXPYear: creditCardData.eXPYear || '',
      cvv: creditCardData.cvv || '',
      ccName: creditCardData.ccName || ''
    };
    
    console.log('Passing payment info to page object:', paymentInfo);
    
    // Use the PaymentPage to fill credit card information
    const fillSuccess = await PaymentPage.fillCreditCardInfo(paymentInfo);
    
    if (!fillSuccess) {
      throw new Error('Failed to fill credit card information');
    }
    
    // Take screenshot after filling form
    await ScreenshotHelper.takeScreenshot('after-credit-card-info');
    
    console.log('Successfully filled credit card information');
  } catch (error) {
    console.error(`Error filling credit card information: ${error}`);
    await ScreenshotHelper.takeScreenshot('credit-card-info-error', true, true);
    throw error;
  }
});

/**
 * Fill billing address information from test data on the Review & Pay page
 */
When('user fill the Billing address using {string}', { timeout: 60000 }, async function (billingAddressKey: string) {
  try {
    console.log(`Filling billing address information from property: ${billingAddressKey}`);
    
    // Get the billing address data from properties
    const rawBillingAddressData = Properties.getProperty(billingAddressKey);
    
    if (!rawBillingAddressData) {
      throw new Error(`Billing address info not found for key: ${billingAddressKey}`);
    }
    
    // Parse the billing address data if it's a string, or use as is if it's already an object
    let billingAddressData: Record<string, any>;
    
    if (typeof rawBillingAddressData === 'string') {
      try {
        // Try to parse as JSON if it's a string representation of JSON
        billingAddressData = JSON.parse(rawBillingAddressData);
      } catch (e) {
        console.warn('Could not parse billing address data as JSON, trying to use as object');
        throw new Error(`Billing address data from "${billingAddressKey}" is not in the expected format`);
      }
    } else if (typeof rawBillingAddressData === 'object' && rawBillingAddressData !== null) {
      // Use directly if it's already an object
      billingAddressData = rawBillingAddressData as Record<string, any>;
    } else {
      // Fallback for unexpected data types
      console.warn(`Unexpected billing address data type: ${typeof rawBillingAddressData}`);
      throw new Error(`Billing address data from "${billingAddressKey}" has an unexpected format`);
    }
    
    // Log data for debugging
    console.log('Billing address data:', JSON.stringify(billingAddressData, null, 2));
    
    // Wait for page to be stable before interacting with form
    await WaitHelper.waitForPageStable(5000);
    
    // Take screenshot before filling form
    await ScreenshotHelper.takeScreenshot('before-billing-address-info');
    
    console.log('Looking for billing address form elements...');
    
    // Try multiple strategies to locate the billing address form
    try {
      // First try checking for the street address field
      const maxRetries = 3;
      let isFormVisible = false;
      
      for (let attempt = 0; attempt < maxRetries && !isFormVisible; attempt++) {
        try {
          console.log(`Attempt ${attempt + 1} to locate billing address form...`);
          
          // Try different selectors for the street address field (in case the ID has changed)
          const possibleSelectors = [
            PaymentPage.elements.streetAddress,
            'input[placeholder="Street Address"]',
            'input[id*="street"]',
            'input[class*="street"]'
          ];
          
          for (const selector of possibleSelectors) {
            console.log(`Trying selector: ${selector}`);
            const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 }).catch(() => false);
            
            if (isVisible) {
              console.log(`Found billing form element with selector: ${selector}`);
              isFormVisible = true;
              break;
            }
          }
          
          if (!isFormVisible) {
            console.log('Billing form not found yet, scrolling and waiting...');
            
            // Scroll down to ensure form is in view
            await fixture.page.evaluate(() => {
              window.scrollBy(0, 300);
            });
            
            // Wait a moment before trying again
            await fixture.page.waitForTimeout(3000);
          }
        } catch (attemptError) {
          console.warn(`Attempt ${attempt + 1} failed:`, attemptError);
        }
      }
      
      if (!isFormVisible) {
        // Try one more approach - take a screenshot to help debug
        await ScreenshotHelper.takeScreenshot('billing-address-form-not-found', true);
        
        // Check for any related element we might be able to use
        const anyFormElement = await fixture.page.locator('input[placeholder*="Address"], input[placeholder*="City"], input[placeholder*="State"]').isVisible().catch(() => false);
        
        if (!anyFormElement) {
          console.error('Could not find any billing address form elements');
          throw new Error('Billing address form is not visible after multiple attempts');
        } else {
          console.log('Found some form elements but not the street address field');
        }
      }
      
      console.log('Billing address form is visible and ready for input');
    } catch (error) {
      console.error('Error checking billing form visibility:', error);
      await ScreenshotHelper.takeScreenshot('billing-address-form-error', true, true);
      throw new Error('Could not verify billing address form visibility: ' + error);
    }
    
    // Create a properly typed object for the payment page
    const billingInfo = {
      streetAddress: billingAddressData.streetAddress || '',
      aptSuiteOther: billingAddressData.aptSuiteOther || '',
      city: billingAddressData.city || '',
      state: billingAddressData.state || '',
      zipcode: billingAddressData.zipcode || '',
      country: billingAddressData.country || ''
    };
    
    console.log('Passing billing address info to page object:', billingInfo);
    
    // Use the PaymentPage to fill billing address information
    const fillSuccess = await PaymentPage.fillBillingAddress(billingInfo);
    
    if (!fillSuccess) {
      throw new Error('Failed to fill billing address information');
    }
    
    // Take screenshot after filling form
    await ScreenshotHelper.takeScreenshot('after-billing-address-info');
    
    console.log('Successfully filled billing address information');
  } catch (error) {
    console.error(`Error filling billing address information: ${error}`);
    await ScreenshotHelper.takeScreenshot('billing-address-info-error', true, true);
    throw error;
  }
});

/**
 * Fill contact information and billing address from test data on the Review & Pay page
 */
When('user fill the contact info using {string}', { timeout: 60000 }, async function (contactInfoKey: string) {
  try {
    console.log(`Filling contact information and billing address from property: ${contactInfoKey}`);
    
    // Get the contact info data from properties
    const rawContactData = Properties.getProperty(contactInfoKey);
    
    if (!rawContactData) {
      throw new Error(`Contact info not found for key: ${contactInfoKey}`);
    }
    
    // Parse the contact data if it's a string, or use as is if it's already an object
    let contactData: Record<string, any>;
    
    if (typeof rawContactData === 'string') {
      try {
        // Try to parse as JSON if it's a string representation of JSON
        contactData = JSON.parse(rawContactData);
      } catch (e) {
        console.warn('Could not parse contact data as JSON, trying to use as object');
        throw new Error(`Contact data from "${contactInfoKey}" is not in the expected format`);
      }
    } else if (typeof rawContactData === 'object' && rawContactData !== null) {
      // Use directly if it's already an object
      contactData = rawContactData as Record<string, any>;
    } else {
      // Fallback for unexpected data types
      console.warn(`Unexpected contact data type: ${typeof rawContactData}`);
      throw new Error(`Contact data from "${contactInfoKey}" has an unexpected format`);
    }
      // Log data for debugging
    console.log('Contact info data:', JSON.stringify(contactData, null, 2));
    
    console.log('Starting enhanced contact form waiting strategy...');
    
    // Take screenshot before filling form
    await ScreenshotHelper.takeScreenshot('before-contact-info');
    
    // Wait for page to be stable with increased timeout
    try {
      await WaitHelper.waitForPageStable(10000);
    } catch (waitError) {
      console.warn(`Initial page stability wait failed: ${waitError.message}`);
      console.log('Continuing anyway...');
    }
    
    // Define multiple possible selectors for contact form elements to improve robustness
    const contactFormSelectors = [
      PaymentPage.elements.titleDropdown, 
      'button#title', 
      '.contactInfo button', 
      'input#name', 
      'input#Email',
      'input[name="phone"]',
      'div.flag',
      'section.contact-info, .contact-details, .contactInfo',
      'form:has(button#title), section:has(button#title)'
    ];
    
    // Enhanced waiting strategy with multiple fallbacks
    const { success, selector } = await WaitHelper.waitForAnySelector(contactFormSelectors, {
      maxAttempts: 5,         // Try multiple times
      initialTimeout: 3000,   // Start with 3s timeout
      maxTimeout: 20000,      // Maximum 20s total waiting time
      state: 'visible'        // Element must be visible
    });
    
    if (!success) {
      console.warn('Contact form elements not found through normal waiting!');
      console.log('Attempting last resort waiting strategy with Promise.race...');
      
      // Last resort: use Promise.race to try multiple approaches in parallel
      try {
        await Promise.race([
          // Wait for any potential load indicators to disappear
          fixture.page.waitForSelector('.loading-overlay, .spinner, [aria-busy="true"]', { state: 'detached', timeout: 10000 })
            .catch(err => console.log('No loading indicators found to wait for')),
            
          // Wait for any of our contact form indicators with extended timeout
          fixture.page.waitForSelector(contactFormSelectors.join(', '), { timeout: 15000 }),
          
          // Hard timeout as last fallback
          fixture.page.waitForTimeout(20000).then(() => {
            console.log('Hard timeout reached while waiting for contact form');
          })
        ]);
        
        // Take screenshot after waiting, even if we timed out
        await ScreenshotHelper.takeScreenshot('after-contact-form-wait');
        
        // Check if form is visible one last time
        for (const formSelector of contactFormSelectors) {
          const isVisible = await fixture.page.isVisible(formSelector, { timeout: 1000 }).catch(() => false);
          if (isVisible) {
            console.log(`Found contact form element: ${formSelector}`);
            break;
          }
        }
      } catch (raceError) {
        console.warn(`All contact form waiting strategies failed: ${raceError.message}`);
        // We'll continue anyway and let the form filling functions handle not finding elements
      }
    } else {
      console.log(`Contact form element found: ${selector}`);
    }
      // Create a properly typed object for the contact info
    const contactInfo = {
      title: contactData.title || '',
      name: contactData.name || '',
      countrycode: contactData.countrycode || '',
      phoneNumber: contactData.phoneNumber || '',
      emailId: contactData.emailId || ''
    };
    
    // Create object for billing address (which should be part of the same contactData)
    const billingInfo = {
      streetAddress: contactData.streetAddress || '',
      aptSuiteOther: contactData.aptSuiteOther || '',
      city: contactData.city || '',
      state: contactData.state || '',
      zipcode: contactData.zipcode || '',
      country: contactData.country || ''
    };
    
    console.log('Passing contact info to page object:', contactInfo);
    console.log('Passing billing info to page object:', billingInfo);
    
    // Fill the contact information first
    const contactFillSuccess = await PaymentPage.fillContactInfo(contactInfo);
    
    if (!contactFillSuccess) {
      throw new Error('Failed to fill contact information');
    }
    
    // Take screenshot after filling contact info
    await ScreenshotHelper.takeScreenshot('after-contact-info');
    
    console.log('Successfully filled contact information');
    
    // Now fill the billing address information
    const billingFillSuccess = await PaymentPage.fillBillingAddress(billingInfo);
    
    if (!billingFillSuccess) {
      throw new Error('Failed to fill billing address information');
    }
    
    // Take screenshot after filling billing info
    await ScreenshotHelper.takeScreenshot('after-billing-info');
    
    console.log('Successfully filled billing address information');
  } catch (error) {
    console.error(`Error filling contact information: ${error}`);
    await ScreenshotHelper.takeScreenshot('contact-info-error', true, true);
    throw error;
  }
});

/**
 * Click the Confirm & Pay button to complete the payment process
 */
Then('user click the {string} button', async function (buttonName) {
  if (buttonName !== 'confirm&pay') {
    throw new Error(`Button ${buttonName} is not supported in this step definition`);
  }
  
  try {
    console.log('Clicking the Confirm & Pay button...');
    
    // Take screenshot before clicking
    await ScreenshotHelper.takeScreenshot('before-confirm-and-pay');
    
    // Define selectors for the button - primary and fallbacks for better reliability
    const confirmPayButtonSelectors = [
      'button:has-text("Confirm & Pay")',
      'button:has-text("Confirm")',
      'button.px-4.py-2[style*="background: linear-gradient"]',
      '.px-4.py-2:has-text("Confirm")',
      'button[style*="background: linear-gradient"][style*="border-radius: 100px"]:has-text("Confirm")'
    ];
    
    // Try to find and click the button using multiple strategies
    let clickSuccessful = false;
    for (const selector of confirmPayButtonSelectors) {
      try {
        // Check if element exists before attempting click
        const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
        if (isVisible) {
          await fixture.page.click(selector);
          console.log(`Successfully clicked Confirm & Pay button using selector: ${selector}`);
          clickSuccessful = true;
          break;
        }
      } catch (error) {
        console.log(`Failed to click using selector ${selector}: ${error}`);
      }
    }
    
    if (!clickSuccessful) {
      // Last resort: try JavaScript click on any matching element
      try {
        const jsResult = await fixture.page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button'));
          const confirmPayButton = buttons.find(button => 
            button.textContent?.includes('Confirm & Pay') || 
            button.textContent?.includes('Confirm') ||
            (button.className?.includes('px-4') && button.className?.includes('py-2') && 
             button.style.background?.includes('linear-gradient'))
          );
          if (confirmPayButton) {
            confirmPayButton.click();
            return true;
          }
          return false;
        });
        console.log('Used JavaScript evaluation to find and click the Confirm & Pay button: ' + jsResult);
        clickSuccessful = jsResult;
      } catch (jsError) {
        console.error('JavaScript click also failed:', jsError);
      }
    }
    
    if (!clickSuccessful) {
      throw new Error('Failed to click the Confirm & Pay button using all available methods');
    }
    
    // Wait for navigation to complete
    await WaitHelper.waitForPageStable(5000);
    
    // Take screenshot after clicking
    await ScreenshotHelper.takeScreenshot('after-confirm-and-pay');
    
    console.log('Successfully clicked the Confirm & Pay button');
  } catch (error) {
    console.error(`Error clicking Confirm & Pay button: ${error}`);
    await ScreenshotHelper.takeScreenshot('confirm-and-pay-error', true, true);
    throw error;
  }
});

/**
 * Step definition for filling credit card information if required
 * - If card info already exists, this step will pass without changes
 * - If card info needs to be entered, it will fill in the details
 */
When('fill the credit card informtion if required', { timeout: 60000 }, async function () {
  try {
    console.log('Checking if credit card information needs to be filled...');
    
    // Get the credit card data from test properties
    const cardInfoKey = 'ui.nxvoy.flight_search_testdata.validinput.CCardInfo';
    const creditCardData = Properties.getProperty(cardInfoKey);
    
    if (!creditCardData) {
      throw new Error(`Credit card info not found for key: ${cardInfoKey}`);
    }
    
    console.log('Retrieved credit card data from properties');
    
    // Default values for card details
    let cardNumber = '****************';
    let expDate = '09/33';
    let cvv = '161';
    
    // Extract card details from the retrieved data
    if (creditCardData && typeof creditCardData === 'object') {
      const typedCardData = creditCardData as Record<string, any>;
      cardNumber = typedCardData.cardnumber || cardNumber;
      expDate = typedCardData.expMonth || typedCardData.expDate || expDate;
      cvv = typedCardData.cvv || cvv;
    }
    
    // Take screenshot before attempting to fill
    await ScreenshotHelper.takeScreenshot('before-cc-info-if-required');
    
    // Call the page object method to fill credit card info conditionally
    const success = await ReviewAndPayPage.fillCreditCardInfoIfRequired(cardNumber, expDate, cvv);
    
    if (!success) {
      console.warn('Failed to fill credit card information. May not be visible or already filled.');
    }
    
    // Take screenshot after attempting to fill
    await ScreenshotHelper.takeScreenshot('after-cc-info-if-required');
    
    console.log('Credit card info check and fill completed');
  } catch (error) {
    console.error(`Error in fill credit card info if required step: ${error}`);
    await ScreenshotHelper.takeScreenshot('cc-info-if-required-error', true, true);
    throw error;
  }
});

/**
 * Clicks the terms checkbox and Pay Now button
 */
When('user click the checkbox & {string} button', { timeout: 90000 }, async function (buttonName) {
  try {
    if (buttonName !== 'Pay Now') {
      throw new Error(`Button ${buttonName} is not supported in this step definition. Expected "Pay Now".`);
    }

    console.log('Clicking the checkbox and Pay Now button...');
    
    // Take screenshot before action
    await ScreenshotHelper.takeScreenshot('before-checkbox-and-pay', true);
    
    // Call the page object method to click the checkbox and Pay Now button
    const result = await ReviewAndPayPage.clickCheckboxAndPayNow();
    
    if (!result) {
      throw new Error('Failed to click checkbox and Pay Now button');
    }    // Enhanced navigation waiting with multiple fallback strategies
    console.log('Waiting for navigation after clicking Pay Now...');
    
    try {
      // First approach: try to wait for load states in parallel with extended timeout
      await Promise.race([
        fixture.page.waitForLoadState('domcontentloaded', { timeout: 60000 }).catch(e => {
          console.log(`DOMContentLoaded wait timeout: ${e.message}. Not critical.`);
          return null;
        }),
        fixture.page.waitForLoadState('networkidle', { timeout: 60000 }).catch(e => {
          console.log(`Network idle wait timeout: ${e.message}. Not critical.`);
          return null;
        })
      ]);
      
      // Second approach: wait for confirmation page URL or content patterns
      const confirmationSelectors = [
        'text="Your Ticket was Booked Successfully"',
        'text="Booking details has been sent to"',
        'div.text-\\[\\#080236\\].font-semibold.text-xl',
        'div.flex.text-\\[\\#1E1E76\\].text-2xl.font-semibold:has-text("Payment Summary")',
        'div:has-text("Order Number")'
      ];
      
      let confirmationDetected = false;
      const maxDetectionAttempts = 5;
      
      for (let attempt = 0; attempt < maxDetectionAttempts; attempt++) {
        console.log(`Checking for confirmation page elements (attempt ${attempt + 1}/${maxDetectionAttempts})...`);
        
        // Check each selector in parallel using Promise.allSettled for better performance
        const detectionResults = await Promise.allSettled(
          confirmationSelectors.map(selector => 
            fixture.page.isVisible(selector, { timeout: 2000 })
              .catch(() => false)
          )
        );
        
        // If any selector is visible, we've detected the confirmation page
        confirmationDetected = detectionResults.some(
          result => result.status === 'fulfilled' && result.value === true
        );
        
        if (confirmationDetected) {
          console.log(`✅ Successfully detected confirmation page elements on attempt ${attempt + 1}`);
          await ScreenshotHelper.takeScreenshot('confirmation-page-detected');
          break;
        }
        
        // If not the final attempt, wait before retrying
        if (attempt < maxDetectionAttempts - 1) {
          console.log('Waiting before next detection attempt...');
          await fixture.page.waitForTimeout(3000);
        }
      }
      
      if (!confirmationDetected) {
        console.log('⚠️ Could not detect confirmation page elements after multiple attempts');
        // Don't throw here, the next step will handle waiting for the confirmation page
      }
    } catch (waitError) {
      console.log(`Navigation wait error, but continuing: ${waitError.message}`);
      // Capture a screenshot for debugging
      await ScreenshotHelper.takeScreenshot('navigation-wait-error', true);
      // Don't throw here, just continue with the test
    }
    
    // Take screenshot after action
    await ScreenshotHelper.takeScreenshot('after-checkbox-and-pay');
    
    console.log('Successfully clicked checkbox and Pay Now button');
  } catch (error) {
    console.error(`Error clicking checkbox and Pay Now button: ${error}`);
    await ScreenshotHelper.takeScreenshot('checkbox-and-pay-error', true, true);
    throw error;
  }
});
