import { Airport, Flight } from "@/constants/models";
import FlightCard from "./FlightCard";
import { useSelector } from "react-redux";
import { selectPickedOutbound, selectPickedReturn } from '@/store/slices/flightJourney'

interface FlightList {
    outbound_flights: Flight[];
    inbound_flights?: Flight[];
    airport: { [iataCode: string]: Airport };
    onOutboundCardSelect?: (picked_flight: Flight, picked_class: string) => void;
    onInboundCardSelect?: (picked_flight: Flight, picked_class: string) => void;
    travel_class: string;
}

export default function FlightList({ outbound_flights, inbound_flights, airport, travel_class, onOutboundCardSelect, onInboundCardSelect }: FlightList) {

    const outbound = useSelector(selectPickedOutbound);
    const returnFlight = useSelector(selectPickedReturn);

    return (
        <div>
            {!outbound && <div>
                <div className="md:text-xl font-bold text-[#1E1E76] text-center">
                    Best Outbound Flights
                </div>
                {outbound_flights.map((item, index) => {
                    return <FlightCard key={index} flightType="OUT-BOUND" airport={airport} class_type={travel_class} flight={item} showDetails={index === 0} onCardSelect={onOutboundCardSelect} />
                })}
            </div>}

            {(!returnFlight && inbound_flights) && <div>
                <div className="md:text-xl font-bold text-[#1E1E76] text-center">
                    Best Inbound Flights
                </div>
                {inbound_flights.map((item, index) => {
                    return <FlightCard key={index} flightType="INBOUND" airport={airport} class_type={travel_class} flight={item} showDetails={index === 0} onCardSelect={onInboundCardSelect} />
                })}
            </div>}

        </div>
    )
}