import React from "react";
import { render, screen } from "@testing-library/react";
import FlightPricing from "./flight-pricing";

// Mock formatFlightPrice
jest.mock("@/lib/utils/formatPrice", () => ({
    formatFlightPrice: jest.fn((amount) => `USD ${amount?.amount ?? "0"}`),
}));

describe("FlightPricing", () => {
    const amount = { amount: 1234 };

    it("renders mobile view with cancellation and per person", () => {
        render(<FlightPricing amount={amount} isMobile />);
        expect(screen.getByText("*Free Cancellation within 24 hours of booking")).toBeInTheDocument();
        expect(screen.getByText("USD 1234")).toBeInTheDocument();
        expect(screen.getByText("per person")).toBeInTheDocument();
    });

    it("renders expanded view with for all passengers and no cancellation", () => {
        render(<FlightPricing amount={amount} isExpanded />);
        expect(screen.getByText("USD 1234")).toBeInTheDocument();
        expect(screen.getByText("for all passengers")).toBeInTheDocument();
        expect(screen.queryByText("*Free Cancellation within 24 hours of booking")).not.toBeInTheDocument();
    });

    it("renders default view with for all passengers and no cancellation", () => {
        render(<FlightPricing amount={amount} />);
        expect(screen.getByText("USD 1234")).toBeInTheDocument();
        expect(screen.getByText("for all passengers")).toBeInTheDocument();
        expect(screen.queryByText("*Free Cancellation within 24 hours of booking")).not.toBeInTheDocument();
    });

    it("does not render cancellation text if showCancellation is false (mobile)", () => {
        render(<FlightPricing amount={amount} isMobile showCancellation={false} />);
        expect(screen.queryByText("*Free Cancellation within 24 hours of booking")).not.toBeInTheDocument();
        expect(screen.getByText("USD 1234")).toBeInTheDocument();
        expect(screen.getByText("per person")).toBeInTheDocument();
    });

    it("does not render cancellation text if showCancellation is false (default)", () => {
        render(<FlightPricing amount={amount} showCancellation={false} />);
        expect(screen.queryByText("*Free Cancellation within 24 hours of booking")).not.toBeInTheDocument();
        expect(screen.getByText("USD 1234")).toBeInTheDocument();
        expect(screen.getByText("for all passengers")).toBeInTheDocument();
    });

    it("renders with custom amount", () => {
        render(<FlightPricing amount={{ amount: 999 }} />);
        expect(screen.getByText("USD 999")).toBeInTheDocument();
    });

    it("renders mobile view even if isExpanded is true", () => {
        render(<FlightPricing amount={amount} isMobile isExpanded />);
        expect(screen.getByText("per person")).toBeInTheDocument();
        expect(screen.queryByText("for all passengers")).not.toBeInTheDocument();
    });
});