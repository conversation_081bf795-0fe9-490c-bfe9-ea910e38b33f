#!/bin/sh

echo "Setting up <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>-Staged with Pre-Push Hooks..."

# Function to check if an NPM package is installed
test_installed() {
  if npm list -g --depth=0 "$1" >/dev/null 2>&1 || npm list --depth=0 "$1" >/dev/null 2>&1; then
    return 0  # Package is installed
  else
    return 1  # Package is not installed
  fi
}

# Function to install NPM packages if not already installed
install_if_missing() {
  if ! test_installed "$1"; then
    echo "Installing $1..."
    npm install "$1" --save-dev --force
  else
    echo "$1 is already installed. Skipping..."
  fi
}

# Step 1: Create lefthook.yml
echo "Creating lefthook.yml..."
cat > lefthook.yml <<EOL
pre-commit:
  parallel: true
  commands:
    eslint:
      description: "Run ESLint on staged files"
      glob: "src/**/*.{js,ts,tsx}"
      run: "npx eslint --fix {staged_files}"

    prettier:
      description: "Format code with Prettier"
      glob: "src/**/*.{js,ts,tsx,css,md,json}"
      run: "npx prettier --write {staged_files}"

    lint-staged:
      description: "Run lint-staged for additional checks"
      run: "npx lint-staged"

pre-push:
  parallel: true
  commands:
    build:
      description: "Ensure the project builds before pushing"
      run: "npm run build:dev"
EOL

echo "lefthook.yml created successfully."

# Step 2: Create .lintstagedrc.json
echo "Creating .lintstagedrc.json..."
cat > .lintstagedrc.json <<EOL
{
  "src/**/*.{js,ts,jsx,tsx}": "eslint --fix --force",
  "src/**/*.{json,md,css,scss,html}": "prettier --write --force"
}
EOL

echo "lintstagedrc.json created successfully."

# Step 3: Create eslint.config.js
echo "Creating eslint.config.js..."
cat > eslint.config.js <<EOL
import { defineConfig } from "eslint/config";
import prettierConfig from "eslint-config-prettier";

export default defineConfig([
  {
    ignores: ["node_modules/", "dist/", "build/", "coverage/", "public/", ".vscode/", ".next/", "out/"],
    files: ["**/*.js", "**/*.cjs", "**/*.mjs"],
    extends: ["eslint:recommended", prettierConfig],
    rules: {
      "prefer-const": "warn",
      "no-constant-binary-expression": "error"
    }
  },
  {
    ignores: ["node_modules/", "dist/", "build/", "coverage/", "public/", ".vscode/", ".next/", "out/"],
    files: ["**/*.ts", "**/*.tsx"],
    extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended", prettierConfig],
    parser: "@typescript-eslint/parser",
    plugins: ["@typescript-eslint"],
    rules: {
      "@typescript-eslint/no-unused-vars": "warn",
      "prefer-const": "warn"
    }
  }
]);
EOL

echo "eslint.config.js created successfully."

# Step 4: Create .eslintignore
echo "Creating .eslintignore..."
cat > .eslintignore <<EOL
node_modules/
dist/
build/
coverage/
public/
.vscode/
.next/
out/
EOL

echo ".eslintignore created successfully."

# Step 5: Install required dependencies
echo "Installing required dependencies..."
install_if_missing eslint
install_if_missing @eslint/js
install_if_missing @typescript-eslint/parser
install_if_missing @typescript-eslint/eslint-plugin
install_if_missing eslint-config-prettier
install_if_missing prettier
install_if_missing lint-staged
install_if_missing @evilmartians/lefthook
install_if_missing typescript
install_if_missing lefthook

# Step 6: Verify installation
echo "Verifying installation..."
npx eslint --version && npx prettier --version && npm lefthook --version && npx lint-staged --version && npx tsc --version && npx playwright --version

echo "Setup complete! 🎉"

echo "All set! Try making a commit and pushing to test the hooks."