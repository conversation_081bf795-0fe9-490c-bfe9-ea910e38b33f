import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { CollapsibleSection } from "./collapsible-section";

// src/components/chat/ui/collapsible-section.test.tsx

const MockIcon = () => <span data-testid="mock-icon">Icon</span>;

describe("CollapsibleSection", () => {
    const title = "Section Title";
    const childrenText = "Section Content";

    it("renders title and icon", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />}>
                {childrenText}
            </CollapsibleSection>
        );
        expect(screen.getByText(title)).toBeInTheDocument();
        expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
    });

    it("renders children when open (defaultOpen=true)", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />}>
                <div>{childrenText}</div>
            </CollapsibleSection>
        );
        expect(screen.getByText(childrenText)).toBeInTheDocument();
    });

    it("does not render children when closed (defaultOpen=false)", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />} defaultOpen={false}>
                <div>{childrenText}</div>
            </CollapsibleSection>
        );
        expect(screen.queryByText(childrenText)).not.toBeInTheDocument();
    });

    it("toggles open/close state when button is clicked", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />}>
                <div>{childrenText}</div>
            </CollapsibleSection>
        );
        const button = screen.getByRole("button");
        // Initially open
        expect(screen.getByText(childrenText)).toBeInTheDocument();
        fireEvent.click(button);
        // Should be closed
        expect(screen.queryByText(childrenText)).not.toBeInTheDocument();
        fireEvent.click(button);
        // Should be open again
        expect(screen.getByText(childrenText)).toBeInTheDocument();
    });

    it("shows correct SVG icon for open/closed state", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />}>
                <div>{childrenText}</div>
            </CollapsibleSection>
        );
        const button = screen.getByRole("button");
        // Open state: up arrow
        expect(button.querySelector("svg")).toBeInTheDocument();
        fireEvent.click(button);
        // Closed state: down arrow
        expect(button.querySelector("svg")).toBeInTheDocument();
    });

    it("children are hidden after toggling closed", () => {
        render(
            <CollapsibleSection title={title} icon={<MockIcon />}>
                <div>{childrenText}</div>
            </CollapsibleSection>
        );
        const button = screen.getByRole("button");
        fireEvent.click(button);
        expect(screen.queryByText(childrenText)).not.toBeInTheDocument();
    });
});