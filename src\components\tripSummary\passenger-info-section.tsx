import PassengerDetails from "@/components/flightsummary/passengerDetails"

interface PassengerInfoSectionProps {
    tripSummaryDetails: any
    formErrors: any
    currentStep: number
}

const PassengerInfoSection = ({ tripSummaryDetails, formErrors, currentStep }: PassengerInfoSectionProps) => {
    // Keep track of traveler counts by type
    const travelerCounters: Record<string, number> = {}

    return (
        <div className="flex flex-col gap-4 mb-4">
            <div className="flex text-2xl font-semibold text-brand-black">Complete your passenger details</div>
            {tripSummaryDetails?.outboundTravelers.map((traveler: any, i: number) => {
                const travelerType = traveler.travelerType

                // Initialize traveler count if not present
                if (!(travelerType in travelerCounters)) {
                    travelerCounters[travelerType] = 0 // Start from 0
                }

                // Increment first, then use the value
                travelerCounters[travelerType]++
                const travelerIndex = travelerCounters[travelerType] // This will be 1, 2, 3...

                console.log("hellllo", travelerIndex, travelerCounters)

                return (
                    <PassengerDetails
                        key={`${travelerType}-${travelerIndex}`}
                        index={travelerIndex - 1} // Now starts from 1
                        travelerType={travelerType}
                        formerror={formErrors.passengers}
                        disabled={currentStep !== 1}
                    />
                )
            })}
        </div>

    )
}

export default PassengerInfoSection
