import React, { useState } from "react";
import { User, ChevronDown, PencilLine } from "lucide-react";
import ConfirmDeleteModal from "../ConfirmDeleteModal";

interface SavedPassengerProfileProps {
  profiles: any[];
  openStates: boolean[];
  onToggle: (idx: number) => void;
  onDelete?: (id: number) => void;
  onEdit?: (id: number) => void;
}

const gradientBox =
  "relative font-proxima-nova w-full p-px rounded-sm h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm";
const innerBox =
  "bg-[#F2F3FA] rounded-sm w-full h-full p-2 md:p-4 flex flex-col justify-between";
const closedBox =
  "bg-[#F2F3FA] rounded-sm px-2 md:px-4 py-2 flex items-center gap-4 shadow-md cursor-pointer justify-between";

const SavedPassengerProfile: React.FC<SavedPassengerProfileProps> = ({
  profiles,
  openStates,
  onToggle,
  onDelete,
  onEdit,
}) => {
  const [deleteIdx, setDeleteIdx] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  const handleDeleteClick = (idx: number) => {
    setDeleteIdx(idx);
    setModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (deleteIdx !== null && onDelete) {
      onDelete(profiles[deleteIdx].id);
    }
    setModalOpen(false);
    setDeleteIdx(null);
  };

  const handleCancel = () => {
    setModalOpen(false);
    setDeleteIdx(null);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancel}
        message="Are Sure you want to delete?"
      />
      {profiles.map((profile, idx) => (
        <div key={profile.id} className="flex flex-col">
          {/* Opened Card */}
          {openStates[idx] ? (
            <div className={gradientBox + " mb-4"} style={{ minHeight: 280 }}>
              <div className={innerBox}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User />
                    <span className="font-semibold text-base md:text-lg text-[#1E1E76] ml-4">{profile.name}</span>
                  </div>
                  <button className="text-[#1E1E76] text-xl md:text-2xl" onClick={() => onToggle(idx)}>
                    <ChevronDown
                      className={`text-gray-500 transform rotate-180`}
                    />
                  </button>
                </div>
                <div className="mt-4 md:mt-6">
                  <div className="text-[#1E1E76] mb-1">{profile.profile.userName}</div>
                  <div className="text-[#1E1E76] mb-1">{profile.profile.userType}</div>
                  <div className="text-[#1E1E76] mb-1">{profile.profile.gender}</div>
                  <div className="text-[#1E1E76] mb-1">{profile.profile.dob}</div>
                  <div className="text-[#1E1E76] mb-1">{profile.profile.country}</div>
                </div>
                <div className="flex flex-col md:flex-row gap-4 mt-6 md:mt-8">
                  <button className="border border-[#4B4BC3] text-[#1E1E76] rounded-full px-6 py-2 w-full md:w-auto" onClick={() => handleDeleteClick(idx)}>Delete User</button>
                  <button className="border border-[#4B4BC3] bg-[#F2F3FA] rounded-full px-6 py-2 w-full md:w-auto flex items-center justify-center gap-2" onClick={() => onEdit && onEdit(profile.id)}>
                    <PencilLine />
                    Edit User
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Closed Card with gradient border and solid background
            <div className={gradientBox + " mb-4"}>
              <div className={closedBox} onClick={() => onToggle(idx)}>
                <div className="flex md:flex-row">
                  <User />
                  <span className="font-semibold text-base md:text-lg text-[#1E1E76] ml-4">{profile.name}</span>
                </div>
                <ChevronDown
                  className={`text-gray-500`}
                />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SavedPassengerProfile; 