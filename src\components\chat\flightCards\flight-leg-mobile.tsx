"use client";

import type React from "react";
import { ChevronDown } from "lucide-react";
import FlightSegmentDetails from "./flight-segment-details";
import FlightPricing from "./flight-pricing";
import { AppState } from "@/store/store";
import { useSelector } from "react-redux";

interface Flight {
    id: string;
    supplier_logo: string;
    departure_date: string;
    departure_time_ampm: string;
    arrival_time_ampm: string;
    arrival_date: string;
    duration: string;
    origin: string;
    destination: string;
    segments: any[];
    price: { amount: number };
}

interface FlightLegMobileProps {
    label: string;
    flightData: Flight;
    isOpen: boolean;
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
    getAirportDisplayName: (code: string, options: any) => string;
    airportOptions: any;
    onSelectFlight: () => void;
    isSelected: boolean;
}

const FlightLegMobile: React.FC<FlightLegMobileProps> = ({
    label,
    flightData,
    isOpen,
    setIsOpen,
    getAirportDisplayName,
    airportOptions,
    onSelectFlight,
    isSelected,
}) => {
    const chatThreadDetails = useSelector((state: AppState) => state.chatThread);
    const { tripType } = chatThreadDetails;
    const isDirect = flightData.segments.length === 1;
    const amount = flightData.price;

    return (
        <div className="block xl:hidden bg-[#F2F3FA]">
            <div className="w-full fixed left-[48%] top-[102px] lg:top-[130px] ">
                <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                    <img
                        src={flightData.supplier_logo || "/placeholder.svg"}
                        alt="airline logo"
                        className="w-6 h-6 object-contain"
                    />
                </div>
            </div>
            <div className="px-4 py-4">
                <div className="flex items-start justify-center mb-3">
                    <div className="flex items-center gap-3 ">

                        <div className="text-[#4B4BC3] font-bold text-base">{label}</div>
                    </div>
                </div>

                {!isOpen ? (
                    <>
                        <div className="flex justify-between items-center mb-4">
                            <div className="flex items-center gap-12 w-full justify-between">
                                <div>
                                    <h2 className="font-bold text-base text-[#1E1E76]">
                                        {flightData.departure_date}
                                    </h2>
                                    <p className="text-sm font-medium text-[#1E1E76]">
                                        {flightData.departure_time_ampm} |{" "}
                                        <span className="text-[#707FF5] font-medium text-sm">
                                            {getAirportDisplayName(flightData.origin, airportOptions)}
                                        </span>
                                    </p>
                                </div>

                                <div className="flex flex-col items-center relative">
                                    <div className="text-[#707FF5] font-medium text-base relative top-2 ">
                                        {flightData.duration}
                                    </div>
                                    <div className="flex items-center">
                                        <img
                                            src="https://storage.googleapis.com/nxvoytrips-img/ChatPage/flight-travel.svg"
                                            alt="air travel"
                                        />
                                    </div>
                                    <p className="text-[#24C72F] text-sm relative bottom-2 ">
                                        {isDirect
                                            ? "Connect"
                                            : `${flightData.segments.length - 1} Stop${flightData.segments.length - 1 > 1 ? "s" : ""}`}
                                    </p>
                                </div>

                                <div>
                                    <h2 className="font-bold text-base text-[#1E1E76]">
                                        {flightData.arrival_date}
                                    </h2>
                                    <p className="text-sm text-[#1E1E76]">
                                        {flightData.arrival_time_ampm} |{" "}
                                        <span className="text-[#707FF5] text-sm">
                                            {getAirportDisplayName(flightData.destination, airportOptions)}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="relative">
                            <FlightPricing amount={amount} isMobile={true} />
                            <div className="absolute bottom-0 left-[48%]">
                                <ChevronDown
                                    onClick={() => setIsOpen(!isOpen)}
                                    className="h-7 w-7 cursor-pointer transition-transform text-[#080236]"
                                />
                            </div>
                        </div>
                    </>
                ) : (
                    <>
                        <FlightSegmentDetails
                            segments={flightData.segments}
                            getAirportDisplayName={getAirportDisplayName}
                            airportOptions={airportOptions}
                            isMobile={true}
                        />

                        <div className="mb-4 relative">
                            <FlightPricing amount={amount} isMobile={true} />
                            <div className="absolute bottom-0 left-[48%]">
                                <ChevronDown
                                    onClick={() => setIsOpen(!isOpen)}
                                    className="h-7 w-7 cursor-pointer transition-transform text-[#080236] rotate-180"
                                />
                            </div>
                        </div>


                    </>
                )}
            </div>

        </div>
    );
};

export default FlightLegMobile;
