import React from "react";
import { render, screen } from "@testing-library/react";
import FlightHeader from "./flight-header";

const mockImages = new Map<number, string>([
    [1, "img1.jpg"],
    [2, "img2.jpg"],
    [3, "img3.jpg"],
]);

describe("FlightHeader", () => {
    it("renders image and badge for rank 1 (Best Value)", () => {
        render(<FlightHeader rank={1} flightRankImage={mockImages} />);
        const img = screen.getByAltText("Flight Rank") as HTMLImageElement;
        expect(img).toBeInTheDocument();
        expect(img.src).toContain("img1.jpg");
        const badge = screen.getByText("Best Value");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass("bg-[#4B4BC3]");
    });

    it("renders image and badge for rank 2 (More Flex)", () => {
        render(<FlightHeader rank={2} flightRankImage={mockImages} />);
        const img = screen.getByAltText("Flight Rank") as HTMLImageElement;
        expect(img).toBeInTheDocument();
        expect(img.src).toContain("img2.jpg");
        const badge = screen.getByText("More Flex");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass("bg-[#4DC34B]");
    });

    it("renders image and badge for rank 3 (Fully Flexible)", () => {
        render(<FlightHeader rank={3} flightRankImage={mockImages} />);
        const img = screen.getByAltText("Flight Rank") as HTMLImageElement;
        expect(img).toBeInTheDocument();
        expect(img.src).toContain("img3.jpg");
        const badge = screen.getByText("Fully Flexible");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass("bg-[#1E1E76]");
    });

    it("renders default badge and image for unknown rank", () => {
        render(<FlightHeader rank={99} flightRankImage={mockImages} />);
        const img = screen.getByAltText("Flight Rank") as HTMLImageElement;
        expect(img).toBeInTheDocument();
        expect(img.src).toContain(""); // fallback to empty string
        const badge = screen.getByText("Best Value");
        expect(badge).toBeInTheDocument();
        expect(badge).toHaveClass("bg-[#4B4BC3]");
    });

    it("renders empty image src if rank not in map", () => {
        render(<FlightHeader rank={5} flightRankImage={mockImages} />);
        const img = screen.getByAltText("Flight Rank") as HTMLImageElement;
        expect(img.src).toContain(""); // fallback to empty string
    });
});