"use client";

import type React from "react";
import { createContext, useState, useEffect, useContext } from "react";
import { useDispatch } from "react-redux";
import { signIn, useSession } from "next-auth/react";
import { useMobileAppAuth } from "@/hooks/use-Mobile-App-Auth";
import { setLoggedInUser } from "@/store/slices/loggedInUser";
import { signOut } from "next-auth/react";
import axios from "axios";
import { UserDetails, setCurrentUser } from "@/store/slices/userDetails";
import { loginSuccess } from "@/store/slices/authSlice";
import { clearReduxOnLogout } from "@/store/clearRedux"
import { isAppUser } from "@/lib/utils/flightUtils";
import { useTokenMonitor } from "@/hooks/useTokenMonitor";
interface AuthContextType {
  token: string | null;
  setToken: (token: string | null) => void;
}

// ✅ Define AuthContext BEFORE using it
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const dispatch = useDispatch();
  const [token, setToken] = useState<string | null>(null);
  const [checkedParams, setCheckedParams] = useState(false);

  // Use custom hook for mobile app authentication via URL params
  useMobileAppAuth({
    setToken,
    setCheckedParams,
  });

  const handleInvalidSession = async () => {
    console.log("Invalid or missing session, logging out...");
    await clearReduxOnLogout();
    signOut({ redirect: false });

  };


  // Only run session-based logic if we didn't find accessToken in params
  useEffect(() => {
    if (!checkedParams) return;
    // Add status check to prevent premature execution
    if (status === "loading") return;
    // If token is already set from params, skip session logic
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      if (params.get("accessToken")) return;
    }
    if (session?.error === "RefreshAccessTokenError") {
      handleInvalidSession();
      return;
    }

    if (session?.accessToken && session?.user) {
      console.log("valid have session user", session.user);
      dispatch(setLoggedInUser(session.user));
      setToken(session.accessToken);
    } else {
      console.log("No session or accessToken, clearing token");
      handleInvalidSession();
    }
  }, [session, status, dispatch, checkedParams]);

  const fetchUserDetails = async () => {
    try {
      if (!token) {
        return;
      }

      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_ENDPOINT}/api/v1/auth/user/get-details`,
        {
          headers: {
            Authorization: `Bearer ${session?.accessToken || token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Failed to fetch user details");
      }


      const data = await response.data;
      dispatch(setCurrentUser(data?.detail?.data as UserDetails));
    } catch (error: any) {
      if (error.status === 401) {
        console.log('I am unauthorized, logout now');
        handleInvalidSession();
      }
      console.error("Error fetching user details:", error);
    }
  };

  useEffect(() => {
    if (!isAppUser()) {
      fetchUserDetails();
    }
  }, [token]);

  const { isTokenExpired, tokenExpiresIn, hasValidToken } = useTokenMonitor();
  if (isTokenExpired) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <h2>Session Expired</h2>
        <p>Please login again</p>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ token, setToken }}>
      {children}
    </AuthContext.Provider>
  );
}

// Define useAuth hook AFTER AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
