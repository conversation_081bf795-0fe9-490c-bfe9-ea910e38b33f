import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

const GuestDetailsForm = () => {
  return (
    <div className="rounded-xl font-proxima-nova w-full p-px rounded-2xl h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
        <div className="bg-gray-100 rounded-xl px-6 pb-6 pt-3">
      <h3 className="text-[24px] font-semibold mb-2">Guest Name</h3>
      <p className="text-sm text-[#FF3B3F] mb-4">The guest checking into each hotel room must be 18 or older, present a valid Photo ID and credit card.</p>

      <div className="grid md:grid-cols-2 gap-4 mb-4">
        <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
          <Input
            placeholder="First Name*"
            className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-full text-[#080236] placeholder-[#707FF5]"
          />
        </div>
        <div className="relative font-proxima-nova w-full p-px rounded-full h-auto bg-gradient-to-tr from-[#F2A1F2] via-[#A195F9] via-[#707FF5] via-[#4B4BC3] to-[#1E1E76] shadow-sm">
          <Input
            placeholder="Last Name*"
            className="bg-indigo-50 border-indigo-100 focus:border-indigo-300 rounded-full text-[#080236] placeholder-[#707FF5]"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox id="same-name" />
        <label htmlFor="same-name" className="text-sm text-[#080236]">
          Guest name and name on card are the same
        </label>
      </div>
    </div>
    </div>
    
  );
};

export default GuestDetailsForm;
