import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"



type SelectLanguageProps = {};

export const SelectLanguage: React.FC<SelectLanguageProps> = () =>  {
    return (
        <Select>
          <SelectTrigger
            style={{ fontWeight: 500 }}
            className="
              md:text-[12px] text-[8px] 
              md:w-[150px] h-[30px] 
              w-[110px]
              bg-[#E6E3FF] border border-[#B4BBE8] 
              text-[#080236] outline-none shadow-none 
              focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 
              md:[&>svg]:h-[12px] md:[&>svg]:w-[12px] 
              [&>svg]:h-4 [&>svg]:w-3 
              [&>svg]:stroke-[3]
            "
          >
            <SelectValue placeholder="Choose Language" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="apple">English UK</SelectItem>
              {/* <SelectItem value="banana">Japanese</SelectItem> */}
              {/* <SelectItem value="blueberry">Chinese</SelectItem> */}
            </SelectGroup>
          </SelectContent>
        </Select>
    )
}