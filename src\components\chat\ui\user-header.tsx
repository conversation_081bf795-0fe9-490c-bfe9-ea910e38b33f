import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface UserHeaderProps {
    profilePicture?: string
    firstName?: string
    lastName?: string
    getInitials: (user: any) => string
    user: any
}

export function UserHeader({ profilePicture, firstName, lastName, getInitials, user }: UserHeaderProps) {
    return (
        <div className="p-6 border-b flex items-center space-x-4">
            <div className="rounded-full overflow-hidden w-12 h-12">
                <Avatar className="w-full h-full">
                    <AvatarImage src={profilePicture || ""} alt="Profile" />
                    <AvatarFallback className="text-white bg-gradient-to-r from-[#4B4BC3] to-[#707FF5] text-lg">
                        {getInitials(user)}
                    </AvatarFallback>
                </Avatar>
            </div>
            <div className="flex items-center">
                <span className="font-semibold text-base">{firstName ? `${firstName} ${lastName || ""}` : "Loading..."}</span>
            </div>
        </div>
    )
}
