import { LocaleHelper } from "../properties/LocaleHelper";
import { Properties } from "../properties/Properties";

const multiReport = require('multiple-cucumber-html-reporter');

multiReport.generate({
  jsonDir: 'test-reports/cucumber-json-reports',
  reportPath: `./test-reports/cucumber-html-reports`,
  reportName: 'Test Report',
  pageTitle: 'Test Report',
  displayDuration: true,
  displayReportTime: true,
  metadata: {
    browser: {
      name: 'chrome',
      version: '114.0.5735.199'
    },
    device: 'Local test machine',
    platform: {
      name: 'ubuntu',
      version: '20.04'
    }
  },
  customData: {
    title: 'Test Execution Info',
    data: [
      { label: 'Project', value: 'NxVoy Trips' },
      { label: 'Environment', value: Properties.ENVIRONMENT },
      { label: 'Locale', value: LocaleHelper.LOCALE },
      { label: 'Execution Start Time', value: new Date().toLocaleString() },
      { label: 'Execution End Time', value: new Date().toLocaleString() },
      { label: 'Tested By', value: 'QA Team' }
    ],
  },
});