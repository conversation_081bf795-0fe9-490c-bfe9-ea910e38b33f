"use client"
import { useState, useEffect } from 'react'

export const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [activeItem, setActiveItem] = useState('Features')

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = ['Features', 'Sign up', 'Connect']
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  return (
            <header className="absolute top-[23px] left-[27px] sm:top-[30px] sm:left-[30px] lg:top-[60px] lg:left-[60px] z-50">
              <div className="flex items-center">
                <img
                  src="/images/Logo.png"
                  alt="NXVoy Logo"
                  className="w-[45.82px] h-[11.3px] sm:w-[77.91px] sm:h-[19.21px] lg:w-[150px] lg:h-[37px]"
                />
              </div>
            </header>
  )
}
