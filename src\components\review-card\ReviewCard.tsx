import React from "react";

interface ReviewProps {
  customerName: string;
  location: string;
  feedback: string;
  rating: number;
}

const ReviewCard = ({
  customerName,
  location,
  feedback,
  rating,
}: ReviewProps) => {
  return (
    <div className="flex justify-center items-start flex-col w-full">
      <div className="flex justify-center items-center gap-2.5">
        {[...Array(5)].map((_, i) => (
          <img
            key={i}
            src={
              i < rating
                ? "https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-filled.png"
                : "https://storage.googleapis.com/nxvoytrips-img/Homepage/Testimonials/star-outline.png"
            }
            width={29}
            height={29}
            alt="rating star"
            className="object-contain m-0"
          />
        ))}
      </div>
      <h3 className="mt-3 xs:mt-1 font-proxima-nova text-lucky-blue text-[14px] font-bold">
        {customerName}, {location}
      </h3>
      <p className="mt-3 xs:mt-1 font-proxima-nova w-full sm:text-xs lg:text-sm xl:text-sm xs:text-xs info-text">
        {feedback}
      </p>
    </div>
  );
};

export default ReviewCard;
