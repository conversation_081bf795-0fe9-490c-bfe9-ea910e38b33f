import { before, after, binding, beforeAll, CucumberAttachments } from 'cucumber-tsflow';
import { ApiService } from '../services/ApiService';
import { Browser, chromium, firefox, webkit } from 'playwright';
import { BrowserContext } from 'playwright';
import { fixture } from "../fixtures/Fixture";
import { ScreenshotHelper } from "../utils/ScreenshotHelper";
import { ConsentHelper } from "../utils/ConsentHelper";
import { ShasaChatCleaner } from "../utils/ShasaChatCleaner";
import { CIHelper } from "../utils/CIHelper";

export let currentScenarioName: string;
let browser: Browser;
let context: BrowserContext;


@binding([CucumberAttachments])
export class Hooks {

  public constructor(private readonly att: CucumberAttachments) { }

  @before('@api')
  public setupApiTest(scenario: any) {
    ApiService.reset();
    currentScenarioName = scenario.pickle.name;
  }  invokeBrowser = () => {
    const browserType = fixture.browser || "chrome";
    // Get browser options from CIHelper to ensure headless mode in CI
    const options = CIHelper.getBrowserLaunchOptions();
    
    switch (browserType) {
      case "chrome":
        return chromium.launch(options);
      case "firefox":
        return firefox.launch(options);
      case "webkit":
        return webkit.launch(options);
      default:
        throw new Error("Please set the proper browser!")
    }
  }
  
  @before('@ui')
  public async setUpUITest(scenario: any) {
    browser = await this.invokeBrowser();
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    fixture.page = await context.newPage();
    fixture.page.setDefaultTimeout(90000); // Set default timeout for all page operations
    currentScenarioName = scenario.pickle.name;
    
    // Check if we're in debug mode for enhanced logging
    const isDebugMode = process.env.DEBUG_SCREENSHOTS === 'true';    // Configure screenshot settings without creating directories
    ScreenshotHelper.configure(!isDebugMode); // invert because the param is "captureOnlyOnFailure"
    
    if (isDebugMode) {
      console.log('🔍 DEBUG MODE ENABLED: Screenshots will be captured for all steps');
    } else {
      console.log('📸 Normal screenshot mode: Capturing only on failures and critical steps');
    }
    
    // Automatically handle cookie consent dialog if it appears during UI tests
    try {
      await ConsentHelper.handleConsentDialog('accept', 2000);
    } catch (error) {
      console.log('No cookie consent dialog found or error handling it:', error);
    }
  }

  @before('@accessibility')
  public async setUpAccessibilityTest(scenario: any) {
    browser = await this.invokeBrowser();
    context = await browser.newContext();
    fixture.page = await context.newPage();
    currentScenarioName = scenario.pickle.name;
  }

  @after('@api')
  public async tearDownApiTest() {
    this.att.attach(ApiService.requestAsString(), 'text/plain');
    this.att.attach(
      `Response body: ${JSON.stringify(
        await ApiService.response.text(),
        null,
        2
      )}`,      'text/plain'
    );
  }

  @after('@ui')
  public async tearDownUiTest(scenario: any) {
    try {
      if (scenario.result.status === 'FAILED') {
        console.log(`🔴 Test failed: ${currentScenarioName}`);
        
        // Force capture screenshot on failure
        const screenshotPath = await ScreenshotHelper.takeScreenshot(
          `failed-${currentScenarioName.replace(/[:\/\\]/g, "-")}`, 
          true, 
          true
        );
        
        // Attach screenshot to report
        if (screenshotPath) {
          const screenshot = await fixture.page.screenshot();
          this.att.attach(screenshot, 'image/png');
        }      } else {
        console.log(`✅ Test passed: ${currentScenarioName} - No screenshots needed`);
      }
      
      // Always clean up any empty screenshot directories regardless of test result
      ScreenshotHelper.cleanupEmptyDirectories();
    } catch (error) {
      console.error(`Error in teardown: ${error}`);
      // Try to clean up even if there was an error in the teardown
      try {
        ScreenshotHelper.cleanupEmptyDirectories();
      } catch (cleanupError) {
        console.error(`Failed to clean up empty directories: ${cleanupError}`);
      }    } finally {
      try {
        // Only try to close the browser if it exists
        if (browser) {
          await browser.close();
        }
      } catch (error) {
        console.log('Browser was already closed or could not be closed:', error.message);
      }
    }
  }

  @after('@accessibility')
  public async tearDownAccessibilityTest() {
    await browser.close();
  }  /**
   * Hook to clear Shasa chat history after each flightbook scenario
   * This runs regardless of whether the test passes or fails
   * It navigates to the chat page using existing login steps before clearing history
   * Enhanced to handle login state safely for both success and failure cases
   */  @after('@flightbook')
  public async clearShasaChatHistory() {
    try {
      console.log(`🧹 Running post-test cleanup for Shasa chat history - Scenario: ${currentScenarioName}`);
      
      // Only attempt to clear history if the browser is still open
      if (browser && fixture.page) {
        // First, check if we're already logged in by looking for the user account icon
        console.log('Checking current login state before chat cleanup...');
        
        // Try to handle the user's login state safely
        await this.ensureProperLogoutIfNeeded();
        
        // Now proceed with the standard chat history cleanup
        // This is the original approach that was working before
        const success = await ShasaChatCleaner.completeChatHistoryCleanup();
        
        if (success) {
          console.log('✅ Shasa chat history cleanup completed successfully');
        } else {
          console.warn('⚠️ Shasa chat history cleanup completed with some issues');
          
          // Simple fallback - try direct cookie deletion
          try {
            console.log('Attempting direct cookie deletion as fallback...');
            await fixture.page.evaluate(() => {
              document.cookie = 'nxvoy_thread_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
              return true;
            });
            
            console.log('Direct cookie deletion completed');
          } catch (cookieError) {
            console.warn(`Cookie deletion error: ${cookieError}`);
          }
        }
      } else {
        console.log('Browser already closed, skipping chat history cleanup');
      }
    } catch (error) {
      console.error(`Error during chat history cleanup: ${error}`);
      // Don't rethrow - we don't want this to affect the test result
    }
  }
  
  /**
   * Helper method to check if user is logged in and log out properly if needed
   * This ensures we start from a consistent state before attempting to clear chat history
   */
  private async ensureProperLogoutIfNeeded(): Promise<void> {
    try {
      // Check if user account icon is visible (indicating logged in state)
      let isLoggedIn = false;
      
      // Try using Playwright locators first (preferred approach)
      try {
        // Look for the user icon with role and accessible name
        const userAccountIcon = fixture.page.getByRole('button', { name: 'User Account' }).or(
          fixture.page.getByRole('button').filter({ has: fixture.page.getByRole('img', { name: 'User' }) })
        );
        
        isLoggedIn = await userAccountIcon.isVisible({ timeout: 3000 }).catch(() => false);
        
        if (isLoggedIn) {
          console.log('Found user account icon with Playwright locator');
        } else {
          // Fallback to attribute-based locators
          const userIconByAttr = fixture.page.getByRole('button').filter({
            has: fixture.page.locator('span.flex.h-full.w-full')
          });
          
          isLoggedIn = await userIconByAttr.isVisible({ timeout: 2000 }).catch(() => false);
          
          if (isLoggedIn) {
            console.log('Found user account icon with attribute-based locator');
          }
        }
      } catch (locatorError) {
        console.log(`Error with modern locators: ${locatorError}`);
        
        // Fall back to legacy CSS selectors if modern locators fail
        const userAccountIconSelectors = [
          'span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer',
          'span[aria-haspopup="menu"]',
          '[data-state="closed"]:has(span.flex.h-full.w-full)',
          '.rounded-full'
        ];
        
        for (const selector of userAccountIconSelectors) {
          try {
            const isVisible = await fixture.page.isVisible(selector, { timeout: 3000 });
            if (isVisible) {
              console.log(`Found user account icon with legacy selector: ${selector}`);
              isLoggedIn = true;
              break;
            }
          } catch (error) {
            // Ignore errors for individual selectors
            console.log(`Selector ${selector} not visible`);
          }
        }
      }
      
      // If logged in, perform logout sequence
      if (isLoggedIn) {
        console.log('User is already logged in. Performing logout before chat cleanup...');
        
        try {
          // Click user account icon to open menu - use a simplified approach
          try {
            // Use a combination of modern locators and traditional selectors
            const accountIcon = fixture.page.getByRole('button', { name: 'User Account' })
              .or(fixture.page.locator('span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full.cursor-pointer'));
            
            // Try clicking with a more generous timeout
            await accountIcon.click({ timeout: 5000 });
            console.log('Successfully clicked user account icon');
          } catch (clickError) {
            console.warn(`Error clicking account icon: ${clickError}`);
            
            // Try with a simple CSS selector as fallback
            try {
              await fixture.page.click('span.relative.flex.h-10.w-10.shrink-0.overflow-hidden.rounded-full', { timeout: 3000 });
              console.log('Clicked user account icon with simplified CSS selector');
            } catch (fallbackError) {
              console.warn(`Fallback click also failed: ${fallbackError}`);
              // Continue with the process - we'll try other ways to clean up chat history
            }
          }
          
          // Wait for menu to appear
          await fixture.page.waitForTimeout(1000);
          
          // Try to find and click logout using a simpler approach
          let logoutClicked = false;
          
          // Wait a bit longer for menu to appear
          await fixture.page.waitForTimeout(2000);
          
          try {
            // Try with role and name first
            const logoutButton = fixture.page.getByRole('menuitem', { name: 'Logout' });
            if (await logoutButton.isVisible({ timeout: 3000 }).catch(() => false)) {
              await logoutButton.click();
              console.log('Clicked logout button with role locator');
              logoutClicked = true;
            }
          } catch (logoutError) {
            console.warn(`Error with role locator for logout: ${logoutError}`);
          }
          
          // If not clicked yet, try with CSS selectors
          if (!logoutClicked) {
            // Try simple CSS selectors
            const logoutSelectors = [
              'div[role="menuitem"]:has(img[alt="Logout"])',
              'div[role="menuitem"]:has-text("Logout")',
              'img[alt="Logout"]'
            ];
            
            // Try each selector
            for (const selector of logoutSelectors) {
              try {
                const isVisible = await fixture.page.isVisible(selector, { timeout: 2000 });
                if (isVisible) {
                  await fixture.page.click(selector);
                  console.log(`Clicked logout button with selector: ${selector}`);
                  logoutClicked = true;
                  break;
                }
              } catch (error) {
                console.log(`Logout selector ${selector} not visible or clickable`);
              }
            }
          }
          
          if (!logoutClicked) {
            // Simple JavaScript approach as last resort
            try {
              console.log('Using JavaScript to find and click logout button');
              await fixture.page.evaluate(() => {
                const menuItems = Array.from(document.querySelectorAll('div[role="menuitem"]'));
                const logoutItem = menuItems.find(item => 
                  item.textContent?.includes('Logout') || 
                  item.querySelector('img[alt="Logout"]')
                );
                
                if (logoutItem) {
                  (logoutItem as HTMLElement).click();
                  return true;
                }
                return false;
              });
            } catch (jsError) {
              console.warn(`JavaScript logout approach failed: ${jsError}`);
            }
          }
          
          // Wait for logout to complete and page to reload
          await fixture.page.waitForTimeout(3000);
          console.log('Logout completed successfully');
        } catch (error) {
          console.warn(`Error during logout process: ${error}`);
          // Try to refresh the page to get to a clean state
          await fixture.page.reload();
          await fixture.page.waitForTimeout(3000);
        }
      } else {
        console.log('User is not logged in. Proceeding with standard cleanup process.');
      }
    } catch (error) {
      console.warn(`Error checking login state: ${error}`);
      // If we can't determine login state, try to refresh the page to a clean state
      try {
        await fixture.page.reload();
        await fixture.page.waitForTimeout(3000);
      } catch (refreshError) {
        console.error(`Error refreshing page: ${refreshError}`);
      }
    }
  }
}
