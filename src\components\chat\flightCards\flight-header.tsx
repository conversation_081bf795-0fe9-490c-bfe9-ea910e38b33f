import type React from "react"

interface FlightHeaderProps {
    rank: number
    flightRankImage: Map<number, string>
}

const FlightHeader: React.FC<FlightHeaderProps> = ({ rank, flightRankImage }) => {
    const getBadgeConfig = (rank: number) => {
        switch (rank) {
            case 1:
                return { text: "Best Value", bgColor: "bg-[#4B4BC3]" }
            case 2:
                return { text: "More Flex", bgColor: "bg-[#4DC34B]" }
            case 3:
                return { text: "Fully Flexible", bgColor: "bg-[#1E1E76]" }
            default:
                return { text: "Best Value", bgColor: "bg-[#4B4BC3]" }
        }
    }

    const { text, bgColor } = getBadgeConfig(rank)

    return (
        <div className="relative overflow-visible">
            <img
                src={flightRankImage.get(rank) || ""}
                alt="Flight Rank"
                className="w-full h-24 lg:h-32 object-cover rounded-t-2xl"
            />
            {/* <PERSON>ge positioned on top of the border - now with better visibility */}
            <span
                className={`absolute -top-3 left-4 ${bgColor} text-white text-xs lg:text-sm px-3 py-1 rounded-full shadow-lg z-20`}
            >
                {text}
            </span>
        </div>
    )
}

export default FlightHeader
