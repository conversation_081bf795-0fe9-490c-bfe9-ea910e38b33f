import router from "next/router";

type Segment = {
  operator_code?: string;
  flight_number?: string;
};

export function getFlightCodes(segments: Segment[]): string {
  if (!segments || segments.length === 0) return "";

  const formattedCodes = segments.map((seg) => {
    const code = seg.operator_code?.toUpperCase() || "";
    const number = seg.flight_number || "";

    return code && number ? `${code} ${number}` : "";
  });

  return formattedCodes.filter(Boolean).join(", ");
}

export const todayDate = () => {
  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  return formattedDate;
};

export const isAppUser = () => {
  return localStorage.getItem("isAppUser")?.length > 0 || false;
};

export const setSearchParam = (key: string, value: string) => {
  const params = new URLSearchParams(window.location.search);
  params.set(key, value);
  router.push(`${window.location.pathname}?${params.toString()}`);
};