"use client"
import Image from 'next/image'
const destinations = [
  {
    id: 1,
    name: 'Paris, France',
    description: 'Experience the city of love',
    image: '/images/paris.jpg',
    rating: 4.8
  },
  {
    id: 2,
    name: 'Tokyo, Japan',
    description: 'Discover modern meets tradition',
    image: '/images/tokyo.jpg',
    rating: 4.9
  },
  {
    id: 3,
    name: 'New York, USA',
    description: 'The city that never sleeps',
    image: '/images/newyork.jpg',
    rating: 4.7
  }
]

export const Cityscape = () => {
  return (
    <section className="py-16 px-4 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Popular Destinations</h2>
          <button className="text-blue-600 hover:text-blue-700 font-medium">
            View all destinations
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {destinations.map((destination) => (
            <div 
              key={destination.id}
              className="rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="relative h-64">
                <Image
                  src={destination.image}
                  alt={destination.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6 bg-white">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-xl font-semibold">{destination.name}</h3>
                  <span className="flex items-center text-yellow-500">
                    ★ {destination.rating}
                  </span>
                </div>
                <p className="text-gray-600">{destination.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
